// Package extracted with entry point: Ox

var A8B=E((Fn5,e6B)=>{var hC0=AJ(),CP6=NE(),t6B=Z71(),KP6=(A,B)=>{A=new CP6(A,B);let Q=new hC0("0.0.0");if(A.test(Q))return Q;if(Q=new hC0("0.0.0-0"),A.test(Q))return Q;Q=null;for(let D=0;D<A.set.length;++D){let Z=A.set[D],G=null;if(Z.forEach((F)=>{let I=new hC0(F.semver.version);switch(F.operator){case">":if(I.prerelease.length===0)I.patch++;else I.prerelease.push(0);I.raw=I.format();case"":case">=":if(!G||t6B(I,G))G=I;break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${F.operator}`)}}),G&&(!Q||t6B(Q,G)))Q=G}if(Q&&A.test(Q))return Q;return null};e6B.exports=KP6});
var C8B=E((Xn5,V8B)=>{var X8B=NE(),PP6=(A,B,Q)=>{return A=new X8B(A,Q),B=new X8B(B,Q),A.intersects(B,Q)};V8B.exports=PP6});
var H8B=E((Vn5,K8B)=>{var SP6=kt(),jP6=qE();K8B.exports=(A,B,Q)=>{let D=[],Z=null,G=null,F=A.sort((J,X)=>jP6(J,X,Q));for(let J of F)if(SP6(J,B,Q)){if(G=J,!Z)Z=J}else{if(G)D.push([Z,G]);G=null,Z=null}if(Z)D.push([Z,null]);let I=[];for(let[J,X]of D)if(J===X)I.push(J);else if(!X&&J===F[0])I.push("*");else if(!X)I.push(`>=${J}`);else if(J===F[0])I.push(`<=${X}`);else I.push(`${J} - ${X}`);let Y=I.join(" || "),W=typeof B.raw==="string"?B.raw:String(B);return Y.length<W.length?Y:B}});
var J8B=E((Jn5,W8B)=>{var OP6=gj1(),TP6=(A,B,Q)=>OP6(A,B,"<",Q);W8B.exports=TP6});
var M6B=E((ii5,L6B)=>{var N6B=AJ(),uT6=(A,B,Q,D,Z)=>{if(typeof Q==="string")Z=D,D=Q,Q=void 0;try{return new N6B(A instanceof N6B?A.version:A,Q).inc(B,D,Z).version}catch(G){return null}};L6B.exports=uT6});
var Ox=E((Kn5,M8B)=>{var dC0=St(),N8B=Q71(),xP6=AJ(),L8B=HX0(),vP6=ru(),bP6=w6B(),fP6=q6B(),hP6=M6B(),gP6=T6B(),uP6=S6B(),mP6=k6B(),dP6=_6B(),cP6=v6B(),lP6=qE(),pP6=f6B(),iP6=g6B(),nP6=hj1(),aP6=c6B(),sP6=p6B(),rP6=Z71(),oP6=QS1(),tP6=EX0(),eP6=UX0(),AS6=D71(),BS6=DS1(),QS6=wX0(),DS6=zX0(),ZS6=F71(),GS6=NE(),FS6=kt(),IS6=n6B(),YS6=s6B(),WS6=o6B(),JS6=A8B(),XS6=Q8B(),VS6=gj1(),CS6=Y8B(),KS6=J8B(),HS6=C8B(),zS6=H8B(),ES6=q8B();M8B.exports={parse:vP6,valid:bP6,clean:fP6,inc:hP6,diff:gP6,major:uP6,minor:mP6,patch:dP6,prerelease:cP6,compare:lP6,rcompare:pP6,compareLoose:iP6,compareBuild:nP6,sort:aP6,rsort:sP6,gt:rP6,lt:oP6,eq:tP6,neq:eP6,gte:AS6,lte:BS6,cmp:QS6,coerce:DS6,Comparator:ZS6,Range:GS6,satisfies:FS6,toComparators:IS6,maxSatisfying:YS6,minSatisfying:WS6,minVersion:JS6,validRange:XS6,outside:VS6,gtr:CS6,ltr:KS6,intersects:HS6,simplifyRange:zS6,subset:ES6,SemVer:xP6,re:dC0.re,src:dC0.src,tokens:dC0.t,SEMVER_SPEC_VERSION:N8B.SEMVER_SPEC_VERSION,RELEASE_TYPES:N8B.RELEASE_TYPES,compareIdentifiers:L8B.compareIdentifiers,rcompareIdentifiers:L8B.rcompareIdentifiers}});
var Q8B=E((In5,B8B)=>{var HP6=NE(),zP6=(A,B)=>{try{return new HP6(A,B).range||"*"}catch(Q){return null}};B8B.exports=zP6});
var S6B=E((ai5,P6B)=>{var dT6=AJ(),cT6=(A,B)=>new dT6(A,B).major;P6B.exports=cT6});
var T6B=E((ni5,O6B)=>{var R6B=ru(),mT6=(A,B)=>{let Q=R6B(A,null,!0),D=R6B(B,null,!0),Z=Q.compare(D);if(Z===0)return null;let G=Z>0,F=G?Q:D,I=G?D:Q,Y=!!F.prerelease.length;if(!!I.prerelease.length&&!Y){if(!I.patch&&!I.minor)return"major";if(I.compareMain(F)===0){if(I.minor&&!I.patch)return"minor";return"patch"}}let J=Y?"pre":"";if(Q.major!==D.major)return J+"major";if(Q.minor!==D.minor)return J+"minor";if(Q.patch!==D.patch)return J+"patch";return"prerelease"};O6B.exports=mT6});
var Y8B=E((Wn5,I8B)=>{var MP6=gj1(),RP6=(A,B,Q)=>MP6(A,B,">",Q);I8B.exports=RP6});
var _6B=E((ri5,y6B)=>{var iT6=AJ(),nT6=(A,B)=>new iT6(A,B).patch;y6B.exports=nT6});
var c6B=E((Bn5,d6B)=>{var BP6=hj1(),QP6=(A,B)=>A.sort((Q,D)=>BP6(Q,D,B));d6B.exports=QP6});
var f6B=E((ti5,b6B)=>{var rT6=qE(),oT6=(A,B,Q)=>rT6(B,A,Q);b6B.exports=oT6});
var g6B=E((ei5,h6B)=>{var tT6=qE(),eT6=(A,B)=>tT6(A,B,!0);h6B.exports=eT6});
var gj1=E((Yn5,F8B)=>{var EP6=AJ(),G8B=F71(),{ANY:UP6}=G8B,wP6=NE(),$P6=kt(),D8B=Z71(),Z8B=QS1(),qP6=DS1(),NP6=D71(),LP6=(A,B,Q,D)=>{A=new EP6(A,D),B=new wP6(B,D);let Z,G,F,I,Y;switch(Q){case">":Z=D8B,G=qP6,F=Z8B,I=">",Y=">=";break;case"<":Z=Z8B,G=NP6,F=D8B,I="<",Y="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if($P6(A,B,D))return!1;for(let W=0;W<B.set.length;++W){let J=B.set[W],X=null,V=null;if(J.forEach((C)=>{if(C.semver===UP6)C=new G8B(">=0.0.0");if(X=X||C,V=V||C,Z(C.semver,X.semver,D))X=C;else if(F(C.semver,V.semver,D))V=C}),X.operator===I||X.operator===Y)return!1;if((!V.operator||V.operator===I)&&G(A,V.semver))return!1;else if(V.operator===Y&&F(A,V.semver))return!1}return!0};F8B.exports=LP6});
var hj1=E((An5,m6B)=>{var u6B=AJ(),AP6=(A,B,Q)=>{let D=new u6B(A,Q),Z=new u6B(B,Q);return D.compare(Z)||D.compareBuild(Z)};m6B.exports=AP6});
var k6B=E((si5,j6B)=>{var lT6=AJ(),pT6=(A,B)=>new lT6(A,B).minor;j6B.exports=pT6});
var n6B=E((Dn5,i6B)=>{var GP6=NE(),FP6=(A,B)=>new GP6(A,B).set.map((Q)=>Q.map((D)=>D.value).join(" ").trim().split(" "));i6B.exports=FP6});
var o6B=E((Gn5,r6B)=>{var JP6=AJ(),XP6=NE(),VP6=(A,B,Q)=>{let D=null,Z=null,G=null;try{G=new XP6(B,Q)}catch(F){return null}return A.forEach((F)=>{if(G.test(F)){if(!D||Z.compare(F)===1)D=F,Z=new JP6(D,Q)}}),D};r6B.exports=VP6});
var p6B=E((Qn5,l6B)=>{var DP6=hj1(),ZP6=(A,B)=>A.sort((Q,D)=>DP6(D,Q,B));l6B.exports=ZP6});
var q6B=E((pi5,$6B)=>{var hT6=ru(),gT6=(A,B)=>{let Q=hT6(A.trim().replace(/^[=v]+/,""),B);return Q?Q.version:null};$6B.exports=gT6});
var q8B=E((Cn5,$8B)=>{var z8B=NE(),uC0=F71(),{ANY:gC0}=uC0,CD1=kt(),mC0=qE(),kP6=(A,B,Q={})=>{if(A===B)return!0;A=new z8B(A,Q),B=new z8B(B,Q);let D=!1;A:for(let Z of A.set){for(let G of B.set){let F=_P6(Z,G,Q);if(D=D||F!==null,F)continue A}if(D)return!1}return!0},yP6=[new uC0(">=0.0.0-0")],E8B=[new uC0(">=0.0.0")],_P6=(A,B,Q)=>{if(A===B)return!0;if(A.length===1&&A[0].semver===gC0)if(B.length===1&&B[0].semver===gC0)return!0;else if(Q.includePrerelease)A=yP6;else A=E8B;if(B.length===1&&B[0].semver===gC0)if(Q.includePrerelease)return!0;else B=E8B;let D=new Set,Z,G;for(let C of A)if(C.operator===">"||C.operator===">=")Z=U8B(Z,C,Q);else if(C.operator==="<"||C.operator==="<=")G=w8B(G,C,Q);else D.add(C.semver);if(D.size>1)return null;let F;if(Z&&G){if(F=mC0(Z.semver,G.semver,Q),F>0)return null;else if(F===0&&(Z.operator!==">="||G.operator!=="<="))return null}for(let C of D){if(Z&&!CD1(C,String(Z),Q))return null;if(G&&!CD1(C,String(G),Q))return null;for(let K of B)if(!CD1(C,String(K),Q))return!1;return!0}let I,Y,W,J,X=G&&!Q.includePrerelease&&G.semver.prerelease.length?G.semver:!1,V=Z&&!Q.includePrerelease&&Z.semver.prerelease.length?Z.semver:!1;if(X&&X.prerelease.length===1&&G.operator==="<"&&X.prerelease[0]===0)X=!1;for(let C of B){if(J=J||C.operator===">"||C.operator===">=",W=W||C.operator==="<"||C.operator==="<=",Z){if(V){if(C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===V.major&&C.semver.minor===V.minor&&C.semver.patch===V.patch)V=!1}if(C.operator===">"||C.operator===">="){if(I=U8B(Z,C,Q),I===C&&I!==Z)return!1}else if(Z.operator===">="&&!CD1(Z.semver,String(C),Q))return!1}if(G){if(X){if(C.semver.prerelease&&C.semver.prerelease.length&&C.semver.major===X.major&&C.semver.minor===X.minor&&C.semver.patch===X.patch)X=!1}if(C.operator==="<"||C.operator==="<="){if(Y=w8B(G,C,Q),Y===C&&Y!==G)return!1}else if(G.operator==="<="&&!CD1(G.semver,String(C),Q))return!1}if(!C.operator&&(G||Z)&&F!==0)return!1}if(Z&&W&&!G&&F!==0)return!1;if(G&&J&&!Z&&F!==0)return!1;if(V||X)return!1;return!0},U8B=(A,B,Q)=>{if(!A)return B;let D=mC0(A.semver,B.semver,Q);return D>0?A:D<0?B:B.operator===">"&&A.operator===">="?B:A},w8B=(A,B,Q)=>{if(!A)return B;let D=mC0(A.semver,B.semver,Q);return D<0?A:D>0?B:B.operator==="<"&&A.operator==="<="?B:A};$8B.exports=kP6});
var s6B=E((Zn5,a6B)=>{var IP6=AJ(),YP6=NE(),WP6=(A,B,Q)=>{let D=null,Z=null,G=null;try{G=new YP6(B,Q)}catch(F){return null}return A.forEach((F)=>{if(G.test(F)){if(!D||Z.compare(F)===-1)D=F,Z=new IP6(D,Q)}}),D};a6B.exports=WP6});
var v6B=E((oi5,x6B)=>{var aT6=ru(),sT6=(A,B)=>{let Q=aT6(A,B);return Q&&Q.prerelease.length?Q.prerelease:null};x6B.exports=sT6});
var w6B=E((li5,U6B)=>{var bT6=ru(),fT6=(A,B)=>{let Q=bT6(A,B);return Q?Q.version:null};U6B.exports=fT6});

module.exports = Ox;
