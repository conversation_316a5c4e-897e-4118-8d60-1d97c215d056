// Package extracted with entry point: ml2

var $F0=E((HT2)=>{Object.defineProperty(HT2,"__esModule",{value:!0});HT2.parseKeyPairsIntoRecord=HT2.parsePairKeyValue=HT2.getKeyPairs=HT2.serializeKeyPairs=void 0;var ts4=ZQ(),Xu=wF0();function es4(A){return A.reduce((B,Q)=>{let D=`${B}${B!==""?Xu.BAGGAGE_ITEMS_SEPARATOR:""}${Q}`;return D.length>Xu.BAGGAGE_MAX_TOTAL_LENGTH?B:D},"")}HT2.serializeKeyPairs=es4;function Ar4(A){return A.getAllEntries().map(([B,Q])=>{let D=`${encodeURIComponent(B)}=${encodeURIComponent(Q.value)}`;if(Q.metadata!==void 0)D+=Xu.BAGGAGE_PROPERTIES_SEPARATOR+Q.metadata.toString();return D})}HT2.getKeyPairs=Ar4;function KT2(A){let B=A.split(Xu.BAGGAGE_PROPERTIES_SEPARATOR);if(B.length<=0)return;let Q=B.shift();if(!Q)return;let D=Q.indexOf(Xu.BAGGAGE_KEY_PAIR_SEPARATOR);if(D<=0)return;let Z=decodeURIComponent(Q.substring(0,D).trim()),G=decodeURIComponent(Q.substring(D+1).trim()),F;if(B.length>0)F=ts4.baggageEntryMetadataFromString(B.join(Xu.BAGGAGE_PROPERTIES_SEPARATOR));return{key:Z,value:G,metadata:F}}HT2.parsePairKeyValue=KT2;function Br4(A){if(typeof A!=="string"||A.length===0)return{};return A.split(Xu.BAGGAGE_ITEMS_SEPARATOR).map((B)=>{return KT2(B)}).filter((B)=>B!==void 0&&B.value.length>0).reduce((B,Q)=>{return B[Q.key]=Q.value,B},{})}HT2.parseKeyPairsIntoRecord=Br4});
var $I0=E((Qd2,wI0)=>{wI0.exports=R76;function R76(moduleName){try{var mod=eval("quire".replace(/^/,"re"))(moduleName);if(mod&&(mod.length||Object.keys(mod).length))return mod}catch(A){}return null}});
var $O1=E((uO2)=>{Object.defineProperty(uO2,"__esModule",{value:!0});uO2.MappingError=void 0;class gO2 extends Error{}uO2.MappingError=gO2});
var $R2=E((UR2)=>{Object.defineProperty(UR2,"__esModule",{value:!0});UR2.deleteBaggage=UR2.setBaggage=UR2.getActiveBaggage=UR2.getBaggage=void 0;var nn4=g51(),an4=f51(),iG0=an4.createContextKey("OpenTelemetry Baggage Key");function ER2(A){return A.getValue(iG0)||void 0}UR2.getBaggage=ER2;function sn4(){return ER2(nn4.ContextAPI.getInstance().active())}UR2.getActiveBaggage=sn4;function rn4(A,B){return A.setValue(iG0,B)}UR2.setBaggage=rn4;function on4(A){return A.deleteValue(iG0)}UR2.deleteBaggage=on4});
var $T2=E((UT2)=>{Object.defineProperty(UT2,"__esModule",{value:!0});UT2.W3CBaggagePropagator=void 0;var qF0=ZQ(),Gr4=c51(),Vu=wF0(),NF0=$F0();class ET2{inject(A,B,Q){let D=qF0.propagation.getBaggage(A);if(!D||Gr4.isTracingSuppressed(A))return;let Z=NF0.getKeyPairs(D).filter((F)=>{return F.length<=Vu.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS}).slice(0,Vu.BAGGAGE_MAX_NAME_VALUE_PAIRS),G=NF0.serializeKeyPairs(Z);if(G.length>0)Q.set(B,Vu.BAGGAGE_HEADER,G)}extract(A,B,Q){let D=Q.get(B,Vu.BAGGAGE_HEADER),Z=Array.isArray(D)?D.join(Vu.BAGGAGE_ITEMS_SEPARATOR):D;if(!Z)return A;let G={};if(Z.length===0)return A;if(Z.split(Vu.BAGGAGE_ITEMS_SEPARATOR).forEach((I)=>{let Y=NF0.parsePairKeyValue(I);if(Y){let W={value:Y.value};if(Y.metadata)W.metadata=Y.metadata;G[Y.key]=W}}),Object.entries(G).length===0)return A;return qF0.propagation.setBaggage(A,qF0.propagation.createBaggage(G))}fields(){return[Vu.BAGGAGE_HEADER]}}UT2.W3CBaggagePropagator=ET2});
var $b2=E((Ub2)=>{Object.defineProperty(Ub2,"__esModule",{value:!0});Ub2.getRPCMetadata=Ub2.deleteRPCMetadata=Ub2.setRPCMetadata=Ub2.RPCType=void 0;var o46=ZQ(),_F0=o46.createContextKey("OpenTelemetry SDK Context Key RPC_METADATA"),t46;(function(A){A.HTTP="http"})(t46=Ub2.RPCType||(Ub2.RPCType={}));function e46(A,B){return A.setValue(_F0,B)}Ub2.setRPCMetadata=e46;function A66(A){return A.deleteValue(_F0)}Ub2.deleteRPCMetadata=A66;function B66(A){return A.getValue(_F0)}Ub2.getRPCMetadata=B66});
var $h2=E((Uh2)=>{Object.defineProperty(Uh2,"__esModule",{value:!0});Uh2.getMachineId=void 0;var S86=hO1(),j86=ZQ();async function k86(){try{let B=(await S86.execAsync('ioreg -rd1 -c "IOPlatformExpertDevice"')).stdout.split(`
`).find((D)=>D.includes("IOPlatformUUID"));if(!B)return;let Q=B.split('" = "');if(Q.length===2)return Q[1].slice(0,-1)}catch(A){j86.diag.debug(`error reading machine id: ${A}`)}return}Uh2.getMachineId=k86});
var A31=E((zg2)=>{Object.defineProperty(zg2,"__esModule",{value:!0});zg2.isValidName=zg2.isDescriptorCompatibleWith=zg2.createInstrumentDescriptorWithView=zg2.createInstrumentDescriptor=void 0;var Kg2=ZQ(),C56=cw();function K56(A,B,Q){if(!Hg2(A))Kg2.diag.warn(`Invalid metric name: "${A}". The metric name should be a ASCII string with a length no greater than 255 characters.`);return{name:A,type:B,description:Q?.description??"",unit:Q?.unit??"",valueType:Q?.valueType??Kg2.ValueType.DOUBLE,advice:Q?.advice??{}}}zg2.createInstrumentDescriptor=K56;function H56(A,B){return{name:A.name??B.name,description:A.description??B.description,type:B.type,unit:B.unit,valueType:B.valueType,advice:B.advice}}zg2.createInstrumentDescriptorWithView=H56;function z56(A,B){return C56.equalsCaseInsensitive(A.name,B.name)&&A.unit===B.unit&&A.type===B.type&&A.valueType===B.valueType}zg2.isDescriptorCompatibleWith=z56;var E56=/^[a-z][a-z0-9_.\-/]{0,254}$/i;function Hg2(A){return A.match(E56)!=null}zg2.isValidName=Hg2});
var AL2=E((Yu)=>{var Fi4=Yu&&Yu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Ii4=Yu&&Yu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))Fi4(B,A,Q)};Object.defineProperty(Yu,"__esModule",{value:!0});Ii4(eN2(),Yu)});
var AT1=E((Qy5,Nd2)=>{Nd2.exports=tZ;var pw=pL(),jI0,$d2=pw.LongBits,y76=pw.utf8;function iw(A,B){return RangeError("index out of range: "+A.pos+" + "+(B||1)+" > "+A.len)}function tZ(A){this.buf=A,this.pos=0,this.len=A.length}var Ud2=typeof Uint8Array!=="undefined"?function A(B){if(B instanceof Uint8Array||Array.isArray(B))return new tZ(B);throw Error("illegal buffer")}:function A(B){if(Array.isArray(B))return new tZ(B);throw Error("illegal buffer")},qd2=function A(){return pw.Buffer?function B(Q){return(tZ.create=function D(Z){return pw.Buffer.isBuffer(Z)?new jI0(Z):Ud2(Z)})(Q)}:Ud2};tZ.create=qd2();tZ.prototype._slice=pw.Array.prototype.subarray||pw.Array.prototype.slice;tZ.prototype.uint32=function A(){var B=4294967295;return function Q(){if(B=(this.buf[this.pos]&127)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&127)<<7)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&127)<<14)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&127)<<21)>>>0,this.buf[this.pos++]<128)return B;if(B=(B|(this.buf[this.pos]&15)<<28)>>>0,this.buf[this.pos++]<128)return B;if((this.pos+=5)>this.len)throw this.pos=this.len,iw(this,10);return B}}();tZ.prototype.int32=function A(){return this.uint32()|0};tZ.prototype.sint32=function A(){var B=this.uint32();return B>>>1^-(B&1)|0};function SI0(){var A=new $d2(0,0),B=0;if(this.len-this.pos>4){for(;B<4;++B)if(A.lo=(A.lo|(this.buf[this.pos]&127)<<B*7)>>>0,this.buf[this.pos++]<128)return A;if(A.lo=(A.lo|(this.buf[this.pos]&127)<<28)>>>0,A.hi=(A.hi|(this.buf[this.pos]&127)>>4)>>>0,this.buf[this.pos++]<128)return A;B=0}else{for(;B<3;++B){if(this.pos>=this.len)throw iw(this);if(A.lo=(A.lo|(this.buf[this.pos]&127)<<B*7)>>>0,this.buf[this.pos++]<128)return A}return A.lo=(A.lo|(this.buf[this.pos++]&127)<<B*7)>>>0,A}if(this.len-this.pos>4){for(;B<5;++B)if(A.hi=(A.hi|(this.buf[this.pos]&127)<<B*7+3)>>>0,this.buf[this.pos++]<128)return A}else for(;B<5;++B){if(this.pos>=this.len)throw iw(this);if(A.hi=(A.hi|(this.buf[this.pos]&127)<<B*7+3)>>>0,this.buf[this.pos++]<128)return A}throw Error("invalid varint encoding")}tZ.prototype.bool=function A(){return this.uint32()!==0};function eO1(A,B){return(A[B-4]|A[B-3]<<8|A[B-2]<<16|A[B-1]<<24)>>>0}tZ.prototype.fixed32=function A(){if(this.pos+4>this.len)throw iw(this,4);return eO1(this.buf,this.pos+=4)};tZ.prototype.sfixed32=function A(){if(this.pos+4>this.len)throw iw(this,4);return eO1(this.buf,this.pos+=4)|0};function wd2(){if(this.pos+8>this.len)throw iw(this,8);return new $d2(eO1(this.buf,this.pos+=4),eO1(this.buf,this.pos+=4))}tZ.prototype.float=function A(){if(this.pos+4>this.len)throw iw(this,4);var B=pw.float.readFloatLE(this.buf,this.pos);return this.pos+=4,B};tZ.prototype.double=function A(){if(this.pos+8>this.len)throw iw(this,4);var B=pw.float.readDoubleLE(this.buf,this.pos);return this.pos+=8,B};tZ.prototype.bytes=function A(){var B=this.uint32(),Q=this.pos,D=this.pos+B;if(D>this.len)throw iw(this,B);if(this.pos+=B,Array.isArray(this.buf))return this.buf.slice(Q,D);if(Q===D){var Z=pw.Buffer;return Z?Z.alloc(0):new this.buf.constructor(0)}return this._slice.call(this.buf,Q,D)};tZ.prototype.string=function A(){var B=this.bytes();return y76.read(B,0,B.length)};tZ.prototype.skip=function A(B){if(typeof B==="number"){if(this.pos+B>this.len)throw iw(this,B);this.pos+=B}else do if(this.pos>=this.len)throw iw(this);while(this.buf[this.pos++]&128);return this};tZ.prototype.skipType=function(A){switch(A){case 0:this.skip();break;case 1:this.skip(8);break;case 2:this.skip(this.uint32());break;case 3:while((A=this.uint32()&7)!==4)this.skipType(A);break;case 5:this.skip(4);break;default:throw Error("invalid wire type "+A+" at offset "+this.pos)}return this};tZ._configure=function(A){jI0=A,tZ.create=qd2(),jI0._configure();var B=pw.Long?"toLong":"toNumber";pw.merge(tZ.prototype,{int64:function Q(){return SI0.call(this)[B](!1)},uint64:function Q(){return SI0.call(this)[B](!0)},sint64:function Q(){return SI0.call(this).zzDecode()[B](!1)},fixed64:function Q(){return wd2.call(this)[B](!0)},sfixed64:function Q(){return wd2.call(this)[B](!1)}})}});
var Ac2=E((td2)=>{Object.defineProperty(td2,"__esModule",{value:!0});td2.ProtobufLogsSerializer=void 0;var od2=BT1(),QD6=uI0(),DD6=od2.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse,ZD6=od2.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;td2.ProtobufLogsSerializer={serializeRequest:(A)=>{let B=QD6.createExportLogsServiceRequest(A);return ZD6.encode(B).finish()},deserializeResponse:(A)=>{return DD6.decode(A)}}});
var B31=E((vg2)=>{Object.defineProperty(vg2,"__esModule",{value:!0});vg2.AttributeHashMap=vg2.HashMap=void 0;var _56=cw();class DI0{_hash;_valueMap=new Map;_keyMap=new Map;constructor(A){this._hash=A}get(A,B){return B??=this._hash(A),this._valueMap.get(B)}getOrDefault(A,B){let Q=this._hash(A);if(this._valueMap.has(Q))return this._valueMap.get(Q);let D=B();if(!this._keyMap.has(Q))this._keyMap.set(Q,A);return this._valueMap.set(Q,D),D}set(A,B,Q){if(Q??=this._hash(A),!this._keyMap.has(Q))this._keyMap.set(Q,A);this._valueMap.set(Q,B)}has(A,B){return B??=this._hash(A),this._valueMap.has(B)}*keys(){let A=this._keyMap.entries(),B=A.next();while(B.done!==!0)yield[B.value[1],B.value[0]],B=A.next()}*entries(){let A=this._valueMap.entries(),B=A.next();while(B.done!==!0)yield[this._keyMap.get(B.value[0]),B.value[1],B.value[0]],B=A.next()}get size(){return this._valueMap.size}}vg2.HashMap=DI0;class xg2 extends DI0{constructor(){super(_56.hashAttributes)}}vg2.AttributeHashMap=xg2});
var BT1=E((_d2,xd2)=>{Object.defineProperty(_d2,"__esModule",{value:!0});var c9=xI0(),d0=c9.Reader,Z4=c9.Writer,C1=c9.util,X1=c9.roots.default||(c9.roots.default={});X1.opentelemetry=function(){var A={};return A.proto=function(){var B={};return B.common=function(){var Q={};return Q.v1=function(){var D={};return D.AnyValue=function(){function Z(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.stringValue=null,Z.prototype.boolValue=null,Z.prototype.intValue=null,Z.prototype.doubleValue=null,Z.prototype.arrayValue=null,Z.prototype.kvlistValue=null,Z.prototype.bytesValue=null;var G;return Object.defineProperty(Z.prototype,"value",{get:C1.oneOfGetter(G=["stringValue","boolValue","intValue","doubleValue","arrayValue","kvlistValue","bytesValue"]),set:C1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.stringValue!=null&&Object.hasOwnProperty.call(I,"stringValue"))Y.uint32(10).string(I.stringValue);if(I.boolValue!=null&&Object.hasOwnProperty.call(I,"boolValue"))Y.uint32(16).bool(I.boolValue);if(I.intValue!=null&&Object.hasOwnProperty.call(I,"intValue"))Y.uint32(24).int64(I.intValue);if(I.doubleValue!=null&&Object.hasOwnProperty.call(I,"doubleValue"))Y.uint32(33).double(I.doubleValue);if(I.arrayValue!=null&&Object.hasOwnProperty.call(I,"arrayValue"))X1.opentelemetry.proto.common.v1.ArrayValue.encode(I.arrayValue,Y.uint32(42).fork()).ldelim();if(I.kvlistValue!=null&&Object.hasOwnProperty.call(I,"kvlistValue"))X1.opentelemetry.proto.common.v1.KeyValueList.encode(I.kvlistValue,Y.uint32(50).fork()).ldelim();if(I.bytesValue!=null&&Object.hasOwnProperty.call(I,"bytesValue"))Y.uint32(58).bytes(I.bytesValue);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.common.v1.AnyValue;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.stringValue=I.string();break}case 2:{J.boolValue=I.bool();break}case 3:{J.intValue=I.int64();break}case 4:{J.doubleValue=I.double();break}case 5:{J.arrayValue=X1.opentelemetry.proto.common.v1.ArrayValue.decode(I,I.uint32());break}case 6:{J.kvlistValue=X1.opentelemetry.proto.common.v1.KeyValueList.decode(I,I.uint32());break}case 7:{J.bytesValue=I.bytes();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.stringValue!=null&&I.hasOwnProperty("stringValue")){if(Y.value=1,!C1.isString(I.stringValue))return"stringValue: string expected"}if(I.boolValue!=null&&I.hasOwnProperty("boolValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,typeof I.boolValue!=="boolean")return"boolValue: boolean expected"}if(I.intValue!=null&&I.hasOwnProperty("intValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!C1.isInteger(I.intValue)&&!(I.intValue&&C1.isInteger(I.intValue.low)&&C1.isInteger(I.intValue.high)))return"intValue: integer|Long expected"}if(I.doubleValue!=null&&I.hasOwnProperty("doubleValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,typeof I.doubleValue!=="number")return"doubleValue: number expected"}if(I.arrayValue!=null&&I.hasOwnProperty("arrayValue")){if(Y.value===1)return"value: multiple values";Y.value=1;{var W=X1.opentelemetry.proto.common.v1.ArrayValue.verify(I.arrayValue);if(W)return"arrayValue."+W}}if(I.kvlistValue!=null&&I.hasOwnProperty("kvlistValue")){if(Y.value===1)return"value: multiple values";Y.value=1;{var W=X1.opentelemetry.proto.common.v1.KeyValueList.verify(I.kvlistValue);if(W)return"kvlistValue."+W}}if(I.bytesValue!=null&&I.hasOwnProperty("bytesValue")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!(I.bytesValue&&typeof I.bytesValue.length==="number"||C1.isString(I.bytesValue)))return"bytesValue: buffer expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.common.v1.AnyValue)return I;var Y=new X1.opentelemetry.proto.common.v1.AnyValue;if(I.stringValue!=null)Y.stringValue=String(I.stringValue);if(I.boolValue!=null)Y.boolValue=Boolean(I.boolValue);if(I.intValue!=null){if(C1.Long)(Y.intValue=C1.Long.fromValue(I.intValue)).unsigned=!1;else if(typeof I.intValue==="string")Y.intValue=parseInt(I.intValue,10);else if(typeof I.intValue==="number")Y.intValue=I.intValue;else if(typeof I.intValue==="object")Y.intValue=new C1.LongBits(I.intValue.low>>>0,I.intValue.high>>>0).toNumber()}if(I.doubleValue!=null)Y.doubleValue=Number(I.doubleValue);if(I.arrayValue!=null){if(typeof I.arrayValue!=="object")throw TypeError(".opentelemetry.proto.common.v1.AnyValue.arrayValue: object expected");Y.arrayValue=X1.opentelemetry.proto.common.v1.ArrayValue.fromObject(I.arrayValue)}if(I.kvlistValue!=null){if(typeof I.kvlistValue!=="object")throw TypeError(".opentelemetry.proto.common.v1.AnyValue.kvlistValue: object expected");Y.kvlistValue=X1.opentelemetry.proto.common.v1.KeyValueList.fromObject(I.kvlistValue)}if(I.bytesValue!=null){if(typeof I.bytesValue==="string")C1.base64.decode(I.bytesValue,Y.bytesValue=C1.newBuffer(C1.base64.length(I.bytesValue)),0);else if(I.bytesValue.length>=0)Y.bytesValue=I.bytesValue}return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(I.stringValue!=null&&I.hasOwnProperty("stringValue")){if(W.stringValue=I.stringValue,Y.oneofs)W.value="stringValue"}if(I.boolValue!=null&&I.hasOwnProperty("boolValue")){if(W.boolValue=I.boolValue,Y.oneofs)W.value="boolValue"}if(I.intValue!=null&&I.hasOwnProperty("intValue")){if(typeof I.intValue==="number")W.intValue=Y.longs===String?String(I.intValue):I.intValue;else W.intValue=Y.longs===String?C1.Long.prototype.toString.call(I.intValue):Y.longs===Number?new C1.LongBits(I.intValue.low>>>0,I.intValue.high>>>0).toNumber():I.intValue;if(Y.oneofs)W.value="intValue"}if(I.doubleValue!=null&&I.hasOwnProperty("doubleValue")){if(W.doubleValue=Y.json&&!isFinite(I.doubleValue)?String(I.doubleValue):I.doubleValue,Y.oneofs)W.value="doubleValue"}if(I.arrayValue!=null&&I.hasOwnProperty("arrayValue")){if(W.arrayValue=X1.opentelemetry.proto.common.v1.ArrayValue.toObject(I.arrayValue,Y),Y.oneofs)W.value="arrayValue"}if(I.kvlistValue!=null&&I.hasOwnProperty("kvlistValue")){if(W.kvlistValue=X1.opentelemetry.proto.common.v1.KeyValueList.toObject(I.kvlistValue,Y),Y.oneofs)W.value="kvlistValue"}if(I.bytesValue!=null&&I.hasOwnProperty("bytesValue")){if(W.bytesValue=Y.bytes===String?C1.base64.encode(I.bytesValue,0,I.bytesValue.length):Y.bytes===Array?Array.prototype.slice.call(I.bytesValue):I.bytesValue,Y.oneofs)W.value="bytesValue"}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.common.v1.AnyValue"},Z}(),D.ArrayValue=function(){function Z(G){if(this.values=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.values=C1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.values!=null&&F.values.length)for(var Y=0;Y<F.values.length;++Y)X1.opentelemetry.proto.common.v1.AnyValue.encode(F.values[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.ArrayValue;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.values&&W.values.length))W.values=[];W.values.push(X1.opentelemetry.proto.common.v1.AnyValue.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.values!=null&&F.hasOwnProperty("values")){if(!Array.isArray(F.values))return"values: array expected";for(var I=0;I<F.values.length;++I){var Y=X1.opentelemetry.proto.common.v1.AnyValue.verify(F.values[I]);if(Y)return"values."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.ArrayValue)return F;var I=new X1.opentelemetry.proto.common.v1.ArrayValue;if(F.values){if(!Array.isArray(F.values))throw TypeError(".opentelemetry.proto.common.v1.ArrayValue.values: array expected");I.values=[];for(var Y=0;Y<F.values.length;++Y){if(typeof F.values[Y]!=="object")throw TypeError(".opentelemetry.proto.common.v1.ArrayValue.values: object expected");I.values[Y]=X1.opentelemetry.proto.common.v1.AnyValue.fromObject(F.values[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.values=[];if(F.values&&F.values.length){Y.values=[];for(var W=0;W<F.values.length;++W)Y.values[W]=X1.opentelemetry.proto.common.v1.AnyValue.toObject(F.values[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.ArrayValue"},Z}(),D.KeyValueList=function(){function Z(G){if(this.values=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.values=C1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.values!=null&&F.values.length)for(var Y=0;Y<F.values.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.values[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.KeyValueList;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.values&&W.values.length))W.values=[];W.values.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.values!=null&&F.hasOwnProperty("values")){if(!Array.isArray(F.values))return"values: array expected";for(var I=0;I<F.values.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.values[I]);if(Y)return"values."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.KeyValueList)return F;var I=new X1.opentelemetry.proto.common.v1.KeyValueList;if(F.values){if(!Array.isArray(F.values))throw TypeError(".opentelemetry.proto.common.v1.KeyValueList.values: array expected");I.values=[];for(var Y=0;Y<F.values.length;++Y){if(typeof F.values[Y]!=="object")throw TypeError(".opentelemetry.proto.common.v1.KeyValueList.values: object expected");I.values[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.values[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.values=[];if(F.values&&F.values.length){Y.values=[];for(var W=0;W<F.values.length;++W)Y.values[W]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.values[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.KeyValueList"},Z}(),D.KeyValue=function(){function Z(G){if(G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.key=null,Z.prototype.value=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.key!=null&&Object.hasOwnProperty.call(F,"key"))I.uint32(10).string(F.key);if(F.value!=null&&Object.hasOwnProperty.call(F,"value"))X1.opentelemetry.proto.common.v1.AnyValue.encode(F.value,I.uint32(18).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.KeyValue;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.key=F.string();break}case 2:{W.value=X1.opentelemetry.proto.common.v1.AnyValue.decode(F,F.uint32());break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.key!=null&&F.hasOwnProperty("key")){if(!C1.isString(F.key))return"key: string expected"}if(F.value!=null&&F.hasOwnProperty("value")){var I=X1.opentelemetry.proto.common.v1.AnyValue.verify(F.value);if(I)return"value."+I}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.KeyValue)return F;var I=new X1.opentelemetry.proto.common.v1.KeyValue;if(F.key!=null)I.key=String(F.key);if(F.value!=null){if(typeof F.value!=="object")throw TypeError(".opentelemetry.proto.common.v1.KeyValue.value: object expected");I.value=X1.opentelemetry.proto.common.v1.AnyValue.fromObject(F.value)}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.defaults)Y.key="",Y.value=null;if(F.key!=null&&F.hasOwnProperty("key"))Y.key=F.key;if(F.value!=null&&F.hasOwnProperty("value"))Y.value=X1.opentelemetry.proto.common.v1.AnyValue.toObject(F.value,I);return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.KeyValue"},Z}(),D.InstrumentationScope=function(){function Z(G){if(this.attributes=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.name=null,Z.prototype.version=null,Z.prototype.attributes=C1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.name!=null&&Object.hasOwnProperty.call(F,"name"))I.uint32(10).string(F.name);if(F.version!=null&&Object.hasOwnProperty.call(F,"version"))I.uint32(18).string(F.version);if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(26).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(32).uint32(F.droppedAttributesCount);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.common.v1.InstrumentationScope;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.name=F.string();break}case 2:{W.version=F.string();break}case 3:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 4:{W.droppedAttributesCount=F.uint32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.name!=null&&F.hasOwnProperty("name")){if(!C1.isString(F.name))return"name: string expected"}if(F.version!=null&&F.hasOwnProperty("version")){if(!C1.isString(F.version))return"version: string expected"}if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!C1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.common.v1.InstrumentationScope)return F;var I=new X1.opentelemetry.proto.common.v1.InstrumentationScope;if(F.name!=null)I.name=String(F.name);if(F.version!=null)I.version=String(F.version);if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.common.v1.InstrumentationScope.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.common.v1.InstrumentationScope.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[];if(I.defaults)Y.name="",Y.version="",Y.droppedAttributesCount=0;if(F.name!=null&&F.hasOwnProperty("name"))Y.name=F.name;if(F.version!=null&&F.hasOwnProperty("version"))Y.version=F.version;if(F.attributes&&F.attributes.length){Y.attributes=[];for(var W=0;W<F.attributes.length;++W)Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[W],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.common.v1.InstrumentationScope"},Z}(),D}(),Q}(),B.resource=function(){var Q={};return Q.v1=function(){var D={};return D.Resource=function(){function Z(G){if(this.attributes=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.attributes=C1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(10).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(16).uint32(F.droppedAttributesCount);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.resource.v1.Resource;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 2:{W.droppedAttributesCount=F.uint32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!C1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.resource.v1.Resource)return F;var I=new X1.opentelemetry.proto.resource.v1.Resource;if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.resource.v1.Resource.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.resource.v1.Resource.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[];if(I.defaults)Y.droppedAttributesCount=0;if(F.attributes&&F.attributes.length){Y.attributes=[];for(var W=0;W<F.attributes.length;++W)Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[W],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.resource.v1.Resource"},Z}(),D}(),Q}(),B.trace=function(){var Q={};return Q.v1=function(){var D={};return D.TracesData=function(){function Z(G){if(this.resourceSpans=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resourceSpans=C1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.resourceSpans!=null&&F.resourceSpans.length)for(var Y=0;Y<F.resourceSpans.length;++Y)X1.opentelemetry.proto.trace.v1.ResourceSpans.encode(F.resourceSpans[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.TracesData;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.resourceSpans&&W.resourceSpans.length))W.resourceSpans=[];W.resourceSpans.push(X1.opentelemetry.proto.trace.v1.ResourceSpans.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resourceSpans!=null&&F.hasOwnProperty("resourceSpans")){if(!Array.isArray(F.resourceSpans))return"resourceSpans: array expected";for(var I=0;I<F.resourceSpans.length;++I){var Y=X1.opentelemetry.proto.trace.v1.ResourceSpans.verify(F.resourceSpans[I]);if(Y)return"resourceSpans."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.TracesData)return F;var I=new X1.opentelemetry.proto.trace.v1.TracesData;if(F.resourceSpans){if(!Array.isArray(F.resourceSpans))throw TypeError(".opentelemetry.proto.trace.v1.TracesData.resourceSpans: array expected");I.resourceSpans=[];for(var Y=0;Y<F.resourceSpans.length;++Y){if(typeof F.resourceSpans[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.TracesData.resourceSpans: object expected");I.resourceSpans[Y]=X1.opentelemetry.proto.trace.v1.ResourceSpans.fromObject(F.resourceSpans[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.resourceSpans=[];if(F.resourceSpans&&F.resourceSpans.length){Y.resourceSpans=[];for(var W=0;W<F.resourceSpans.length;++W)Y.resourceSpans[W]=X1.opentelemetry.proto.trace.v1.ResourceSpans.toObject(F.resourceSpans[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.TracesData"},Z}(),D.ResourceSpans=function(){function Z(G){if(this.scopeSpans=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resource=null,Z.prototype.scopeSpans=C1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.resource!=null&&Object.hasOwnProperty.call(F,"resource"))X1.opentelemetry.proto.resource.v1.Resource.encode(F.resource,I.uint32(10).fork()).ldelim();if(F.scopeSpans!=null&&F.scopeSpans.length)for(var Y=0;Y<F.scopeSpans.length;++Y)X1.opentelemetry.proto.trace.v1.ScopeSpans.encode(F.scopeSpans[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.ResourceSpans;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.resource=X1.opentelemetry.proto.resource.v1.Resource.decode(F,F.uint32());break}case 2:{if(!(W.scopeSpans&&W.scopeSpans.length))W.scopeSpans=[];W.scopeSpans.push(X1.opentelemetry.proto.trace.v1.ScopeSpans.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resource!=null&&F.hasOwnProperty("resource")){var I=X1.opentelemetry.proto.resource.v1.Resource.verify(F.resource);if(I)return"resource."+I}if(F.scopeSpans!=null&&F.hasOwnProperty("scopeSpans")){if(!Array.isArray(F.scopeSpans))return"scopeSpans: array expected";for(var Y=0;Y<F.scopeSpans.length;++Y){var I=X1.opentelemetry.proto.trace.v1.ScopeSpans.verify(F.scopeSpans[Y]);if(I)return"scopeSpans."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!C1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.ResourceSpans)return F;var I=new X1.opentelemetry.proto.trace.v1.ResourceSpans;if(F.resource!=null){if(typeof F.resource!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.resource: object expected");I.resource=X1.opentelemetry.proto.resource.v1.Resource.fromObject(F.resource)}if(F.scopeSpans){if(!Array.isArray(F.scopeSpans))throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.scopeSpans: array expected");I.scopeSpans=[];for(var Y=0;Y<F.scopeSpans.length;++Y){if(typeof F.scopeSpans[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ResourceSpans.scopeSpans: object expected");I.scopeSpans[Y]=X1.opentelemetry.proto.trace.v1.ScopeSpans.fromObject(F.scopeSpans[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.scopeSpans=[];if(I.defaults)Y.resource=null,Y.schemaUrl="";if(F.resource!=null&&F.hasOwnProperty("resource"))Y.resource=X1.opentelemetry.proto.resource.v1.Resource.toObject(F.resource,I);if(F.scopeSpans&&F.scopeSpans.length){Y.scopeSpans=[];for(var W=0;W<F.scopeSpans.length;++W)Y.scopeSpans[W]=X1.opentelemetry.proto.trace.v1.ScopeSpans.toObject(F.scopeSpans[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.ResourceSpans"},Z}(),D.ScopeSpans=function(){function Z(G){if(this.spans=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.scope=null,Z.prototype.spans=C1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.scope!=null&&Object.hasOwnProperty.call(F,"scope"))X1.opentelemetry.proto.common.v1.InstrumentationScope.encode(F.scope,I.uint32(10).fork()).ldelim();if(F.spans!=null&&F.spans.length)for(var Y=0;Y<F.spans.length;++Y)X1.opentelemetry.proto.trace.v1.Span.encode(F.spans[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.ScopeSpans;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.decode(F,F.uint32());break}case 2:{if(!(W.spans&&W.spans.length))W.spans=[];W.spans.push(X1.opentelemetry.proto.trace.v1.Span.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.scope!=null&&F.hasOwnProperty("scope")){var I=X1.opentelemetry.proto.common.v1.InstrumentationScope.verify(F.scope);if(I)return"scope."+I}if(F.spans!=null&&F.hasOwnProperty("spans")){if(!Array.isArray(F.spans))return"spans: array expected";for(var Y=0;Y<F.spans.length;++Y){var I=X1.opentelemetry.proto.trace.v1.Span.verify(F.spans[Y]);if(I)return"spans."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!C1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.ScopeSpans)return F;var I=new X1.opentelemetry.proto.trace.v1.ScopeSpans;if(F.scope!=null){if(typeof F.scope!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.scope: object expected");I.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(F.scope)}if(F.spans){if(!Array.isArray(F.spans))throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.spans: array expected");I.spans=[];for(var Y=0;Y<F.spans.length;++Y){if(typeof F.spans[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.ScopeSpans.spans: object expected");I.spans[Y]=X1.opentelemetry.proto.trace.v1.Span.fromObject(F.spans[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.spans=[];if(I.defaults)Y.scope=null,Y.schemaUrl="";if(F.scope!=null&&F.hasOwnProperty("scope"))Y.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.toObject(F.scope,I);if(F.spans&&F.spans.length){Y.spans=[];for(var W=0;W<F.spans.length;++W)Y.spans[W]=X1.opentelemetry.proto.trace.v1.Span.toObject(F.spans[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.ScopeSpans"},Z}(),D.Span=function(){function Z(G){if(this.attributes=[],this.events=[],this.links=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.traceId=null,Z.prototype.spanId=null,Z.prototype.traceState=null,Z.prototype.parentSpanId=null,Z.prototype.name=null,Z.prototype.kind=null,Z.prototype.startTimeUnixNano=null,Z.prototype.endTimeUnixNano=null,Z.prototype.attributes=C1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.prototype.events=C1.emptyArray,Z.prototype.droppedEventsCount=null,Z.prototype.links=C1.emptyArray,Z.prototype.droppedLinksCount=null,Z.prototype.status=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.traceId!=null&&Object.hasOwnProperty.call(F,"traceId"))I.uint32(10).bytes(F.traceId);if(F.spanId!=null&&Object.hasOwnProperty.call(F,"spanId"))I.uint32(18).bytes(F.spanId);if(F.traceState!=null&&Object.hasOwnProperty.call(F,"traceState"))I.uint32(26).string(F.traceState);if(F.parentSpanId!=null&&Object.hasOwnProperty.call(F,"parentSpanId"))I.uint32(34).bytes(F.parentSpanId);if(F.name!=null&&Object.hasOwnProperty.call(F,"name"))I.uint32(42).string(F.name);if(F.kind!=null&&Object.hasOwnProperty.call(F,"kind"))I.uint32(48).int32(F.kind);if(F.startTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"startTimeUnixNano"))I.uint32(57).fixed64(F.startTimeUnixNano);if(F.endTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"endTimeUnixNano"))I.uint32(65).fixed64(F.endTimeUnixNano);if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(74).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(80).uint32(F.droppedAttributesCount);if(F.events!=null&&F.events.length)for(var Y=0;Y<F.events.length;++Y)X1.opentelemetry.proto.trace.v1.Span.Event.encode(F.events[Y],I.uint32(90).fork()).ldelim();if(F.droppedEventsCount!=null&&Object.hasOwnProperty.call(F,"droppedEventsCount"))I.uint32(96).uint32(F.droppedEventsCount);if(F.links!=null&&F.links.length)for(var Y=0;Y<F.links.length;++Y)X1.opentelemetry.proto.trace.v1.Span.Link.encode(F.links[Y],I.uint32(106).fork()).ldelim();if(F.droppedLinksCount!=null&&Object.hasOwnProperty.call(F,"droppedLinksCount"))I.uint32(112).uint32(F.droppedLinksCount);if(F.status!=null&&Object.hasOwnProperty.call(F,"status"))X1.opentelemetry.proto.trace.v1.Status.encode(F.status,I.uint32(122).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.Span;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.traceId=F.bytes();break}case 2:{W.spanId=F.bytes();break}case 3:{W.traceState=F.string();break}case 4:{W.parentSpanId=F.bytes();break}case 5:{W.name=F.string();break}case 6:{W.kind=F.int32();break}case 7:{W.startTimeUnixNano=F.fixed64();break}case 8:{W.endTimeUnixNano=F.fixed64();break}case 9:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 10:{W.droppedAttributesCount=F.uint32();break}case 11:{if(!(W.events&&W.events.length))W.events=[];W.events.push(X1.opentelemetry.proto.trace.v1.Span.Event.decode(F,F.uint32()));break}case 12:{W.droppedEventsCount=F.uint32();break}case 13:{if(!(W.links&&W.links.length))W.links=[];W.links.push(X1.opentelemetry.proto.trace.v1.Span.Link.decode(F,F.uint32()));break}case 14:{W.droppedLinksCount=F.uint32();break}case 15:{W.status=X1.opentelemetry.proto.trace.v1.Status.decode(F,F.uint32());break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.traceId!=null&&F.hasOwnProperty("traceId")){if(!(F.traceId&&typeof F.traceId.length==="number"||C1.isString(F.traceId)))return"traceId: buffer expected"}if(F.spanId!=null&&F.hasOwnProperty("spanId")){if(!(F.spanId&&typeof F.spanId.length==="number"||C1.isString(F.spanId)))return"spanId: buffer expected"}if(F.traceState!=null&&F.hasOwnProperty("traceState")){if(!C1.isString(F.traceState))return"traceState: string expected"}if(F.parentSpanId!=null&&F.hasOwnProperty("parentSpanId")){if(!(F.parentSpanId&&typeof F.parentSpanId.length==="number"||C1.isString(F.parentSpanId)))return"parentSpanId: buffer expected"}if(F.name!=null&&F.hasOwnProperty("name")){if(!C1.isString(F.name))return"name: string expected"}if(F.kind!=null&&F.hasOwnProperty("kind"))switch(F.kind){default:return"kind: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:break}if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano")){if(!C1.isInteger(F.startTimeUnixNano)&&!(F.startTimeUnixNano&&C1.isInteger(F.startTimeUnixNano.low)&&C1.isInteger(F.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(F.endTimeUnixNano!=null&&F.hasOwnProperty("endTimeUnixNano")){if(!C1.isInteger(F.endTimeUnixNano)&&!(F.endTimeUnixNano&&C1.isInteger(F.endTimeUnixNano.low)&&C1.isInteger(F.endTimeUnixNano.high)))return"endTimeUnixNano: integer|Long expected"}if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!C1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}if(F.events!=null&&F.hasOwnProperty("events")){if(!Array.isArray(F.events))return"events: array expected";for(var I=0;I<F.events.length;++I){var Y=X1.opentelemetry.proto.trace.v1.Span.Event.verify(F.events[I]);if(Y)return"events."+Y}}if(F.droppedEventsCount!=null&&F.hasOwnProperty("droppedEventsCount")){if(!C1.isInteger(F.droppedEventsCount))return"droppedEventsCount: integer expected"}if(F.links!=null&&F.hasOwnProperty("links")){if(!Array.isArray(F.links))return"links: array expected";for(var I=0;I<F.links.length;++I){var Y=X1.opentelemetry.proto.trace.v1.Span.Link.verify(F.links[I]);if(Y)return"links."+Y}}if(F.droppedLinksCount!=null&&F.hasOwnProperty("droppedLinksCount")){if(!C1.isInteger(F.droppedLinksCount))return"droppedLinksCount: integer expected"}if(F.status!=null&&F.hasOwnProperty("status")){var Y=X1.opentelemetry.proto.trace.v1.Status.verify(F.status);if(Y)return"status."+Y}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.Span)return F;var I=new X1.opentelemetry.proto.trace.v1.Span;if(F.traceId!=null){if(typeof F.traceId==="string")C1.base64.decode(F.traceId,I.traceId=C1.newBuffer(C1.base64.length(F.traceId)),0);else if(F.traceId.length>=0)I.traceId=F.traceId}if(F.spanId!=null){if(typeof F.spanId==="string")C1.base64.decode(F.spanId,I.spanId=C1.newBuffer(C1.base64.length(F.spanId)),0);else if(F.spanId.length>=0)I.spanId=F.spanId}if(F.traceState!=null)I.traceState=String(F.traceState);if(F.parentSpanId!=null){if(typeof F.parentSpanId==="string")C1.base64.decode(F.parentSpanId,I.parentSpanId=C1.newBuffer(C1.base64.length(F.parentSpanId)),0);else if(F.parentSpanId.length>=0)I.parentSpanId=F.parentSpanId}if(F.name!=null)I.name=String(F.name);switch(F.kind){default:if(typeof F.kind==="number"){I.kind=F.kind;break}break;case"SPAN_KIND_UNSPECIFIED":case 0:I.kind=0;break;case"SPAN_KIND_INTERNAL":case 1:I.kind=1;break;case"SPAN_KIND_SERVER":case 2:I.kind=2;break;case"SPAN_KIND_CLIENT":case 3:I.kind=3;break;case"SPAN_KIND_PRODUCER":case 4:I.kind=4;break;case"SPAN_KIND_CONSUMER":case 5:I.kind=5;break}if(F.startTimeUnixNano!=null){if(C1.Long)(I.startTimeUnixNano=C1.Long.fromValue(F.startTimeUnixNano)).unsigned=!1;else if(typeof F.startTimeUnixNano==="string")I.startTimeUnixNano=parseInt(F.startTimeUnixNano,10);else if(typeof F.startTimeUnixNano==="number")I.startTimeUnixNano=F.startTimeUnixNano;else if(typeof F.startTimeUnixNano==="object")I.startTimeUnixNano=new C1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber()}if(F.endTimeUnixNano!=null){if(C1.Long)(I.endTimeUnixNano=C1.Long.fromValue(F.endTimeUnixNano)).unsigned=!1;else if(typeof F.endTimeUnixNano==="string")I.endTimeUnixNano=parseInt(F.endTimeUnixNano,10);else if(typeof F.endTimeUnixNano==="number")I.endTimeUnixNano=F.endTimeUnixNano;else if(typeof F.endTimeUnixNano==="object")I.endTimeUnixNano=new C1.LongBits(F.endTimeUnixNano.low>>>0,F.endTimeUnixNano.high>>>0).toNumber()}if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;if(F.events){if(!Array.isArray(F.events))throw TypeError(".opentelemetry.proto.trace.v1.Span.events: array expected");I.events=[];for(var Y=0;Y<F.events.length;++Y){if(typeof F.events[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.events: object expected");I.events[Y]=X1.opentelemetry.proto.trace.v1.Span.Event.fromObject(F.events[Y])}}if(F.droppedEventsCount!=null)I.droppedEventsCount=F.droppedEventsCount>>>0;if(F.links){if(!Array.isArray(F.links))throw TypeError(".opentelemetry.proto.trace.v1.Span.links: array expected");I.links=[];for(var Y=0;Y<F.links.length;++Y){if(typeof F.links[Y]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.links: object expected");I.links[Y]=X1.opentelemetry.proto.trace.v1.Span.Link.fromObject(F.links[Y])}}if(F.droppedLinksCount!=null)I.droppedLinksCount=F.droppedLinksCount>>>0;if(F.status!=null){if(typeof F.status!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.status: object expected");I.status=X1.opentelemetry.proto.trace.v1.Status.fromObject(F.status)}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[],Y.events=[],Y.links=[];if(I.defaults){if(I.bytes===String)Y.traceId="";else if(Y.traceId=[],I.bytes!==Array)Y.traceId=C1.newBuffer(Y.traceId);if(I.bytes===String)Y.spanId="";else if(Y.spanId=[],I.bytes!==Array)Y.spanId=C1.newBuffer(Y.spanId);if(Y.traceState="",I.bytes===String)Y.parentSpanId="";else if(Y.parentSpanId=[],I.bytes!==Array)Y.parentSpanId=C1.newBuffer(Y.parentSpanId);if(Y.name="",Y.kind=I.enums===String?"SPAN_KIND_UNSPECIFIED":0,C1.Long){var W=new C1.Long(0,0,!1);Y.startTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.startTimeUnixNano=I.longs===String?"0":0;if(C1.Long){var W=new C1.Long(0,0,!1);Y.endTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.endTimeUnixNano=I.longs===String?"0":0;Y.droppedAttributesCount=0,Y.droppedEventsCount=0,Y.droppedLinksCount=0,Y.status=null}if(F.traceId!=null&&F.hasOwnProperty("traceId"))Y.traceId=I.bytes===String?C1.base64.encode(F.traceId,0,F.traceId.length):I.bytes===Array?Array.prototype.slice.call(F.traceId):F.traceId;if(F.spanId!=null&&F.hasOwnProperty("spanId"))Y.spanId=I.bytes===String?C1.base64.encode(F.spanId,0,F.spanId.length):I.bytes===Array?Array.prototype.slice.call(F.spanId):F.spanId;if(F.traceState!=null&&F.hasOwnProperty("traceState"))Y.traceState=F.traceState;if(F.parentSpanId!=null&&F.hasOwnProperty("parentSpanId"))Y.parentSpanId=I.bytes===String?C1.base64.encode(F.parentSpanId,0,F.parentSpanId.length):I.bytes===Array?Array.prototype.slice.call(F.parentSpanId):F.parentSpanId;if(F.name!=null&&F.hasOwnProperty("name"))Y.name=F.name;if(F.kind!=null&&F.hasOwnProperty("kind"))Y.kind=I.enums===String?X1.opentelemetry.proto.trace.v1.Span.SpanKind[F.kind]===void 0?F.kind:X1.opentelemetry.proto.trace.v1.Span.SpanKind[F.kind]:F.kind;if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano"))if(typeof F.startTimeUnixNano==="number")Y.startTimeUnixNano=I.longs===String?String(F.startTimeUnixNano):F.startTimeUnixNano;else Y.startTimeUnixNano=I.longs===String?C1.Long.prototype.toString.call(F.startTimeUnixNano):I.longs===Number?new C1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber():F.startTimeUnixNano;if(F.endTimeUnixNano!=null&&F.hasOwnProperty("endTimeUnixNano"))if(typeof F.endTimeUnixNano==="number")Y.endTimeUnixNano=I.longs===String?String(F.endTimeUnixNano):F.endTimeUnixNano;else Y.endTimeUnixNano=I.longs===String?C1.Long.prototype.toString.call(F.endTimeUnixNano):I.longs===Number?new C1.LongBits(F.endTimeUnixNano.low>>>0,F.endTimeUnixNano.high>>>0).toNumber():F.endTimeUnixNano;if(F.attributes&&F.attributes.length){Y.attributes=[];for(var J=0;J<F.attributes.length;++J)Y.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[J],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;if(F.events&&F.events.length){Y.events=[];for(var J=0;J<F.events.length;++J)Y.events[J]=X1.opentelemetry.proto.trace.v1.Span.Event.toObject(F.events[J],I)}if(F.droppedEventsCount!=null&&F.hasOwnProperty("droppedEventsCount"))Y.droppedEventsCount=F.droppedEventsCount;if(F.links&&F.links.length){Y.links=[];for(var J=0;J<F.links.length;++J)Y.links[J]=X1.opentelemetry.proto.trace.v1.Span.Link.toObject(F.links[J],I)}if(F.droppedLinksCount!=null&&F.hasOwnProperty("droppedLinksCount"))Y.droppedLinksCount=F.droppedLinksCount;if(F.status!=null&&F.hasOwnProperty("status"))Y.status=X1.opentelemetry.proto.trace.v1.Status.toObject(F.status,I);return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.Span"},Z.SpanKind=function(){var G={},F=Object.create(G);return F[G[0]="SPAN_KIND_UNSPECIFIED"]=0,F[G[1]="SPAN_KIND_INTERNAL"]=1,F[G[2]="SPAN_KIND_SERVER"]=2,F[G[3]="SPAN_KIND_CLIENT"]=3,F[G[4]="SPAN_KIND_PRODUCER"]=4,F[G[5]="SPAN_KIND_CONSUMER"]=5,F}(),Z.Event=function(){function G(F){if(this.attributes=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.timeUnixNano=null,G.prototype.name=null,G.prototype.attributes=C1.emptyArray,G.prototype.droppedAttributesCount=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(9).fixed64(I.timeUnixNano);if(I.name!=null&&Object.hasOwnProperty.call(I,"name"))Y.uint32(18).string(I.name);if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(26).fork()).ldelim();if(I.droppedAttributesCount!=null&&Object.hasOwnProperty.call(I,"droppedAttributesCount"))Y.uint32(32).uint32(I.droppedAttributesCount);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.trace.v1.Span.Event;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.timeUnixNano=I.fixed64();break}case 2:{J.name=I.string();break}case 3:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 4:{J.droppedAttributesCount=I.uint32();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!C1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&C1.isInteger(I.timeUnixNano.low)&&C1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.name!=null&&I.hasOwnProperty("name")){if(!C1.isString(I.name))return"name: string expected"}if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var Y=0;Y<I.attributes.length;++Y){var W=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[Y]);if(W)return"attributes."+W}}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount")){if(!C1.isInteger(I.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.trace.v1.Span.Event)return I;var Y=new X1.opentelemetry.proto.trace.v1.Span.Event;if(I.timeUnixNano!=null){if(C1.Long)(Y.timeUnixNano=C1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.name!=null)Y.name=String(I.name);if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.Event.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.Event.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.droppedAttributesCount!=null)Y.droppedAttributesCount=I.droppedAttributesCount>>>0;return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.attributes=[];if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;W.name="",W.droppedAttributesCount=0}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.name!=null&&I.hasOwnProperty("name"))W.name=I.name;if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount"))W.droppedAttributesCount=I.droppedAttributesCount;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.trace.v1.Span.Event"},G}(),Z.Link=function(){function G(F){if(this.attributes=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.traceId=null,G.prototype.spanId=null,G.prototype.traceState=null,G.prototype.attributes=C1.emptyArray,G.prototype.droppedAttributesCount=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.traceId!=null&&Object.hasOwnProperty.call(I,"traceId"))Y.uint32(10).bytes(I.traceId);if(I.spanId!=null&&Object.hasOwnProperty.call(I,"spanId"))Y.uint32(18).bytes(I.spanId);if(I.traceState!=null&&Object.hasOwnProperty.call(I,"traceState"))Y.uint32(26).string(I.traceState);if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(34).fork()).ldelim();if(I.droppedAttributesCount!=null&&Object.hasOwnProperty.call(I,"droppedAttributesCount"))Y.uint32(40).uint32(I.droppedAttributesCount);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.trace.v1.Span.Link;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.traceId=I.bytes();break}case 2:{J.spanId=I.bytes();break}case 3:{J.traceState=I.string();break}case 4:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 5:{J.droppedAttributesCount=I.uint32();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.traceId!=null&&I.hasOwnProperty("traceId")){if(!(I.traceId&&typeof I.traceId.length==="number"||C1.isString(I.traceId)))return"traceId: buffer expected"}if(I.spanId!=null&&I.hasOwnProperty("spanId")){if(!(I.spanId&&typeof I.spanId.length==="number"||C1.isString(I.spanId)))return"spanId: buffer expected"}if(I.traceState!=null&&I.hasOwnProperty("traceState")){if(!C1.isString(I.traceState))return"traceState: string expected"}if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var Y=0;Y<I.attributes.length;++Y){var W=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[Y]);if(W)return"attributes."+W}}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount")){if(!C1.isInteger(I.droppedAttributesCount))return"droppedAttributesCount: integer expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.trace.v1.Span.Link)return I;var Y=new X1.opentelemetry.proto.trace.v1.Span.Link;if(I.traceId!=null){if(typeof I.traceId==="string")C1.base64.decode(I.traceId,Y.traceId=C1.newBuffer(C1.base64.length(I.traceId)),0);else if(I.traceId.length>=0)Y.traceId=I.traceId}if(I.spanId!=null){if(typeof I.spanId==="string")C1.base64.decode(I.spanId,Y.spanId=C1.newBuffer(C1.base64.length(I.spanId)),0);else if(I.spanId.length>=0)Y.spanId=I.spanId}if(I.traceState!=null)Y.traceState=String(I.traceState);if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.trace.v1.Span.Link.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.trace.v1.Span.Link.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.droppedAttributesCount!=null)Y.droppedAttributesCount=I.droppedAttributesCount>>>0;return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.attributes=[];if(Y.defaults){if(Y.bytes===String)W.traceId="";else if(W.traceId=[],Y.bytes!==Array)W.traceId=C1.newBuffer(W.traceId);if(Y.bytes===String)W.spanId="";else if(W.spanId=[],Y.bytes!==Array)W.spanId=C1.newBuffer(W.spanId);W.traceState="",W.droppedAttributesCount=0}if(I.traceId!=null&&I.hasOwnProperty("traceId"))W.traceId=Y.bytes===String?C1.base64.encode(I.traceId,0,I.traceId.length):Y.bytes===Array?Array.prototype.slice.call(I.traceId):I.traceId;if(I.spanId!=null&&I.hasOwnProperty("spanId"))W.spanId=Y.bytes===String?C1.base64.encode(I.spanId,0,I.spanId.length):Y.bytes===Array?Array.prototype.slice.call(I.spanId):I.spanId;if(I.traceState!=null&&I.hasOwnProperty("traceState"))W.traceState=I.traceState;if(I.attributes&&I.attributes.length){W.attributes=[];for(var J=0;J<I.attributes.length;++J)W.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[J],Y)}if(I.droppedAttributesCount!=null&&I.hasOwnProperty("droppedAttributesCount"))W.droppedAttributesCount=I.droppedAttributesCount;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.trace.v1.Span.Link"},G}(),Z}(),D.Status=function(){function Z(G){if(G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.message=null,Z.prototype.code=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.message!=null&&Object.hasOwnProperty.call(F,"message"))I.uint32(18).string(F.message);if(F.code!=null&&Object.hasOwnProperty.call(F,"code"))I.uint32(24).int32(F.code);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.trace.v1.Status;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 2:{W.message=F.string();break}case 3:{W.code=F.int32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.message!=null&&F.hasOwnProperty("message")){if(!C1.isString(F.message))return"message: string expected"}if(F.code!=null&&F.hasOwnProperty("code"))switch(F.code){default:return"code: enum value expected";case 0:case 1:case 2:break}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.trace.v1.Status)return F;var I=new X1.opentelemetry.proto.trace.v1.Status;if(F.message!=null)I.message=String(F.message);switch(F.code){default:if(typeof F.code==="number"){I.code=F.code;break}break;case"STATUS_CODE_UNSET":case 0:I.code=0;break;case"STATUS_CODE_OK":case 1:I.code=1;break;case"STATUS_CODE_ERROR":case 2:I.code=2;break}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.defaults)Y.message="",Y.code=I.enums===String?"STATUS_CODE_UNSET":0;if(F.message!=null&&F.hasOwnProperty("message"))Y.message=F.message;if(F.code!=null&&F.hasOwnProperty("code"))Y.code=I.enums===String?X1.opentelemetry.proto.trace.v1.Status.StatusCode[F.code]===void 0?F.code:X1.opentelemetry.proto.trace.v1.Status.StatusCode[F.code]:F.code;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.trace.v1.Status"},Z.StatusCode=function(){var G={},F=Object.create(G);return F[G[0]="STATUS_CODE_UNSET"]=0,F[G[1]="STATUS_CODE_OK"]=1,F[G[2]="STATUS_CODE_ERROR"]=2,F}(),Z}(),D}(),Q}(),B.collector=function(){var Q={};return Q.trace=function(){var D={};return D.v1=function(){var Z={};return Z.TraceService=function(){function G(F,I,Y){c9.rpc.Service.call(this,F,I,Y)}return(G.prototype=Object.create(c9.rpc.Service.prototype)).constructor=G,G.create=function F(I,Y,W){return new this(I,Y,W)},Object.defineProperty(G.prototype.export=function F(I,Y){return this.rpcCall(F,X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest,X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse,I,Y)},"name",{value:"Export"}),G}(),Z.ExportTraceServiceRequest=function(){function G(F){if(this.resourceSpans=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.resourceSpans=C1.emptyArray,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.resourceSpans!=null&&I.resourceSpans.length)for(var W=0;W<I.resourceSpans.length;++W)X1.opentelemetry.proto.trace.v1.ResourceSpans.encode(I.resourceSpans[W],Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.resourceSpans&&J.resourceSpans.length))J.resourceSpans=[];J.resourceSpans.push(X1.opentelemetry.proto.trace.v1.ResourceSpans.decode(I,I.uint32()));break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.resourceSpans!=null&&I.hasOwnProperty("resourceSpans")){if(!Array.isArray(I.resourceSpans))return"resourceSpans: array expected";for(var Y=0;Y<I.resourceSpans.length;++Y){var W=X1.opentelemetry.proto.trace.v1.ResourceSpans.verify(I.resourceSpans[Y]);if(W)return"resourceSpans."+W}}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest)return I;var Y=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;if(I.resourceSpans){if(!Array.isArray(I.resourceSpans))throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest.resourceSpans: array expected");Y.resourceSpans=[];for(var W=0;W<I.resourceSpans.length;++W){if(typeof I.resourceSpans[W]!=="object")throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest.resourceSpans: object expected");Y.resourceSpans[W]=X1.opentelemetry.proto.trace.v1.ResourceSpans.fromObject(I.resourceSpans[W])}}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.resourceSpans=[];if(I.resourceSpans&&I.resourceSpans.length){W.resourceSpans=[];for(var J=0;J<I.resourceSpans.length;++J)W.resourceSpans[J]=X1.opentelemetry.proto.trace.v1.ResourceSpans.toObject(I.resourceSpans[J],Y)}return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest"},G}(),Z.ExportTraceServiceResponse=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.partialSuccess=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.partialSuccess!=null&&Object.hasOwnProperty.call(I,"partialSuccess"))X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.encode(I.partialSuccess,Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.partialSuccess=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess")){var Y=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.verify(I.partialSuccess);if(Y)return"partialSuccess."+Y}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse)return I;var Y=new X1.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse;if(I.partialSuccess!=null){if(typeof I.partialSuccess!=="object")throw TypeError(".opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse.partialSuccess: object expected");Y.partialSuccess=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.fromObject(I.partialSuccess)}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.partialSuccess=null;if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess"))W.partialSuccess=X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess.toObject(I.partialSuccess,Y);return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse"},G}(),Z.ExportTracePartialSuccess=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.rejectedSpans=null,G.prototype.errorMessage=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.rejectedSpans!=null&&Object.hasOwnProperty.call(I,"rejectedSpans"))Y.uint32(8).int64(I.rejectedSpans);if(I.errorMessage!=null&&Object.hasOwnProperty.call(I,"errorMessage"))Y.uint32(18).string(I.errorMessage);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.rejectedSpans=I.int64();break}case 2:{J.errorMessage=I.string();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.rejectedSpans!=null&&I.hasOwnProperty("rejectedSpans")){if(!C1.isInteger(I.rejectedSpans)&&!(I.rejectedSpans&&C1.isInteger(I.rejectedSpans.low)&&C1.isInteger(I.rejectedSpans.high)))return"rejectedSpans: integer|Long expected"}if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage")){if(!C1.isString(I.errorMessage))return"errorMessage: string expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess)return I;var Y=new X1.opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess;if(I.rejectedSpans!=null){if(C1.Long)(Y.rejectedSpans=C1.Long.fromValue(I.rejectedSpans)).unsigned=!1;else if(typeof I.rejectedSpans==="string")Y.rejectedSpans=parseInt(I.rejectedSpans,10);else if(typeof I.rejectedSpans==="number")Y.rejectedSpans=I.rejectedSpans;else if(typeof I.rejectedSpans==="object")Y.rejectedSpans=new C1.LongBits(I.rejectedSpans.low>>>0,I.rejectedSpans.high>>>0).toNumber()}if(I.errorMessage!=null)Y.errorMessage=String(I.errorMessage);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.rejectedSpans=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.rejectedSpans=Y.longs===String?"0":0;W.errorMessage=""}if(I.rejectedSpans!=null&&I.hasOwnProperty("rejectedSpans"))if(typeof I.rejectedSpans==="number")W.rejectedSpans=Y.longs===String?String(I.rejectedSpans):I.rejectedSpans;else W.rejectedSpans=Y.longs===String?C1.Long.prototype.toString.call(I.rejectedSpans):Y.longs===Number?new C1.LongBits(I.rejectedSpans.low>>>0,I.rejectedSpans.high>>>0).toNumber():I.rejectedSpans;if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage"))W.errorMessage=I.errorMessage;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.trace.v1.ExportTracePartialSuccess"},G}(),Z}(),D}(),Q.metrics=function(){var D={};return D.v1=function(){var Z={};return Z.MetricsService=function(){function G(F,I,Y){c9.rpc.Service.call(this,F,I,Y)}return(G.prototype=Object.create(c9.rpc.Service.prototype)).constructor=G,G.create=function F(I,Y,W){return new this(I,Y,W)},Object.defineProperty(G.prototype.export=function F(I,Y){return this.rpcCall(F,X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest,X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse,I,Y)},"name",{value:"Export"}),G}(),Z.ExportMetricsServiceRequest=function(){function G(F){if(this.resourceMetrics=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.resourceMetrics=C1.emptyArray,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.resourceMetrics!=null&&I.resourceMetrics.length)for(var W=0;W<I.resourceMetrics.length;++W)X1.opentelemetry.proto.metrics.v1.ResourceMetrics.encode(I.resourceMetrics[W],Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.resourceMetrics&&J.resourceMetrics.length))J.resourceMetrics=[];J.resourceMetrics.push(X1.opentelemetry.proto.metrics.v1.ResourceMetrics.decode(I,I.uint32()));break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.resourceMetrics!=null&&I.hasOwnProperty("resourceMetrics")){if(!Array.isArray(I.resourceMetrics))return"resourceMetrics: array expected";for(var Y=0;Y<I.resourceMetrics.length;++Y){var W=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.verify(I.resourceMetrics[Y]);if(W)return"resourceMetrics."+W}}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest)return I;var Y=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;if(I.resourceMetrics){if(!Array.isArray(I.resourceMetrics))throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest.resourceMetrics: array expected");Y.resourceMetrics=[];for(var W=0;W<I.resourceMetrics.length;++W){if(typeof I.resourceMetrics[W]!=="object")throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest.resourceMetrics: object expected");Y.resourceMetrics[W]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.fromObject(I.resourceMetrics[W])}}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.resourceMetrics=[];if(I.resourceMetrics&&I.resourceMetrics.length){W.resourceMetrics=[];for(var J=0;J<I.resourceMetrics.length;++J)W.resourceMetrics[J]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.toObject(I.resourceMetrics[J],Y)}return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest"},G}(),Z.ExportMetricsServiceResponse=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.partialSuccess=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.partialSuccess!=null&&Object.hasOwnProperty.call(I,"partialSuccess"))X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.encode(I.partialSuccess,Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.partialSuccess=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess")){var Y=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.verify(I.partialSuccess);if(Y)return"partialSuccess."+Y}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse)return I;var Y=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse;if(I.partialSuccess!=null){if(typeof I.partialSuccess!=="object")throw TypeError(".opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse.partialSuccess: object expected");Y.partialSuccess=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.fromObject(I.partialSuccess)}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.partialSuccess=null;if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess"))W.partialSuccess=X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess.toObject(I.partialSuccess,Y);return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse"},G}(),Z.ExportMetricsPartialSuccess=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.rejectedDataPoints=null,G.prototype.errorMessage=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.rejectedDataPoints!=null&&Object.hasOwnProperty.call(I,"rejectedDataPoints"))Y.uint32(8).int64(I.rejectedDataPoints);if(I.errorMessage!=null&&Object.hasOwnProperty.call(I,"errorMessage"))Y.uint32(18).string(I.errorMessage);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.rejectedDataPoints=I.int64();break}case 2:{J.errorMessage=I.string();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.rejectedDataPoints!=null&&I.hasOwnProperty("rejectedDataPoints")){if(!C1.isInteger(I.rejectedDataPoints)&&!(I.rejectedDataPoints&&C1.isInteger(I.rejectedDataPoints.low)&&C1.isInteger(I.rejectedDataPoints.high)))return"rejectedDataPoints: integer|Long expected"}if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage")){if(!C1.isString(I.errorMessage))return"errorMessage: string expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess)return I;var Y=new X1.opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess;if(I.rejectedDataPoints!=null){if(C1.Long)(Y.rejectedDataPoints=C1.Long.fromValue(I.rejectedDataPoints)).unsigned=!1;else if(typeof I.rejectedDataPoints==="string")Y.rejectedDataPoints=parseInt(I.rejectedDataPoints,10);else if(typeof I.rejectedDataPoints==="number")Y.rejectedDataPoints=I.rejectedDataPoints;else if(typeof I.rejectedDataPoints==="object")Y.rejectedDataPoints=new C1.LongBits(I.rejectedDataPoints.low>>>0,I.rejectedDataPoints.high>>>0).toNumber()}if(I.errorMessage!=null)Y.errorMessage=String(I.errorMessage);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.rejectedDataPoints=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.rejectedDataPoints=Y.longs===String?"0":0;W.errorMessage=""}if(I.rejectedDataPoints!=null&&I.hasOwnProperty("rejectedDataPoints"))if(typeof I.rejectedDataPoints==="number")W.rejectedDataPoints=Y.longs===String?String(I.rejectedDataPoints):I.rejectedDataPoints;else W.rejectedDataPoints=Y.longs===String?C1.Long.prototype.toString.call(I.rejectedDataPoints):Y.longs===Number?new C1.LongBits(I.rejectedDataPoints.low>>>0,I.rejectedDataPoints.high>>>0).toNumber():I.rejectedDataPoints;if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage"))W.errorMessage=I.errorMessage;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.metrics.v1.ExportMetricsPartialSuccess"},G}(),Z}(),D}(),Q.logs=function(){var D={};return D.v1=function(){var Z={};return Z.LogsService=function(){function G(F,I,Y){c9.rpc.Service.call(this,F,I,Y)}return(G.prototype=Object.create(c9.rpc.Service.prototype)).constructor=G,G.create=function F(I,Y,W){return new this(I,Y,W)},Object.defineProperty(G.prototype.export=function F(I,Y){return this.rpcCall(F,X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest,X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse,I,Y)},"name",{value:"Export"}),G}(),Z.ExportLogsServiceRequest=function(){function G(F){if(this.resourceLogs=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.resourceLogs=C1.emptyArray,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.resourceLogs!=null&&I.resourceLogs.length)for(var W=0;W<I.resourceLogs.length;++W)X1.opentelemetry.proto.logs.v1.ResourceLogs.encode(I.resourceLogs[W],Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.resourceLogs&&J.resourceLogs.length))J.resourceLogs=[];J.resourceLogs.push(X1.opentelemetry.proto.logs.v1.ResourceLogs.decode(I,I.uint32()));break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.resourceLogs!=null&&I.hasOwnProperty("resourceLogs")){if(!Array.isArray(I.resourceLogs))return"resourceLogs: array expected";for(var Y=0;Y<I.resourceLogs.length;++Y){var W=X1.opentelemetry.proto.logs.v1.ResourceLogs.verify(I.resourceLogs[Y]);if(W)return"resourceLogs."+W}}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest)return I;var Y=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest;if(I.resourceLogs){if(!Array.isArray(I.resourceLogs))throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest.resourceLogs: array expected");Y.resourceLogs=[];for(var W=0;W<I.resourceLogs.length;++W){if(typeof I.resourceLogs[W]!=="object")throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest.resourceLogs: object expected");Y.resourceLogs[W]=X1.opentelemetry.proto.logs.v1.ResourceLogs.fromObject(I.resourceLogs[W])}}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.resourceLogs=[];if(I.resourceLogs&&I.resourceLogs.length){W.resourceLogs=[];for(var J=0;J<I.resourceLogs.length;++J)W.resourceLogs[J]=X1.opentelemetry.proto.logs.v1.ResourceLogs.toObject(I.resourceLogs[J],Y)}return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.logs.v1.ExportLogsServiceRequest"},G}(),Z.ExportLogsServiceResponse=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.partialSuccess=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.partialSuccess!=null&&Object.hasOwnProperty.call(I,"partialSuccess"))X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.encode(I.partialSuccess,Y.uint32(10).fork()).ldelim();return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.partialSuccess=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess")){var Y=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.verify(I.partialSuccess);if(Y)return"partialSuccess."+Y}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse)return I;var Y=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse;if(I.partialSuccess!=null){if(typeof I.partialSuccess!=="object")throw TypeError(".opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse.partialSuccess: object expected");Y.partialSuccess=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.fromObject(I.partialSuccess)}return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.partialSuccess=null;if(I.partialSuccess!=null&&I.hasOwnProperty("partialSuccess"))W.partialSuccess=X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess.toObject(I.partialSuccess,Y);return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.logs.v1.ExportLogsServiceResponse"},G}(),Z.ExportLogsPartialSuccess=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.rejectedLogRecords=null,G.prototype.errorMessage=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.rejectedLogRecords!=null&&Object.hasOwnProperty.call(I,"rejectedLogRecords"))Y.uint32(8).int64(I.rejectedLogRecords);if(I.errorMessage!=null&&Object.hasOwnProperty.call(I,"errorMessage"))Y.uint32(18).string(I.errorMessage);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.rejectedLogRecords=I.int64();break}case 2:{J.errorMessage=I.string();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.rejectedLogRecords!=null&&I.hasOwnProperty("rejectedLogRecords")){if(!C1.isInteger(I.rejectedLogRecords)&&!(I.rejectedLogRecords&&C1.isInteger(I.rejectedLogRecords.low)&&C1.isInteger(I.rejectedLogRecords.high)))return"rejectedLogRecords: integer|Long expected"}if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage")){if(!C1.isString(I.errorMessage))return"errorMessage: string expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess)return I;var Y=new X1.opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess;if(I.rejectedLogRecords!=null){if(C1.Long)(Y.rejectedLogRecords=C1.Long.fromValue(I.rejectedLogRecords)).unsigned=!1;else if(typeof I.rejectedLogRecords==="string")Y.rejectedLogRecords=parseInt(I.rejectedLogRecords,10);else if(typeof I.rejectedLogRecords==="number")Y.rejectedLogRecords=I.rejectedLogRecords;else if(typeof I.rejectedLogRecords==="object")Y.rejectedLogRecords=new C1.LongBits(I.rejectedLogRecords.low>>>0,I.rejectedLogRecords.high>>>0).toNumber()}if(I.errorMessage!=null)Y.errorMessage=String(I.errorMessage);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.rejectedLogRecords=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.rejectedLogRecords=Y.longs===String?"0":0;W.errorMessage=""}if(I.rejectedLogRecords!=null&&I.hasOwnProperty("rejectedLogRecords"))if(typeof I.rejectedLogRecords==="number")W.rejectedLogRecords=Y.longs===String?String(I.rejectedLogRecords):I.rejectedLogRecords;else W.rejectedLogRecords=Y.longs===String?C1.Long.prototype.toString.call(I.rejectedLogRecords):Y.longs===Number?new C1.LongBits(I.rejectedLogRecords.low>>>0,I.rejectedLogRecords.high>>>0).toNumber():I.rejectedLogRecords;if(I.errorMessage!=null&&I.hasOwnProperty("errorMessage"))W.errorMessage=I.errorMessage;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.collector.logs.v1.ExportLogsPartialSuccess"},G}(),Z}(),D}(),Q}(),B.metrics=function(){var Q={};return Q.v1=function(){var D={};return D.MetricsData=function(){function Z(G){if(this.resourceMetrics=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resourceMetrics=C1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.resourceMetrics!=null&&F.resourceMetrics.length)for(var Y=0;Y<F.resourceMetrics.length;++Y)X1.opentelemetry.proto.metrics.v1.ResourceMetrics.encode(F.resourceMetrics[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.MetricsData;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.resourceMetrics&&W.resourceMetrics.length))W.resourceMetrics=[];W.resourceMetrics.push(X1.opentelemetry.proto.metrics.v1.ResourceMetrics.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resourceMetrics!=null&&F.hasOwnProperty("resourceMetrics")){if(!Array.isArray(F.resourceMetrics))return"resourceMetrics: array expected";for(var I=0;I<F.resourceMetrics.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.verify(F.resourceMetrics[I]);if(Y)return"resourceMetrics."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.MetricsData)return F;var I=new X1.opentelemetry.proto.metrics.v1.MetricsData;if(F.resourceMetrics){if(!Array.isArray(F.resourceMetrics))throw TypeError(".opentelemetry.proto.metrics.v1.MetricsData.resourceMetrics: array expected");I.resourceMetrics=[];for(var Y=0;Y<F.resourceMetrics.length;++Y){if(typeof F.resourceMetrics[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.MetricsData.resourceMetrics: object expected");I.resourceMetrics[Y]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.fromObject(F.resourceMetrics[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.resourceMetrics=[];if(F.resourceMetrics&&F.resourceMetrics.length){Y.resourceMetrics=[];for(var W=0;W<F.resourceMetrics.length;++W)Y.resourceMetrics[W]=X1.opentelemetry.proto.metrics.v1.ResourceMetrics.toObject(F.resourceMetrics[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.MetricsData"},Z}(),D.ResourceMetrics=function(){function Z(G){if(this.scopeMetrics=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resource=null,Z.prototype.scopeMetrics=C1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.resource!=null&&Object.hasOwnProperty.call(F,"resource"))X1.opentelemetry.proto.resource.v1.Resource.encode(F.resource,I.uint32(10).fork()).ldelim();if(F.scopeMetrics!=null&&F.scopeMetrics.length)for(var Y=0;Y<F.scopeMetrics.length;++Y)X1.opentelemetry.proto.metrics.v1.ScopeMetrics.encode(F.scopeMetrics[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.ResourceMetrics;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.resource=X1.opentelemetry.proto.resource.v1.Resource.decode(F,F.uint32());break}case 2:{if(!(W.scopeMetrics&&W.scopeMetrics.length))W.scopeMetrics=[];W.scopeMetrics.push(X1.opentelemetry.proto.metrics.v1.ScopeMetrics.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resource!=null&&F.hasOwnProperty("resource")){var I=X1.opentelemetry.proto.resource.v1.Resource.verify(F.resource);if(I)return"resource."+I}if(F.scopeMetrics!=null&&F.hasOwnProperty("scopeMetrics")){if(!Array.isArray(F.scopeMetrics))return"scopeMetrics: array expected";for(var Y=0;Y<F.scopeMetrics.length;++Y){var I=X1.opentelemetry.proto.metrics.v1.ScopeMetrics.verify(F.scopeMetrics[Y]);if(I)return"scopeMetrics."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!C1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.ResourceMetrics)return F;var I=new X1.opentelemetry.proto.metrics.v1.ResourceMetrics;if(F.resource!=null){if(typeof F.resource!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.resource: object expected");I.resource=X1.opentelemetry.proto.resource.v1.Resource.fromObject(F.resource)}if(F.scopeMetrics){if(!Array.isArray(F.scopeMetrics))throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.scopeMetrics: array expected");I.scopeMetrics=[];for(var Y=0;Y<F.scopeMetrics.length;++Y){if(typeof F.scopeMetrics[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ResourceMetrics.scopeMetrics: object expected");I.scopeMetrics[Y]=X1.opentelemetry.proto.metrics.v1.ScopeMetrics.fromObject(F.scopeMetrics[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.scopeMetrics=[];if(I.defaults)Y.resource=null,Y.schemaUrl="";if(F.resource!=null&&F.hasOwnProperty("resource"))Y.resource=X1.opentelemetry.proto.resource.v1.Resource.toObject(F.resource,I);if(F.scopeMetrics&&F.scopeMetrics.length){Y.scopeMetrics=[];for(var W=0;W<F.scopeMetrics.length;++W)Y.scopeMetrics[W]=X1.opentelemetry.proto.metrics.v1.ScopeMetrics.toObject(F.scopeMetrics[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.ResourceMetrics"},Z}(),D.ScopeMetrics=function(){function Z(G){if(this.metrics=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.scope=null,Z.prototype.metrics=C1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.scope!=null&&Object.hasOwnProperty.call(F,"scope"))X1.opentelemetry.proto.common.v1.InstrumentationScope.encode(F.scope,I.uint32(10).fork()).ldelim();if(F.metrics!=null&&F.metrics.length)for(var Y=0;Y<F.metrics.length;++Y)X1.opentelemetry.proto.metrics.v1.Metric.encode(F.metrics[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.ScopeMetrics;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.decode(F,F.uint32());break}case 2:{if(!(W.metrics&&W.metrics.length))W.metrics=[];W.metrics.push(X1.opentelemetry.proto.metrics.v1.Metric.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.scope!=null&&F.hasOwnProperty("scope")){var I=X1.opentelemetry.proto.common.v1.InstrumentationScope.verify(F.scope);if(I)return"scope."+I}if(F.metrics!=null&&F.hasOwnProperty("metrics")){if(!Array.isArray(F.metrics))return"metrics: array expected";for(var Y=0;Y<F.metrics.length;++Y){var I=X1.opentelemetry.proto.metrics.v1.Metric.verify(F.metrics[Y]);if(I)return"metrics."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!C1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.ScopeMetrics)return F;var I=new X1.opentelemetry.proto.metrics.v1.ScopeMetrics;if(F.scope!=null){if(typeof F.scope!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.scope: object expected");I.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(F.scope)}if(F.metrics){if(!Array.isArray(F.metrics))throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.metrics: array expected");I.metrics=[];for(var Y=0;Y<F.metrics.length;++Y){if(typeof F.metrics[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ScopeMetrics.metrics: object expected");I.metrics[Y]=X1.opentelemetry.proto.metrics.v1.Metric.fromObject(F.metrics[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.metrics=[];if(I.defaults)Y.scope=null,Y.schemaUrl="";if(F.scope!=null&&F.hasOwnProperty("scope"))Y.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.toObject(F.scope,I);if(F.metrics&&F.metrics.length){Y.metrics=[];for(var W=0;W<F.metrics.length;++W)Y.metrics[W]=X1.opentelemetry.proto.metrics.v1.Metric.toObject(F.metrics[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.ScopeMetrics"},Z}(),D.Metric=function(){function Z(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.name=null,Z.prototype.description=null,Z.prototype.unit=null,Z.prototype.gauge=null,Z.prototype.sum=null,Z.prototype.histogram=null,Z.prototype.exponentialHistogram=null,Z.prototype.summary=null;var G;return Object.defineProperty(Z.prototype,"data",{get:C1.oneOfGetter(G=["gauge","sum","histogram","exponentialHistogram","summary"]),set:C1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.name!=null&&Object.hasOwnProperty.call(I,"name"))Y.uint32(10).string(I.name);if(I.description!=null&&Object.hasOwnProperty.call(I,"description"))Y.uint32(18).string(I.description);if(I.unit!=null&&Object.hasOwnProperty.call(I,"unit"))Y.uint32(26).string(I.unit);if(I.gauge!=null&&Object.hasOwnProperty.call(I,"gauge"))X1.opentelemetry.proto.metrics.v1.Gauge.encode(I.gauge,Y.uint32(42).fork()).ldelim();if(I.sum!=null&&Object.hasOwnProperty.call(I,"sum"))X1.opentelemetry.proto.metrics.v1.Sum.encode(I.sum,Y.uint32(58).fork()).ldelim();if(I.histogram!=null&&Object.hasOwnProperty.call(I,"histogram"))X1.opentelemetry.proto.metrics.v1.Histogram.encode(I.histogram,Y.uint32(74).fork()).ldelim();if(I.exponentialHistogram!=null&&Object.hasOwnProperty.call(I,"exponentialHistogram"))X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.encode(I.exponentialHistogram,Y.uint32(82).fork()).ldelim();if(I.summary!=null&&Object.hasOwnProperty.call(I,"summary"))X1.opentelemetry.proto.metrics.v1.Summary.encode(I.summary,Y.uint32(90).fork()).ldelim();return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.Metric;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.name=I.string();break}case 2:{J.description=I.string();break}case 3:{J.unit=I.string();break}case 5:{J.gauge=X1.opentelemetry.proto.metrics.v1.Gauge.decode(I,I.uint32());break}case 7:{J.sum=X1.opentelemetry.proto.metrics.v1.Sum.decode(I,I.uint32());break}case 9:{J.histogram=X1.opentelemetry.proto.metrics.v1.Histogram.decode(I,I.uint32());break}case 10:{J.exponentialHistogram=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.decode(I,I.uint32());break}case 11:{J.summary=X1.opentelemetry.proto.metrics.v1.Summary.decode(I,I.uint32());break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.name!=null&&I.hasOwnProperty("name")){if(!C1.isString(I.name))return"name: string expected"}if(I.description!=null&&I.hasOwnProperty("description")){if(!C1.isString(I.description))return"description: string expected"}if(I.unit!=null&&I.hasOwnProperty("unit")){if(!C1.isString(I.unit))return"unit: string expected"}if(I.gauge!=null&&I.hasOwnProperty("gauge")){Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Gauge.verify(I.gauge);if(W)return"gauge."+W}}if(I.sum!=null&&I.hasOwnProperty("sum")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Sum.verify(I.sum);if(W)return"sum."+W}}if(I.histogram!=null&&I.hasOwnProperty("histogram")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Histogram.verify(I.histogram);if(W)return"histogram."+W}}if(I.exponentialHistogram!=null&&I.hasOwnProperty("exponentialHistogram")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.verify(I.exponentialHistogram);if(W)return"exponentialHistogram."+W}}if(I.summary!=null&&I.hasOwnProperty("summary")){if(Y.data===1)return"data: multiple values";Y.data=1;{var W=X1.opentelemetry.proto.metrics.v1.Summary.verify(I.summary);if(W)return"summary."+W}}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.Metric)return I;var Y=new X1.opentelemetry.proto.metrics.v1.Metric;if(I.name!=null)Y.name=String(I.name);if(I.description!=null)Y.description=String(I.description);if(I.unit!=null)Y.unit=String(I.unit);if(I.gauge!=null){if(typeof I.gauge!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.gauge: object expected");Y.gauge=X1.opentelemetry.proto.metrics.v1.Gauge.fromObject(I.gauge)}if(I.sum!=null){if(typeof I.sum!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.sum: object expected");Y.sum=X1.opentelemetry.proto.metrics.v1.Sum.fromObject(I.sum)}if(I.histogram!=null){if(typeof I.histogram!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.histogram: object expected");Y.histogram=X1.opentelemetry.proto.metrics.v1.Histogram.fromObject(I.histogram)}if(I.exponentialHistogram!=null){if(typeof I.exponentialHistogram!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.exponentialHistogram: object expected");Y.exponentialHistogram=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.fromObject(I.exponentialHistogram)}if(I.summary!=null){if(typeof I.summary!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Metric.summary: object expected");Y.summary=X1.opentelemetry.proto.metrics.v1.Summary.fromObject(I.summary)}return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.name="",W.description="",W.unit="";if(I.name!=null&&I.hasOwnProperty("name"))W.name=I.name;if(I.description!=null&&I.hasOwnProperty("description"))W.description=I.description;if(I.unit!=null&&I.hasOwnProperty("unit"))W.unit=I.unit;if(I.gauge!=null&&I.hasOwnProperty("gauge")){if(W.gauge=X1.opentelemetry.proto.metrics.v1.Gauge.toObject(I.gauge,Y),Y.oneofs)W.data="gauge"}if(I.sum!=null&&I.hasOwnProperty("sum")){if(W.sum=X1.opentelemetry.proto.metrics.v1.Sum.toObject(I.sum,Y),Y.oneofs)W.data="sum"}if(I.histogram!=null&&I.hasOwnProperty("histogram")){if(W.histogram=X1.opentelemetry.proto.metrics.v1.Histogram.toObject(I.histogram,Y),Y.oneofs)W.data="histogram"}if(I.exponentialHistogram!=null&&I.hasOwnProperty("exponentialHistogram")){if(W.exponentialHistogram=X1.opentelemetry.proto.metrics.v1.ExponentialHistogram.toObject(I.exponentialHistogram,Y),Y.oneofs)W.data="exponentialHistogram"}if(I.summary!=null&&I.hasOwnProperty("summary")){if(W.summary=X1.opentelemetry.proto.metrics.v1.Summary.toObject(I.summary,Y),Y.oneofs)W.data="summary"}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.Metric"},Z}(),D.Gauge=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=C1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.NumberDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Gauge;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.NumberDataPoint.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Gauge)return F;var I=new X1.opentelemetry.proto.metrics.v1.Gauge;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Gauge.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Gauge.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.fromObject(F.dataPoints[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.toObject(F.dataPoints[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Gauge"},Z}(),D.Sum=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=C1.emptyArray,Z.prototype.aggregationTemporality=null,Z.prototype.isMonotonic=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.NumberDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();if(F.aggregationTemporality!=null&&Object.hasOwnProperty.call(F,"aggregationTemporality"))I.uint32(16).int32(F.aggregationTemporality);if(F.isMonotonic!=null&&Object.hasOwnProperty.call(F,"isMonotonic"))I.uint32(24).bool(F.isMonotonic);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Sum;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.NumberDataPoint.decode(F,F.uint32()));break}case 2:{W.aggregationTemporality=F.int32();break}case 3:{W.isMonotonic=F.bool();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))switch(F.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:break}if(F.isMonotonic!=null&&F.hasOwnProperty("isMonotonic")){if(typeof F.isMonotonic!=="boolean")return"isMonotonic: boolean expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Sum)return F;var I=new X1.opentelemetry.proto.metrics.v1.Sum;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Sum.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Sum.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.fromObject(F.dataPoints[Y])}}switch(F.aggregationTemporality){default:if(typeof F.aggregationTemporality==="number"){I.aggregationTemporality=F.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:I.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:I.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:I.aggregationTemporality=2;break}if(F.isMonotonic!=null)I.isMonotonic=Boolean(F.isMonotonic);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(I.defaults)Y.aggregationTemporality=I.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0,Y.isMonotonic=!1;if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.NumberDataPoint.toObject(F.dataPoints[W],I)}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))Y.aggregationTemporality=I.enums===String?X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]===void 0?F.aggregationTemporality:X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]:F.aggregationTemporality;if(F.isMonotonic!=null&&F.hasOwnProperty("isMonotonic"))Y.isMonotonic=F.isMonotonic;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Sum"},Z}(),D.Histogram=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=C1.emptyArray,Z.prototype.aggregationTemporality=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();if(F.aggregationTemporality!=null&&Object.hasOwnProperty.call(F,"aggregationTemporality"))I.uint32(16).int32(F.aggregationTemporality);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Histogram;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.decode(F,F.uint32()));break}case 2:{W.aggregationTemporality=F.int32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))switch(F.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:break}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Histogram)return F;var I=new X1.opentelemetry.proto.metrics.v1.Histogram;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Histogram.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Histogram.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.fromObject(F.dataPoints[Y])}}switch(F.aggregationTemporality){default:if(typeof F.aggregationTemporality==="number"){I.aggregationTemporality=F.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:I.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:I.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:I.aggregationTemporality=2;break}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(I.defaults)Y.aggregationTemporality=I.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0;if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.HistogramDataPoint.toObject(F.dataPoints[W],I)}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))Y.aggregationTemporality=I.enums===String?X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]===void 0?F.aggregationTemporality:X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]:F.aggregationTemporality;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Histogram"},Z}(),D.ExponentialHistogram=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=C1.emptyArray,Z.prototype.aggregationTemporality=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();if(F.aggregationTemporality!=null&&Object.hasOwnProperty.call(F,"aggregationTemporality"))I.uint32(16).int32(F.aggregationTemporality);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogram;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.decode(F,F.uint32()));break}case 2:{W.aggregationTemporality=F.int32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))switch(F.aggregationTemporality){default:return"aggregationTemporality: enum value expected";case 0:case 1:case 2:break}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.ExponentialHistogram)return F;var I=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogram;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogram.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogram.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.fromObject(F.dataPoints[Y])}}switch(F.aggregationTemporality){default:if(typeof F.aggregationTemporality==="number"){I.aggregationTemporality=F.aggregationTemporality;break}break;case"AGGREGATION_TEMPORALITY_UNSPECIFIED":case 0:I.aggregationTemporality=0;break;case"AGGREGATION_TEMPORALITY_DELTA":case 1:I.aggregationTemporality=1;break;case"AGGREGATION_TEMPORALITY_CUMULATIVE":case 2:I.aggregationTemporality=2;break}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(I.defaults)Y.aggregationTemporality=I.enums===String?"AGGREGATION_TEMPORALITY_UNSPECIFIED":0;if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.toObject(F.dataPoints[W],I)}if(F.aggregationTemporality!=null&&F.hasOwnProperty("aggregationTemporality"))Y.aggregationTemporality=I.enums===String?X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]===void 0?F.aggregationTemporality:X1.opentelemetry.proto.metrics.v1.AggregationTemporality[F.aggregationTemporality]:F.aggregationTemporality;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.ExponentialHistogram"},Z}(),D.Summary=function(){function Z(G){if(this.dataPoints=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.dataPoints=C1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.dataPoints!=null&&F.dataPoints.length)for(var Y=0;Y<F.dataPoints.length;++Y)X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.encode(F.dataPoints[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.Summary;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.dataPoints&&W.dataPoints.length))W.dataPoints=[];W.dataPoints.push(X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.dataPoints!=null&&F.hasOwnProperty("dataPoints")){if(!Array.isArray(F.dataPoints))return"dataPoints: array expected";for(var I=0;I<F.dataPoints.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.verify(F.dataPoints[I]);if(Y)return"dataPoints."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.Summary)return F;var I=new X1.opentelemetry.proto.metrics.v1.Summary;if(F.dataPoints){if(!Array.isArray(F.dataPoints))throw TypeError(".opentelemetry.proto.metrics.v1.Summary.dataPoints: array expected");I.dataPoints=[];for(var Y=0;Y<F.dataPoints.length;++Y){if(typeof F.dataPoints[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Summary.dataPoints: object expected");I.dataPoints[Y]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.fromObject(F.dataPoints[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.dataPoints=[];if(F.dataPoints&&F.dataPoints.length){Y.dataPoints=[];for(var W=0;W<F.dataPoints.length;++W)Y.dataPoints[W]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.toObject(F.dataPoints[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.Summary"},Z}(),D.AggregationTemporality=function(){var Z={},G=Object.create(Z);return G[Z[0]="AGGREGATION_TEMPORALITY_UNSPECIFIED"]=0,G[Z[1]="AGGREGATION_TEMPORALITY_DELTA"]=1,G[Z[2]="AGGREGATION_TEMPORALITY_CUMULATIVE"]=2,G}(),D.DataPointFlags=function(){var Z={},G=Object.create(Z);return G[Z[0]="DATA_POINT_FLAGS_DO_NOT_USE"]=0,G[Z[1]="DATA_POINT_FLAGS_NO_RECORDED_VALUE_MASK"]=1,G}(),D.NumberDataPoint=function(){function Z(F){if(this.attributes=[],this.exemplars=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.attributes=C1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.asDouble=null,Z.prototype.asInt=null,Z.prototype.exemplars=C1.emptyArray,Z.prototype.flags=null;var G;return Object.defineProperty(Z.prototype,"value",{get:C1.oneOfGetter(G=["asDouble","asInt"]),set:C1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.startTimeUnixNano!=null&&Object.hasOwnProperty.call(I,"startTimeUnixNano"))Y.uint32(17).fixed64(I.startTimeUnixNano);if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(25).fixed64(I.timeUnixNano);if(I.asDouble!=null&&Object.hasOwnProperty.call(I,"asDouble"))Y.uint32(33).double(I.asDouble);if(I.exemplars!=null&&I.exemplars.length)for(var W=0;W<I.exemplars.length;++W)X1.opentelemetry.proto.metrics.v1.Exemplar.encode(I.exemplars[W],Y.uint32(42).fork()).ldelim();if(I.asInt!=null&&Object.hasOwnProperty.call(I,"asInt"))Y.uint32(49).sfixed64(I.asInt);if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(58).fork()).ldelim();if(I.flags!=null&&Object.hasOwnProperty.call(I,"flags"))Y.uint32(64).uint32(I.flags);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.NumberDataPoint;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 7:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.startTimeUnixNano=I.fixed64();break}case 3:{J.timeUnixNano=I.fixed64();break}case 4:{J.asDouble=I.double();break}case 6:{J.asInt=I.sfixed64();break}case 5:{if(!(J.exemplars&&J.exemplars.length))J.exemplars=[];J.exemplars.push(X1.opentelemetry.proto.metrics.v1.Exemplar.decode(I,I.uint32()));break}case 8:{J.flags=I.uint32();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var W=0;W<I.attributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[W]);if(J)return"attributes."+J}}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano")){if(!C1.isInteger(I.startTimeUnixNano)&&!(I.startTimeUnixNano&&C1.isInteger(I.startTimeUnixNano.low)&&C1.isInteger(I.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!C1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&C1.isInteger(I.timeUnixNano.low)&&C1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(Y.value=1,typeof I.asDouble!=="number")return"asDouble: number expected"}if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!C1.isInteger(I.asInt)&&!(I.asInt&&C1.isInteger(I.asInt.low)&&C1.isInteger(I.asInt.high)))return"asInt: integer|Long expected"}if(I.exemplars!=null&&I.hasOwnProperty("exemplars")){if(!Array.isArray(I.exemplars))return"exemplars: array expected";for(var W=0;W<I.exemplars.length;++W){var J=X1.opentelemetry.proto.metrics.v1.Exemplar.verify(I.exemplars[W]);if(J)return"exemplars."+J}}if(I.flags!=null&&I.hasOwnProperty("flags")){if(!C1.isInteger(I.flags))return"flags: integer expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.NumberDataPoint)return I;var Y=new X1.opentelemetry.proto.metrics.v1.NumberDataPoint;if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.startTimeUnixNano!=null){if(C1.Long)(Y.startTimeUnixNano=C1.Long.fromValue(I.startTimeUnixNano)).unsigned=!1;else if(typeof I.startTimeUnixNano==="string")Y.startTimeUnixNano=parseInt(I.startTimeUnixNano,10);else if(typeof I.startTimeUnixNano==="number")Y.startTimeUnixNano=I.startTimeUnixNano;else if(typeof I.startTimeUnixNano==="object")Y.startTimeUnixNano=new C1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber()}if(I.timeUnixNano!=null){if(C1.Long)(Y.timeUnixNano=C1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.asDouble!=null)Y.asDouble=Number(I.asDouble);if(I.asInt!=null){if(C1.Long)(Y.asInt=C1.Long.fromValue(I.asInt)).unsigned=!1;else if(typeof I.asInt==="string")Y.asInt=parseInt(I.asInt,10);else if(typeof I.asInt==="number")Y.asInt=I.asInt;else if(typeof I.asInt==="object")Y.asInt=new C1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber()}if(I.exemplars){if(!Array.isArray(I.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.exemplars: array expected");Y.exemplars=[];for(var W=0;W<I.exemplars.length;++W){if(typeof I.exemplars[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.NumberDataPoint.exemplars: object expected");Y.exemplars[W]=X1.opentelemetry.proto.metrics.v1.Exemplar.fromObject(I.exemplars[W])}}if(I.flags!=null)Y.flags=I.flags>>>0;return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.exemplars=[],W.attributes=[];if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.startTimeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.startTimeUnixNano=Y.longs===String?"0":0;if(C1.Long){var J=new C1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;W.flags=0}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano"))if(typeof I.startTimeUnixNano==="number")W.startTimeUnixNano=Y.longs===String?String(I.startTimeUnixNano):I.startTimeUnixNano;else W.startTimeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.startTimeUnixNano):Y.longs===Number?new C1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber():I.startTimeUnixNano;if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(W.asDouble=Y.json&&!isFinite(I.asDouble)?String(I.asDouble):I.asDouble,Y.oneofs)W.value="asDouble"}if(I.exemplars&&I.exemplars.length){W.exemplars=[];for(var X=0;X<I.exemplars.length;++X)W.exemplars[X]=X1.opentelemetry.proto.metrics.v1.Exemplar.toObject(I.exemplars[X],Y)}if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(typeof I.asInt==="number")W.asInt=Y.longs===String?String(I.asInt):I.asInt;else W.asInt=Y.longs===String?C1.Long.prototype.toString.call(I.asInt):Y.longs===Number?new C1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber():I.asInt;if(Y.oneofs)W.value="asInt"}if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.flags!=null&&I.hasOwnProperty("flags"))W.flags=I.flags;return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.NumberDataPoint"},Z}(),D.HistogramDataPoint=function(){function Z(F){if(this.attributes=[],this.bucketCounts=[],this.explicitBounds=[],this.exemplars=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.attributes=C1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.count=null,Z.prototype.sum=null,Z.prototype.bucketCounts=C1.emptyArray,Z.prototype.explicitBounds=C1.emptyArray,Z.prototype.exemplars=C1.emptyArray,Z.prototype.flags=null,Z.prototype.min=null,Z.prototype.max=null;var G;return Object.defineProperty(Z.prototype,"_sum",{get:C1.oneOfGetter(G=["sum"]),set:C1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_min",{get:C1.oneOfGetter(G=["min"]),set:C1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_max",{get:C1.oneOfGetter(G=["max"]),set:C1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.startTimeUnixNano!=null&&Object.hasOwnProperty.call(I,"startTimeUnixNano"))Y.uint32(17).fixed64(I.startTimeUnixNano);if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(25).fixed64(I.timeUnixNano);if(I.count!=null&&Object.hasOwnProperty.call(I,"count"))Y.uint32(33).fixed64(I.count);if(I.sum!=null&&Object.hasOwnProperty.call(I,"sum"))Y.uint32(41).double(I.sum);if(I.bucketCounts!=null&&I.bucketCounts.length){Y.uint32(50).fork();for(var W=0;W<I.bucketCounts.length;++W)Y.fixed64(I.bucketCounts[W]);Y.ldelim()}if(I.explicitBounds!=null&&I.explicitBounds.length){Y.uint32(58).fork();for(var W=0;W<I.explicitBounds.length;++W)Y.double(I.explicitBounds[W]);Y.ldelim()}if(I.exemplars!=null&&I.exemplars.length)for(var W=0;W<I.exemplars.length;++W)X1.opentelemetry.proto.metrics.v1.Exemplar.encode(I.exemplars[W],Y.uint32(66).fork()).ldelim();if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(74).fork()).ldelim();if(I.flags!=null&&Object.hasOwnProperty.call(I,"flags"))Y.uint32(80).uint32(I.flags);if(I.min!=null&&Object.hasOwnProperty.call(I,"min"))Y.uint32(89).double(I.min);if(I.max!=null&&Object.hasOwnProperty.call(I,"max"))Y.uint32(97).double(I.max);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.HistogramDataPoint;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 9:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.startTimeUnixNano=I.fixed64();break}case 3:{J.timeUnixNano=I.fixed64();break}case 4:{J.count=I.fixed64();break}case 5:{J.sum=I.double();break}case 6:{if(!(J.bucketCounts&&J.bucketCounts.length))J.bucketCounts=[];if((X&7)===2){var V=I.uint32()+I.pos;while(I.pos<V)J.bucketCounts.push(I.fixed64())}else J.bucketCounts.push(I.fixed64());break}case 7:{if(!(J.explicitBounds&&J.explicitBounds.length))J.explicitBounds=[];if((X&7)===2){var V=I.uint32()+I.pos;while(I.pos<V)J.explicitBounds.push(I.double())}else J.explicitBounds.push(I.double());break}case 8:{if(!(J.exemplars&&J.exemplars.length))J.exemplars=[];J.exemplars.push(X1.opentelemetry.proto.metrics.v1.Exemplar.decode(I,I.uint32()));break}case 10:{J.flags=I.uint32();break}case 11:{J.min=I.double();break}case 12:{J.max=I.double();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var W=0;W<I.attributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[W]);if(J)return"attributes."+J}}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano")){if(!C1.isInteger(I.startTimeUnixNano)&&!(I.startTimeUnixNano&&C1.isInteger(I.startTimeUnixNano.low)&&C1.isInteger(I.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!C1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&C1.isInteger(I.timeUnixNano.low)&&C1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.count!=null&&I.hasOwnProperty("count")){if(!C1.isInteger(I.count)&&!(I.count&&C1.isInteger(I.count.low)&&C1.isInteger(I.count.high)))return"count: integer|Long expected"}if(I.sum!=null&&I.hasOwnProperty("sum")){if(Y._sum=1,typeof I.sum!=="number")return"sum: number expected"}if(I.bucketCounts!=null&&I.hasOwnProperty("bucketCounts")){if(!Array.isArray(I.bucketCounts))return"bucketCounts: array expected";for(var W=0;W<I.bucketCounts.length;++W)if(!C1.isInteger(I.bucketCounts[W])&&!(I.bucketCounts[W]&&C1.isInteger(I.bucketCounts[W].low)&&C1.isInteger(I.bucketCounts[W].high)))return"bucketCounts: integer|Long[] expected"}if(I.explicitBounds!=null&&I.hasOwnProperty("explicitBounds")){if(!Array.isArray(I.explicitBounds))return"explicitBounds: array expected";for(var W=0;W<I.explicitBounds.length;++W)if(typeof I.explicitBounds[W]!=="number")return"explicitBounds: number[] expected"}if(I.exemplars!=null&&I.hasOwnProperty("exemplars")){if(!Array.isArray(I.exemplars))return"exemplars: array expected";for(var W=0;W<I.exemplars.length;++W){var J=X1.opentelemetry.proto.metrics.v1.Exemplar.verify(I.exemplars[W]);if(J)return"exemplars."+J}}if(I.flags!=null&&I.hasOwnProperty("flags")){if(!C1.isInteger(I.flags))return"flags: integer expected"}if(I.min!=null&&I.hasOwnProperty("min")){if(Y._min=1,typeof I.min!=="number")return"min: number expected"}if(I.max!=null&&I.hasOwnProperty("max")){if(Y._max=1,typeof I.max!=="number")return"max: number expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.HistogramDataPoint)return I;var Y=new X1.opentelemetry.proto.metrics.v1.HistogramDataPoint;if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.startTimeUnixNano!=null){if(C1.Long)(Y.startTimeUnixNano=C1.Long.fromValue(I.startTimeUnixNano)).unsigned=!1;else if(typeof I.startTimeUnixNano==="string")Y.startTimeUnixNano=parseInt(I.startTimeUnixNano,10);else if(typeof I.startTimeUnixNano==="number")Y.startTimeUnixNano=I.startTimeUnixNano;else if(typeof I.startTimeUnixNano==="object")Y.startTimeUnixNano=new C1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber()}if(I.timeUnixNano!=null){if(C1.Long)(Y.timeUnixNano=C1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.count!=null){if(C1.Long)(Y.count=C1.Long.fromValue(I.count)).unsigned=!1;else if(typeof I.count==="string")Y.count=parseInt(I.count,10);else if(typeof I.count==="number")Y.count=I.count;else if(typeof I.count==="object")Y.count=new C1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber()}if(I.sum!=null)Y.sum=Number(I.sum);if(I.bucketCounts){if(!Array.isArray(I.bucketCounts))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.bucketCounts: array expected");Y.bucketCounts=[];for(var W=0;W<I.bucketCounts.length;++W)if(C1.Long)(Y.bucketCounts[W]=C1.Long.fromValue(I.bucketCounts[W])).unsigned=!1;else if(typeof I.bucketCounts[W]==="string")Y.bucketCounts[W]=parseInt(I.bucketCounts[W],10);else if(typeof I.bucketCounts[W]==="number")Y.bucketCounts[W]=I.bucketCounts[W];else if(typeof I.bucketCounts[W]==="object")Y.bucketCounts[W]=new C1.LongBits(I.bucketCounts[W].low>>>0,I.bucketCounts[W].high>>>0).toNumber()}if(I.explicitBounds){if(!Array.isArray(I.explicitBounds))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.explicitBounds: array expected");Y.explicitBounds=[];for(var W=0;W<I.explicitBounds.length;++W)Y.explicitBounds[W]=Number(I.explicitBounds[W])}if(I.exemplars){if(!Array.isArray(I.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.exemplars: array expected");Y.exemplars=[];for(var W=0;W<I.exemplars.length;++W){if(typeof I.exemplars[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.HistogramDataPoint.exemplars: object expected");Y.exemplars[W]=X1.opentelemetry.proto.metrics.v1.Exemplar.fromObject(I.exemplars[W])}}if(I.flags!=null)Y.flags=I.flags>>>0;if(I.min!=null)Y.min=Number(I.min);if(I.max!=null)Y.max=Number(I.max);return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.bucketCounts=[],W.explicitBounds=[],W.exemplars=[],W.attributes=[];if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.startTimeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.startTimeUnixNano=Y.longs===String?"0":0;if(C1.Long){var J=new C1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;if(C1.Long){var J=new C1.Long(0,0,!1);W.count=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.count=Y.longs===String?"0":0;W.flags=0}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano"))if(typeof I.startTimeUnixNano==="number")W.startTimeUnixNano=Y.longs===String?String(I.startTimeUnixNano):I.startTimeUnixNano;else W.startTimeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.startTimeUnixNano):Y.longs===Number?new C1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber():I.startTimeUnixNano;if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.count!=null&&I.hasOwnProperty("count"))if(typeof I.count==="number")W.count=Y.longs===String?String(I.count):I.count;else W.count=Y.longs===String?C1.Long.prototype.toString.call(I.count):Y.longs===Number?new C1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber():I.count;if(I.sum!=null&&I.hasOwnProperty("sum")){if(W.sum=Y.json&&!isFinite(I.sum)?String(I.sum):I.sum,Y.oneofs)W._sum="sum"}if(I.bucketCounts&&I.bucketCounts.length){W.bucketCounts=[];for(var X=0;X<I.bucketCounts.length;++X)if(typeof I.bucketCounts[X]==="number")W.bucketCounts[X]=Y.longs===String?String(I.bucketCounts[X]):I.bucketCounts[X];else W.bucketCounts[X]=Y.longs===String?C1.Long.prototype.toString.call(I.bucketCounts[X]):Y.longs===Number?new C1.LongBits(I.bucketCounts[X].low>>>0,I.bucketCounts[X].high>>>0).toNumber():I.bucketCounts[X]}if(I.explicitBounds&&I.explicitBounds.length){W.explicitBounds=[];for(var X=0;X<I.explicitBounds.length;++X)W.explicitBounds[X]=Y.json&&!isFinite(I.explicitBounds[X])?String(I.explicitBounds[X]):I.explicitBounds[X]}if(I.exemplars&&I.exemplars.length){W.exemplars=[];for(var X=0;X<I.exemplars.length;++X)W.exemplars[X]=X1.opentelemetry.proto.metrics.v1.Exemplar.toObject(I.exemplars[X],Y)}if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.flags!=null&&I.hasOwnProperty("flags"))W.flags=I.flags;if(I.min!=null&&I.hasOwnProperty("min")){if(W.min=Y.json&&!isFinite(I.min)?String(I.min):I.min,Y.oneofs)W._min="min"}if(I.max!=null&&I.hasOwnProperty("max")){if(W.max=Y.json&&!isFinite(I.max)?String(I.max):I.max,Y.oneofs)W._max="max"}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.HistogramDataPoint"},Z}(),D.ExponentialHistogramDataPoint=function(){function Z(F){if(this.attributes=[],this.exemplars=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.attributes=C1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.count=null,Z.prototype.sum=null,Z.prototype.scale=null,Z.prototype.zeroCount=null,Z.prototype.positive=null,Z.prototype.negative=null,Z.prototype.flags=null,Z.prototype.exemplars=C1.emptyArray,Z.prototype.min=null,Z.prototype.max=null,Z.prototype.zeroThreshold=null;var G;return Object.defineProperty(Z.prototype,"_sum",{get:C1.oneOfGetter(G=["sum"]),set:C1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_min",{get:C1.oneOfGetter(G=["min"]),set:C1.oneOfSetter(G)}),Object.defineProperty(Z.prototype,"_max",{get:C1.oneOfGetter(G=["max"]),set:C1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.attributes!=null&&I.attributes.length)for(var W=0;W<I.attributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.attributes[W],Y.uint32(10).fork()).ldelim();if(I.startTimeUnixNano!=null&&Object.hasOwnProperty.call(I,"startTimeUnixNano"))Y.uint32(17).fixed64(I.startTimeUnixNano);if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(25).fixed64(I.timeUnixNano);if(I.count!=null&&Object.hasOwnProperty.call(I,"count"))Y.uint32(33).fixed64(I.count);if(I.sum!=null&&Object.hasOwnProperty.call(I,"sum"))Y.uint32(41).double(I.sum);if(I.scale!=null&&Object.hasOwnProperty.call(I,"scale"))Y.uint32(48).sint32(I.scale);if(I.zeroCount!=null&&Object.hasOwnProperty.call(I,"zeroCount"))Y.uint32(57).fixed64(I.zeroCount);if(I.positive!=null&&Object.hasOwnProperty.call(I,"positive"))X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.encode(I.positive,Y.uint32(66).fork()).ldelim();if(I.negative!=null&&Object.hasOwnProperty.call(I,"negative"))X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.encode(I.negative,Y.uint32(74).fork()).ldelim();if(I.flags!=null&&Object.hasOwnProperty.call(I,"flags"))Y.uint32(80).uint32(I.flags);if(I.exemplars!=null&&I.exemplars.length)for(var W=0;W<I.exemplars.length;++W)X1.opentelemetry.proto.metrics.v1.Exemplar.encode(I.exemplars[W],Y.uint32(90).fork()).ldelim();if(I.min!=null&&Object.hasOwnProperty.call(I,"min"))Y.uint32(97).double(I.min);if(I.max!=null&&Object.hasOwnProperty.call(I,"max"))Y.uint32(105).double(I.max);if(I.zeroThreshold!=null&&Object.hasOwnProperty.call(I,"zeroThreshold"))Y.uint32(113).double(I.zeroThreshold);return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{if(!(J.attributes&&J.attributes.length))J.attributes=[];J.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.startTimeUnixNano=I.fixed64();break}case 3:{J.timeUnixNano=I.fixed64();break}case 4:{J.count=I.fixed64();break}case 5:{J.sum=I.double();break}case 6:{J.scale=I.sint32();break}case 7:{J.zeroCount=I.fixed64();break}case 8:{J.positive=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.decode(I,I.uint32());break}case 9:{J.negative=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.decode(I,I.uint32());break}case 10:{J.flags=I.uint32();break}case 11:{if(!(J.exemplars&&J.exemplars.length))J.exemplars=[];J.exemplars.push(X1.opentelemetry.proto.metrics.v1.Exemplar.decode(I,I.uint32()));break}case 12:{J.min=I.double();break}case 13:{J.max=I.double();break}case 14:{J.zeroThreshold=I.double();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.attributes!=null&&I.hasOwnProperty("attributes")){if(!Array.isArray(I.attributes))return"attributes: array expected";for(var W=0;W<I.attributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.attributes[W]);if(J)return"attributes."+J}}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano")){if(!C1.isInteger(I.startTimeUnixNano)&&!(I.startTimeUnixNano&&C1.isInteger(I.startTimeUnixNano.low)&&C1.isInteger(I.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!C1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&C1.isInteger(I.timeUnixNano.low)&&C1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.count!=null&&I.hasOwnProperty("count")){if(!C1.isInteger(I.count)&&!(I.count&&C1.isInteger(I.count.low)&&C1.isInteger(I.count.high)))return"count: integer|Long expected"}if(I.sum!=null&&I.hasOwnProperty("sum")){if(Y._sum=1,typeof I.sum!=="number")return"sum: number expected"}if(I.scale!=null&&I.hasOwnProperty("scale")){if(!C1.isInteger(I.scale))return"scale: integer expected"}if(I.zeroCount!=null&&I.hasOwnProperty("zeroCount")){if(!C1.isInteger(I.zeroCount)&&!(I.zeroCount&&C1.isInteger(I.zeroCount.low)&&C1.isInteger(I.zeroCount.high)))return"zeroCount: integer|Long expected"}if(I.positive!=null&&I.hasOwnProperty("positive")){var J=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.verify(I.positive);if(J)return"positive."+J}if(I.negative!=null&&I.hasOwnProperty("negative")){var J=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.verify(I.negative);if(J)return"negative."+J}if(I.flags!=null&&I.hasOwnProperty("flags")){if(!C1.isInteger(I.flags))return"flags: integer expected"}if(I.exemplars!=null&&I.hasOwnProperty("exemplars")){if(!Array.isArray(I.exemplars))return"exemplars: array expected";for(var W=0;W<I.exemplars.length;++W){var J=X1.opentelemetry.proto.metrics.v1.Exemplar.verify(I.exemplars[W]);if(J)return"exemplars."+J}}if(I.min!=null&&I.hasOwnProperty("min")){if(Y._min=1,typeof I.min!=="number")return"min: number expected"}if(I.max!=null&&I.hasOwnProperty("max")){if(Y._max=1,typeof I.max!=="number")return"max: number expected"}if(I.zeroThreshold!=null&&I.hasOwnProperty("zeroThreshold")){if(typeof I.zeroThreshold!=="number")return"zeroThreshold: number expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint)return I;var Y=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint;if(I.attributes){if(!Array.isArray(I.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.attributes: array expected");Y.attributes=[];for(var W=0;W<I.attributes.length;++W){if(typeof I.attributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.attributes: object expected");Y.attributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.attributes[W])}}if(I.startTimeUnixNano!=null){if(C1.Long)(Y.startTimeUnixNano=C1.Long.fromValue(I.startTimeUnixNano)).unsigned=!1;else if(typeof I.startTimeUnixNano==="string")Y.startTimeUnixNano=parseInt(I.startTimeUnixNano,10);else if(typeof I.startTimeUnixNano==="number")Y.startTimeUnixNano=I.startTimeUnixNano;else if(typeof I.startTimeUnixNano==="object")Y.startTimeUnixNano=new C1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber()}if(I.timeUnixNano!=null){if(C1.Long)(Y.timeUnixNano=C1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.count!=null){if(C1.Long)(Y.count=C1.Long.fromValue(I.count)).unsigned=!1;else if(typeof I.count==="string")Y.count=parseInt(I.count,10);else if(typeof I.count==="number")Y.count=I.count;else if(typeof I.count==="object")Y.count=new C1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber()}if(I.sum!=null)Y.sum=Number(I.sum);if(I.scale!=null)Y.scale=I.scale|0;if(I.zeroCount!=null){if(C1.Long)(Y.zeroCount=C1.Long.fromValue(I.zeroCount)).unsigned=!1;else if(typeof I.zeroCount==="string")Y.zeroCount=parseInt(I.zeroCount,10);else if(typeof I.zeroCount==="number")Y.zeroCount=I.zeroCount;else if(typeof I.zeroCount==="object")Y.zeroCount=new C1.LongBits(I.zeroCount.low>>>0,I.zeroCount.high>>>0).toNumber()}if(I.positive!=null){if(typeof I.positive!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.positive: object expected");Y.positive=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.fromObject(I.positive)}if(I.negative!=null){if(typeof I.negative!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.negative: object expected");Y.negative=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.fromObject(I.negative)}if(I.flags!=null)Y.flags=I.flags>>>0;if(I.exemplars){if(!Array.isArray(I.exemplars))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.exemplars: array expected");Y.exemplars=[];for(var W=0;W<I.exemplars.length;++W){if(typeof I.exemplars[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.exemplars: object expected");Y.exemplars[W]=X1.opentelemetry.proto.metrics.v1.Exemplar.fromObject(I.exemplars[W])}}if(I.min!=null)Y.min=Number(I.min);if(I.max!=null)Y.max=Number(I.max);if(I.zeroThreshold!=null)Y.zeroThreshold=Number(I.zeroThreshold);return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.attributes=[],W.exemplars=[];if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.startTimeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.startTimeUnixNano=Y.longs===String?"0":0;if(C1.Long){var J=new C1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;if(C1.Long){var J=new C1.Long(0,0,!1);W.count=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.count=Y.longs===String?"0":0;if(W.scale=0,C1.Long){var J=new C1.Long(0,0,!1);W.zeroCount=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.zeroCount=Y.longs===String?"0":0;W.positive=null,W.negative=null,W.flags=0,W.zeroThreshold=0}if(I.attributes&&I.attributes.length){W.attributes=[];for(var X=0;X<I.attributes.length;++X)W.attributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.attributes[X],Y)}if(I.startTimeUnixNano!=null&&I.hasOwnProperty("startTimeUnixNano"))if(typeof I.startTimeUnixNano==="number")W.startTimeUnixNano=Y.longs===String?String(I.startTimeUnixNano):I.startTimeUnixNano;else W.startTimeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.startTimeUnixNano):Y.longs===Number?new C1.LongBits(I.startTimeUnixNano.low>>>0,I.startTimeUnixNano.high>>>0).toNumber():I.startTimeUnixNano;if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.count!=null&&I.hasOwnProperty("count"))if(typeof I.count==="number")W.count=Y.longs===String?String(I.count):I.count;else W.count=Y.longs===String?C1.Long.prototype.toString.call(I.count):Y.longs===Number?new C1.LongBits(I.count.low>>>0,I.count.high>>>0).toNumber():I.count;if(I.sum!=null&&I.hasOwnProperty("sum")){if(W.sum=Y.json&&!isFinite(I.sum)?String(I.sum):I.sum,Y.oneofs)W._sum="sum"}if(I.scale!=null&&I.hasOwnProperty("scale"))W.scale=I.scale;if(I.zeroCount!=null&&I.hasOwnProperty("zeroCount"))if(typeof I.zeroCount==="number")W.zeroCount=Y.longs===String?String(I.zeroCount):I.zeroCount;else W.zeroCount=Y.longs===String?C1.Long.prototype.toString.call(I.zeroCount):Y.longs===Number?new C1.LongBits(I.zeroCount.low>>>0,I.zeroCount.high>>>0).toNumber():I.zeroCount;if(I.positive!=null&&I.hasOwnProperty("positive"))W.positive=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.toObject(I.positive,Y);if(I.negative!=null&&I.hasOwnProperty("negative"))W.negative=X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.toObject(I.negative,Y);if(I.flags!=null&&I.hasOwnProperty("flags"))W.flags=I.flags;if(I.exemplars&&I.exemplars.length){W.exemplars=[];for(var X=0;X<I.exemplars.length;++X)W.exemplars[X]=X1.opentelemetry.proto.metrics.v1.Exemplar.toObject(I.exemplars[X],Y)}if(I.min!=null&&I.hasOwnProperty("min")){if(W.min=Y.json&&!isFinite(I.min)?String(I.min):I.min,Y.oneofs)W._min="min"}if(I.max!=null&&I.hasOwnProperty("max")){if(W.max=Y.json&&!isFinite(I.max)?String(I.max):I.max,Y.oneofs)W._max="max"}if(I.zeroThreshold!=null&&I.hasOwnProperty("zeroThreshold"))W.zeroThreshold=Y.json&&!isFinite(I.zeroThreshold)?String(I.zeroThreshold):I.zeroThreshold;return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint"},Z.Buckets=function(){function F(I){if(this.bucketCounts=[],I){for(var Y=Object.keys(I),W=0;W<Y.length;++W)if(I[Y[W]]!=null)this[Y[W]]=I[Y[W]]}}return F.prototype.offset=null,F.prototype.bucketCounts=C1.emptyArray,F.create=function I(Y){return new F(Y)},F.encode=function I(Y,W){if(!W)W=Z4.create();if(Y.offset!=null&&Object.hasOwnProperty.call(Y,"offset"))W.uint32(8).sint32(Y.offset);if(Y.bucketCounts!=null&&Y.bucketCounts.length){W.uint32(18).fork();for(var J=0;J<Y.bucketCounts.length;++J)W.uint64(Y.bucketCounts[J]);W.ldelim()}return W},F.encodeDelimited=function I(Y,W){return this.encode(Y,W).ldelim()},F.decode=function I(Y,W){if(!(Y instanceof d0))Y=d0.create(Y);var J=W===void 0?Y.len:Y.pos+W,X=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets;while(Y.pos<J){var V=Y.uint32();switch(V>>>3){case 1:{X.offset=Y.sint32();break}case 2:{if(!(X.bucketCounts&&X.bucketCounts.length))X.bucketCounts=[];if((V&7)===2){var C=Y.uint32()+Y.pos;while(Y.pos<C)X.bucketCounts.push(Y.uint64())}else X.bucketCounts.push(Y.uint64());break}default:Y.skipType(V&7);break}}return X},F.decodeDelimited=function I(Y){if(!(Y instanceof d0))Y=new d0(Y);return this.decode(Y,Y.uint32())},F.verify=function I(Y){if(typeof Y!=="object"||Y===null)return"object expected";if(Y.offset!=null&&Y.hasOwnProperty("offset")){if(!C1.isInteger(Y.offset))return"offset: integer expected"}if(Y.bucketCounts!=null&&Y.hasOwnProperty("bucketCounts")){if(!Array.isArray(Y.bucketCounts))return"bucketCounts: array expected";for(var W=0;W<Y.bucketCounts.length;++W)if(!C1.isInteger(Y.bucketCounts[W])&&!(Y.bucketCounts[W]&&C1.isInteger(Y.bucketCounts[W].low)&&C1.isInteger(Y.bucketCounts[W].high)))return"bucketCounts: integer|Long[] expected"}return null},F.fromObject=function I(Y){if(Y instanceof X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets)return Y;var W=new X1.opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets;if(Y.offset!=null)W.offset=Y.offset|0;if(Y.bucketCounts){if(!Array.isArray(Y.bucketCounts))throw TypeError(".opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets.bucketCounts: array expected");W.bucketCounts=[];for(var J=0;J<Y.bucketCounts.length;++J)if(C1.Long)(W.bucketCounts[J]=C1.Long.fromValue(Y.bucketCounts[J])).unsigned=!0;else if(typeof Y.bucketCounts[J]==="string")W.bucketCounts[J]=parseInt(Y.bucketCounts[J],10);else if(typeof Y.bucketCounts[J]==="number")W.bucketCounts[J]=Y.bucketCounts[J];else if(typeof Y.bucketCounts[J]==="object")W.bucketCounts[J]=new C1.LongBits(Y.bucketCounts[J].low>>>0,Y.bucketCounts[J].high>>>0).toNumber(!0)}return W},F.toObject=function I(Y,W){if(!W)W={};var J={};if(W.arrays||W.defaults)J.bucketCounts=[];if(W.defaults)J.offset=0;if(Y.offset!=null&&Y.hasOwnProperty("offset"))J.offset=Y.offset;if(Y.bucketCounts&&Y.bucketCounts.length){J.bucketCounts=[];for(var X=0;X<Y.bucketCounts.length;++X)if(typeof Y.bucketCounts[X]==="number")J.bucketCounts[X]=W.longs===String?String(Y.bucketCounts[X]):Y.bucketCounts[X];else J.bucketCounts[X]=W.longs===String?C1.Long.prototype.toString.call(Y.bucketCounts[X]):W.longs===Number?new C1.LongBits(Y.bucketCounts[X].low>>>0,Y.bucketCounts[X].high>>>0).toNumber(!0):Y.bucketCounts[X]}return J},F.prototype.toJSON=function I(){return this.constructor.toObject(this,c9.util.toJSONOptions)},F.getTypeUrl=function I(Y){if(Y===void 0)Y="type.googleapis.com";return Y+"/opentelemetry.proto.metrics.v1.ExponentialHistogramDataPoint.Buckets"},F}(),Z}(),D.SummaryDataPoint=function(){function Z(G){if(this.attributes=[],this.quantileValues=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.attributes=C1.emptyArray,Z.prototype.startTimeUnixNano=null,Z.prototype.timeUnixNano=null,Z.prototype.count=null,Z.prototype.sum=null,Z.prototype.quantileValues=C1.emptyArray,Z.prototype.flags=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.startTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"startTimeUnixNano"))I.uint32(17).fixed64(F.startTimeUnixNano);if(F.timeUnixNano!=null&&Object.hasOwnProperty.call(F,"timeUnixNano"))I.uint32(25).fixed64(F.timeUnixNano);if(F.count!=null&&Object.hasOwnProperty.call(F,"count"))I.uint32(33).fixed64(F.count);if(F.sum!=null&&Object.hasOwnProperty.call(F,"sum"))I.uint32(41).double(F.sum);if(F.quantileValues!=null&&F.quantileValues.length)for(var Y=0;Y<F.quantileValues.length;++Y)X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.encode(F.quantileValues[Y],I.uint32(50).fork()).ldelim();if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(58).fork()).ldelim();if(F.flags!=null&&Object.hasOwnProperty.call(F,"flags"))I.uint32(64).uint32(F.flags);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 7:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 2:{W.startTimeUnixNano=F.fixed64();break}case 3:{W.timeUnixNano=F.fixed64();break}case 4:{W.count=F.fixed64();break}case 5:{W.sum=F.double();break}case 6:{if(!(W.quantileValues&&W.quantileValues.length))W.quantileValues=[];W.quantileValues.push(X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.decode(F,F.uint32()));break}case 8:{W.flags=F.uint32();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var I=0;I<F.attributes.length;++I){var Y=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[I]);if(Y)return"attributes."+Y}}if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano")){if(!C1.isInteger(F.startTimeUnixNano)&&!(F.startTimeUnixNano&&C1.isInteger(F.startTimeUnixNano.low)&&C1.isInteger(F.startTimeUnixNano.high)))return"startTimeUnixNano: integer|Long expected"}if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano")){if(!C1.isInteger(F.timeUnixNano)&&!(F.timeUnixNano&&C1.isInteger(F.timeUnixNano.low)&&C1.isInteger(F.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(F.count!=null&&F.hasOwnProperty("count")){if(!C1.isInteger(F.count)&&!(F.count&&C1.isInteger(F.count.low)&&C1.isInteger(F.count.high)))return"count: integer|Long expected"}if(F.sum!=null&&F.hasOwnProperty("sum")){if(typeof F.sum!=="number")return"sum: number expected"}if(F.quantileValues!=null&&F.hasOwnProperty("quantileValues")){if(!Array.isArray(F.quantileValues))return"quantileValues: array expected";for(var I=0;I<F.quantileValues.length;++I){var Y=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.verify(F.quantileValues[I]);if(Y)return"quantileValues."+Y}}if(F.flags!=null&&F.hasOwnProperty("flags")){if(!C1.isInteger(F.flags))return"flags: integer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.metrics.v1.SummaryDataPoint)return F;var I=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint;if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.startTimeUnixNano!=null){if(C1.Long)(I.startTimeUnixNano=C1.Long.fromValue(F.startTimeUnixNano)).unsigned=!1;else if(typeof F.startTimeUnixNano==="string")I.startTimeUnixNano=parseInt(F.startTimeUnixNano,10);else if(typeof F.startTimeUnixNano==="number")I.startTimeUnixNano=F.startTimeUnixNano;else if(typeof F.startTimeUnixNano==="object")I.startTimeUnixNano=new C1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber()}if(F.timeUnixNano!=null){if(C1.Long)(I.timeUnixNano=C1.Long.fromValue(F.timeUnixNano)).unsigned=!1;else if(typeof F.timeUnixNano==="string")I.timeUnixNano=parseInt(F.timeUnixNano,10);else if(typeof F.timeUnixNano==="number")I.timeUnixNano=F.timeUnixNano;else if(typeof F.timeUnixNano==="object")I.timeUnixNano=new C1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber()}if(F.count!=null){if(C1.Long)(I.count=C1.Long.fromValue(F.count)).unsigned=!1;else if(typeof F.count==="string")I.count=parseInt(F.count,10);else if(typeof F.count==="number")I.count=F.count;else if(typeof F.count==="object")I.count=new C1.LongBits(F.count.low>>>0,F.count.high>>>0).toNumber()}if(F.sum!=null)I.sum=Number(F.sum);if(F.quantileValues){if(!Array.isArray(F.quantileValues))throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.quantileValues: array expected");I.quantileValues=[];for(var Y=0;Y<F.quantileValues.length;++Y){if(typeof F.quantileValues[Y]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.SummaryDataPoint.quantileValues: object expected");I.quantileValues[Y]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.fromObject(F.quantileValues[Y])}}if(F.flags!=null)I.flags=F.flags>>>0;return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.quantileValues=[],Y.attributes=[];if(I.defaults){if(C1.Long){var W=new C1.Long(0,0,!1);Y.startTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.startTimeUnixNano=I.longs===String?"0":0;if(C1.Long){var W=new C1.Long(0,0,!1);Y.timeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.timeUnixNano=I.longs===String?"0":0;if(C1.Long){var W=new C1.Long(0,0,!1);Y.count=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.count=I.longs===String?"0":0;Y.sum=0,Y.flags=0}if(F.startTimeUnixNano!=null&&F.hasOwnProperty("startTimeUnixNano"))if(typeof F.startTimeUnixNano==="number")Y.startTimeUnixNano=I.longs===String?String(F.startTimeUnixNano):F.startTimeUnixNano;else Y.startTimeUnixNano=I.longs===String?C1.Long.prototype.toString.call(F.startTimeUnixNano):I.longs===Number?new C1.LongBits(F.startTimeUnixNano.low>>>0,F.startTimeUnixNano.high>>>0).toNumber():F.startTimeUnixNano;if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano"))if(typeof F.timeUnixNano==="number")Y.timeUnixNano=I.longs===String?String(F.timeUnixNano):F.timeUnixNano;else Y.timeUnixNano=I.longs===String?C1.Long.prototype.toString.call(F.timeUnixNano):I.longs===Number?new C1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber():F.timeUnixNano;if(F.count!=null&&F.hasOwnProperty("count"))if(typeof F.count==="number")Y.count=I.longs===String?String(F.count):F.count;else Y.count=I.longs===String?C1.Long.prototype.toString.call(F.count):I.longs===Number?new C1.LongBits(F.count.low>>>0,F.count.high>>>0).toNumber():F.count;if(F.sum!=null&&F.hasOwnProperty("sum"))Y.sum=I.json&&!isFinite(F.sum)?String(F.sum):F.sum;if(F.quantileValues&&F.quantileValues.length){Y.quantileValues=[];for(var J=0;J<F.quantileValues.length;++J)Y.quantileValues[J]=X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile.toObject(F.quantileValues[J],I)}if(F.attributes&&F.attributes.length){Y.attributes=[];for(var J=0;J<F.attributes.length;++J)Y.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[J],I)}if(F.flags!=null&&F.hasOwnProperty("flags"))Y.flags=F.flags;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.metrics.v1.SummaryDataPoint"},Z.ValueAtQuantile=function(){function G(F){if(F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}return G.prototype.quantile=null,G.prototype.value=null,G.create=function F(I){return new G(I)},G.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.quantile!=null&&Object.hasOwnProperty.call(I,"quantile"))Y.uint32(9).double(I.quantile);if(I.value!=null&&Object.hasOwnProperty.call(I,"value"))Y.uint32(17).double(I.value);return Y},G.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},G.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 1:{J.quantile=I.double();break}case 2:{J.value=I.double();break}default:I.skipType(X&7);break}}return J},G.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},G.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";if(I.quantile!=null&&I.hasOwnProperty("quantile")){if(typeof I.quantile!=="number")return"quantile: number expected"}if(I.value!=null&&I.hasOwnProperty("value")){if(typeof I.value!=="number")return"value: number expected"}return null},G.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile)return I;var Y=new X1.opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile;if(I.quantile!=null)Y.quantile=Number(I.quantile);if(I.value!=null)Y.value=Number(I.value);return Y},G.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.defaults)W.quantile=0,W.value=0;if(I.quantile!=null&&I.hasOwnProperty("quantile"))W.quantile=Y.json&&!isFinite(I.quantile)?String(I.quantile):I.quantile;if(I.value!=null&&I.hasOwnProperty("value"))W.value=Y.json&&!isFinite(I.value)?String(I.value):I.value;return W},G.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},G.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.SummaryDataPoint.ValueAtQuantile"},G}(),Z}(),D.Exemplar=function(){function Z(F){if(this.filteredAttributes=[],F){for(var I=Object.keys(F),Y=0;Y<I.length;++Y)if(F[I[Y]]!=null)this[I[Y]]=F[I[Y]]}}Z.prototype.filteredAttributes=C1.emptyArray,Z.prototype.timeUnixNano=null,Z.prototype.asDouble=null,Z.prototype.asInt=null,Z.prototype.spanId=null,Z.prototype.traceId=null;var G;return Object.defineProperty(Z.prototype,"value",{get:C1.oneOfGetter(G=["asDouble","asInt"]),set:C1.oneOfSetter(G)}),Z.create=function F(I){return new Z(I)},Z.encode=function F(I,Y){if(!Y)Y=Z4.create();if(I.timeUnixNano!=null&&Object.hasOwnProperty.call(I,"timeUnixNano"))Y.uint32(17).fixed64(I.timeUnixNano);if(I.asDouble!=null&&Object.hasOwnProperty.call(I,"asDouble"))Y.uint32(25).double(I.asDouble);if(I.spanId!=null&&Object.hasOwnProperty.call(I,"spanId"))Y.uint32(34).bytes(I.spanId);if(I.traceId!=null&&Object.hasOwnProperty.call(I,"traceId"))Y.uint32(42).bytes(I.traceId);if(I.asInt!=null&&Object.hasOwnProperty.call(I,"asInt"))Y.uint32(49).sfixed64(I.asInt);if(I.filteredAttributes!=null&&I.filteredAttributes.length)for(var W=0;W<I.filteredAttributes.length;++W)X1.opentelemetry.proto.common.v1.KeyValue.encode(I.filteredAttributes[W],Y.uint32(58).fork()).ldelim();return Y},Z.encodeDelimited=function F(I,Y){return this.encode(I,Y).ldelim()},Z.decode=function F(I,Y){if(!(I instanceof d0))I=d0.create(I);var W=Y===void 0?I.len:I.pos+Y,J=new X1.opentelemetry.proto.metrics.v1.Exemplar;while(I.pos<W){var X=I.uint32();switch(X>>>3){case 7:{if(!(J.filteredAttributes&&J.filteredAttributes.length))J.filteredAttributes=[];J.filteredAttributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(I,I.uint32()));break}case 2:{J.timeUnixNano=I.fixed64();break}case 3:{J.asDouble=I.double();break}case 6:{J.asInt=I.sfixed64();break}case 4:{J.spanId=I.bytes();break}case 5:{J.traceId=I.bytes();break}default:I.skipType(X&7);break}}return J},Z.decodeDelimited=function F(I){if(!(I instanceof d0))I=new d0(I);return this.decode(I,I.uint32())},Z.verify=function F(I){if(typeof I!=="object"||I===null)return"object expected";var Y={};if(I.filteredAttributes!=null&&I.hasOwnProperty("filteredAttributes")){if(!Array.isArray(I.filteredAttributes))return"filteredAttributes: array expected";for(var W=0;W<I.filteredAttributes.length;++W){var J=X1.opentelemetry.proto.common.v1.KeyValue.verify(I.filteredAttributes[W]);if(J)return"filteredAttributes."+J}}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano")){if(!C1.isInteger(I.timeUnixNano)&&!(I.timeUnixNano&&C1.isInteger(I.timeUnixNano.low)&&C1.isInteger(I.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(Y.value=1,typeof I.asDouble!=="number")return"asDouble: number expected"}if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(Y.value===1)return"value: multiple values";if(Y.value=1,!C1.isInteger(I.asInt)&&!(I.asInt&&C1.isInteger(I.asInt.low)&&C1.isInteger(I.asInt.high)))return"asInt: integer|Long expected"}if(I.spanId!=null&&I.hasOwnProperty("spanId")){if(!(I.spanId&&typeof I.spanId.length==="number"||C1.isString(I.spanId)))return"spanId: buffer expected"}if(I.traceId!=null&&I.hasOwnProperty("traceId")){if(!(I.traceId&&typeof I.traceId.length==="number"||C1.isString(I.traceId)))return"traceId: buffer expected"}return null},Z.fromObject=function F(I){if(I instanceof X1.opentelemetry.proto.metrics.v1.Exemplar)return I;var Y=new X1.opentelemetry.proto.metrics.v1.Exemplar;if(I.filteredAttributes){if(!Array.isArray(I.filteredAttributes))throw TypeError(".opentelemetry.proto.metrics.v1.Exemplar.filteredAttributes: array expected");Y.filteredAttributes=[];for(var W=0;W<I.filteredAttributes.length;++W){if(typeof I.filteredAttributes[W]!=="object")throw TypeError(".opentelemetry.proto.metrics.v1.Exemplar.filteredAttributes: object expected");Y.filteredAttributes[W]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(I.filteredAttributes[W])}}if(I.timeUnixNano!=null){if(C1.Long)(Y.timeUnixNano=C1.Long.fromValue(I.timeUnixNano)).unsigned=!1;else if(typeof I.timeUnixNano==="string")Y.timeUnixNano=parseInt(I.timeUnixNano,10);else if(typeof I.timeUnixNano==="number")Y.timeUnixNano=I.timeUnixNano;else if(typeof I.timeUnixNano==="object")Y.timeUnixNano=new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber()}if(I.asDouble!=null)Y.asDouble=Number(I.asDouble);if(I.asInt!=null){if(C1.Long)(Y.asInt=C1.Long.fromValue(I.asInt)).unsigned=!1;else if(typeof I.asInt==="string")Y.asInt=parseInt(I.asInt,10);else if(typeof I.asInt==="number")Y.asInt=I.asInt;else if(typeof I.asInt==="object")Y.asInt=new C1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber()}if(I.spanId!=null){if(typeof I.spanId==="string")C1.base64.decode(I.spanId,Y.spanId=C1.newBuffer(C1.base64.length(I.spanId)),0);else if(I.spanId.length>=0)Y.spanId=I.spanId}if(I.traceId!=null){if(typeof I.traceId==="string")C1.base64.decode(I.traceId,Y.traceId=C1.newBuffer(C1.base64.length(I.traceId)),0);else if(I.traceId.length>=0)Y.traceId=I.traceId}return Y},Z.toObject=function F(I,Y){if(!Y)Y={};var W={};if(Y.arrays||Y.defaults)W.filteredAttributes=[];if(Y.defaults){if(C1.Long){var J=new C1.Long(0,0,!1);W.timeUnixNano=Y.longs===String?J.toString():Y.longs===Number?J.toNumber():J}else W.timeUnixNano=Y.longs===String?"0":0;if(Y.bytes===String)W.spanId="";else if(W.spanId=[],Y.bytes!==Array)W.spanId=C1.newBuffer(W.spanId);if(Y.bytes===String)W.traceId="";else if(W.traceId=[],Y.bytes!==Array)W.traceId=C1.newBuffer(W.traceId)}if(I.timeUnixNano!=null&&I.hasOwnProperty("timeUnixNano"))if(typeof I.timeUnixNano==="number")W.timeUnixNano=Y.longs===String?String(I.timeUnixNano):I.timeUnixNano;else W.timeUnixNano=Y.longs===String?C1.Long.prototype.toString.call(I.timeUnixNano):Y.longs===Number?new C1.LongBits(I.timeUnixNano.low>>>0,I.timeUnixNano.high>>>0).toNumber():I.timeUnixNano;if(I.asDouble!=null&&I.hasOwnProperty("asDouble")){if(W.asDouble=Y.json&&!isFinite(I.asDouble)?String(I.asDouble):I.asDouble,Y.oneofs)W.value="asDouble"}if(I.spanId!=null&&I.hasOwnProperty("spanId"))W.spanId=Y.bytes===String?C1.base64.encode(I.spanId,0,I.spanId.length):Y.bytes===Array?Array.prototype.slice.call(I.spanId):I.spanId;if(I.traceId!=null&&I.hasOwnProperty("traceId"))W.traceId=Y.bytes===String?C1.base64.encode(I.traceId,0,I.traceId.length):Y.bytes===Array?Array.prototype.slice.call(I.traceId):I.traceId;if(I.asInt!=null&&I.hasOwnProperty("asInt")){if(typeof I.asInt==="number")W.asInt=Y.longs===String?String(I.asInt):I.asInt;else W.asInt=Y.longs===String?C1.Long.prototype.toString.call(I.asInt):Y.longs===Number?new C1.LongBits(I.asInt.low>>>0,I.asInt.high>>>0).toNumber():I.asInt;if(Y.oneofs)W.value="asInt"}if(I.filteredAttributes&&I.filteredAttributes.length){W.filteredAttributes=[];for(var X=0;X<I.filteredAttributes.length;++X)W.filteredAttributes[X]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(I.filteredAttributes[X],Y)}return W},Z.prototype.toJSON=function F(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function F(I){if(I===void 0)I="type.googleapis.com";return I+"/opentelemetry.proto.metrics.v1.Exemplar"},Z}(),D}(),Q}(),B.logs=function(){var Q={};return Q.v1=function(){var D={};return D.LogsData=function(){function Z(G){if(this.resourceLogs=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resourceLogs=C1.emptyArray,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.resourceLogs!=null&&F.resourceLogs.length)for(var Y=0;Y<F.resourceLogs.length;++Y)X1.opentelemetry.proto.logs.v1.ResourceLogs.encode(F.resourceLogs[Y],I.uint32(10).fork()).ldelim();return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.LogsData;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{if(!(W.resourceLogs&&W.resourceLogs.length))W.resourceLogs=[];W.resourceLogs.push(X1.opentelemetry.proto.logs.v1.ResourceLogs.decode(F,F.uint32()));break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resourceLogs!=null&&F.hasOwnProperty("resourceLogs")){if(!Array.isArray(F.resourceLogs))return"resourceLogs: array expected";for(var I=0;I<F.resourceLogs.length;++I){var Y=X1.opentelemetry.proto.logs.v1.ResourceLogs.verify(F.resourceLogs[I]);if(Y)return"resourceLogs."+Y}}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.LogsData)return F;var I=new X1.opentelemetry.proto.logs.v1.LogsData;if(F.resourceLogs){if(!Array.isArray(F.resourceLogs))throw TypeError(".opentelemetry.proto.logs.v1.LogsData.resourceLogs: array expected");I.resourceLogs=[];for(var Y=0;Y<F.resourceLogs.length;++Y){if(typeof F.resourceLogs[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.LogsData.resourceLogs: object expected");I.resourceLogs[Y]=X1.opentelemetry.proto.logs.v1.ResourceLogs.fromObject(F.resourceLogs[Y])}}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.resourceLogs=[];if(F.resourceLogs&&F.resourceLogs.length){Y.resourceLogs=[];for(var W=0;W<F.resourceLogs.length;++W)Y.resourceLogs[W]=X1.opentelemetry.proto.logs.v1.ResourceLogs.toObject(F.resourceLogs[W],I)}return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.LogsData"},Z}(),D.ResourceLogs=function(){function Z(G){if(this.scopeLogs=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.resource=null,Z.prototype.scopeLogs=C1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.resource!=null&&Object.hasOwnProperty.call(F,"resource"))X1.opentelemetry.proto.resource.v1.Resource.encode(F.resource,I.uint32(10).fork()).ldelim();if(F.scopeLogs!=null&&F.scopeLogs.length)for(var Y=0;Y<F.scopeLogs.length;++Y)X1.opentelemetry.proto.logs.v1.ScopeLogs.encode(F.scopeLogs[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.ResourceLogs;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.resource=X1.opentelemetry.proto.resource.v1.Resource.decode(F,F.uint32());break}case 2:{if(!(W.scopeLogs&&W.scopeLogs.length))W.scopeLogs=[];W.scopeLogs.push(X1.opentelemetry.proto.logs.v1.ScopeLogs.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.resource!=null&&F.hasOwnProperty("resource")){var I=X1.opentelemetry.proto.resource.v1.Resource.verify(F.resource);if(I)return"resource."+I}if(F.scopeLogs!=null&&F.hasOwnProperty("scopeLogs")){if(!Array.isArray(F.scopeLogs))return"scopeLogs: array expected";for(var Y=0;Y<F.scopeLogs.length;++Y){var I=X1.opentelemetry.proto.logs.v1.ScopeLogs.verify(F.scopeLogs[Y]);if(I)return"scopeLogs."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!C1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.ResourceLogs)return F;var I=new X1.opentelemetry.proto.logs.v1.ResourceLogs;if(F.resource!=null){if(typeof F.resource!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.resource: object expected");I.resource=X1.opentelemetry.proto.resource.v1.Resource.fromObject(F.resource)}if(F.scopeLogs){if(!Array.isArray(F.scopeLogs))throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.scopeLogs: array expected");I.scopeLogs=[];for(var Y=0;Y<F.scopeLogs.length;++Y){if(typeof F.scopeLogs[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ResourceLogs.scopeLogs: object expected");I.scopeLogs[Y]=X1.opentelemetry.proto.logs.v1.ScopeLogs.fromObject(F.scopeLogs[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.scopeLogs=[];if(I.defaults)Y.resource=null,Y.schemaUrl="";if(F.resource!=null&&F.hasOwnProperty("resource"))Y.resource=X1.opentelemetry.proto.resource.v1.Resource.toObject(F.resource,I);if(F.scopeLogs&&F.scopeLogs.length){Y.scopeLogs=[];for(var W=0;W<F.scopeLogs.length;++W)Y.scopeLogs[W]=X1.opentelemetry.proto.logs.v1.ScopeLogs.toObject(F.scopeLogs[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.ResourceLogs"},Z}(),D.ScopeLogs=function(){function Z(G){if(this.logRecords=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.scope=null,Z.prototype.logRecords=C1.emptyArray,Z.prototype.schemaUrl=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.scope!=null&&Object.hasOwnProperty.call(F,"scope"))X1.opentelemetry.proto.common.v1.InstrumentationScope.encode(F.scope,I.uint32(10).fork()).ldelim();if(F.logRecords!=null&&F.logRecords.length)for(var Y=0;Y<F.logRecords.length;++Y)X1.opentelemetry.proto.logs.v1.LogRecord.encode(F.logRecords[Y],I.uint32(18).fork()).ldelim();if(F.schemaUrl!=null&&Object.hasOwnProperty.call(F,"schemaUrl"))I.uint32(26).string(F.schemaUrl);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.ScopeLogs;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.decode(F,F.uint32());break}case 2:{if(!(W.logRecords&&W.logRecords.length))W.logRecords=[];W.logRecords.push(X1.opentelemetry.proto.logs.v1.LogRecord.decode(F,F.uint32()));break}case 3:{W.schemaUrl=F.string();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.scope!=null&&F.hasOwnProperty("scope")){var I=X1.opentelemetry.proto.common.v1.InstrumentationScope.verify(F.scope);if(I)return"scope."+I}if(F.logRecords!=null&&F.hasOwnProperty("logRecords")){if(!Array.isArray(F.logRecords))return"logRecords: array expected";for(var Y=0;Y<F.logRecords.length;++Y){var I=X1.opentelemetry.proto.logs.v1.LogRecord.verify(F.logRecords[Y]);if(I)return"logRecords."+I}}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl")){if(!C1.isString(F.schemaUrl))return"schemaUrl: string expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.ScopeLogs)return F;var I=new X1.opentelemetry.proto.logs.v1.ScopeLogs;if(F.scope!=null){if(typeof F.scope!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.scope: object expected");I.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.fromObject(F.scope)}if(F.logRecords){if(!Array.isArray(F.logRecords))throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.logRecords: array expected");I.logRecords=[];for(var Y=0;Y<F.logRecords.length;++Y){if(typeof F.logRecords[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.ScopeLogs.logRecords: object expected");I.logRecords[Y]=X1.opentelemetry.proto.logs.v1.LogRecord.fromObject(F.logRecords[Y])}}if(F.schemaUrl!=null)I.schemaUrl=String(F.schemaUrl);return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.logRecords=[];if(I.defaults)Y.scope=null,Y.schemaUrl="";if(F.scope!=null&&F.hasOwnProperty("scope"))Y.scope=X1.opentelemetry.proto.common.v1.InstrumentationScope.toObject(F.scope,I);if(F.logRecords&&F.logRecords.length){Y.logRecords=[];for(var W=0;W<F.logRecords.length;++W)Y.logRecords[W]=X1.opentelemetry.proto.logs.v1.LogRecord.toObject(F.logRecords[W],I)}if(F.schemaUrl!=null&&F.hasOwnProperty("schemaUrl"))Y.schemaUrl=F.schemaUrl;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.ScopeLogs"},Z}(),D.SeverityNumber=function(){var Z={},G=Object.create(Z);return G[Z[0]="SEVERITY_NUMBER_UNSPECIFIED"]=0,G[Z[1]="SEVERITY_NUMBER_TRACE"]=1,G[Z[2]="SEVERITY_NUMBER_TRACE2"]=2,G[Z[3]="SEVERITY_NUMBER_TRACE3"]=3,G[Z[4]="SEVERITY_NUMBER_TRACE4"]=4,G[Z[5]="SEVERITY_NUMBER_DEBUG"]=5,G[Z[6]="SEVERITY_NUMBER_DEBUG2"]=6,G[Z[7]="SEVERITY_NUMBER_DEBUG3"]=7,G[Z[8]="SEVERITY_NUMBER_DEBUG4"]=8,G[Z[9]="SEVERITY_NUMBER_INFO"]=9,G[Z[10]="SEVERITY_NUMBER_INFO2"]=10,G[Z[11]="SEVERITY_NUMBER_INFO3"]=11,G[Z[12]="SEVERITY_NUMBER_INFO4"]=12,G[Z[13]="SEVERITY_NUMBER_WARN"]=13,G[Z[14]="SEVERITY_NUMBER_WARN2"]=14,G[Z[15]="SEVERITY_NUMBER_WARN3"]=15,G[Z[16]="SEVERITY_NUMBER_WARN4"]=16,G[Z[17]="SEVERITY_NUMBER_ERROR"]=17,G[Z[18]="SEVERITY_NUMBER_ERROR2"]=18,G[Z[19]="SEVERITY_NUMBER_ERROR3"]=19,G[Z[20]="SEVERITY_NUMBER_ERROR4"]=20,G[Z[21]="SEVERITY_NUMBER_FATAL"]=21,G[Z[22]="SEVERITY_NUMBER_FATAL2"]=22,G[Z[23]="SEVERITY_NUMBER_FATAL3"]=23,G[Z[24]="SEVERITY_NUMBER_FATAL4"]=24,G}(),D.LogRecordFlags=function(){var Z={},G=Object.create(Z);return G[Z[0]="LOG_RECORD_FLAGS_DO_NOT_USE"]=0,G[Z[255]="LOG_RECORD_FLAGS_TRACE_FLAGS_MASK"]=255,G}(),D.LogRecord=function(){function Z(G){if(this.attributes=[],G){for(var F=Object.keys(G),I=0;I<F.length;++I)if(G[F[I]]!=null)this[F[I]]=G[F[I]]}}return Z.prototype.timeUnixNano=null,Z.prototype.observedTimeUnixNano=null,Z.prototype.severityNumber=null,Z.prototype.severityText=null,Z.prototype.body=null,Z.prototype.attributes=C1.emptyArray,Z.prototype.droppedAttributesCount=null,Z.prototype.flags=null,Z.prototype.traceId=null,Z.prototype.spanId=null,Z.create=function G(F){return new Z(F)},Z.encode=function G(F,I){if(!I)I=Z4.create();if(F.timeUnixNano!=null&&Object.hasOwnProperty.call(F,"timeUnixNano"))I.uint32(9).fixed64(F.timeUnixNano);if(F.severityNumber!=null&&Object.hasOwnProperty.call(F,"severityNumber"))I.uint32(16).int32(F.severityNumber);if(F.severityText!=null&&Object.hasOwnProperty.call(F,"severityText"))I.uint32(26).string(F.severityText);if(F.body!=null&&Object.hasOwnProperty.call(F,"body"))X1.opentelemetry.proto.common.v1.AnyValue.encode(F.body,I.uint32(42).fork()).ldelim();if(F.attributes!=null&&F.attributes.length)for(var Y=0;Y<F.attributes.length;++Y)X1.opentelemetry.proto.common.v1.KeyValue.encode(F.attributes[Y],I.uint32(50).fork()).ldelim();if(F.droppedAttributesCount!=null&&Object.hasOwnProperty.call(F,"droppedAttributesCount"))I.uint32(56).uint32(F.droppedAttributesCount);if(F.flags!=null&&Object.hasOwnProperty.call(F,"flags"))I.uint32(69).fixed32(F.flags);if(F.traceId!=null&&Object.hasOwnProperty.call(F,"traceId"))I.uint32(74).bytes(F.traceId);if(F.spanId!=null&&Object.hasOwnProperty.call(F,"spanId"))I.uint32(82).bytes(F.spanId);if(F.observedTimeUnixNano!=null&&Object.hasOwnProperty.call(F,"observedTimeUnixNano"))I.uint32(89).fixed64(F.observedTimeUnixNano);return I},Z.encodeDelimited=function G(F,I){return this.encode(F,I).ldelim()},Z.decode=function G(F,I){if(!(F instanceof d0))F=d0.create(F);var Y=I===void 0?F.len:F.pos+I,W=new X1.opentelemetry.proto.logs.v1.LogRecord;while(F.pos<Y){var J=F.uint32();switch(J>>>3){case 1:{W.timeUnixNano=F.fixed64();break}case 11:{W.observedTimeUnixNano=F.fixed64();break}case 2:{W.severityNumber=F.int32();break}case 3:{W.severityText=F.string();break}case 5:{W.body=X1.opentelemetry.proto.common.v1.AnyValue.decode(F,F.uint32());break}case 6:{if(!(W.attributes&&W.attributes.length))W.attributes=[];W.attributes.push(X1.opentelemetry.proto.common.v1.KeyValue.decode(F,F.uint32()));break}case 7:{W.droppedAttributesCount=F.uint32();break}case 8:{W.flags=F.fixed32();break}case 9:{W.traceId=F.bytes();break}case 10:{W.spanId=F.bytes();break}default:F.skipType(J&7);break}}return W},Z.decodeDelimited=function G(F){if(!(F instanceof d0))F=new d0(F);return this.decode(F,F.uint32())},Z.verify=function G(F){if(typeof F!=="object"||F===null)return"object expected";if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano")){if(!C1.isInteger(F.timeUnixNano)&&!(F.timeUnixNano&&C1.isInteger(F.timeUnixNano.low)&&C1.isInteger(F.timeUnixNano.high)))return"timeUnixNano: integer|Long expected"}if(F.observedTimeUnixNano!=null&&F.hasOwnProperty("observedTimeUnixNano")){if(!C1.isInteger(F.observedTimeUnixNano)&&!(F.observedTimeUnixNano&&C1.isInteger(F.observedTimeUnixNano.low)&&C1.isInteger(F.observedTimeUnixNano.high)))return"observedTimeUnixNano: integer|Long expected"}if(F.severityNumber!=null&&F.hasOwnProperty("severityNumber"))switch(F.severityNumber){default:return"severityNumber: enum value expected";case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 12:case 13:case 14:case 15:case 16:case 17:case 18:case 19:case 20:case 21:case 22:case 23:case 24:break}if(F.severityText!=null&&F.hasOwnProperty("severityText")){if(!C1.isString(F.severityText))return"severityText: string expected"}if(F.body!=null&&F.hasOwnProperty("body")){var I=X1.opentelemetry.proto.common.v1.AnyValue.verify(F.body);if(I)return"body."+I}if(F.attributes!=null&&F.hasOwnProperty("attributes")){if(!Array.isArray(F.attributes))return"attributes: array expected";for(var Y=0;Y<F.attributes.length;++Y){var I=X1.opentelemetry.proto.common.v1.KeyValue.verify(F.attributes[Y]);if(I)return"attributes."+I}}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount")){if(!C1.isInteger(F.droppedAttributesCount))return"droppedAttributesCount: integer expected"}if(F.flags!=null&&F.hasOwnProperty("flags")){if(!C1.isInteger(F.flags))return"flags: integer expected"}if(F.traceId!=null&&F.hasOwnProperty("traceId")){if(!(F.traceId&&typeof F.traceId.length==="number"||C1.isString(F.traceId)))return"traceId: buffer expected"}if(F.spanId!=null&&F.hasOwnProperty("spanId")){if(!(F.spanId&&typeof F.spanId.length==="number"||C1.isString(F.spanId)))return"spanId: buffer expected"}return null},Z.fromObject=function G(F){if(F instanceof X1.opentelemetry.proto.logs.v1.LogRecord)return F;var I=new X1.opentelemetry.proto.logs.v1.LogRecord;if(F.timeUnixNano!=null){if(C1.Long)(I.timeUnixNano=C1.Long.fromValue(F.timeUnixNano)).unsigned=!1;else if(typeof F.timeUnixNano==="string")I.timeUnixNano=parseInt(F.timeUnixNano,10);else if(typeof F.timeUnixNano==="number")I.timeUnixNano=F.timeUnixNano;else if(typeof F.timeUnixNano==="object")I.timeUnixNano=new C1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber()}if(F.observedTimeUnixNano!=null){if(C1.Long)(I.observedTimeUnixNano=C1.Long.fromValue(F.observedTimeUnixNano)).unsigned=!1;else if(typeof F.observedTimeUnixNano==="string")I.observedTimeUnixNano=parseInt(F.observedTimeUnixNano,10);else if(typeof F.observedTimeUnixNano==="number")I.observedTimeUnixNano=F.observedTimeUnixNano;else if(typeof F.observedTimeUnixNano==="object")I.observedTimeUnixNano=new C1.LongBits(F.observedTimeUnixNano.low>>>0,F.observedTimeUnixNano.high>>>0).toNumber()}switch(F.severityNumber){default:if(typeof F.severityNumber==="number"){I.severityNumber=F.severityNumber;break}break;case"SEVERITY_NUMBER_UNSPECIFIED":case 0:I.severityNumber=0;break;case"SEVERITY_NUMBER_TRACE":case 1:I.severityNumber=1;break;case"SEVERITY_NUMBER_TRACE2":case 2:I.severityNumber=2;break;case"SEVERITY_NUMBER_TRACE3":case 3:I.severityNumber=3;break;case"SEVERITY_NUMBER_TRACE4":case 4:I.severityNumber=4;break;case"SEVERITY_NUMBER_DEBUG":case 5:I.severityNumber=5;break;case"SEVERITY_NUMBER_DEBUG2":case 6:I.severityNumber=6;break;case"SEVERITY_NUMBER_DEBUG3":case 7:I.severityNumber=7;break;case"SEVERITY_NUMBER_DEBUG4":case 8:I.severityNumber=8;break;case"SEVERITY_NUMBER_INFO":case 9:I.severityNumber=9;break;case"SEVERITY_NUMBER_INFO2":case 10:I.severityNumber=10;break;case"SEVERITY_NUMBER_INFO3":case 11:I.severityNumber=11;break;case"SEVERITY_NUMBER_INFO4":case 12:I.severityNumber=12;break;case"SEVERITY_NUMBER_WARN":case 13:I.severityNumber=13;break;case"SEVERITY_NUMBER_WARN2":case 14:I.severityNumber=14;break;case"SEVERITY_NUMBER_WARN3":case 15:I.severityNumber=15;break;case"SEVERITY_NUMBER_WARN4":case 16:I.severityNumber=16;break;case"SEVERITY_NUMBER_ERROR":case 17:I.severityNumber=17;break;case"SEVERITY_NUMBER_ERROR2":case 18:I.severityNumber=18;break;case"SEVERITY_NUMBER_ERROR3":case 19:I.severityNumber=19;break;case"SEVERITY_NUMBER_ERROR4":case 20:I.severityNumber=20;break;case"SEVERITY_NUMBER_FATAL":case 21:I.severityNumber=21;break;case"SEVERITY_NUMBER_FATAL2":case 22:I.severityNumber=22;break;case"SEVERITY_NUMBER_FATAL3":case 23:I.severityNumber=23;break;case"SEVERITY_NUMBER_FATAL4":case 24:I.severityNumber=24;break}if(F.severityText!=null)I.severityText=String(F.severityText);if(F.body!=null){if(typeof F.body!=="object")throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.body: object expected");I.body=X1.opentelemetry.proto.common.v1.AnyValue.fromObject(F.body)}if(F.attributes){if(!Array.isArray(F.attributes))throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.attributes: array expected");I.attributes=[];for(var Y=0;Y<F.attributes.length;++Y){if(typeof F.attributes[Y]!=="object")throw TypeError(".opentelemetry.proto.logs.v1.LogRecord.attributes: object expected");I.attributes[Y]=X1.opentelemetry.proto.common.v1.KeyValue.fromObject(F.attributes[Y])}}if(F.droppedAttributesCount!=null)I.droppedAttributesCount=F.droppedAttributesCount>>>0;if(F.flags!=null)I.flags=F.flags>>>0;if(F.traceId!=null){if(typeof F.traceId==="string")C1.base64.decode(F.traceId,I.traceId=C1.newBuffer(C1.base64.length(F.traceId)),0);else if(F.traceId.length>=0)I.traceId=F.traceId}if(F.spanId!=null){if(typeof F.spanId==="string")C1.base64.decode(F.spanId,I.spanId=C1.newBuffer(C1.base64.length(F.spanId)),0);else if(F.spanId.length>=0)I.spanId=F.spanId}return I},Z.toObject=function G(F,I){if(!I)I={};var Y={};if(I.arrays||I.defaults)Y.attributes=[];if(I.defaults){if(C1.Long){var W=new C1.Long(0,0,!1);Y.timeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.timeUnixNano=I.longs===String?"0":0;if(Y.severityNumber=I.enums===String?"SEVERITY_NUMBER_UNSPECIFIED":0,Y.severityText="",Y.body=null,Y.droppedAttributesCount=0,Y.flags=0,I.bytes===String)Y.traceId="";else if(Y.traceId=[],I.bytes!==Array)Y.traceId=C1.newBuffer(Y.traceId);if(I.bytes===String)Y.spanId="";else if(Y.spanId=[],I.bytes!==Array)Y.spanId=C1.newBuffer(Y.spanId);if(C1.Long){var W=new C1.Long(0,0,!1);Y.observedTimeUnixNano=I.longs===String?W.toString():I.longs===Number?W.toNumber():W}else Y.observedTimeUnixNano=I.longs===String?"0":0}if(F.timeUnixNano!=null&&F.hasOwnProperty("timeUnixNano"))if(typeof F.timeUnixNano==="number")Y.timeUnixNano=I.longs===String?String(F.timeUnixNano):F.timeUnixNano;else Y.timeUnixNano=I.longs===String?C1.Long.prototype.toString.call(F.timeUnixNano):I.longs===Number?new C1.LongBits(F.timeUnixNano.low>>>0,F.timeUnixNano.high>>>0).toNumber():F.timeUnixNano;if(F.severityNumber!=null&&F.hasOwnProperty("severityNumber"))Y.severityNumber=I.enums===String?X1.opentelemetry.proto.logs.v1.SeverityNumber[F.severityNumber]===void 0?F.severityNumber:X1.opentelemetry.proto.logs.v1.SeverityNumber[F.severityNumber]:F.severityNumber;if(F.severityText!=null&&F.hasOwnProperty("severityText"))Y.severityText=F.severityText;if(F.body!=null&&F.hasOwnProperty("body"))Y.body=X1.opentelemetry.proto.common.v1.AnyValue.toObject(F.body,I);if(F.attributes&&F.attributes.length){Y.attributes=[];for(var J=0;J<F.attributes.length;++J)Y.attributes[J]=X1.opentelemetry.proto.common.v1.KeyValue.toObject(F.attributes[J],I)}if(F.droppedAttributesCount!=null&&F.hasOwnProperty("droppedAttributesCount"))Y.droppedAttributesCount=F.droppedAttributesCount;if(F.flags!=null&&F.hasOwnProperty("flags"))Y.flags=F.flags;if(F.traceId!=null&&F.hasOwnProperty("traceId"))Y.traceId=I.bytes===String?C1.base64.encode(F.traceId,0,F.traceId.length):I.bytes===Array?Array.prototype.slice.call(F.traceId):F.traceId;if(F.spanId!=null&&F.hasOwnProperty("spanId"))Y.spanId=I.bytes===String?C1.base64.encode(F.spanId,0,F.spanId.length):I.bytes===Array?Array.prototype.slice.call(F.spanId):F.spanId;if(F.observedTimeUnixNano!=null&&F.hasOwnProperty("observedTimeUnixNano"))if(typeof F.observedTimeUnixNano==="number")Y.observedTimeUnixNano=I.longs===String?String(F.observedTimeUnixNano):F.observedTimeUnixNano;else Y.observedTimeUnixNano=I.longs===String?C1.Long.prototype.toString.call(F.observedTimeUnixNano):I.longs===Number?new C1.LongBits(F.observedTimeUnixNano.low>>>0,F.observedTimeUnixNano.high>>>0).toNumber():F.observedTimeUnixNano;return Y},Z.prototype.toJSON=function G(){return this.constructor.toObject(this,c9.util.toJSONOptions)},Z.getTypeUrl=function G(F){if(F===void 0)F="type.googleapis.com";return F+"/opentelemetry.proto.logs.v1.LogRecord"},Z}(),D}(),Q}(),B}(),A}();xd2.exports=X1});
var Bc2=E((mI0)=>{Object.defineProperty(mI0,"__esModule",{value:!0});mI0.ProtobufLogsSerializer=void 0;var GD6=Ac2();Object.defineProperty(mI0,"ProtobufLogsSerializer",{enumerable:!0,get:function(){return GD6.ProtobufLogsSerializer}})});
var Bd2=E((sk5,Ad2)=>{Ad2.exports=sm2(sm2);function sm2(A){if(typeof Float32Array!=="undefined")(function(){var B=new Float32Array([-0]),Q=new Uint8Array(B.buffer),D=Q[3]===128;function Z(Y,W,J){B[0]=Y,W[J]=Q[0],W[J+1]=Q[1],W[J+2]=Q[2],W[J+3]=Q[3]}function G(Y,W,J){B[0]=Y,W[J]=Q[3],W[J+1]=Q[2],W[J+2]=Q[1],W[J+3]=Q[0]}A.writeFloatLE=D?Z:G,A.writeFloatBE=D?G:Z;function F(Y,W){return Q[0]=Y[W],Q[1]=Y[W+1],Q[2]=Y[W+2],Q[3]=Y[W+3],B[0]}function I(Y,W){return Q[3]=Y[W],Q[2]=Y[W+1],Q[1]=Y[W+2],Q[0]=Y[W+3],B[0]}A.readFloatLE=D?F:I,A.readFloatBE=D?I:F})();else(function(){function B(D,Z,G,F){var I=Z<0?1:0;if(I)Z=-Z;if(Z===0)D(1/Z>0?0:2147483648,G,F);else if(isNaN(Z))D(2143289344,G,F);else if(Z>340282346638528860000000000000000000000)D((I<<31|2139095040)>>>0,G,F);else if(Z<0.000000000000000000000000000000000000011754943508222875)D((I<<31|Math.round(Z/0.000000000000000000000000000000000000000000001401298464324817))>>>0,G,F);else{var Y=Math.floor(Math.log(Z)/Math.LN2),W=Math.round(Z*Math.pow(2,-Y)*8388608)&8388607;D((I<<31|Y+127<<23|W)>>>0,G,F)}}A.writeFloatLE=B.bind(null,rm2),A.writeFloatBE=B.bind(null,om2);function Q(D,Z,G){var F=D(Z,G),I=(F>>31)*2+1,Y=F>>>23&255,W=F&8388607;return Y===255?W?NaN:I*(1/0):Y===0?I*0.000000000000000000000000000000000000000000001401298464324817*W:I*Math.pow(2,Y-150)*(W+8388608)}A.readFloatLE=Q.bind(null,tm2),A.readFloatBE=Q.bind(null,em2)})();if(typeof Float64Array!=="undefined")(function(){var B=new Float64Array([-0]),Q=new Uint8Array(B.buffer),D=Q[7]===128;function Z(Y,W,J){B[0]=Y,W[J]=Q[0],W[J+1]=Q[1],W[J+2]=Q[2],W[J+3]=Q[3],W[J+4]=Q[4],W[J+5]=Q[5],W[J+6]=Q[6],W[J+7]=Q[7]}function G(Y,W,J){B[0]=Y,W[J]=Q[7],W[J+1]=Q[6],W[J+2]=Q[5],W[J+3]=Q[4],W[J+4]=Q[3],W[J+5]=Q[2],W[J+6]=Q[1],W[J+7]=Q[0]}A.writeDoubleLE=D?Z:G,A.writeDoubleBE=D?G:Z;function F(Y,W){return Q[0]=Y[W],Q[1]=Y[W+1],Q[2]=Y[W+2],Q[3]=Y[W+3],Q[4]=Y[W+4],Q[5]=Y[W+5],Q[6]=Y[W+6],Q[7]=Y[W+7],B[0]}function I(Y,W){return Q[7]=Y[W],Q[6]=Y[W+1],Q[5]=Y[W+2],Q[4]=Y[W+3],Q[3]=Y[W+4],Q[2]=Y[W+5],Q[1]=Y[W+6],Q[0]=Y[W+7],B[0]}A.readDoubleLE=D?F:I,A.readDoubleBE=D?I:F})();else(function(){function B(D,Z,G,F,I,Y){var W=F<0?1:0;if(W)F=-F;if(F===0)D(0,I,Y+Z),D(1/F>0?0:2147483648,I,Y+G);else if(isNaN(F))D(0,I,Y+Z),D(2146959360,I,Y+G);else if(F>179769313486231570000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000)D(0,I,Y+Z),D((W<<31|**********)>>>0,I,Y+G);else{var J;if(F<0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022250738585072014)J=F/0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005,D(J>>>0,I,Y+Z),D((W<<31|J/4294967296)>>>0,I,Y+G);else{var X=Math.floor(Math.log(F)/Math.LN2);if(X===1024)X=1023;J=F*Math.pow(2,-X),D(J*4503599627370496>>>0,I,Y+Z),D((W<<31|X+1023<<20|J*1048576&1048575)>>>0,I,Y+G)}}}A.writeDoubleLE=B.bind(null,rm2,0,4),A.writeDoubleBE=B.bind(null,om2,4,0);function Q(D,Z,G,F,I){var Y=D(F,I+Z),W=D(F,I+G),J=(W>>31)*2+1,X=W>>>20&2047,V=4294967296*(W&1048575)+Y;return X===2047?V?NaN:J*(1/0):X===0?J*0.000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005*V:J*Math.pow(2,X-1075)*(V+4503599627370496)}A.readDoubleLE=Q.bind(null,tm2,0,4),A.readDoubleBE=Q.bind(null,em2,4,0)})();return A}function rm2(A,B,Q){B[Q]=A&255,B[Q+1]=A>>>8&255,B[Q+2]=A>>>16&255,B[Q+3]=A>>>24}function om2(A,B,Q){B[Q]=A>>>24,B[Q+1]=A>>>16&255,B[Q+2]=A>>>8&255,B[Q+3]=A&255}function tm2(A,B){return(A[B]|A[B+1]<<8|A[B+2]<<16|A[B+3]<<24)>>>0}function em2(A,B){return(A[B]<<24|A[B+1]<<16|A[B+2]<<8|A[B+3])>>>0}});
var Bh2=E((ef2)=>{Object.defineProperty(ef2,"__esModule",{value:!0});ef2.defaultServiceName=void 0;function Y86(){return`unknown_service:${process.argv0}`}ef2.defaultServiceName=Y86});
var CI0=E((Rm2)=>{Object.defineProperty(Rm2,"__esModule",{value:!0});Rm2.createBoundedQueueExportPromiseHandler=void 0;class Mm2{_concurrencyLimit;_sendingPromises=[];constructor(A){this._concurrencyLimit=A}pushPromise(A){if(this.hasReachedLimit())throw new Error("Concurrency Limit reached");this._sendingPromises.push(A);let B=()=>{let Q=this._sendingPromises.indexOf(A);this._sendingPromises.splice(Q,1)};A.then(B,B)}hasReachedLimit(){return this._sendingPromises.length>=this._concurrencyLimit}async awaitAll(){await Promise.all(this._sendingPromises)}}function e36(A){return new Mm2(A.concurrencyLimit)}Rm2.createBoundedQueueExportPromiseHandler=e36});
var CL2=E((XL2)=>{Object.defineProperty(XL2,"__esModule",{value:!0});XL2.DiagComponentLogger=void 0;var Ui4=Wu();class JL2{constructor(A){this._namespace=A.namespace||"DiagComponentLogger"}debug(...A){return b51("debug",this._namespace,A)}error(...A){return b51("error",this._namespace,A)}info(...A){return b51("info",this._namespace,A)}warn(...A){return b51("warn",this._namespace,A)}verbose(...A){return b51("verbose",this._namespace,A)}}XL2.DiagComponentLogger=JL2;function b51(A,B,Q){let D=Ui4.getGlobal("diag");if(!D)return;return Q.unshift(B),D[A](...Q)}});
var CO1=E((zM2)=>{Object.defineProperty(zM2,"__esModule",{value:!0});zM2.wrapSpanContext=zM2.isSpanContextValid=zM2.isValidSpanId=zM2.isValidTraceId=void 0;var CM2=XO1(),Jn4=VO1(),Xn4=/^([0-9a-f]{32})$/i,Vn4=/^[0-9a-f]{16}$/i;function KM2(A){return Xn4.test(A)&&A!==CM2.INVALID_TRACEID}zM2.isValidTraceId=KM2;function HM2(A){return Vn4.test(A)&&A!==CM2.INVALID_SPANID}zM2.isValidSpanId=HM2;function Cn4(A){return KM2(A.traceId)&&HM2(A.spanId)}zM2.isSpanContextValid=Cn4;function Kn4(A){return new Jn4.NonRecordingSpan(A)}zM2.wrapSpanContext=Kn4});
var Cc2=E((cI0)=>{Object.defineProperty(cI0,"__esModule",{value:!0});cI0.ProtobufMetricsSerializer=void 0;var wD6=Vc2();Object.defineProperty(cI0,"ProtobufMetricsSerializer",{enumerable:!0,get:function(){return wD6.ProtobufMetricsSerializer}})});
var Cg2=E((Xg2)=>{Object.defineProperty(Xg2,"__esModule",{value:!0});Xg2.ViewRegistry=void 0;class Jg2{_registeredViews=[];addView(A){this._registeredViews.push(A)}findViews(A,B){return this._registeredViews.filter((D)=>{return this._matchInstrument(D.instrumentSelector,A)&&this._matchMeter(D.meterSelector,B)})}_matchInstrument(A,B){return(A.getType()===void 0||B.type===A.getType())&&A.getNameFilter().match(B.name)&&A.getUnitFilter().match(B.unit)}_matchMeter(A,B){return A.getNameFilter().match(B.name)&&(B.version===void 0||A.getVersionFilter().match(B.version))&&(B.schemaUrl===void 0||A.getSchemaUrlFilter().match(B.schemaUrl))}}Xg2.ViewRegistry=Jg2});
var DT1=E((id2)=>{Object.defineProperty(id2,"__esModule",{value:!0});id2.toAnyValue=id2.toKeyValue=id2.toAttributes=id2.createInstrumentationScope=id2.createResource=void 0;function c76(A){return{attributes:pd2(A.attributes),droppedAttributesCount:0}}id2.createResource=c76;function l76(A){return{name:A.name,version:A.version}}id2.createInstrumentationScope=l76;function pd2(A){return Object.keys(A).map((B)=>hI0(B,A[B]))}id2.toAttributes=pd2;function hI0(A,B){return{key:A,value:gI0(B)}}id2.toKeyValue=hI0;function gI0(A){let B=typeof A;if(B==="string")return{stringValue:A};if(B==="number"){if(!Number.isInteger(A))return{doubleValue:A};return{intValue:A}}if(B==="boolean")return{boolValue:A};if(A instanceof Uint8Array)return{bytesValue:A};if(Array.isArray(A))return{arrayValue:{values:A.map(gI0)}};if(B==="object"&&A!=null)return{kvlistValue:{values:Object.entries(A).map(([Q,D])=>hI0(Q,D))}};return{}}id2.toAnyValue=gI0});
var DT2=E((BT2)=>{Object.defineProperty(BT2,"__esModule",{value:!0});BT2.getMapping=void 0;var Ss4=iO2(),js4=tO2(),ks4=$O1(),eO2=-10,AT2=20,ys4=Array.from({length:31},(A,B)=>{if(B>10)return new js4.LogarithmMapping(B-10);return new Ss4.ExponentMapping(B-10)});function _s4(A){if(A>AT2||A<eO2)throw new ks4.MappingError(`expected scale >= ${eO2} && <= ${AT2}, got: ${A}`);return ys4[A+10]}BT2.getMapping=_s4});
var Df2=E((Bf2)=>{Object.defineProperty(Bf2,"__esModule",{value:!0});Bf2._export=void 0;var Af2=ZQ(),M66=c51();function R66(A,B){return new Promise((Q)=>{Af2.context.with(M66.suppressTracing(Af2.context.active()),()=>{A.export(B,(D)=>{Q(D)})})})}Bf2._export=R66});
var Dg2=E((Bg2)=>{Object.defineProperty(Bg2,"__esModule",{value:!0});Bg2.serviceInstanceIdDetector=void 0;var t86=GP(),e86=J1("crypto");class Ag2{detect(A){return{attributes:{[t86.SEMRESATTRS_SERVICE_INSTANCE_ID]:e86.randomUUID()}}}}Bg2.serviceInstanceIdDetector=new Ag2});
var Dl2=E((Bl2)=>{Object.defineProperty(Bl2,"__esModule",{value:!0});Bl2.createRetryingTransport=void 0;var IZ6=5,YZ6=1000,WZ6=5000,JZ6=1.5,ec2=0.2;function XZ6(){return Math.random()*(2*ec2)-ec2}class Al2{_transport;constructor(A){this._transport=A}retry(A,B,Q){return new Promise((D,Z)=>{setTimeout(()=>{this._transport.send(A,B).then(D,Z)},Q)})}async send(A,B){let Q=Date.now()+B,D=await this._transport.send(A,B),Z=IZ6,G=YZ6;while(D.status==="retryable"&&Z>0){Z--;let F=Math.max(Math.min(G,WZ6)+XZ6(),0);G=G*JZ6;let I=D.retryInMillis??F,Y=Q-Date.now();if(I>Y)return D;D=await this.retry(A,Y,I)}return D}shutdown(){return this._transport.shutdown()}}function VZ6(A){return new Al2(A.transport)}Bl2.createRetryingTransport=VZ6});
var EI0=E((um2)=>{Object.defineProperty(um2,"__esModule",{value:!0});um2.OTLPMetricExporterBase=um2.LowMemoryTemporalitySelector=um2.DeltaTemporalitySelector=um2.CumulativeTemporalitySelector=void 0;var K76=y3(),mG=f_(),hm2=XI0(),H76=Tu(),z76=ZQ(),E76=()=>mG.AggregationTemporality.CUMULATIVE;um2.CumulativeTemporalitySelector=E76;var U76=(A)=>{switch(A){case mG.InstrumentType.COUNTER:case mG.InstrumentType.OBSERVABLE_COUNTER:case mG.InstrumentType.GAUGE:case mG.InstrumentType.HISTOGRAM:case mG.InstrumentType.OBSERVABLE_GAUGE:return mG.AggregationTemporality.DELTA;case mG.InstrumentType.UP_DOWN_COUNTER:case mG.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:return mG.AggregationTemporality.CUMULATIVE}};um2.DeltaTemporalitySelector=U76;var w76=(A)=>{switch(A){case mG.InstrumentType.COUNTER:case mG.InstrumentType.HISTOGRAM:return mG.AggregationTemporality.DELTA;case mG.InstrumentType.GAUGE:case mG.InstrumentType.UP_DOWN_COUNTER:case mG.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:case mG.InstrumentType.OBSERVABLE_COUNTER:case mG.InstrumentType.OBSERVABLE_GAUGE:return mG.AggregationTemporality.CUMULATIVE}};um2.LowMemoryTemporalitySelector=w76;function $76(){let A=(K76.getStringFromEnv("OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE")??"cumulative").toLowerCase();if(A==="cumulative")return um2.CumulativeTemporalitySelector;if(A==="delta")return um2.DeltaTemporalitySelector;if(A==="lowmemory")return um2.LowMemoryTemporalitySelector;return z76.diag.warn(`OTEL_EXPORTER_OTLP_METRICS_TEMPORALITY_PREFERENCE is set to '${A}', but only 'cumulative' and 'delta' are allowed. Using default ('cumulative') instead.`),um2.CumulativeTemporalitySelector}function q76(A){if(A!=null){if(A===hm2.AggregationTemporalityPreference.DELTA)return um2.DeltaTemporalitySelector;else if(A===hm2.AggregationTemporalityPreference.LOWMEMORY)return um2.LowMemoryTemporalitySelector;return um2.CumulativeTemporalitySelector}return $76()}var N76=Object.freeze({type:mG.AggregationType.DEFAULT});function L76(A){return A?.aggregationPreference??(()=>N76)}class gm2 extends H76.OTLPExporterBase{_aggregationTemporalitySelector;_aggregationSelector;constructor(A,B){super(A);this._aggregationSelector=L76(B),this._aggregationTemporalitySelector=q76(B?.temporalityPreference)}selectAggregation(A){return this._aggregationSelector(A)}selectAggregationTemporality(A){return this._aggregationTemporalitySelector(A)}}um2.OTLPMetricExporterBase=gm2});
var EL2=E((HL2)=>{Object.defineProperty(HL2,"__esModule",{value:!0});HL2.createLogLevelDiagLogger=void 0;var DP=YO1();function $i4(A,B){if(A<DP.DiagLogLevel.NONE)A=DP.DiagLogLevel.NONE;else if(A>DP.DiagLogLevel.ALL)A=DP.DiagLogLevel.ALL;B=B||{};function Q(D,Z){let G=B[D];if(typeof G==="function"&&A>=Z)return G.bind(B);return function(){}}return{error:Q("error",DP.DiagLogLevel.ERROR),warn:Q("warn",DP.DiagLogLevel.WARN),info:Q("info",DP.DiagLogLevel.INFO),debug:Q("debug",DP.DiagLogLevel.DEBUG),verbose:Q("verbose",DP.DiagLogLevel.VERBOSE)}}HL2.createLogLevelDiagLogger=$i4});
var EO1=E((UO2)=>{Object.defineProperty(UO2,"__esModule",{value:!0});UO2.AggregationTemporality=void 0;var ma4;(function(A){A[A.DELTA=0]="DELTA",A[A.CUMULATIVE=1]="CUMULATIVE"})(ma4=UO2.AggregationTemporality||(UO2.AggregationTemporality={}))});
var Ed2=E((By5,zd2)=>{zd2.exports=iL;var Hd2=tO1();(iL.prototype=Object.create(Hd2.prototype)).constructor=iL;var u_=pL();function iL(){Hd2.call(this)}iL._configure=function(){iL.alloc=u_._Buffer_allocUnsafe,iL.writeBytesBuffer=u_.Buffer&&u_.Buffer.prototype instanceof Uint8Array&&u_.Buffer.prototype.set.name==="set"?function A(B,Q,D){Q.set(B,D)}:function A(B,Q,D){if(B.copy)B.copy(Q,D,0,B.length);else for(var Z=0;Z<B.length;)Q[D++]=B[Z++]}};iL.prototype.bytes=function A(B){if(u_.isString(B))B=u_._Buffer_from(B,"base64");var Q=B.length>>>0;if(this.uint32(Q),Q)this._push(iL.writeBytesBuffer,Q,B);return this};function k76(A,B,Q){if(A.length<40)u_.utf8.write(A,B,Q);else if(B.utf8Write)B.utf8Write(A,Q);else B.write(A,Q)}iL.prototype.string=function A(B){var Q=u_.Buffer.byteLength(B);if(this.uint32(Q),Q)this._push(k76,Q,B);return this};iL._configure()});
var FI0=E((ug2)=>{Object.defineProperty(ug2,"__esModule",{value:!0});ug2.TemporalMetricProcessor=void 0;var b56=EO1(),f56=B31();class Q31{_aggregator;_unreportedAccumulations=new Map;_reportHistory=new Map;constructor(A,B){this._aggregator=A,B.forEach((Q)=>{this._unreportedAccumulations.set(Q,[])})}buildMetrics(A,B,Q,D){this._stashAccumulations(Q);let Z=this._getMergedUnreportedAccumulations(A),G=Z,F;if(this._reportHistory.has(A)){let Y=this._reportHistory.get(A),W=Y.collectionTime;if(F=Y.aggregationTemporality,F===b56.AggregationTemporality.CUMULATIVE)G=Q31.merge(Y.accumulations,Z,this._aggregator);else G=Q31.calibrateStartTime(Y.accumulations,Z,W)}else F=A.selectAggregationTemporality(B.type);this._reportHistory.set(A,{accumulations:G,collectionTime:D,aggregationTemporality:F});let I=h56(G);if(I.length===0)return;return this._aggregator.toMetricData(B,F,I,D)}_stashAccumulations(A){let B=this._unreportedAccumulations.keys();for(let Q of B){let D=this._unreportedAccumulations.get(Q);if(D===void 0)D=[],this._unreportedAccumulations.set(Q,D);D.push(A)}}_getMergedUnreportedAccumulations(A){let B=new f56.AttributeHashMap,Q=this._unreportedAccumulations.get(A);if(this._unreportedAccumulations.set(A,[]),Q===void 0)return B;for(let D of Q)B=Q31.merge(B,D,this._aggregator);return B}static merge(A,B,Q){let D=A,Z=B.entries(),G=Z.next();while(G.done!==!0){let[F,I,Y]=G.value;if(A.has(F,Y)){let W=A.get(F,Y),J=Q.merge(W,I);D.set(F,J,Y)}else D.set(F,I,Y);G=Z.next()}return D}static calibrateStartTime(A,B,Q){for(let[D,Z]of A.keys())B.get(D,Z)?.setStartTime(Q);return B}}ug2.TemporalMetricProcessor=Q31;function h56(A){return Array.from(A.entries())}});
var Fd2=E((ok5,Gd2)=>{Gd2.exports=O76;function O76(A,B,Q){var D=Q||8192,Z=D>>>1,G=null,F=D;return function I(Y){if(Y<1||Y>Z)return A(Y);if(F+Y>D)G=A(D),F=0;var W=B.call(G,F,F+=Y);if(F&7)F=(F|7)+1;return W}}});
var Fl2=E((Zl2)=>{Object.defineProperty(Zl2,"__esModule",{value:!0});Zl2.createOtlpHttpExportDelegate=void 0;var CZ6=KI0(),KZ6=tc2(),HZ6=CI0(),zZ6=Dl2();function EZ6(A,B){return CZ6.createOtlpExportDelegate({transport:zZ6.createRetryingTransport({transport:KZ6.createHttpExporterTransport(A)}),serializer:B,promiseHandler:HZ6.createBoundedQueueExportPromiseHandler(A)},{timeout:A.timeoutMillis})}Zl2.createOtlpHttpExportDelegate=EZ6});
var Fu2=E((Zu2)=>{Object.defineProperty(Zu2,"__esModule",{value:!0});Zu2.MultiMetricStorage=void 0;class Du2{_backingStorages;constructor(A){this._backingStorages=A}record(A,B,Q,D){this._backingStorages.forEach((Z)=>{Z.record(A,B,Q,D)})}}Zu2.MultiMetricStorage=Du2});
var GI0=E((hg2)=>{Object.defineProperty(hg2,"__esModule",{value:!0});hg2.DeltaMetricProcessor=void 0;var v56=cw(),ZI0=B31();class fg2{_aggregator;_activeCollectionStorage=new ZI0.AttributeHashMap;_cumulativeMemoStorage=new ZI0.AttributeHashMap;_cardinalityLimit;_overflowAttributes={"otel.metric.overflow":!0};_overflowHashCode;constructor(A,B){this._aggregator=A,this._cardinalityLimit=(B??2000)-1,this._overflowHashCode=v56.hashAttributes(this._overflowAttributes)}record(A,B,Q,D){let Z=this._activeCollectionStorage.get(B);if(!Z){if(this._activeCollectionStorage.size>=this._cardinalityLimit){this._activeCollectionStorage.getOrDefault(this._overflowAttributes,()=>this._aggregator.createAccumulation(D))?.record(A);return}Z=this._aggregator.createAccumulation(D),this._activeCollectionStorage.set(B,Z)}Z?.record(A)}batchCumulate(A,B){Array.from(A.entries()).forEach(([Q,D,Z])=>{let G=this._aggregator.createAccumulation(B);G?.record(D);let F=G;if(this._cumulativeMemoStorage.has(Q,Z)){let I=this._cumulativeMemoStorage.get(Q,Z);F=this._aggregator.diff(I,G)}else if(this._cumulativeMemoStorage.size>=this._cardinalityLimit){if(Q=this._overflowAttributes,Z=this._overflowHashCode,this._cumulativeMemoStorage.has(Q,Z)){let I=this._cumulativeMemoStorage.get(Q,Z);F=this._aggregator.diff(I,G)}}if(this._activeCollectionStorage.has(Q,Z)){let I=this._activeCollectionStorage.get(Q,Z);F=this._aggregator.merge(I,F)}this._cumulativeMemoStorage.set(Q,G,Z),this._activeCollectionStorage.set(Q,F,Z)})}collect(){let A=this._activeCollectionStorage;return this._activeCollectionStorage=new ZI0.AttributeHashMap,A}}hg2.DeltaMetricProcessor=fg2});
var GP=E((mL)=>{var D46=mL&&mL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),NO1=mL&&mL.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))D46(B,A,Q)};Object.defineProperty(mL,"__esModule",{value:!0});NO1(ty2(),mL);NO1(Pv2(),mL);NO1(_v2(),mL);NO1(bv2(),mL)});
var GR2=E((DR2)=>{Object.defineProperty(DR2,"__esModule",{value:!0});DR2.NOOP_METER_PROVIDER=DR2.NoopMeterProvider=void 0;var cn4=qG0();class dG0{getMeter(A,B,Q){return cn4.NOOP_METER}}DR2.NoopMeterProvider=dG0;DR2.NOOP_METER_PROVIDER=new dG0});
var Gb2=E((Db2)=>{Object.defineProperty(Db2,"__esModule",{value:!0});Db2.validateValue=Db2.validateKey=void 0;var jF0="[_0-9a-z-*/]",_46=`[a-z]${jF0}{0,255}`,x46=`[a-z0-9]${jF0}{0,240}@[a-z]${jF0}{0,13}`,v46=new RegExp(`^(?:${_46}|${x46})$`),b46=/^[ -~]{0,255}[!-~]$/,f46=/,|=/;function h46(A){return v46.test(A)}Db2.validateKey=h46;function g46(A){return b46.test(A)&&!f46.test(A)}Db2.validateValue=g46});
var Gg2=E((_o)=>{Object.defineProperty(_o,"__esModule",{value:!0});_o.serviceInstanceIdDetector=_o.processDetector=_o.osDetector=_o.hostDetector=void 0;var gO1=Zg2();Object.defineProperty(_o,"hostDetector",{enumerable:!0,get:function(){return gO1.hostDetector}});Object.defineProperty(_o,"osDetector",{enumerable:!0,get:function(){return gO1.osDetector}});Object.defineProperty(_o,"processDetector",{enumerable:!0,get:function(){return gO1.processDetector}});Object.defineProperty(_o,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return gO1.serviceInstanceIdDetector}})});
var Gh2=E((Dh2)=>{Object.defineProperty(Dh2,"__esModule",{value:!0});Dh2.identity=Dh2.isPromiseLike=void 0;var C86=(A)=>{return A!==null&&typeof A==="object"&&typeof A.then==="function"};Dh2.isPromiseLike=C86;function K86(A){return A}Dh2.identity=K86});
var Hh2=E((Ch2)=>{Object.defineProperty(Ch2,"__esModule",{value:!0});Ch2.envDetector=void 0;var R86=ZQ(),O86=GP(),Xh2=y3();class Vh2{_MAX_LENGTH=255;_COMMA_SEPARATOR=",";_LABEL_KEY_VALUE_SPLITTER="=";_ERROR_MESSAGE_INVALID_CHARS="should be a ASCII string with a length greater than 0 and not exceed "+this._MAX_LENGTH+" characters.";_ERROR_MESSAGE_INVALID_VALUE="should be a ASCII string with a length not exceed "+this._MAX_LENGTH+" characters.";detect(A){let B={},Q=Xh2.getStringFromEnv("OTEL_RESOURCE_ATTRIBUTES"),D=Xh2.getStringFromEnv("OTEL_SERVICE_NAME");if(Q)try{let Z=this._parseResourceAttributes(Q);Object.assign(B,Z)}catch(Z){R86.diag.debug(`EnvDetector failed: ${Z.message}`)}if(D)B[O86.SEMRESATTRS_SERVICE_NAME]=D;return{attributes:B}}_parseResourceAttributes(A){if(!A)return{};let B={},Q=A.split(this._COMMA_SEPARATOR,-1);for(let D of Q){let Z=D.split(this._LABEL_KEY_VALUE_SPLITTER,-1);if(Z.length!==2)continue;let[G,F]=Z;if(G=G.trim(),F=F.trim().split(/^"|"$/).join(""),!this._isValidAndNotEmpty(G))throw new Error(`Attribute key ${this._ERROR_MESSAGE_INVALID_CHARS}`);if(!this._isValid(F))throw new Error(`Attribute value ${this._ERROR_MESSAGE_INVALID_VALUE}`);B[G]=decodeURIComponent(F)}return B}_isValid(A){return A.length<=this._MAX_LENGTH&&this._isBaggageOctetString(A)}_isBaggageOctetString(A){for(let B=0;B<A.length;B++){let Q=A.charCodeAt(B);if(Q<33||Q===44||Q===59||Q===92||Q>126)return!1}return!0}_isValidAndNotEmpty(A){return A.length>0&&this._isValid(A)}}Ch2.envDetector=new Vh2});
var Hm2=E((Cm2)=>{Object.defineProperty(Cm2,"__esModule",{value:!0});Cm2.OTLPExporterBase=void 0;class Vm2{_delegate;constructor(A){this._delegate=A}export(A,B){this._delegate.export(A,B)}forceFlush(){return this._delegate.forceFlush()}shutdown(){return this._delegate.shutdown()}}Cm2.OTLPExporterBase=Vm2});
var IL2=E((GL2)=>{Object.defineProperty(GL2,"__esModule",{value:!0});GL2.isCompatible=GL2._makeCompatibilityCheck=void 0;var Yi4=YG0(),DL2=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function ZL2(A){let B=new Set([A]),Q=new Set,D=A.match(DL2);if(!D)return()=>!1;let Z={major:+D[1],minor:+D[2],patch:+D[3],prerelease:D[4]};if(Z.prerelease!=null)return function I(Y){return Y===A};function G(I){return Q.add(I),!1}function F(I){return B.add(I),!0}return function I(Y){if(B.has(Y))return!0;if(Q.has(Y))return!1;let W=Y.match(DL2);if(!W)return G(Y);let J={major:+W[1],minor:+W[2],patch:+W[3],prerelease:W[4]};if(J.prerelease!=null)return G(Y);if(Z.major!==J.major)return G(Y);if(Z.major===0){if(Z.minor===J.minor&&Z.patch<=J.patch)return F(Y);return G(Y)}if(Z.minor<=J.minor)return F(Y);return G(Y)}}GL2._makeCompatibilityCheck=ZL2;GL2.isCompatible=ZL2(Yi4.VERSION)});
var IT1=E((d_)=>{Object.defineProperty(d_,"__esModule",{value:!0});d_.OTLPMetricExporterBase=d_.LowMemoryTemporalitySelector=d_.DeltaTemporalitySelector=d_.CumulativeTemporalitySelector=d_.AggregationTemporalityPreference=d_.OTLPMetricExporter=void 0;var AG6=yl2();Object.defineProperty(d_,"OTLPMetricExporter",{enumerable:!0,get:function(){return AG6.OTLPMetricExporter}});var BG6=XI0();Object.defineProperty(d_,"AggregationTemporalityPreference",{enumerable:!0,get:function(){return BG6.AggregationTemporalityPreference}});var FT1=EI0();Object.defineProperty(d_,"CumulativeTemporalitySelector",{enumerable:!0,get:function(){return FT1.CumulativeTemporalitySelector}});Object.defineProperty(d_,"DeltaTemporalitySelector",{enumerable:!0,get:function(){return FT1.DeltaTemporalitySelector}});Object.defineProperty(d_,"LowMemoryTemporalitySelector",{enumerable:!0,get:function(){return FT1.LowMemoryTemporalitySelector}});Object.defineProperty(d_,"OTLPMetricExporterBase",{enumerable:!0,get:function(){return FT1.OTLPMetricExporterBase}})});
var Jh2=E((Yh2)=>{Object.defineProperty(Yh2,"__esModule",{value:!0});Yh2.detectResources=void 0;var oF0=ZQ(),rF0=sF0(),L86=(A={})=>{let B=(A.detectors||[]).map((Q)=>{try{let D=rF0.resourceFromDetectedResource(Q.detect(A));return oF0.diag.debug(`${Q.constructor.name} found resource.`,D),D}catch(D){return oF0.diag.debug(`${Q.constructor.name} failed: ${D.message}`),rF0.emptyResource()}});return M86(B),B.reduce((Q,D)=>Q.merge(D),rF0.emptyResource())};Yh2.detectResources=L86;var M86=(A)=>{A.forEach((B)=>{if(Object.keys(B.attributes).length>0){let Q=JSON.stringify(B.attributes,null,4);oF0.diag.verbose(Q)}})}});
var Ju=E((wL2)=>{Object.defineProperty(wL2,"__esModule",{value:!0});wL2.DiagAPI=void 0;var qi4=CL2(),Ni4=EL2(),UL2=YO1(),WO1=Wu(),Li4="diag";class JG0{constructor(){function A(D){return function(...Z){let G=WO1.getGlobal("diag");if(!G)return;return G[D](...Z)}}let B=this,Q=(D,Z={logLevel:UL2.DiagLogLevel.INFO})=>{var G,F,I;if(D===B){let J=new Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return B.error((G=J.stack)!==null&&G!==void 0?G:J.message),!1}if(typeof Z==="number")Z={logLevel:Z};let Y=WO1.getGlobal("diag"),W=Ni4.createLogLevelDiagLogger((F=Z.logLevel)!==null&&F!==void 0?F:UL2.DiagLogLevel.INFO,D);if(Y&&!Z.suppressOverrideMessage){let J=(I=new Error().stack)!==null&&I!==void 0?I:"<failed to generate stacktrace>";Y.warn(`Current logger will be overwritten from ${J}`),W.warn(`Current logger will overwrite one already registered from ${J}`)}return WO1.registerGlobal("diag",W,B,!0)};B.setLogger=Q,B.disable=()=>{WO1.unregisterGlobal(Li4,B)},B.createComponentLogger=(D)=>{return new qi4.DiagComponentLogger(D)},B.verbose=A("verbose"),B.debug=A("debug"),B.info=A("info"),B.warn=A("warn"),B.error=A("error")}static instance(){if(!this._instance)this._instance=new JG0;return this._instance}}wL2.DiagAPI=JG0});
var KI0=E((ym2)=>{Object.defineProperty(ym2,"__esModule",{value:!0});ym2.createOtlpExportDelegate=void 0;var Ou=y3(),jm2=nO1(),D76=Sm2(),Z76=ZQ();class km2{_transport;_serializer;_responseHandler;_promiseQueue;_timeout;_diagLogger;constructor(A,B,Q,D,Z){this._transport=A,this._serializer=B,this._responseHandler=Q,this._promiseQueue=D,this._timeout=Z,this._diagLogger=Z76.diag.createComponentLogger({namespace:"OTLPExportDelegate"})}export(A,B){if(this._diagLogger.debug("items to be sent",A),this._promiseQueue.hasReachedLimit()){B({code:Ou.ExportResultCode.FAILED,error:new Error("Concurrent export limit reached")});return}let Q=this._serializer.serializeRequest(A);if(Q==null){B({code:Ou.ExportResultCode.FAILED,error:new Error("Nothing to send")});return}this._promiseQueue.pushPromise(this._transport.send(Q,this._timeout).then((D)=>{if(D.status==="success"){if(D.data!=null)try{this._responseHandler.handleResponse(this._serializer.deserializeResponse(D.data))}catch(Z){this._diagLogger.warn("Export succeeded but could not deserialize response - is the response specification compliant?",Z,D.data)}B({code:Ou.ExportResultCode.SUCCESS});return}else if(D.status==="failure"&&D.error){B({code:Ou.ExportResultCode.FAILED,error:D.error});return}else if(D.status==="retryable")B({code:Ou.ExportResultCode.FAILED,error:new jm2.OTLPExporterError("Export failed with retryable status")});else B({code:Ou.ExportResultCode.FAILED,error:new jm2.OTLPExporterError("Export failed with unknown error")})},(D)=>B({code:Ou.ExportResultCode.FAILED,error:D})))}forceFlush(){return this._promiseQueue.awaitAll()}async shutdown(){this._diagLogger.debug("shutdown started"),await this.forceFlush(),this._transport.shutdown()}}function G76(A,B){return new km2(A.transport,A.serializer,D76.createLoggingPartialSuccessResponseHandler(),A.promiseHandler,B.timeout)}ym2.createOtlpExportDelegate=G76});
var Kl2=E((Vl2)=>{Object.defineProperty(Vl2,"__esModule",{value:!0});Vl2.validateAndNormalizeHeaders=void 0;var qZ6=ZQ();function NZ6(A){return()=>{let B={};return Object.entries(A?.()??{}).forEach(([Q,D])=>{if(typeof D!=="undefined")B[Q]=String(D);else qZ6.diag.warn(`Header "${Q}" has invalid value (${D}) and will be ignored`)}),B}}Vl2.validateAndNormalizeHeaders=NZ6});
var LF0=E((yT2)=>{Object.defineProperty(yT2,"__esModule",{value:!0});yT2.loggingErrorHandler=void 0;var Jr4=ZQ();function Xr4(){return(A)=>{Jr4.diag.error(Vr4(A))}}yT2.loggingErrorHandler=Xr4;function Vr4(A){if(typeof A==="string")return A;else return JSON.stringify(Cr4(A))}function Cr4(A){let B={},Q=A;while(Q!==null)Object.getOwnPropertyNames(Q).forEach((D)=>{if(B[D])return;let Z=Q[D];if(Z)B[D]=String(Z)}),Q=Object.getPrototypeOf(Q);return B}});
var LG0=E((nL2)=>{Object.defineProperty(nL2,"__esModule",{value:!0});nL2.defaultTextMapSetter=nL2.defaultTextMapGetter=void 0;nL2.defaultTextMapGetter={get(A,B){if(A==null)return;return A[B]},keys(A){if(A==null)return[];return Object.keys(A)}};nL2.defaultTextMapSetter={set(A,B,Q){if(A==null)return;A[B]=Q}}});
var LL2=E((qL2)=>{Object.defineProperty(qL2,"__esModule",{value:!0});qL2.BaggageImpl=void 0;class Mo{constructor(A){this._entries=A?new Map(A):new Map}getEntry(A){let B=this._entries.get(A);if(!B)return;return Object.assign({},B)}getAllEntries(){return Array.from(this._entries.entries()).map(([A,B])=>[A,B])}setEntry(A,B){let Q=new Mo(this._entries);return Q._entries.set(A,B),Q}removeEntry(A){let B=new Mo(this._entries);return B._entries.delete(A),B}removeEntries(...A){let B=new Mo(this._entries);for(let Q of A)B._entries.delete(Q);return B}clear(){return new Mo}}qL2.BaggageImpl=Mo});
var Lc2=E((pI0)=>{Object.defineProperty(pI0,"__esModule",{value:!0});pI0.ProtobufTraceSerializer=void 0;var kD6=Nc2();Object.defineProperty(pI0,"ProtobufTraceSerializer",{enumerable:!0,get:function(){return kD6.ProtobufTraceSerializer}})});
var Lh2=E((qh2)=>{Object.defineProperty(qh2,"__esModule",{value:!0});qh2.getMachineId=void 0;var y86=J1("fs"),_86=ZQ();async function x86(){let A=["/etc/machine-id","/var/lib/dbus/machine-id"];for(let B of A)try{return(await y86.promises.readFile(B,{encoding:"utf8"})).trim()}catch(Q){_86.diag.debug(`error reading machine id: ${Q}`)}return}qh2.getMachineId=x86});
var Lm2=E((Nm2)=>{Object.defineProperty(Nm2,"__esModule",{value:!0});Nm2.CompressionAlgorithm=void 0;var t36;(function(A){A.NONE="none",A.GZIP="gzip"})(t36=Nm2.CompressionAlgorithm||(Nm2.CompressionAlgorithm={}))});
var MF0=E((eT2)=>{Object.defineProperty(eT2,"__esModule",{value:!0});eT2.createConstMap=void 0;function Rr4(A){let B={},Q=A.length;for(let D=0;D<Q;D++){let Z=A[D];if(Z)B[String(Z).toUpperCase().replace(/[-.]/g,"_")]=Z}return B}eT2.createConstMap=Rr4});
var MT2=E((NT2)=>{Object.defineProperty(NT2,"__esModule",{value:!0});NT2.AnchoredClock=void 0;class qT2{_monotonicClock;_epochMillis;_performanceMillis;constructor(A,B){this._monotonicClock=B,this._epochMillis=A.now(),this._performanceMillis=B.now()}now(){let A=this._monotonicClock.now()-this._performanceMillis;return this._epochMillis+A}}NT2.AnchoredClock=qT2});
var Nc2=E(($c2)=>{Object.defineProperty($c2,"__esModule",{value:!0});$c2.ProtobufTraceSerializer=void 0;var wc2=BT1(),PD6=lI0(),SD6=wc2.opentelemetry.proto.collector.trace.v1.ExportTraceServiceResponse,jD6=wc2.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;$c2.ProtobufTraceSerializer={serializeRequest:(A)=>{let B=PD6.createExportTraceServiceRequest(A);return jD6.encode(B).finish()},deserializeResponse:(A)=>{return SD6.decode(A)}}});
var Nu2=E(($u2)=>{Object.defineProperty($u2,"__esModule",{value:!0});$u2.SyncMetricStorage=void 0;var A36=QI0(),B36=GI0(),Q36=FI0();class wu2 extends A36.MetricStorage{_attributesProcessor;_aggregationCardinalityLimit;_deltaMetricStorage;_temporalMetricStorage;constructor(A,B,Q,D,Z){super(A);this._attributesProcessor=Q,this._aggregationCardinalityLimit=Z,this._deltaMetricStorage=new B36.DeltaMetricProcessor(B,this._aggregationCardinalityLimit),this._temporalMetricStorage=new Q36.TemporalMetricProcessor(B,D)}record(A,B,Q,D){B=this._attributesProcessor.process(B,Q),this._deltaMetricStorage.record(A,B,Q,D)}collect(A,B){let Q=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(A,this._instrumentDescriptor,Q,B)}}$u2.SyncMetricStorage=wu2});
var OL2=E((ML2)=>{Object.defineProperty(ML2,"__esModule",{value:!0});ML2.baggageEntryMetadataSymbol=void 0;ML2.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")});
var Oc2=E((Mc2)=>{Object.defineProperty(Mc2,"__esModule",{value:!0});Mc2.JsonLogsSerializer=void 0;var _D6=uI0();Mc2.JsonLogsSerializer={serializeRequest:(A)=>{let B=_D6.createExportLogsServiceRequest(A,{useHex:!0,useLongBits:!1});return new TextEncoder().encode(JSON.stringify(B))},deserializeResponse:(A)=>{return JSON.parse(new TextDecoder().decode(A))}}});
var Od2=E((Dy5,Rd2)=>{Rd2.exports=Su;var Md2=AT1();(Su.prototype=Object.create(Md2.prototype)).constructor=Su;var Ld2=pL();function Su(A){Md2.call(this,A)}Su._configure=function(){if(Ld2.Buffer)Su.prototype._slice=Ld2.Buffer.prototype.slice};Su.prototype.string=function A(){var B=this.uint32();return this.buf.utf8Slice?this.buf.utf8Slice(this.pos,this.pos=Math.min(this.pos+B,this.len)):this.buf.toString("utf-8",this.pos,this.pos=Math.min(this.pos+B,this.len))};Su._configure()});
var PG0=E((QM2)=>{Object.defineProperty(QM2,"__esModule",{value:!0});QM2.TraceFlags=void 0;var si4;(function(A){A[A.NONE=0]="NONE",A[A.SAMPLED=1]="SAMPLED"})(si4=QM2.TraceFlags||(QM2.TraceFlags={}))});
var PM2=E((OM2)=>{Object.defineProperty(OM2,"__esModule",{value:!0});OM2.NoopTracerProvider=void 0;var Ln4=xG0();class RM2{getTracer(A,B,Q){return new Ln4.NoopTracer}}OM2.NoopTracerProvider=RM2});
var PR2=E((OR2)=>{Object.defineProperty(OR2,"__esModule",{value:!0});OR2.propagation=void 0;var Za4=RR2();OR2.propagation=Za4.PropagationAPI.getInstance()});
var Pd2=E((Zy5,Td2)=>{Td2.exports=I31;var kI0=pL();(I31.prototype=Object.create(kI0.EventEmitter.prototype)).constructor=I31;function I31(A,B,Q){if(typeof A!=="function")throw TypeError("rpcImpl must be a function");kI0.EventEmitter.call(this),this.rpcImpl=A,this.requestDelimited=Boolean(B),this.responseDelimited=Boolean(Q)}I31.prototype.rpcCall=function A(B,Q,D,Z,G){if(!Z)throw TypeError("request must be specified");var F=this;if(!G)return kI0.asPromise(A,F,B,Q,D,Z);if(!F.rpcImpl){setTimeout(function(){G(Error("already ended"))},0);return}try{return F.rpcImpl(B,Q[F.requestDelimited?"encodeDelimited":"encode"](Z).finish(),function I(Y,W){if(Y)return F.emit("error",Y,B),G(Y);if(W===null){F.end(!0);return}if(!(W instanceof D))try{W=D[F.responseDelimited?"decodeDelimited":"decode"](W)}catch(J){return F.emit("error",J,B),G(J)}return F.emit("data",W,B),G(null,W)})}catch(I){F.emit("error",I,B),setTimeout(function(){G(I)},0);return}};I31.prototype.end=function A(B){if(this.rpcImpl){if(!B)this.rpcImpl(null,null,null);this.rpcImpl=null,this.emit("end").off()}return this}});
var Pf2=E((Nf2)=>{Object.defineProperty(Nf2,"__esModule",{value:!0});Nf2.DEFAULT_AGGREGATION=Nf2.EXPONENTIAL_HISTOGRAM_AGGREGATION=Nf2.HISTOGRAM_AGGREGATION=Nf2.LAST_VALUE_AGGREGATION=Nf2.SUM_AGGREGATION=Nf2.DROP_AGGREGATION=Nf2.DefaultAggregation=Nf2.ExponentialHistogramAggregation=Nf2.ExplicitBucketHistogramAggregation=Nf2.HistogramAggregation=Nf2.LastValueAggregation=Nf2.SumAggregation=Nf2.DropAggregation=void 0;var p66=ZQ(),Uu=qf2(),lL=__();class _O1{static DEFAULT_INSTANCE=new Uu.DropAggregator;createAggregator(A){return _O1.DEFAULT_INSTANCE}}Nf2.DropAggregation=_O1;class s51{static MONOTONIC_INSTANCE=new Uu.SumAggregator(!0);static NON_MONOTONIC_INSTANCE=new Uu.SumAggregator(!1);createAggregator(A){switch(A.type){case lL.InstrumentType.COUNTER:case lL.InstrumentType.OBSERVABLE_COUNTER:case lL.InstrumentType.HISTOGRAM:return s51.MONOTONIC_INSTANCE;default:return s51.NON_MONOTONIC_INSTANCE}}}Nf2.SumAggregation=s51;class xO1{static DEFAULT_INSTANCE=new Uu.LastValueAggregator;createAggregator(A){return xO1.DEFAULT_INSTANCE}}Nf2.LastValueAggregation=xO1;class vO1{static DEFAULT_INSTANCE=new Uu.HistogramAggregator([0,5,10,25,50,75,100,250,500,750,1000,2500,5000,7500,1e4],!0);createAggregator(A){return vO1.DEFAULT_INSTANCE}}Nf2.HistogramAggregation=vO1;class bF0{_recordMinMax;_boundaries;constructor(A,B=!0){if(this._recordMinMax=B,A==null)throw new Error("ExplicitBucketHistogramAggregation should be created with explicit boundaries, if a single bucket histogram is required, please pass an empty array");A=A.concat(),A=A.sort((Z,G)=>Z-G);let Q=A.lastIndexOf(-1/0),D=A.indexOf(1/0);if(D===-1)D=void 0;this._boundaries=A.slice(Q+1,D)}createAggregator(A){return new Uu.HistogramAggregator(this._boundaries,this._recordMinMax)}}Nf2.ExplicitBucketHistogramAggregation=bF0;class fF0{_maxSize;_recordMinMax;constructor(A=160,B=!0){this._maxSize=A,this._recordMinMax=B}createAggregator(A){return new Uu.ExponentialHistogramAggregator(this._maxSize,this._recordMinMax)}}Nf2.ExponentialHistogramAggregation=fF0;class hF0{_resolve(A){switch(A.type){case lL.InstrumentType.COUNTER:case lL.InstrumentType.UP_DOWN_COUNTER:case lL.InstrumentType.OBSERVABLE_COUNTER:case lL.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER:return Nf2.SUM_AGGREGATION;case lL.InstrumentType.GAUGE:case lL.InstrumentType.OBSERVABLE_GAUGE:return Nf2.LAST_VALUE_AGGREGATION;case lL.InstrumentType.HISTOGRAM:{if(A.advice.explicitBucketBoundaries)return new bF0(A.advice.explicitBucketBoundaries);return Nf2.HISTOGRAM_AGGREGATION}}return p66.diag.warn(`Unable to recognize instrument type: ${A.type}`),Nf2.DROP_AGGREGATION}createAggregator(A){return this._resolve(A).createAggregator(A)}}Nf2.DefaultAggregation=hF0;Nf2.DROP_AGGREGATION=new _O1;Nf2.SUM_AGGREGATION=new s51;Nf2.LAST_VALUE_AGGREGATION=new xO1;Nf2.HISTOGRAM_AGGREGATION=new vO1;Nf2.EXPONENTIAL_HISTOGRAM_AGGREGATION=new fF0;Nf2.DEFAULT_AGGREGATION=new hF0});
var Pv2=E((Hu)=>{var e26=Hu&&Hu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),AB6=Hu&&Hu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))e26(B,A,Q)};Object.defineProperty(Hu,"__esModule",{value:!0});AB6(Tv2(),Hu)});
var QI0=E((yg2)=>{Object.defineProperty(yg2,"__esModule",{value:!0});yg2.MetricStorage=void 0;var y56=A31();class kg2{_instrumentDescriptor;constructor(A){this._instrumentDescriptor=A}getInstrumentDescriptor(){return this._instrumentDescriptor}updateDescription(A){this._instrumentDescriptor=y56.createInstrumentDescriptor(this._instrumentDescriptor.name,this._instrumentDescriptor.type,{description:A,valueType:this._instrumentDescriptor.valueType,unit:this._instrumentDescriptor.unit,advice:this._instrumentDescriptor.advice})}}yg2.MetricStorage=kg2});
var QR2=E((AR2)=>{Object.defineProperty(AR2,"__esModule",{value:!0});AR2.diag=void 0;var dn4=Ju();AR2.diag=dn4.DiagAPI.instance()});
var QT1=E((cd2)=>{Object.defineProperty(cd2,"__esModule",{value:!0});cd2.getOtlpEncoder=cd2.encodeAsString=cd2.encodeAsLongBits=cd2.toLongBits=cd2.hrTimeToNanos=void 0;var v76=y3(),vI0=hd2();function bI0(A){let B=BigInt(1e9);return BigInt(A[0])*B+BigInt(A[1])}cd2.hrTimeToNanos=bI0;function ud2(A){let B=Number(BigInt.asUintN(32,A)),Q=Number(BigInt.asUintN(32,A>>BigInt(32)));return{low:B,high:Q}}cd2.toLongBits=ud2;function fI0(A){let B=bI0(A);return ud2(B)}cd2.encodeAsLongBits=fI0;function md2(A){return bI0(A).toString()}cd2.encodeAsString=md2;var b76=typeof BigInt!=="undefined"?md2:v76.hrTimeToNanoseconds;function gd2(A){return A}function dd2(A){if(A===void 0)return;return vI0.hexToBinary(A)}var f76={encodeHrTime:fI0,encodeSpanContext:vI0.hexToBinary,encodeOptionalSpanContext:dd2};function h76(A){if(A===void 0)return f76;let B=A.useLongBits??!0,Q=A.useHex??!1;return{encodeHrTime:B?fI0:b76,encodeSpanContext:Q?gd2:vI0.hexToBinary,encodeOptionalSpanContext:Q?gd2:dd2}}cd2.getOtlpEncoder=h76});
var Qb2=E((Ab2)=>{Object.defineProperty(Ab2,"__esModule",{value:!0});Ab2.CompositePropagator=void 0;var tv2=ZQ();class ev2{_propagators;_fields;constructor(A={}){this._propagators=A.propagators??[],this._fields=Array.from(new Set(this._propagators.map((B)=>typeof B.fields==="function"?B.fields():[]).reduce((B,Q)=>B.concat(Q),[])))}inject(A,B,Q){for(let D of this._propagators)try{D.inject(A,B,Q)}catch(Z){tv2.diag.warn(`Failed to inject with ${D.constructor.name}. Err: ${Z.message}`)}}extract(A,B,Q){return this._propagators.reduce((D,Z)=>{try{return Z.extract(D,B,Q)}catch(G){tv2.diag.warn(`Failed to extract with ${Z.constructor.name}. Err: ${G.message}`)}return D},A)}fields(){return this._fields.slice()}}Ab2.CompositePropagator=ev2});
var Qh2=E((cF0)=>{Object.defineProperty(cF0,"__esModule",{value:!0});cF0.defaultServiceName=void 0;var W86=Bh2();Object.defineProperty(cF0,"defaultServiceName",{enumerable:!0,get:function(){return W86.defaultServiceName}})});
var Qu2=E((Au2)=>{Object.defineProperty(Au2,"__esModule",{value:!0});Au2.MetricStorageRegistry=void 0;var r56=A31(),eg2=ZQ(),cO1=tg2();class II0{_sharedRegistry=new Map;_perCollectorRegistry=new Map;static create(){return new II0}getStorages(A){let B=[];for(let D of this._sharedRegistry.values())B=B.concat(D);let Q=this._perCollectorRegistry.get(A);if(Q!=null)for(let D of Q.values())B=B.concat(D);return B}register(A){this._registerStorage(A,this._sharedRegistry)}registerForCollector(A,B){let Q=this._perCollectorRegistry.get(A);if(Q==null)Q=new Map,this._perCollectorRegistry.set(A,Q);this._registerStorage(B,Q)}findOrUpdateCompatibleStorage(A){let B=this._sharedRegistry.get(A.name);if(B===void 0)return null;return this._findOrUpdateCompatibleStorage(A,B)}findOrUpdateCompatibleCollectorStorage(A,B){let Q=this._perCollectorRegistry.get(A);if(Q===void 0)return null;let D=Q.get(B.name);if(D===void 0)return null;return this._findOrUpdateCompatibleStorage(B,D)}_registerStorage(A,B){let Q=A.getInstrumentDescriptor(),D=B.get(Q.name);if(D===void 0){B.set(Q.name,[A]);return}D.push(A)}_findOrUpdateCompatibleStorage(A,B){let Q=null;for(let D of B){let Z=D.getInstrumentDescriptor();if(r56.isDescriptorCompatibleWith(Z,A)){if(Z.description!==A.description){if(A.description.length>Z.description.length)D.updateDescription(A.description);eg2.diag.warn("A view or instrument with the name ",A.name,` has already been registered, but has a different description and is incompatible with another registered view.
`,`Details:
`,cO1.getIncompatibilityDetails(Z,A),`The longer description will be used.
To resolve the conflict:`,cO1.getConflictResolutionRecipe(Z,A))}Q=D}else eg2.diag.warn("A view or instrument with the name ",A.name,` has already been registered and is incompatible with another registered view.
`,`Details:
`,cO1.getIncompatibilityDetails(Z,A),`To resolve the conflict:
`,cO1.getConflictResolutionRecipe(Z,A))}return Q}}Au2.MetricStorageRegistry=II0});
var RF0=E((cL)=>{Object.defineProperty(cL,"__esModule",{value:!0});cL.getStringListFromEnv=cL.getNumberFromEnv=cL.getStringFromEnv=cL.getBooleanFromEnv=cL.unrefTimer=cL.otperformance=cL._globalThis=cL.SDK_INFO=void 0;var x_=cv2();Object.defineProperty(cL,"SDK_INFO",{enumerable:!0,get:function(){return x_.SDK_INFO}});Object.defineProperty(cL,"_globalThis",{enumerable:!0,get:function(){return x_._globalThis}});Object.defineProperty(cL,"otperformance",{enumerable:!0,get:function(){return x_.otperformance}});Object.defineProperty(cL,"unrefTimer",{enumerable:!0,get:function(){return x_.unrefTimer}});Object.defineProperty(cL,"getBooleanFromEnv",{enumerable:!0,get:function(){return x_.getBooleanFromEnv}});Object.defineProperty(cL,"getStringFromEnv",{enumerable:!0,get:function(){return x_.getStringFromEnv}});Object.defineProperty(cL,"getNumberFromEnv",{enumerable:!0,get:function(){return x_.getNumberFromEnv}});Object.defineProperty(cL,"getStringListFromEnv",{enumerable:!0,get:function(){return x_.getStringListFromEnv}})});
var RR2=E((LR2)=>{Object.defineProperty(LR2,"__esModule",{value:!0});LR2.PropagationAPI=void 0;var nG0=Wu(),Ba4=zR2(),qR2=LG0(),KO1=$R2(),Qa4=XG0(),NR2=Ju(),aG0="propagation",Da4=new Ba4.NoopTextMapPropagator;class sG0{constructor(){this.createBaggage=Qa4.createBaggage,this.getBaggage=KO1.getBaggage,this.getActiveBaggage=KO1.getActiveBaggage,this.setBaggage=KO1.setBaggage,this.deleteBaggage=KO1.deleteBaggage}static getInstance(){if(!this._instance)this._instance=new sG0;return this._instance}setGlobalPropagator(A){return nG0.registerGlobal(aG0,A,NR2.DiagAPI.instance())}inject(A,B,Q=qR2.defaultTextMapSetter){return this._getGlobalPropagator().inject(A,B,Q)}extract(A,B,Q=qR2.defaultTextMapGetter){return this._getGlobalPropagator().extract(A,B,Q)}fields(){return this._getGlobalPropagator().fields()}disable(){nG0.unregisterGlobal(aG0,NR2.DiagAPI.instance())}_getGlobalPropagator(){return nG0.getGlobal(aG0)||Da4}}LR2.PropagationAPI=sG0});
var Rl2=E((Ll2)=>{Object.defineProperty(Ll2,"__esModule",{value:!0});Ll2.convertLegacyHttpOptions=void 0;var Nl2=Ul2(),fZ6=ql2(),hZ6=ZQ(),gZ6=Z31();function uZ6(A){if(A?.keepAlive!=null)if(A.httpAgentOptions!=null){if(A.httpAgentOptions.keepAlive==null)A.httpAgentOptions.keepAlive=A.keepAlive}else A.httpAgentOptions={keepAlive:A.keepAlive};return A.httpAgentOptions}function mZ6(A,B,Q,D){if(A.metadata)hZ6.diag.warn("Metadata cannot be set when using http");return Nl2.mergeOtlpHttpConfigurationWithDefaults({url:A.url,headers:gZ6.wrapStaticHeadersInFunction(A.headers),concurrencyLimit:A.concurrencyLimit,timeoutMillis:A.timeoutMillis,compression:A.compression,agentOptions:uZ6(A)},fZ6.getHttpConfigurationFromEnvironment(B,Q),Nl2.getHttpConfigurationDefaults(D,Q))}Ll2.convertLegacyHttpOptions=mZ6});
var Sm2=E((Tm2)=>{Object.defineProperty(Tm2,"__esModule",{value:!0});Tm2.createLoggingPartialSuccessResponseHandler=void 0;var A76=ZQ();function B76(A){return Object.prototype.hasOwnProperty.call(A,"partialSuccess")}function Q76(){return{handleResponse(A){if(A==null||!B76(A)||A.partialSuccess==null||Object.keys(A.partialSuccess).length===0)return;A76.diag.warn("Received Partial Success response:",JSON.stringify(A.partialSuccess))}}}Tm2.createLoggingPartialSuccessResponseHandler=Q76});
var TO2=E((RO2)=>{Object.defineProperty(RO2,"__esModule",{value:!0});RO2.DropAggregator=void 0;var Xs4=To();class MO2{kind=Xs4.AggregatorKind.DROP;createAccumulation(){return}merge(A,B){return}diff(A,B){return}toMetricData(A,B,Q,D){return}}RO2.DropAggregator=MO2});
var Tb2=E((Rb2)=>{Object.defineProperty(Rb2,"__esModule",{value:!0});Rb2.isPlainObject=void 0;var Z66="[object Object]",G66="[object Null]",F66="[object Undefined]",I66=Function.prototype,qb2=I66.toString,Y66=qb2.call(Object),W66=Object.getPrototypeOf,Nb2=Object.prototype,Lb2=Nb2.hasOwnProperty,zu=Symbol?Symbol.toStringTag:void 0,Mb2=Nb2.toString;function J66(A){if(!X66(A)||V66(A)!==Z66)return!1;let B=W66(A);if(B===null)return!0;let Q=Lb2.call(B,"constructor")&&B.constructor;return typeof Q=="function"&&Q instanceof Q&&qb2.call(Q)===Y66}Rb2.isPlainObject=J66;function X66(A){return A!=null&&typeof A=="object"}function V66(A){if(A==null)return A===void 0?F66:G66;return zu&&zu in Object(A)?C66(A):K66(A)}function C66(A){let B=Lb2.call(A,zu),Q=A[zu],D=!1;try{A[zu]=void 0,D=!0}catch(G){}let Z=Mb2.call(A);if(D)if(B)A[zu]=Q;else delete A[zu];return Z}function K66(A){return Mb2.call(A)}});
var Tc2=E((iI0)=>{Object.defineProperty(iI0,"__esModule",{value:!0});iI0.JsonLogsSerializer=void 0;var xD6=Oc2();Object.defineProperty(iI0,"JsonLogsSerializer",{enumerable:!0,get:function(){return xD6.JsonLogsSerializer}})});
var Th2=E((Rh2)=>{Object.defineProperty(Rh2,"__esModule",{value:!0});Rh2.getMachineId=void 0;var v86=J1("fs"),b86=hO1(),Mh2=ZQ();async function f86(){try{return(await v86.promises.readFile("/etc/hostid",{encoding:"utf8"})).trim()}catch(A){Mh2.diag.debug(`error reading machine id: ${A}`)}try{return(await b86.execAsync("kenv -q smbios.system.uuid")).stdout.trim()}catch(A){Mh2.diag.debug(`error reading machine id: ${A}`)}return}Rh2.getMachineId=f86});
var To=E((LO2)=>{Object.defineProperty(LO2,"__esModule",{value:!0});LO2.AggregatorKind=void 0;var Js4;(function(A){A[A.DROP=0]="DROP",A[A.SUM=1]="SUM",A[A.LAST_VALUE=2]="LAST_VALUE",A[A.HISTOGRAM=3]="HISTOGRAM",A[A.EXPONENTIAL_HISTOGRAM=4]="EXPONENTIAL_HISTOGRAM"})(Js4=LO2.AggregatorKind||(LO2.AggregatorKind={}))});
var Tu=E((h_)=>{Object.defineProperty(h_,"__esModule",{value:!0});h_.createOtlpNetworkExportDelegate=h_.CompressionAlgorithm=h_.getSharedConfigurationDefaults=h_.mergeOtlpSharedConfigurationWithDefaults=h_.OTLPExporterError=h_.OTLPExporterBase=void 0;var W76=Hm2();Object.defineProperty(h_,"OTLPExporterBase",{enumerable:!0,get:function(){return W76.OTLPExporterBase}});var J76=nO1();Object.defineProperty(h_,"OTLPExporterError",{enumerable:!0,get:function(){return J76.OTLPExporterError}});var fm2=Z31();Object.defineProperty(h_,"mergeOtlpSharedConfigurationWithDefaults",{enumerable:!0,get:function(){return fm2.mergeOtlpSharedConfigurationWithDefaults}});Object.defineProperty(h_,"getSharedConfigurationDefaults",{enumerable:!0,get:function(){return fm2.getSharedConfigurationDefaults}});var X76=Lm2();Object.defineProperty(h_,"CompressionAlgorithm",{enumerable:!0,get:function(){return X76.CompressionAlgorithm}});var V76=bm2();Object.defineProperty(h_,"createOtlpNetworkExportDelegate",{enumerable:!0,get:function(){return V76.createOtlpNetworkExportDelegate}})});
var Tv2=E((Lv2)=>{Object.defineProperty(Lv2,"__esModule",{value:!0});Lv2.SEMRESATTRS_K8S_STATEFULSET_NAME=Lv2.SEMRESATTRS_K8S_STATEFULSET_UID=Lv2.SEMRESATTRS_K8S_DEPLOYMENT_NAME=Lv2.SEMRESATTRS_K8S_DEPLOYMENT_UID=Lv2.SEMRESATTRS_K8S_REPLICASET_NAME=Lv2.SEMRESATTRS_K8S_REPLICASET_UID=Lv2.SEMRESATTRS_K8S_CONTAINER_NAME=Lv2.SEMRESATTRS_K8S_POD_NAME=Lv2.SEMRESATTRS_K8S_POD_UID=Lv2.SEMRESATTRS_K8S_NAMESPACE_NAME=Lv2.SEMRESATTRS_K8S_NODE_UID=Lv2.SEMRESATTRS_K8S_NODE_NAME=Lv2.SEMRESATTRS_K8S_CLUSTER_NAME=Lv2.SEMRESATTRS_HOST_IMAGE_VERSION=Lv2.SEMRESATTRS_HOST_IMAGE_ID=Lv2.SEMRESATTRS_HOST_IMAGE_NAME=Lv2.SEMRESATTRS_HOST_ARCH=Lv2.SEMRESATTRS_HOST_TYPE=Lv2.SEMRESATTRS_HOST_NAME=Lv2.SEMRESATTRS_HOST_ID=Lv2.SEMRESATTRS_FAAS_MAX_MEMORY=Lv2.SEMRESATTRS_FAAS_INSTANCE=Lv2.SEMRESATTRS_FAAS_VERSION=Lv2.SEMRESATTRS_FAAS_ID=Lv2.SEMRESATTRS_FAAS_NAME=Lv2.SEMRESATTRS_DEVICE_MODEL_NAME=Lv2.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER=Lv2.SEMRESATTRS_DEVICE_ID=Lv2.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT=Lv2.SEMRESATTRS_CONTAINER_IMAGE_TAG=Lv2.SEMRESATTRS_CONTAINER_IMAGE_NAME=Lv2.SEMRESATTRS_CONTAINER_RUNTIME=Lv2.SEMRESATTRS_CONTAINER_ID=Lv2.SEMRESATTRS_CONTAINER_NAME=Lv2.SEMRESATTRS_AWS_LOG_STREAM_ARNS=Lv2.SEMRESATTRS_AWS_LOG_STREAM_NAMES=Lv2.SEMRESATTRS_AWS_LOG_GROUP_ARNS=Lv2.SEMRESATTRS_AWS_LOG_GROUP_NAMES=Lv2.SEMRESATTRS_AWS_EKS_CLUSTER_ARN=Lv2.SEMRESATTRS_AWS_ECS_TASK_REVISION=Lv2.SEMRESATTRS_AWS_ECS_TASK_FAMILY=Lv2.SEMRESATTRS_AWS_ECS_TASK_ARN=Lv2.SEMRESATTRS_AWS_ECS_LAUNCHTYPE=Lv2.SEMRESATTRS_AWS_ECS_CLUSTER_ARN=Lv2.SEMRESATTRS_AWS_ECS_CONTAINER_ARN=Lv2.SEMRESATTRS_CLOUD_PLATFORM=Lv2.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE=Lv2.SEMRESATTRS_CLOUD_REGION=Lv2.SEMRESATTRS_CLOUD_ACCOUNT_ID=Lv2.SEMRESATTRS_CLOUD_PROVIDER=void 0;Lv2.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE=Lv2.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE=Lv2.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS=Lv2.CLOUDPLATFORMVALUES_AZURE_AKS=Lv2.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES=Lv2.CLOUDPLATFORMVALUES_AZURE_VM=Lv2.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK=Lv2.CLOUDPLATFORMVALUES_AWS_LAMBDA=Lv2.CLOUDPLATFORMVALUES_AWS_EKS=Lv2.CLOUDPLATFORMVALUES_AWS_ECS=Lv2.CLOUDPLATFORMVALUES_AWS_EC2=Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC=Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS=Lv2.CloudProviderValues=Lv2.CLOUDPROVIDERVALUES_GCP=Lv2.CLOUDPROVIDERVALUES_AZURE=Lv2.CLOUDPROVIDERVALUES_AWS=Lv2.CLOUDPROVIDERVALUES_ALIBABA_CLOUD=Lv2.SemanticResourceAttributes=Lv2.SEMRESATTRS_WEBENGINE_DESCRIPTION=Lv2.SEMRESATTRS_WEBENGINE_VERSION=Lv2.SEMRESATTRS_WEBENGINE_NAME=Lv2.SEMRESATTRS_TELEMETRY_AUTO_VERSION=Lv2.SEMRESATTRS_TELEMETRY_SDK_VERSION=Lv2.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE=Lv2.SEMRESATTRS_TELEMETRY_SDK_NAME=Lv2.SEMRESATTRS_SERVICE_VERSION=Lv2.SEMRESATTRS_SERVICE_INSTANCE_ID=Lv2.SEMRESATTRS_SERVICE_NAMESPACE=Lv2.SEMRESATTRS_SERVICE_NAME=Lv2.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION=Lv2.SEMRESATTRS_PROCESS_RUNTIME_VERSION=Lv2.SEMRESATTRS_PROCESS_RUNTIME_NAME=Lv2.SEMRESATTRS_PROCESS_OWNER=Lv2.SEMRESATTRS_PROCESS_COMMAND_ARGS=Lv2.SEMRESATTRS_PROCESS_COMMAND_LINE=Lv2.SEMRESATTRS_PROCESS_COMMAND=Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_PATH=Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_NAME=Lv2.SEMRESATTRS_PROCESS_PID=Lv2.SEMRESATTRS_OS_VERSION=Lv2.SEMRESATTRS_OS_NAME=Lv2.SEMRESATTRS_OS_DESCRIPTION=Lv2.SEMRESATTRS_OS_TYPE=Lv2.SEMRESATTRS_K8S_CRONJOB_NAME=Lv2.SEMRESATTRS_K8S_CRONJOB_UID=Lv2.SEMRESATTRS_K8S_JOB_NAME=Lv2.SEMRESATTRS_K8S_JOB_UID=Lv2.SEMRESATTRS_K8S_DAEMONSET_NAME=Lv2.SEMRESATTRS_K8S_DAEMONSET_UID=void 0;Lv2.TelemetrySdkLanguageValues=Lv2.TELEMETRYSDKLANGUAGEVALUES_WEBJS=Lv2.TELEMETRYSDKLANGUAGEVALUES_RUBY=Lv2.TELEMETRYSDKLANGUAGEVALUES_PYTHON=Lv2.TELEMETRYSDKLANGUAGEVALUES_PHP=Lv2.TELEMETRYSDKLANGUAGEVALUES_NODEJS=Lv2.TELEMETRYSDKLANGUAGEVALUES_JAVA=Lv2.TELEMETRYSDKLANGUAGEVALUES_GO=Lv2.TELEMETRYSDKLANGUAGEVALUES_ERLANG=Lv2.TELEMETRYSDKLANGUAGEVALUES_DOTNET=Lv2.TELEMETRYSDKLANGUAGEVALUES_CPP=Lv2.OsTypeValues=Lv2.OSTYPEVALUES_Z_OS=Lv2.OSTYPEVALUES_SOLARIS=Lv2.OSTYPEVALUES_AIX=Lv2.OSTYPEVALUES_HPUX=Lv2.OSTYPEVALUES_DRAGONFLYBSD=Lv2.OSTYPEVALUES_OPENBSD=Lv2.OSTYPEVALUES_NETBSD=Lv2.OSTYPEVALUES_FREEBSD=Lv2.OSTYPEVALUES_DARWIN=Lv2.OSTYPEVALUES_LINUX=Lv2.OSTYPEVALUES_WINDOWS=Lv2.HostArchValues=Lv2.HOSTARCHVALUES_X86=Lv2.HOSTARCHVALUES_PPC64=Lv2.HOSTARCHVALUES_PPC32=Lv2.HOSTARCHVALUES_IA64=Lv2.HOSTARCHVALUES_ARM64=Lv2.HOSTARCHVALUES_ARM32=Lv2.HOSTARCHVALUES_AMD64=Lv2.AwsEcsLaunchtypeValues=Lv2.AWSECSLAUNCHTYPEVALUES_FARGATE=Lv2.AWSECSLAUNCHTYPEVALUES_EC2=Lv2.CloudPlatformValues=Lv2.CLOUDPLATFORMVALUES_GCP_APP_ENGINE=Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS=Lv2.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE=Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN=void 0;var Ku=MF0(),ey2="cloud.provider",A_2="cloud.account.id",B_2="cloud.region",Q_2="cloud.availability_zone",D_2="cloud.platform",Z_2="aws.ecs.container.arn",G_2="aws.ecs.cluster.arn",F_2="aws.ecs.launchtype",I_2="aws.ecs.task.arn",Y_2="aws.ecs.task.family",W_2="aws.ecs.task.revision",J_2="aws.eks.cluster.arn",X_2="aws.log.group.names",V_2="aws.log.group.arns",C_2="aws.log.stream.names",K_2="aws.log.stream.arns",H_2="container.name",z_2="container.id",E_2="container.runtime",U_2="container.image.name",w_2="container.image.tag",$_2="deployment.environment",q_2="device.id",N_2="device.model.identifier",L_2="device.model.name",M_2="faas.name",R_2="faas.id",O_2="faas.version",T_2="faas.instance",P_2="faas.max_memory",S_2="host.id",j_2="host.name",k_2="host.type",y_2="host.arch",__2="host.image.name",x_2="host.image.id",v_2="host.image.version",b_2="k8s.cluster.name",f_2="k8s.node.name",h_2="k8s.node.uid",g_2="k8s.namespace.name",u_2="k8s.pod.uid",m_2="k8s.pod.name",d_2="k8s.container.name",c_2="k8s.replicaset.uid",l_2="k8s.replicaset.name",p_2="k8s.deployment.uid",i_2="k8s.deployment.name",n_2="k8s.statefulset.uid",a_2="k8s.statefulset.name",s_2="k8s.daemonset.uid",r_2="k8s.daemonset.name",o_2="k8s.job.uid",t_2="k8s.job.name",e_2="k8s.cronjob.uid",Ax2="k8s.cronjob.name",Bx2="os.type",Qx2="os.description",Dx2="os.name",Zx2="os.version",Gx2="process.pid",Fx2="process.executable.name",Ix2="process.executable.path",Yx2="process.command",Wx2="process.command_line",Jx2="process.command_args",Xx2="process.owner",Vx2="process.runtime.name",Cx2="process.runtime.version",Kx2="process.runtime.description",Hx2="service.name",zx2="service.namespace",Ex2="service.instance.id",Ux2="service.version",wx2="telemetry.sdk.name",$x2="telemetry.sdk.language",qx2="telemetry.sdk.version",Nx2="telemetry.auto.version",Lx2="webengine.name",Mx2="webengine.version",Rx2="webengine.description";Lv2.SEMRESATTRS_CLOUD_PROVIDER=ey2;Lv2.SEMRESATTRS_CLOUD_ACCOUNT_ID=A_2;Lv2.SEMRESATTRS_CLOUD_REGION=B_2;Lv2.SEMRESATTRS_CLOUD_AVAILABILITY_ZONE=Q_2;Lv2.SEMRESATTRS_CLOUD_PLATFORM=D_2;Lv2.SEMRESATTRS_AWS_ECS_CONTAINER_ARN=Z_2;Lv2.SEMRESATTRS_AWS_ECS_CLUSTER_ARN=G_2;Lv2.SEMRESATTRS_AWS_ECS_LAUNCHTYPE=F_2;Lv2.SEMRESATTRS_AWS_ECS_TASK_ARN=I_2;Lv2.SEMRESATTRS_AWS_ECS_TASK_FAMILY=Y_2;Lv2.SEMRESATTRS_AWS_ECS_TASK_REVISION=W_2;Lv2.SEMRESATTRS_AWS_EKS_CLUSTER_ARN=J_2;Lv2.SEMRESATTRS_AWS_LOG_GROUP_NAMES=X_2;Lv2.SEMRESATTRS_AWS_LOG_GROUP_ARNS=V_2;Lv2.SEMRESATTRS_AWS_LOG_STREAM_NAMES=C_2;Lv2.SEMRESATTRS_AWS_LOG_STREAM_ARNS=K_2;Lv2.SEMRESATTRS_CONTAINER_NAME=H_2;Lv2.SEMRESATTRS_CONTAINER_ID=z_2;Lv2.SEMRESATTRS_CONTAINER_RUNTIME=E_2;Lv2.SEMRESATTRS_CONTAINER_IMAGE_NAME=U_2;Lv2.SEMRESATTRS_CONTAINER_IMAGE_TAG=w_2;Lv2.SEMRESATTRS_DEPLOYMENT_ENVIRONMENT=$_2;Lv2.SEMRESATTRS_DEVICE_ID=q_2;Lv2.SEMRESATTRS_DEVICE_MODEL_IDENTIFIER=N_2;Lv2.SEMRESATTRS_DEVICE_MODEL_NAME=L_2;Lv2.SEMRESATTRS_FAAS_NAME=M_2;Lv2.SEMRESATTRS_FAAS_ID=R_2;Lv2.SEMRESATTRS_FAAS_VERSION=O_2;Lv2.SEMRESATTRS_FAAS_INSTANCE=T_2;Lv2.SEMRESATTRS_FAAS_MAX_MEMORY=P_2;Lv2.SEMRESATTRS_HOST_ID=S_2;Lv2.SEMRESATTRS_HOST_NAME=j_2;Lv2.SEMRESATTRS_HOST_TYPE=k_2;Lv2.SEMRESATTRS_HOST_ARCH=y_2;Lv2.SEMRESATTRS_HOST_IMAGE_NAME=__2;Lv2.SEMRESATTRS_HOST_IMAGE_ID=x_2;Lv2.SEMRESATTRS_HOST_IMAGE_VERSION=v_2;Lv2.SEMRESATTRS_K8S_CLUSTER_NAME=b_2;Lv2.SEMRESATTRS_K8S_NODE_NAME=f_2;Lv2.SEMRESATTRS_K8S_NODE_UID=h_2;Lv2.SEMRESATTRS_K8S_NAMESPACE_NAME=g_2;Lv2.SEMRESATTRS_K8S_POD_UID=u_2;Lv2.SEMRESATTRS_K8S_POD_NAME=m_2;Lv2.SEMRESATTRS_K8S_CONTAINER_NAME=d_2;Lv2.SEMRESATTRS_K8S_REPLICASET_UID=c_2;Lv2.SEMRESATTRS_K8S_REPLICASET_NAME=l_2;Lv2.SEMRESATTRS_K8S_DEPLOYMENT_UID=p_2;Lv2.SEMRESATTRS_K8S_DEPLOYMENT_NAME=i_2;Lv2.SEMRESATTRS_K8S_STATEFULSET_UID=n_2;Lv2.SEMRESATTRS_K8S_STATEFULSET_NAME=a_2;Lv2.SEMRESATTRS_K8S_DAEMONSET_UID=s_2;Lv2.SEMRESATTRS_K8S_DAEMONSET_NAME=r_2;Lv2.SEMRESATTRS_K8S_JOB_UID=o_2;Lv2.SEMRESATTRS_K8S_JOB_NAME=t_2;Lv2.SEMRESATTRS_K8S_CRONJOB_UID=e_2;Lv2.SEMRESATTRS_K8S_CRONJOB_NAME=Ax2;Lv2.SEMRESATTRS_OS_TYPE=Bx2;Lv2.SEMRESATTRS_OS_DESCRIPTION=Qx2;Lv2.SEMRESATTRS_OS_NAME=Dx2;Lv2.SEMRESATTRS_OS_VERSION=Zx2;Lv2.SEMRESATTRS_PROCESS_PID=Gx2;Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_NAME=Fx2;Lv2.SEMRESATTRS_PROCESS_EXECUTABLE_PATH=Ix2;Lv2.SEMRESATTRS_PROCESS_COMMAND=Yx2;Lv2.SEMRESATTRS_PROCESS_COMMAND_LINE=Wx2;Lv2.SEMRESATTRS_PROCESS_COMMAND_ARGS=Jx2;Lv2.SEMRESATTRS_PROCESS_OWNER=Xx2;Lv2.SEMRESATTRS_PROCESS_RUNTIME_NAME=Vx2;Lv2.SEMRESATTRS_PROCESS_RUNTIME_VERSION=Cx2;Lv2.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION=Kx2;Lv2.SEMRESATTRS_SERVICE_NAME=Hx2;Lv2.SEMRESATTRS_SERVICE_NAMESPACE=zx2;Lv2.SEMRESATTRS_SERVICE_INSTANCE_ID=Ex2;Lv2.SEMRESATTRS_SERVICE_VERSION=Ux2;Lv2.SEMRESATTRS_TELEMETRY_SDK_NAME=wx2;Lv2.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE=$x2;Lv2.SEMRESATTRS_TELEMETRY_SDK_VERSION=qx2;Lv2.SEMRESATTRS_TELEMETRY_AUTO_VERSION=Nx2;Lv2.SEMRESATTRS_WEBENGINE_NAME=Lx2;Lv2.SEMRESATTRS_WEBENGINE_VERSION=Mx2;Lv2.SEMRESATTRS_WEBENGINE_DESCRIPTION=Rx2;Lv2.SemanticResourceAttributes=Ku.createConstMap([ey2,A_2,B_2,Q_2,D_2,Z_2,G_2,F_2,I_2,Y_2,W_2,J_2,X_2,V_2,C_2,K_2,H_2,z_2,E_2,U_2,w_2,$_2,q_2,N_2,L_2,M_2,R_2,O_2,T_2,P_2,S_2,j_2,k_2,y_2,__2,x_2,v_2,b_2,f_2,h_2,g_2,u_2,m_2,d_2,c_2,l_2,p_2,i_2,n_2,a_2,s_2,r_2,o_2,t_2,e_2,Ax2,Bx2,Qx2,Dx2,Zx2,Gx2,Fx2,Ix2,Yx2,Wx2,Jx2,Xx2,Vx2,Cx2,Kx2,Hx2,zx2,Ex2,Ux2,wx2,$x2,qx2,Nx2,Lx2,Mx2,Rx2]);var Ox2="alibaba_cloud",Tx2="aws",Px2="azure",Sx2="gcp";Lv2.CLOUDPROVIDERVALUES_ALIBABA_CLOUD=Ox2;Lv2.CLOUDPROVIDERVALUES_AWS=Tx2;Lv2.CLOUDPROVIDERVALUES_AZURE=Px2;Lv2.CLOUDPROVIDERVALUES_GCP=Sx2;Lv2.CloudProviderValues=Ku.createConstMap([Ox2,Tx2,Px2,Sx2]);var jx2="alibaba_cloud_ecs",kx2="alibaba_cloud_fc",yx2="aws_ec2",_x2="aws_ecs",xx2="aws_eks",vx2="aws_lambda",bx2="aws_elastic_beanstalk",fx2="azure_vm",hx2="azure_container_instances",gx2="azure_aks",ux2="azure_functions",mx2="azure_app_service",dx2="gcp_compute_engine",cx2="gcp_cloud_run",lx2="gcp_kubernetes_engine",px2="gcp_cloud_functions",ix2="gcp_app_engine";Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_ECS=jx2;Lv2.CLOUDPLATFORMVALUES_ALIBABA_CLOUD_FC=kx2;Lv2.CLOUDPLATFORMVALUES_AWS_EC2=yx2;Lv2.CLOUDPLATFORMVALUES_AWS_ECS=_x2;Lv2.CLOUDPLATFORMVALUES_AWS_EKS=xx2;Lv2.CLOUDPLATFORMVALUES_AWS_LAMBDA=vx2;Lv2.CLOUDPLATFORMVALUES_AWS_ELASTIC_BEANSTALK=bx2;Lv2.CLOUDPLATFORMVALUES_AZURE_VM=fx2;Lv2.CLOUDPLATFORMVALUES_AZURE_CONTAINER_INSTANCES=hx2;Lv2.CLOUDPLATFORMVALUES_AZURE_AKS=gx2;Lv2.CLOUDPLATFORMVALUES_AZURE_FUNCTIONS=ux2;Lv2.CLOUDPLATFORMVALUES_AZURE_APP_SERVICE=mx2;Lv2.CLOUDPLATFORMVALUES_GCP_COMPUTE_ENGINE=dx2;Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_RUN=cx2;Lv2.CLOUDPLATFORMVALUES_GCP_KUBERNETES_ENGINE=lx2;Lv2.CLOUDPLATFORMVALUES_GCP_CLOUD_FUNCTIONS=px2;Lv2.CLOUDPLATFORMVALUES_GCP_APP_ENGINE=ix2;Lv2.CloudPlatformValues=Ku.createConstMap([jx2,kx2,yx2,_x2,xx2,vx2,bx2,fx2,hx2,gx2,ux2,mx2,dx2,cx2,lx2,px2,ix2]);var nx2="ec2",ax2="fargate";Lv2.AWSECSLAUNCHTYPEVALUES_EC2=nx2;Lv2.AWSECSLAUNCHTYPEVALUES_FARGATE=ax2;Lv2.AwsEcsLaunchtypeValues=Ku.createConstMap([nx2,ax2]);var sx2="amd64",rx2="arm32",ox2="arm64",tx2="ia64",ex2="ppc32",Av2="ppc64",Bv2="x86";Lv2.HOSTARCHVALUES_AMD64=sx2;Lv2.HOSTARCHVALUES_ARM32=rx2;Lv2.HOSTARCHVALUES_ARM64=ox2;Lv2.HOSTARCHVALUES_IA64=tx2;Lv2.HOSTARCHVALUES_PPC32=ex2;Lv2.HOSTARCHVALUES_PPC64=Av2;Lv2.HOSTARCHVALUES_X86=Bv2;Lv2.HostArchValues=Ku.createConstMap([sx2,rx2,ox2,tx2,ex2,Av2,Bv2]);var Qv2="windows",Dv2="linux",Zv2="darwin",Gv2="freebsd",Fv2="netbsd",Iv2="openbsd",Yv2="dragonflybsd",Wv2="hpux",Jv2="aix",Xv2="solaris",Vv2="z_os";Lv2.OSTYPEVALUES_WINDOWS=Qv2;Lv2.OSTYPEVALUES_LINUX=Dv2;Lv2.OSTYPEVALUES_DARWIN=Zv2;Lv2.OSTYPEVALUES_FREEBSD=Gv2;Lv2.OSTYPEVALUES_NETBSD=Fv2;Lv2.OSTYPEVALUES_OPENBSD=Iv2;Lv2.OSTYPEVALUES_DRAGONFLYBSD=Yv2;Lv2.OSTYPEVALUES_HPUX=Wv2;Lv2.OSTYPEVALUES_AIX=Jv2;Lv2.OSTYPEVALUES_SOLARIS=Xv2;Lv2.OSTYPEVALUES_Z_OS=Vv2;Lv2.OsTypeValues=Ku.createConstMap([Qv2,Dv2,Zv2,Gv2,Fv2,Iv2,Yv2,Wv2,Jv2,Xv2,Vv2]);var Cv2="cpp",Kv2="dotnet",Hv2="erlang",zv2="go",Ev2="java",Uv2="nodejs",wv2="php",$v2="python",qv2="ruby",Nv2="webjs";Lv2.TELEMETRYSDKLANGUAGEVALUES_CPP=Cv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_DOTNET=Kv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_ERLANG=Hv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_GO=zv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_JAVA=Ev2;Lv2.TELEMETRYSDKLANGUAGEVALUES_NODEJS=Uv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_PHP=wv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_PYTHON=$v2;Lv2.TELEMETRYSDKLANGUAGEVALUES_RUBY=qv2;Lv2.TELEMETRYSDKLANGUAGEVALUES_WEBJS=Nv2;Lv2.TelemetrySdkLanguageValues=Ku.createConstMap([Cv2,Kv2,Hv2,zv2,Ev2,Uv2,wv2,$v2,qv2,Nv2])});
var UI0=E((ik5,dm2)=>{dm2.exports=M76;function M76(A,B){var Q=new Array(arguments.length-1),D=0,Z=2,G=!0;while(Z<arguments.length)Q[D++]=arguments[Z++];return new Promise(function F(I,Y){Q[D]=function W(J){if(G)if(G=!1,J)Y(J);else{var X=new Array(arguments.length-1),V=0;while(V<X.length)X[V++]=arguments[V];I.apply(null,X)}};try{A.apply(B||null,Q)}catch(W){if(G)G=!1,Y(W)}})}});
var Ul2=E((zl2)=>{Object.defineProperty(zl2,"__esModule",{value:!0});zl2.getHttpConfigurationDefaults=zl2.mergeOtlpHttpConfigurationWithDefaults=void 0;var Hl2=Z31(),LZ6=Kl2();function MZ6(A,B,Q){let D={...Q()},Z={};return()=>{if(B!=null)Object.assign(Z,B());if(A!=null)Object.assign(Z,A());return Object.assign(Z,D)}}function RZ6(A){if(A==null)return;try{return new URL(A),A}catch(B){throw new Error(`Configuration: Could not parse user-provided export URL: '${A}'`)}}function OZ6(A,B,Q){return{...Hl2.mergeOtlpSharedConfigurationWithDefaults(A,B,Q),headers:MZ6(LZ6.validateAndNormalizeHeaders(A.headers),B.headers,Q.headers),url:RZ6(A.url)??B.url??Q.url,agentOptions:A.agentOptions??B.agentOptions??Q.agentOptions}}zl2.mergeOtlpHttpConfigurationWithDefaults=OZ6;function TZ6(A,B){return{...Hl2.getSharedConfigurationDefaults(),headers:()=>A,url:"http://localhost:4318/"+B,agentOptions:{keepAlive:!0}}}zl2.getHttpConfigurationDefaults=TZ6});
var Uu2=E((zu2)=>{Object.defineProperty(zu2,"__esModule",{value:!0});zu2.ObservableRegistry=void 0;var e56=ZQ(),Cu2=dO1(),Ku2=Vu2(),D31=cw();class Hu2{_callbacks=[];_batchCallbacks=[];addCallback(A,B){if(this._findCallback(A,B)>=0)return;this._callbacks.push({callback:A,instrument:B})}removeCallback(A,B){let Q=this._findCallback(A,B);if(Q<0)return;this._callbacks.splice(Q,1)}addBatchCallback(A,B){let Q=new Set(B.filter(Cu2.isObservableInstrument));if(Q.size===0){e56.diag.error("BatchObservableCallback is not associated with valid instruments",B);return}if(this._findBatchCallback(A,Q)>=0)return;this._batchCallbacks.push({callback:A,instruments:Q})}removeBatchCallback(A,B){let Q=new Set(B.filter(Cu2.isObservableInstrument)),D=this._findBatchCallback(A,Q);if(D<0)return;this._batchCallbacks.splice(D,1)}async observe(A,B){let Q=this._observeCallbacks(A,B),D=this._observeBatchCallbacks(A,B);return(await D31.PromiseAllSettled([...Q,...D])).filter(D31.isPromiseAllSettledRejectionResult).map((F)=>F.reason)}_observeCallbacks(A,B){return this._callbacks.map(async({callback:Q,instrument:D})=>{let Z=new Ku2.ObservableResultImpl(D._descriptor.name,D._descriptor.valueType),G=Promise.resolve(Q(Z));if(B!=null)G=D31.callWithTimeout(G,B);await G,D._metricStorages.forEach((F)=>{F.record(Z._buffer,A)})})}_observeBatchCallbacks(A,B){return this._batchCallbacks.map(async({callback:Q,instruments:D})=>{let Z=new Ku2.BatchObservableResultImpl,G=Promise.resolve(Q(Z));if(B!=null)G=D31.callWithTimeout(G,B);await G,D.forEach((F)=>{let I=Z._buffer.get(F);if(I==null)return;F._metricStorages.forEach((Y)=>{Y.record(I,A)})})})}_findCallback(A,B){return this._callbacks.findIndex((Q)=>{return Q.callback===A&&Q.instrument===B})}_findBatchCallback(A,B){return this._batchCallbacks.findIndex((Q)=>{return Q.callback===A&&D31.setEquals(Q.instruments,B)})}}zu2.ObservableRegistry=Hu2});
var VO1=E((YM2)=>{Object.defineProperty(YM2,"__esModule",{value:!0});YM2.NonRecordingSpan=void 0;var oi4=XO1();class IM2{constructor(A=oi4.INVALID_SPAN_CONTEXT){this._spanContext=A}spanContext(){return this._spanContext}setAttribute(A,B){return this}setAttributes(A){return this}addEvent(A,B){return this}addLink(A){return this}addLinks(A){return this}setStatus(A){return this}updateName(A){return this}end(A){}isRecording(){return!1}recordException(A,B){}}YM2.NonRecordingSpan=IM2});
var VR2=E((JR2)=>{Object.defineProperty(JR2,"__esModule",{value:!0});JR2.metrics=void 0;var in4=WR2();JR2.metrics=in4.MetricsAPI.getInstance()});
var Vc2=E((Jc2)=>{Object.defineProperty(Jc2,"__esModule",{value:!0});Jc2.ProtobufMetricsSerializer=void 0;var Wc2=BT1(),zD6=dI0(),ED6=Wc2.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceResponse,UD6=Wc2.opentelemetry.proto.collector.metrics.v1.ExportMetricsServiceRequest;Jc2.ProtobufMetricsSerializer={serializeRequest:(A)=>{let B=zD6.createExportMetricsServiceRequest([A]);return UD6.encode(B).finish()},deserializeResponse:(A)=>{return ED6.decode(A)}}});
var Vf2=E((Jf2)=>{Object.defineProperty(Jf2,"__esModule",{value:!0});Jf2.LastValueAggregator=Jf2.LastValueAccumulation=void 0;var f66=To(),n51=y3(),h66=__();class a51{startTime;_current;sampleTime;constructor(A,B=0,Q=[0,0]){this.startTime=A,this._current=B,this.sampleTime=Q}record(A){this._current=A,this.sampleTime=n51.millisToHrTime(Date.now())}setStartTime(A){this.startTime=A}toPointValue(){return this._current}}Jf2.LastValueAccumulation=a51;class Wf2{kind=f66.AggregatorKind.LAST_VALUE;createAccumulation(A){return new a51(A)}merge(A,B){let Q=n51.hrTimeToMicroseconds(B.sampleTime)>=n51.hrTimeToMicroseconds(A.sampleTime)?B:A;return new a51(A.startTime,Q.toPointValue(),Q.sampleTime)}diff(A,B){let Q=n51.hrTimeToMicroseconds(B.sampleTime)>=n51.hrTimeToMicroseconds(A.sampleTime)?B:A;return new a51(B.startTime,Q.toPointValue(),Q.sampleTime)}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:h66.DataPointType.GAUGE,dataPoints:Q.map(([Z,G])=>{return{attributes:Z,startTime:G.startTime,endTime:D,value:G.toPointValue()}})}}}Jf2.LastValueAggregator=Wf2});
var Vu2=E((Ju2)=>{Object.defineProperty(Ju2,"__esModule",{value:!0});Ju2.BatchObservableResultImpl=Ju2.ObservableResultImpl=void 0;var fo=ZQ(),Iu2=B31(),o56=dO1();class Yu2{_instrumentName;_valueType;_buffer=new Iu2.AttributeHashMap;constructor(A,B){this._instrumentName=A,this._valueType=B}observe(A,B={}){if(typeof A!=="number"){fo.diag.warn(`non-number value provided to metric ${this._instrumentName}: ${A}`);return}if(this._valueType===fo.ValueType.INT&&!Number.isInteger(A)){if(fo.diag.warn(`INT value type cannot accept a floating-point value for ${this._instrumentName}, ignoring the fractional digits.`),A=Math.trunc(A),!Number.isInteger(A))return}this._buffer.set(B,A)}}Ju2.ObservableResultImpl=Yu2;class Wu2{_buffer=new Map;observe(A,B,Q={}){if(!o56.isObservableInstrument(A))return;let D=this._buffer.get(A);if(D==null)D=new Iu2.AttributeHashMap,this._buffer.set(A,D);if(typeof B!=="number"){fo.diag.warn(`non-number value provided to metric ${A._descriptor.name}: ${B}`);return}if(A._descriptor.valueType===fo.ValueType.INT&&!Number.isInteger(B)){if(fo.diag.warn(`INT value type cannot accept a floating-point value for ${A._descriptor.name}, ignoring the fractional digits.`),B=Math.trunc(B),!Number.isInteger(B))return}D.set(Q,B)}}Ju2.BatchObservableResultImpl=Wu2});
var WR2=E((IR2)=>{Object.defineProperty(IR2,"__esModule",{value:!0});IR2.MetricsAPI=void 0;var pn4=GR2(),cG0=Wu(),FR2=Ju(),lG0="metrics";class pG0{constructor(){}static getInstance(){if(!this._instance)this._instance=new pG0;return this._instance}setGlobalMeterProvider(A){return cG0.registerGlobal(lG0,A,FR2.DiagAPI.instance())}getMeterProvider(){return cG0.getGlobal(lG0)||pn4.NOOP_METER_PROVIDER}getMeter(A,B,Q){return this.getMeterProvider().getMeter(A,B,Q)}disable(){cG0.unregisterGlobal(lG0,FR2.DiagAPI.instance())}}IR2.MetricsAPI=pG0});
var WT2=E((IT2)=>{Object.defineProperty(IT2,"__esModule",{value:!0});IT2.ExponentialHistogramAggregator=IT2.ExponentialHistogramAccumulation=void 0;var xs4=To(),d51=__(),vs4=ZQ(),ZT2=xO2(),GT2=DT2(),bs4=wO1();class jo{low;high;static combine(A,B){return new jo(Math.min(A.low,B.low),Math.max(A.high,B.high))}constructor(A,B){this.low=A,this.high=B}}var fs4=20,hs4=160,EF0=2;class qO1{startTime;_maxSize;_recordMinMax;_sum;_count;_zeroCount;_min;_max;_positive;_negative;_mapping;constructor(A=A,B=hs4,Q=!0,D=0,Z=0,G=0,F=Number.POSITIVE_INFINITY,I=Number.NEGATIVE_INFINITY,Y=new ZT2.Buckets,W=new ZT2.Buckets,J=GT2.getMapping(fs4)){if(this.startTime=A,this._maxSize=B,this._recordMinMax=Q,this._sum=D,this._count=Z,this._zeroCount=G,this._min=F,this._max=I,this._positive=Y,this._negative=W,this._mapping=J,this._maxSize<EF0)vs4.diag.warn(`Exponential Histogram Max Size set to ${this._maxSize},                 changing to the minimum size of: ${EF0}`),this._maxSize=EF0}record(A){this.updateByIncrement(A,1)}setStartTime(A){this.startTime=A}toPointValue(){return{hasMinMax:this._recordMinMax,min:this.min,max:this.max,sum:this.sum,positive:{offset:this.positive.offset,bucketCounts:this.positive.counts()},negative:{offset:this.negative.offset,bucketCounts:this.negative.counts()},count:this.count,scale:this.scale,zeroCount:this.zeroCount}}get sum(){return this._sum}get min(){return this._min}get max(){return this._max}get count(){return this._count}get zeroCount(){return this._zeroCount}get scale(){if(this._count===this._zeroCount)return 0;return this._mapping.scale}get positive(){return this._positive}get negative(){return this._negative}updateByIncrement(A,B){if(Number.isNaN(A))return;if(A>this._max)this._max=A;if(A<this._min)this._min=A;if(this._count+=B,A===0){this._zeroCount+=B;return}if(this._sum+=A*B,A>0)this._updateBuckets(this._positive,A,B);else this._updateBuckets(this._negative,-A,B)}merge(A){if(this._count===0)this._min=A.min,this._max=A.max;else if(A.count!==0){if(A.min<this.min)this._min=A.min;if(A.max>this.max)this._max=A.max}this.startTime=A.startTime,this._sum+=A.sum,this._count+=A.count,this._zeroCount+=A.zeroCount;let B=this._minScale(A);this._downscale(this.scale-B),this._mergeBuckets(this.positive,A,A.positive,B),this._mergeBuckets(this.negative,A,A.negative,B)}diff(A){this._min=1/0,this._max=-1/0,this._sum-=A.sum,this._count-=A.count,this._zeroCount-=A.zeroCount;let B=this._minScale(A);this._downscale(this.scale-B),this._diffBuckets(this.positive,A,A.positive,B),this._diffBuckets(this.negative,A,A.negative,B)}clone(){return new qO1(this.startTime,this._maxSize,this._recordMinMax,this._sum,this._count,this._zeroCount,this._min,this._max,this.positive.clone(),this.negative.clone(),this._mapping)}_updateBuckets(A,B,Q){let D=this._mapping.mapToIndex(B),Z=!1,G=0,F=0;if(A.length===0)A.indexStart=D,A.indexEnd=A.indexStart,A.indexBase=A.indexStart;else if(D<A.indexStart&&A.indexEnd-D>=this._maxSize)Z=!0,F=D,G=A.indexEnd;else if(D>A.indexEnd&&D-A.indexStart>=this._maxSize)Z=!0,F=A.indexStart,G=D;if(Z){let I=this._changeScale(G,F);this._downscale(I),D=this._mapping.mapToIndex(B)}this._incrementIndexBy(A,D,Q)}_incrementIndexBy(A,B,Q){if(Q===0)return;if(A.length===0)A.indexStart=A.indexEnd=A.indexBase=B;if(B<A.indexStart){let Z=A.indexEnd-B;if(Z>=A.backing.length)this._grow(A,Z+1);A.indexStart=B}else if(B>A.indexEnd){let Z=B-A.indexStart;if(Z>=A.backing.length)this._grow(A,Z+1);A.indexEnd=B}let D=B-A.indexBase;if(D<0)D+=A.backing.length;A.incrementBucket(D,Q)}_grow(A,B){let Q=A.backing.length,D=A.indexBase-A.indexStart,Z=Q-D,G=bs4.nextGreaterSquare(B);if(G>this._maxSize)G=this._maxSize;let F=G-D;A.backing.growTo(G,Z,F)}_changeScale(A,B){let Q=0;while(A-B>=this._maxSize)A>>=1,B>>=1,Q++;return Q}_downscale(A){if(A===0)return;if(A<0)throw new Error(`impossible change of scale: ${this.scale}`);let B=this._mapping.scale-A;this._positive.downscale(A),this._negative.downscale(A),this._mapping=GT2.getMapping(B)}_minScale(A){let B=Math.min(this.scale,A.scale),Q=jo.combine(this._highLowAtScale(this.positive,this.scale,B),this._highLowAtScale(A.positive,A.scale,B)),D=jo.combine(this._highLowAtScale(this.negative,this.scale,B),this._highLowAtScale(A.negative,A.scale,B));return Math.min(B-this._changeScale(Q.high,Q.low),B-this._changeScale(D.high,D.low))}_highLowAtScale(A,B,Q){if(A.length===0)return new jo(0,-1);let D=B-Q;return new jo(A.indexStart>>D,A.indexEnd>>D)}_mergeBuckets(A,B,Q,D){let Z=Q.offset,G=B.scale-D;for(let F=0;F<Q.length;F++)this._incrementIndexBy(A,Z+F>>G,Q.at(F))}_diffBuckets(A,B,Q,D){let Z=Q.offset,G=B.scale-D;for(let F=0;F<Q.length;F++){let Y=(Z+F>>G)-A.indexBase;if(Y<0)Y+=A.backing.length;A.decrementBucket(Y,Q.at(F))}A.trim()}}IT2.ExponentialHistogramAccumulation=qO1;class FT2{_maxSize;_recordMinMax;kind=xs4.AggregatorKind.EXPONENTIAL_HISTOGRAM;constructor(A,B){this._maxSize=A,this._recordMinMax=B}createAccumulation(A){return new qO1(A,this._maxSize,this._recordMinMax)}merge(A,B){let Q=B.clone();return Q.merge(A),Q}diff(A,B){let Q=B.clone();return Q.diff(A),Q}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:d51.DataPointType.EXPONENTIAL_HISTOGRAM,dataPoints:Q.map(([Z,G])=>{let F=G.toPointValue(),I=A.type===d51.InstrumentType.GAUGE||A.type===d51.InstrumentType.UP_DOWN_COUNTER||A.type===d51.InstrumentType.OBSERVABLE_GAUGE||A.type===d51.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:Z,startTime:G.startTime,endTime:D,value:{min:F.hasMinMax?F.min:void 0,max:F.hasMinMax?F.max:void 0,sum:!I?F.sum:void 0,positive:{offset:F.positive.offset,bucketCounts:F.positive.bucketCounts},negative:{offset:F.negative.offset,bucketCounts:F.negative.bucketCounts},count:F.count,scale:F.scale,zeroCount:F.zeroCount}}})}}}IT2.ExponentialHistogramAggregator=FT2});
var Wg2=E((b_)=>{Object.defineProperty(b_,"__esModule",{value:!0});b_.noopDetector=b_.serviceInstanceIdDetector=b_.processDetector=b_.osDetector=b_.hostDetector=b_.envDetector=void 0;var I56=Hh2();Object.defineProperty(b_,"envDetector",{enumerable:!0,get:function(){return I56.envDetector}});var uO1=Gg2();Object.defineProperty(b_,"hostDetector",{enumerable:!0,get:function(){return uO1.hostDetector}});Object.defineProperty(b_,"osDetector",{enumerable:!0,get:function(){return uO1.osDetector}});Object.defineProperty(b_,"processDetector",{enumerable:!0,get:function(){return uO1.processDetector}});Object.defineProperty(b_,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return uO1.serviceInstanceIdDetector}});var Y56=Yg2();Object.defineProperty(b_,"noopDetector",{enumerable:!0,get:function(){return Y56.noopDetector}})});
var Wu=E((YL2)=>{Object.defineProperty(YL2,"__esModule",{value:!0});YL2.unregisterGlobal=YL2.getGlobal=YL2.registerGlobal=void 0;var Ji4=AL2(),Lo=YG0(),Xi4=IL2(),Vi4=Lo.VERSION.split(".")[0],x51=Symbol.for(`opentelemetry.js.api.${Vi4}`),v51=Ji4._globalThis;function Ci4(A,B,Q,D=!1){var Z;let G=v51[x51]=(Z=v51[x51])!==null&&Z!==void 0?Z:{version:Lo.VERSION};if(!D&&G[A]){let F=new Error(`@opentelemetry/api: Attempted duplicate registration of API: ${A}`);return Q.error(F.stack||F.message),!1}if(G.version!==Lo.VERSION){let F=new Error(`@opentelemetry/api: Registration of version v${G.version} for ${A} does not match previously registered API v${Lo.VERSION}`);return Q.error(F.stack||F.message),!1}return G[A]=B,Q.debug(`@opentelemetry/api: Registered a global for ${A} v${Lo.VERSION}.`),!0}YL2.registerGlobal=Ci4;function Ki4(A){var B,Q;let D=(B=v51[x51])===null||B===void 0?void 0:B.version;if(!D||!Xi4.isCompatible(D))return;return(Q=v51[x51])===null||Q===void 0?void 0:Q[A]}YL2.getGlobal=Ki4;function Hi4(A,B){B.debug(`@opentelemetry/api: Unregistering a global for ${A} v${Lo.VERSION}.`);let Q=v51[x51];if(Q)delete Q[A]}YL2.unregisterGlobal=Hi4});
var XG0=E((TL2)=>{Object.defineProperty(TL2,"__esModule",{value:!0});TL2.baggageEntryMetadataFromString=TL2.createBaggage=void 0;var Mi4=Ju(),Ri4=LL2(),Oi4=OL2(),Ti4=Mi4.DiagAPI.instance();function Pi4(A={}){return new Ri4.BaggageImpl(new Map(Object.entries(A)))}TL2.createBaggage=Pi4;function Si4(A){if(typeof A!=="string")Ti4.error(`Cannot create baggage metadata from unknown type: ${typeof A}`),A="";return{__TYPE__:Oi4.baggageEntryMetadataSymbol,toString(){return A}}}TL2.baggageEntryMetadataFromString=Si4});
var XI0=E((Xm2)=>{Object.defineProperty(Xm2,"__esModule",{value:!0});Xm2.AggregationTemporalityPreference=void 0;var p36;(function(A){A[A.DELTA=0]="DELTA",A[A.CUMULATIVE=1]="CUMULATIVE",A[A.LOWMEMORY=2]="LOWMEMORY"})(p36=Xm2.AggregationTemporalityPreference||(Xm2.AggregationTemporalityPreference={}))});
var XO1=E((DM2)=>{Object.defineProperty(DM2,"__esModule",{value:!0});DM2.INVALID_SPAN_CONTEXT=DM2.INVALID_TRACEID=DM2.INVALID_SPANID=void 0;var ri4=PG0();DM2.INVALID_SPANID="0000000000000000";DM2.INVALID_TRACEID="00000000000000000000000000000000";DM2.INVALID_SPAN_CONTEXT={traceId:DM2.INVALID_TRACEID,spanId:DM2.INVALID_SPANID,traceFlags:ri4.TraceFlags.NONE}});
var YG0=E((BL2)=>{Object.defineProperty(BL2,"__esModule",{value:!0});BL2.VERSION=void 0;BL2.VERSION="1.9.0"});
var YO1=E((KL2)=>{Object.defineProperty(KL2,"__esModule",{value:!0});KL2.DiagLogLevel=void 0;var wi4;(function(A){A[A.NONE=0]="NONE",A[A.ERROR=30]="ERROR",A[A.WARN=50]="WARN",A[A.INFO=60]="INFO",A[A.DEBUG=70]="DEBUG",A[A.VERBOSE=80]="VERBOSE",A[A.ALL=9999]="ALL"})(wi4=KL2.DiagLogLevel||(KL2.DiagLogLevel={}))});
var Yd2=E((tk5,Id2)=>{Id2.exports=CI;var G31=pL();function CI(A,B){this.lo=A>>>0,this.hi=B>>>0}var Pu=CI.zero=new CI(0,0);Pu.toNumber=function(){return 0};Pu.zzEncode=Pu.zzDecode=function(){return this};Pu.length=function(){return 1};var T76=CI.zeroHash="\x00\x00\x00\x00\x00\x00\x00\x00";CI.fromNumber=function A(B){if(B===0)return Pu;var Q=B<0;if(Q)B=-B;var D=B>>>0,Z=(B-D)/4294967296>>>0;if(Q){if(Z=~Z>>>0,D=~D>>>0,++D>4294967295){if(D=0,++Z>4294967295)Z=0}}return new CI(D,Z)};CI.from=function A(B){if(typeof B==="number")return CI.fromNumber(B);if(G31.isString(B))if(G31.Long)B=G31.Long.fromString(B);else return CI.fromNumber(parseInt(B,10));return B.low||B.high?new CI(B.low>>>0,B.high>>>0):Pu};CI.prototype.toNumber=function A(B){if(!B&&this.hi>>>31){var Q=~this.lo+1>>>0,D=~this.hi>>>0;if(!Q)D=D+1>>>0;return-(Q+D*4294967296)}return this.lo+this.hi*4294967296};CI.prototype.toLong=function A(B){return G31.Long?new G31.Long(this.lo|0,this.hi|0,Boolean(B)):{low:this.lo|0,high:this.hi|0,unsigned:Boolean(B)}};var g_=String.prototype.charCodeAt;CI.fromHash=function A(B){if(B===T76)return Pu;return new CI((g_.call(B,0)|g_.call(B,1)<<8|g_.call(B,2)<<16|g_.call(B,3)<<24)>>>0,(g_.call(B,4)|g_.call(B,5)<<8|g_.call(B,6)<<16|g_.call(B,7)<<24)>>>0)};CI.prototype.toHash=function A(){return String.fromCharCode(this.lo&255,this.lo>>>8&255,this.lo>>>16&255,this.lo>>>24,this.hi&255,this.hi>>>8&255,this.hi>>>16&255,this.hi>>>24)};CI.prototype.zzEncode=function A(){var B=this.hi>>31;return this.hi=((this.hi<<1|this.lo>>>31)^B)>>>0,this.lo=(this.lo<<1^B)>>>0,this};CI.prototype.zzDecode=function A(){var B=-(this.lo&1);return this.lo=((this.lo>>>1|this.hi<<31)^B)>>>0,this.hi=(this.hi>>>1^B)>>>0,this};CI.prototype.length=function A(){var B=this.lo,Q=(this.lo>>>28|this.hi<<4)>>>0,D=this.hi>>>24;return D===0?Q===0?B<16384?B<128?1:2:B<2097152?3:4:Q<16384?Q<128?5:6:Q<2097152?7:8:D<128?9:10}});
var Yg2=E((Fg2)=>{Object.defineProperty(Fg2,"__esModule",{value:!0});Fg2.noopDetector=Fg2.NoopDetector=void 0;class AI0{detect(){return{attributes:{}}}}Fg2.NoopDetector=AI0;Fg2.noopDetector=new AI0});
var Ym2=E((Fm2)=>{Object.defineProperty(Fm2,"__esModule",{value:!0});Fm2.MeterProvider=void 0;var iO1=ZQ(),y36=mO1(),_36=bu2(),x36=uu2(),v36=Zm2();class Gm2{_sharedState;_shutdown=!1;constructor(A){if(this._sharedState=new _36.MeterProviderSharedState(A?.resource??y36.defaultResource()),A?.views!=null&&A.views.length>0)for(let B of A.views)this._sharedState.viewRegistry.addView(new v36.View(B));if(A?.readers!=null&&A.readers.length>0)for(let B of A.readers){let Q=new x36.MetricCollector(this._sharedState,B);B.setMetricProducer(Q),this._sharedState.metricCollectors.push(Q)}}getMeter(A,B="",Q={}){if(this._shutdown)return iO1.diag.warn("A shutdown MeterProvider cannot provide a Meter"),iO1.createNoopMeter();return this._sharedState.getMeterSharedState({name:A,version:B,schemaUrl:Q.schemaUrl}).meter}async shutdown(A){if(this._shutdown){iO1.diag.warn("shutdown may only be called once per MeterProvider");return}this._shutdown=!0,await Promise.all(this._sharedState.metricCollectors.map((B)=>{return B.shutdown(A)}))}async forceFlush(A){if(this._shutdown){iO1.diag.warn("invalid attempt to force flush after MeterProvider shutdown");return}await Promise.all(this._sharedState.metricCollectors.map((B)=>{return B.forceFlush(A)}))}}Fm2.MeterProvider=Gm2});
var Z31=E(($m2)=>{Object.defineProperty($m2,"__esModule",{value:!0});$m2.getSharedConfigurationDefaults=$m2.mergeOtlpSharedConfigurationWithDefaults=$m2.wrapStaticHeadersInFunction=$m2.validateTimeoutMillis=void 0;function wm2(A){if(Number.isFinite(A)&&A>0)return A;throw new Error(`Configuration: timeoutMillis is invalid, expected number greater than 0 (actual: '${A}')`)}$m2.validateTimeoutMillis=wm2;function i36(A){if(A==null)return;return()=>A}$m2.wrapStaticHeadersInFunction=i36;function n36(A,B,Q){return{timeoutMillis:wm2(A.timeoutMillis??B.timeoutMillis??Q.timeoutMillis),concurrencyLimit:A.concurrencyLimit??B.concurrencyLimit??Q.concurrencyLimit,compression:A.compression??B.compression??Q.compression}}$m2.mergeOtlpSharedConfigurationWithDefaults=n36;function a36(){return{timeoutMillis:1e4,concurrencyLimit:30,compression:"none"}}$m2.getSharedConfigurationDefaults=a36});
var ZQ=E((d5)=>{Object.defineProperty(d5,"__esModule",{value:!0});d5.trace=d5.propagation=d5.metrics=d5.diag=d5.context=d5.INVALID_SPAN_CONTEXT=d5.INVALID_TRACEID=d5.INVALID_SPANID=d5.isValidSpanId=d5.isValidTraceId=d5.isSpanContextValid=d5.createTraceState=d5.TraceFlags=d5.SpanStatusCode=d5.SpanKind=d5.SamplingDecision=d5.ProxyTracerProvider=d5.ProxyTracer=d5.defaultTextMapSetter=d5.defaultTextMapGetter=d5.ValueType=d5.createNoopMeter=d5.DiagLogLevel=d5.DiagConsoleLogger=d5.ROOT_CONTEXT=d5.createContextKey=d5.baggageEntryMetadataFromString=void 0;var Fa4=XG0();Object.defineProperty(d5,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return Fa4.baggageEntryMetadataFromString}});var hR2=f51();Object.defineProperty(d5,"createContextKey",{enumerable:!0,get:function(){return hR2.createContextKey}});Object.defineProperty(d5,"ROOT_CONTEXT",{enumerable:!0,get:function(){return hR2.ROOT_CONTEXT}});var Ia4=xL2();Object.defineProperty(d5,"DiagConsoleLogger",{enumerable:!0,get:function(){return Ia4.DiagConsoleLogger}});var Ya4=YO1();Object.defineProperty(d5,"DiagLogLevel",{enumerable:!0,get:function(){return Ya4.DiagLogLevel}});var Wa4=qG0();Object.defineProperty(d5,"createNoopMeter",{enumerable:!0,get:function(){return Wa4.createNoopMeter}});var Ja4=iL2();Object.defineProperty(d5,"ValueType",{enumerable:!0,get:function(){return Ja4.ValueType}});var gR2=LG0();Object.defineProperty(d5,"defaultTextMapGetter",{enumerable:!0,get:function(){return gR2.defaultTextMapGetter}});Object.defineProperty(d5,"defaultTextMapSetter",{enumerable:!0,get:function(){return gR2.defaultTextMapSetter}});var Xa4=vG0();Object.defineProperty(d5,"ProxyTracer",{enumerable:!0,get:function(){return Xa4.ProxyTracer}});var Va4=bG0();Object.defineProperty(d5,"ProxyTracerProvider",{enumerable:!0,get:function(){return Va4.ProxyTracerProvider}});var Ca4=_M2();Object.defineProperty(d5,"SamplingDecision",{enumerable:!0,get:function(){return Ca4.SamplingDecision}});var Ka4=vM2();Object.defineProperty(d5,"SpanKind",{enumerable:!0,get:function(){return Ka4.SpanKind}});var Ha4=fM2();Object.defineProperty(d5,"SpanStatusCode",{enumerable:!0,get:function(){return Ha4.SpanStatusCode}});var za4=PG0();Object.defineProperty(d5,"TraceFlags",{enumerable:!0,get:function(){return za4.TraceFlags}});var Ea4=rM2();Object.defineProperty(d5,"createTraceState",{enumerable:!0,get:function(){return Ea4.createTraceState}});var eG0=CO1();Object.defineProperty(d5,"isSpanContextValid",{enumerable:!0,get:function(){return eG0.isSpanContextValid}});Object.defineProperty(d5,"isValidTraceId",{enumerable:!0,get:function(){return eG0.isValidTraceId}});Object.defineProperty(d5,"isValidSpanId",{enumerable:!0,get:function(){return eG0.isValidSpanId}});var AF0=XO1();Object.defineProperty(d5,"INVALID_SPANID",{enumerable:!0,get:function(){return AF0.INVALID_SPANID}});Object.defineProperty(d5,"INVALID_TRACEID",{enumerable:!0,get:function(){return AF0.INVALID_TRACEID}});Object.defineProperty(d5,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return AF0.INVALID_SPAN_CONTEXT}});var uR2=eM2();Object.defineProperty(d5,"context",{enumerable:!0,get:function(){return uR2.context}});var mR2=QR2();Object.defineProperty(d5,"diag",{enumerable:!0,get:function(){return mR2.diag}});var dR2=VR2();Object.defineProperty(d5,"metrics",{enumerable:!0,get:function(){return dR2.metrics}});var cR2=PR2();Object.defineProperty(d5,"propagation",{enumerable:!0,get:function(){return cR2.propagation}});var lR2=fR2();Object.defineProperty(d5,"trace",{enumerable:!0,get:function(){return lR2.trace}});d5.default={context:uR2.context,diag:mR2.diag,metrics:dR2.metrics,propagation:cR2.propagation,trace:lR2.trace}});
var Zd2=E((Dd2)=>{var qI0=Dd2;qI0.length=function A(B){var Q=0,D=0;for(var Z=0;Z<B.length;++Z)if(D=B.charCodeAt(Z),D<128)Q+=1;else if(D<2048)Q+=2;else if((D&64512)===55296&&(B.charCodeAt(Z+1)&64512)===56320)++Z,Q+=4;else Q+=3;return Q};qI0.read=function A(B,Q,D){var Z=D-Q;if(Z<1)return"";var G=null,F=[],I=0,Y;while(Q<D){if(Y=B[Q++],Y<128)F[I++]=Y;else if(Y>191&&Y<224)F[I++]=(Y&31)<<6|B[Q++]&63;else if(Y>239&&Y<365)Y=((Y&7)<<18|(B[Q++]&63)<<12|(B[Q++]&63)<<6|B[Q++]&63)-65536,F[I++]=55296+(Y>>10),F[I++]=56320+(Y&1023);else F[I++]=(Y&15)<<12|(B[Q++]&63)<<6|B[Q++]&63;if(I>8191)(G||(G=[])).push(String.fromCharCode.apply(String,F)),I=0}if(G){if(I)G.push(String.fromCharCode.apply(String,F.slice(0,I)));return G.join("")}return String.fromCharCode.apply(String,F.slice(0,I))};qI0.write=function A(B,Q,D){var Z=D,G,F;for(var I=0;I<B.length;++I)if(G=B.charCodeAt(I),G<128)Q[D++]=G;else if(G<2048)Q[D++]=G>>6|192,Q[D++]=G&63|128;else if((G&64512)===55296&&((F=B.charCodeAt(I+1))&64512)===56320)G=65536+((G&1023)<<10)+(F&1023),++I,Q[D++]=G>>18|240,Q[D++]=G>>12&63|128,Q[D++]=G>>6&63|128,Q[D++]=G&63|128;else Q[D++]=G>>12|224,Q[D++]=G>>6&63|128,Q[D++]=G&63|128;return D-Z}});
var Zg2=E((yo)=>{Object.defineProperty(yo,"__esModule",{value:!0});yo.serviceInstanceIdDetector=yo.processDetector=yo.osDetector=yo.hostDetector=void 0;var A56=ch2();Object.defineProperty(yo,"hostDetector",{enumerable:!0,get:function(){return A56.hostDetector}});var B56=sh2();Object.defineProperty(yo,"osDetector",{enumerable:!0,get:function(){return B56.osDetector}});var Q56=eh2();Object.defineProperty(yo,"processDetector",{enumerable:!0,get:function(){return Q56.processDetector}});var D56=Dg2();Object.defineProperty(yo,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return D56.serviceInstanceIdDetector}})});
var Zm2=E((Qm2)=>{Object.defineProperty(Qm2,"__esModule",{value:!0});Qm2.View=void 0;var T36=pO1(),eu2=lO1(),P36=au2(),S36=tu2(),Am2=r51();function j36(A){return A.instrumentName==null&&A.instrumentType==null&&A.instrumentUnit==null&&A.meterName==null&&A.meterVersion==null&&A.meterSchemaUrl==null}function k36(A){if(j36(A))throw new Error("Cannot create view with no selector arguments supplied");if(A.name!=null&&(A?.instrumentName==null||T36.PatternPredicate.hasWildcard(A.instrumentName)))throw new Error("Views with a specified name must be declared with an instrument selector that selects at most one instrument per meter.")}class Bm2{name;description;aggregation;attributesProcessor;instrumentSelector;meterSelector;aggregationCardinalityLimit;constructor(A){if(k36(A),A.attributesProcessors!=null)this.attributesProcessor=eu2.createMultiAttributesProcessor(A.attributesProcessors);else this.attributesProcessor=eu2.createNoopAttributesProcessor();this.name=A.name,this.description=A.description,this.aggregation=Am2.toAggregation(A.aggregation??{type:Am2.AggregationType.DEFAULT}),this.instrumentSelector=new P36.InstrumentSelector({name:A.instrumentName,type:A.instrumentType,unit:A.instrumentUnit}),this.meterSelector=new S36.MeterSelector({name:A.meterName,version:A.meterVersion,schemaUrl:A.meterSchemaUrl}),this.aggregationCardinalityLimit=A.aggregationCardinalityLimit}}Qm2.View=Bm2});
var _I0=E((Fy5,jd2)=>{jd2.exports={}});
var _M2=E((yM2)=>{Object.defineProperty(yM2,"__esModule",{value:!0});yM2.SamplingDecision=void 0;var Tn4;(function(A){A[A.NOT_RECORD=0]="NOT_RECORD",A[A.RECORD=1]="RECORD",A[A.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"})(Tn4=yM2.SamplingDecision||(yM2.SamplingDecision={}))});
var __=E(($O2)=>{Object.defineProperty($O2,"__esModule",{value:!0});$O2.DataPointType=$O2.InstrumentType=void 0;var da4;(function(A){A.COUNTER="COUNTER",A.GAUGE="GAUGE",A.HISTOGRAM="HISTOGRAM",A.UP_DOWN_COUNTER="UP_DOWN_COUNTER",A.OBSERVABLE_COUNTER="OBSERVABLE_COUNTER",A.OBSERVABLE_GAUGE="OBSERVABLE_GAUGE",A.OBSERVABLE_UP_DOWN_COUNTER="OBSERVABLE_UP_DOWN_COUNTER"})(da4=$O2.InstrumentType||($O2.InstrumentType={}));var ca4;(function(A){A[A.HISTOGRAM=0]="HISTOGRAM",A[A.EXPONENTIAL_HISTOGRAM=1]="EXPONENTIAL_HISTOGRAM",A[A.GAUGE=2]="GAUGE",A[A.SUM=3]="SUM"})(ca4=$O2.DataPointType||($O2.DataPointType={}))});
var _v2=E((Sv2)=>{Object.defineProperty(Sv2,"__esModule",{value:!0});Sv2.ATTR_JVM_GC_NAME=Sv2.ATTR_JVM_GC_ACTION=Sv2.ATTR_HTTP_ROUTE=Sv2.ATTR_HTTP_RESPONSE_STATUS_CODE=Sv2.ATTR_HTTP_RESPONSE_HEADER=Sv2.ATTR_HTTP_REQUEST_RESEND_COUNT=Sv2.ATTR_HTTP_REQUEST_METHOD_ORIGINAL=Sv2.HTTP_REQUEST_METHOD_VALUE_TRACE=Sv2.HTTP_REQUEST_METHOD_VALUE_PUT=Sv2.HTTP_REQUEST_METHOD_VALUE_POST=Sv2.HTTP_REQUEST_METHOD_VALUE_PATCH=Sv2.HTTP_REQUEST_METHOD_VALUE_OPTIONS=Sv2.HTTP_REQUEST_METHOD_VALUE_HEAD=Sv2.HTTP_REQUEST_METHOD_VALUE_GET=Sv2.HTTP_REQUEST_METHOD_VALUE_DELETE=Sv2.HTTP_REQUEST_METHOD_VALUE_CONNECT=Sv2.HTTP_REQUEST_METHOD_VALUE_OTHER=Sv2.ATTR_HTTP_REQUEST_METHOD=Sv2.ATTR_HTTP_REQUEST_HEADER=Sv2.ATTR_EXCEPTION_TYPE=Sv2.ATTR_EXCEPTION_STACKTRACE=Sv2.ATTR_EXCEPTION_MESSAGE=Sv2.ATTR_EXCEPTION_ESCAPED=Sv2.ERROR_TYPE_VALUE_OTHER=Sv2.ATTR_ERROR_TYPE=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_POH=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_LOH=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1=Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0=Sv2.ATTR_DOTNET_GC_HEAP_GENERATION=Sv2.ATTR_CLIENT_PORT=Sv2.ATTR_CLIENT_ADDRESS=Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS=Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE=Sv2.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS=Sv2.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK=Sv2.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER=Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED=Sv2.ATTR_ASPNETCORE_RATE_LIMITING_RESULT=Sv2.ATTR_ASPNETCORE_RATE_LIMITING_POLICY=Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED=Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED=Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT=void 0;Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_GO=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_CPP=Sv2.ATTR_TELEMETRY_SDK_LANGUAGE=Sv2.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS=Sv2.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS=Sv2.SIGNALR_TRANSPORT_VALUE_LONG_POLLING=Sv2.ATTR_SIGNALR_TRANSPORT=Sv2.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT=Sv2.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE=Sv2.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN=Sv2.ATTR_SIGNALR_CONNECTION_STATUS=Sv2.ATTR_SERVICE_VERSION=Sv2.ATTR_SERVICE_NAME=Sv2.ATTR_SERVER_PORT=Sv2.ATTR_SERVER_ADDRESS=Sv2.ATTR_OTEL_STATUS_DESCRIPTION=Sv2.OTEL_STATUS_CODE_VALUE_OK=Sv2.OTEL_STATUS_CODE_VALUE_ERROR=Sv2.ATTR_OTEL_STATUS_CODE=Sv2.ATTR_OTEL_SCOPE_VERSION=Sv2.ATTR_OTEL_SCOPE_NAME=Sv2.NETWORK_TYPE_VALUE_IPV6=Sv2.NETWORK_TYPE_VALUE_IPV4=Sv2.ATTR_NETWORK_TYPE=Sv2.NETWORK_TRANSPORT_VALUE_UNIX=Sv2.NETWORK_TRANSPORT_VALUE_UDP=Sv2.NETWORK_TRANSPORT_VALUE_TCP=Sv2.NETWORK_TRANSPORT_VALUE_QUIC=Sv2.NETWORK_TRANSPORT_VALUE_PIPE=Sv2.ATTR_NETWORK_TRANSPORT=Sv2.ATTR_NETWORK_PROTOCOL_VERSION=Sv2.ATTR_NETWORK_PROTOCOL_NAME=Sv2.ATTR_NETWORK_PEER_PORT=Sv2.ATTR_NETWORK_PEER_ADDRESS=Sv2.ATTR_NETWORK_LOCAL_PORT=Sv2.ATTR_NETWORK_LOCAL_ADDRESS=Sv2.JVM_THREAD_STATE_VALUE_WAITING=Sv2.JVM_THREAD_STATE_VALUE_TIMED_WAITING=Sv2.JVM_THREAD_STATE_VALUE_TERMINATED=Sv2.JVM_THREAD_STATE_VALUE_RUNNABLE=Sv2.JVM_THREAD_STATE_VALUE_NEW=Sv2.JVM_THREAD_STATE_VALUE_BLOCKED=Sv2.ATTR_JVM_THREAD_STATE=Sv2.ATTR_JVM_THREAD_DAEMON=Sv2.JVM_MEMORY_TYPE_VALUE_NON_HEAP=Sv2.JVM_MEMORY_TYPE_VALUE_HEAP=Sv2.ATTR_JVM_MEMORY_TYPE=Sv2.ATTR_JVM_MEMORY_POOL_NAME=void 0;Sv2.ATTR_USER_AGENT_ORIGINAL=Sv2.ATTR_URL_SCHEME=Sv2.ATTR_URL_QUERY=Sv2.ATTR_URL_PATH=Sv2.ATTR_URL_FULL=Sv2.ATTR_URL_FRAGMENT=Sv2.ATTR_TELEMETRY_SDK_VERSION=Sv2.ATTR_TELEMETRY_SDK_NAME=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUST=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PHP=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS=Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA=void 0;Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT="aspnetcore.diagnostics.exception.result";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_ABORTED="aborted";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_HANDLED="handled";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_SKIPPED="skipped";Sv2.ASPNETCORE_DIAGNOSTICS_EXCEPTION_RESULT_VALUE_UNHANDLED="unhandled";Sv2.ATTR_ASPNETCORE_DIAGNOSTICS_HANDLER_TYPE="aspnetcore.diagnostics.handler.type";Sv2.ATTR_ASPNETCORE_RATE_LIMITING_POLICY="aspnetcore.rate_limiting.policy";Sv2.ATTR_ASPNETCORE_RATE_LIMITING_RESULT="aspnetcore.rate_limiting.result";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ACQUIRED="acquired";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_ENDPOINT_LIMITER="endpoint_limiter";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_GLOBAL_LIMITER="global_limiter";Sv2.ASPNETCORE_RATE_LIMITING_RESULT_VALUE_REQUEST_CANCELED="request_canceled";Sv2.ATTR_ASPNETCORE_REQUEST_IS_UNHANDLED="aspnetcore.request.is_unhandled";Sv2.ATTR_ASPNETCORE_ROUTING_IS_FALLBACK="aspnetcore.routing.is_fallback";Sv2.ATTR_ASPNETCORE_ROUTING_MATCH_STATUS="aspnetcore.routing.match_status";Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_FAILURE="failure";Sv2.ASPNETCORE_ROUTING_MATCH_STATUS_VALUE_SUCCESS="success";Sv2.ATTR_CLIENT_ADDRESS="client.address";Sv2.ATTR_CLIENT_PORT="client.port";Sv2.ATTR_DOTNET_GC_HEAP_GENERATION="dotnet.gc.heap.generation";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN0="gen0";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN1="gen1";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_GEN2="gen2";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_LOH="loh";Sv2.DOTNET_GC_HEAP_GENERATION_VALUE_POH="poh";Sv2.ATTR_ERROR_TYPE="error.type";Sv2.ERROR_TYPE_VALUE_OTHER="_OTHER";Sv2.ATTR_EXCEPTION_ESCAPED="exception.escaped";Sv2.ATTR_EXCEPTION_MESSAGE="exception.message";Sv2.ATTR_EXCEPTION_STACKTRACE="exception.stacktrace";Sv2.ATTR_EXCEPTION_TYPE="exception.type";var BB6=(A)=>`http.request.header.${A}`;Sv2.ATTR_HTTP_REQUEST_HEADER=BB6;Sv2.ATTR_HTTP_REQUEST_METHOD="http.request.method";Sv2.HTTP_REQUEST_METHOD_VALUE_OTHER="_OTHER";Sv2.HTTP_REQUEST_METHOD_VALUE_CONNECT="CONNECT";Sv2.HTTP_REQUEST_METHOD_VALUE_DELETE="DELETE";Sv2.HTTP_REQUEST_METHOD_VALUE_GET="GET";Sv2.HTTP_REQUEST_METHOD_VALUE_HEAD="HEAD";Sv2.HTTP_REQUEST_METHOD_VALUE_OPTIONS="OPTIONS";Sv2.HTTP_REQUEST_METHOD_VALUE_PATCH="PATCH";Sv2.HTTP_REQUEST_METHOD_VALUE_POST="POST";Sv2.HTTP_REQUEST_METHOD_VALUE_PUT="PUT";Sv2.HTTP_REQUEST_METHOD_VALUE_TRACE="TRACE";Sv2.ATTR_HTTP_REQUEST_METHOD_ORIGINAL="http.request.method_original";Sv2.ATTR_HTTP_REQUEST_RESEND_COUNT="http.request.resend_count";var QB6=(A)=>`http.response.header.${A}`;Sv2.ATTR_HTTP_RESPONSE_HEADER=QB6;Sv2.ATTR_HTTP_RESPONSE_STATUS_CODE="http.response.status_code";Sv2.ATTR_HTTP_ROUTE="http.route";Sv2.ATTR_JVM_GC_ACTION="jvm.gc.action";Sv2.ATTR_JVM_GC_NAME="jvm.gc.name";Sv2.ATTR_JVM_MEMORY_POOL_NAME="jvm.memory.pool.name";Sv2.ATTR_JVM_MEMORY_TYPE="jvm.memory.type";Sv2.JVM_MEMORY_TYPE_VALUE_HEAP="heap";Sv2.JVM_MEMORY_TYPE_VALUE_NON_HEAP="non_heap";Sv2.ATTR_JVM_THREAD_DAEMON="jvm.thread.daemon";Sv2.ATTR_JVM_THREAD_STATE="jvm.thread.state";Sv2.JVM_THREAD_STATE_VALUE_BLOCKED="blocked";Sv2.JVM_THREAD_STATE_VALUE_NEW="new";Sv2.JVM_THREAD_STATE_VALUE_RUNNABLE="runnable";Sv2.JVM_THREAD_STATE_VALUE_TERMINATED="terminated";Sv2.JVM_THREAD_STATE_VALUE_TIMED_WAITING="timed_waiting";Sv2.JVM_THREAD_STATE_VALUE_WAITING="waiting";Sv2.ATTR_NETWORK_LOCAL_ADDRESS="network.local.address";Sv2.ATTR_NETWORK_LOCAL_PORT="network.local.port";Sv2.ATTR_NETWORK_PEER_ADDRESS="network.peer.address";Sv2.ATTR_NETWORK_PEER_PORT="network.peer.port";Sv2.ATTR_NETWORK_PROTOCOL_NAME="network.protocol.name";Sv2.ATTR_NETWORK_PROTOCOL_VERSION="network.protocol.version";Sv2.ATTR_NETWORK_TRANSPORT="network.transport";Sv2.NETWORK_TRANSPORT_VALUE_PIPE="pipe";Sv2.NETWORK_TRANSPORT_VALUE_QUIC="quic";Sv2.NETWORK_TRANSPORT_VALUE_TCP="tcp";Sv2.NETWORK_TRANSPORT_VALUE_UDP="udp";Sv2.NETWORK_TRANSPORT_VALUE_UNIX="unix";Sv2.ATTR_NETWORK_TYPE="network.type";Sv2.NETWORK_TYPE_VALUE_IPV4="ipv4";Sv2.NETWORK_TYPE_VALUE_IPV6="ipv6";Sv2.ATTR_OTEL_SCOPE_NAME="otel.scope.name";Sv2.ATTR_OTEL_SCOPE_VERSION="otel.scope.version";Sv2.ATTR_OTEL_STATUS_CODE="otel.status_code";Sv2.OTEL_STATUS_CODE_VALUE_ERROR="ERROR";Sv2.OTEL_STATUS_CODE_VALUE_OK="OK";Sv2.ATTR_OTEL_STATUS_DESCRIPTION="otel.status_description";Sv2.ATTR_SERVER_ADDRESS="server.address";Sv2.ATTR_SERVER_PORT="server.port";Sv2.ATTR_SERVICE_NAME="service.name";Sv2.ATTR_SERVICE_VERSION="service.version";Sv2.ATTR_SIGNALR_CONNECTION_STATUS="signalr.connection.status";Sv2.SIGNALR_CONNECTION_STATUS_VALUE_APP_SHUTDOWN="app_shutdown";Sv2.SIGNALR_CONNECTION_STATUS_VALUE_NORMAL_CLOSURE="normal_closure";Sv2.SIGNALR_CONNECTION_STATUS_VALUE_TIMEOUT="timeout";Sv2.ATTR_SIGNALR_TRANSPORT="signalr.transport";Sv2.SIGNALR_TRANSPORT_VALUE_LONG_POLLING="long_polling";Sv2.SIGNALR_TRANSPORT_VALUE_SERVER_SENT_EVENTS="server_sent_events";Sv2.SIGNALR_TRANSPORT_VALUE_WEB_SOCKETS="web_sockets";Sv2.ATTR_TELEMETRY_SDK_LANGUAGE="telemetry.sdk.language";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_CPP="cpp";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_DOTNET="dotnet";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_ERLANG="erlang";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_GO="go";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_JAVA="java";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_NODEJS="nodejs";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PHP="php";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_PYTHON="python";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUBY="ruby";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_RUST="rust";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_SWIFT="swift";Sv2.TELEMETRY_SDK_LANGUAGE_VALUE_WEBJS="webjs";Sv2.ATTR_TELEMETRY_SDK_NAME="telemetry.sdk.name";Sv2.ATTR_TELEMETRY_SDK_VERSION="telemetry.sdk.version";Sv2.ATTR_URL_FRAGMENT="url.fragment";Sv2.ATTR_URL_FULL="url.full";Sv2.ATTR_URL_PATH="url.path";Sv2.ATTR_URL_QUERY="url.query";Sv2.ATTR_URL_SCHEME="url.scheme";Sv2.ATTR_USER_AGENT_ORIGINAL="user_agent.original"});
var ac2=E((ic2)=>{Object.defineProperty(ic2,"__esModule",{value:!0});ic2.createHttpAgent=ic2.compressAndSend=ic2.sendWithHttp=void 0;var cc2=J1("http"),lc2=J1("https"),tD6=J1("zlib"),eD6=J1("stream"),dc2=mc2(),AZ6=nO1();function BZ6(A,B,Q,D,Z){let G=new URL(A.url),F=Number(process.versions.node.split(".")[0]),I={hostname:G.hostname,port:G.port,path:G.pathname,method:"POST",headers:{...A.headers()},agent:B},W=(G.protocol==="http:"?cc2.request:lc2.request)(I,(X)=>{let V=[];X.on("data",(C)=>V.push(C)),X.on("end",()=>{if(X.statusCode&&X.statusCode<299)D({status:"success",data:Buffer.concat(V)});else if(X.statusCode&&dc2.isExportRetryable(X.statusCode))D({status:"retryable",retryInMillis:dc2.parseRetryAfterToMills(X.headers["retry-after"])});else{let C=new AZ6.OTLPExporterError(X.statusMessage,X.statusCode,Buffer.concat(V).toString());D({status:"failure",error:C})}})});W.setTimeout(Z,()=>{W.destroy(),D({status:"failure",error:new Error("Request Timeout")})}),W.on("error",(X)=>{D({status:"failure",error:X})});let J=F>=14?"close":"abort";W.on(J,()=>{D({status:"failure",error:new Error("Request timed out")})}),pc2(W,A.compression,Q,(X)=>{D({status:"failure",error:X})})}ic2.sendWithHttp=BZ6;function pc2(A,B,Q,D){let Z=QZ6(Q);if(B==="gzip")A.setHeader("Content-Encoding","gzip"),Z=Z.on("error",D).pipe(tD6.createGzip()).on("error",D);Z.pipe(A).on("error",D)}ic2.compressAndSend=pc2;function QZ6(A){let B=new eD6.Readable;return B.push(A),B.push(null),B}function DZ6(A,B){return new(new URL(A).protocol==="http:"?cc2.Agent:lc2.Agent)(B)}ic2.createHttpAgent=DZ6});
var af2=E((if2)=>{Object.defineProperty(if2,"__esModule",{value:!0});if2.InMemoryMetricExporter=void 0;var lf2=y3();class pf2{_shutdown=!1;_aggregationTemporality;_metrics=[];constructor(A){this._aggregationTemporality=A}export(A,B){if(this._shutdown){setTimeout(()=>B({code:lf2.ExportResultCode.FAILED}),0);return}this._metrics.push(A),setTimeout(()=>B({code:lf2.ExportResultCode.SUCCESS}),0)}getMetrics(){return this._metrics}forceFlush(){return Promise.resolve()}reset(){this._metrics=[]}selectAggregationTemporality(A){return this._aggregationTemporality}shutdown(){return this._shutdown=!0,Promise.resolve()}}if2.InMemoryMetricExporter=pf2});
var am2=E((ak5,nm2)=>{nm2.exports=rO1;function rO1(){this._listeners={}}rO1.prototype.on=function A(B,Q,D){return(this._listeners[B]||(this._listeners[B]=[])).push({fn:Q,ctx:D||this}),this};rO1.prototype.off=function A(B,Q){if(B===void 0)this._listeners={};else if(Q===void 0)this._listeners[B]=[];else{var D=this._listeners[B];for(var Z=0;Z<D.length;)if(D[Z].fn===Q)D.splice(Z,1);else++Z}return this};rO1.prototype.emit=function A(B){var Q=this._listeners[B];if(Q){var D=[],Z=1;for(;Z<arguments.length;)D.push(arguments[Z++]);for(Z=0;Z<Q.length;)Q[Z].fn.apply(Q[Z++].ctx,D)}return this}});
var au2=E((iu2)=>{Object.defineProperty(iu2,"__esModule",{value:!0});iu2.InstrumentSelector=void 0;var lu2=pO1();class pu2{_nameFilter;_type;_unitFilter;constructor(A){this._nameFilter=new lu2.PatternPredicate(A?.name??"*"),this._type=A?.type,this._unitFilter=new lu2.ExactPredicate(A?.unit)}getType(){return this._type}getNameFilter(){return this._nameFilter}getUnitFilter(){return this._unitFilter}}iu2.InstrumentSelector=pu2});
var bG0=E((jM2)=>{Object.defineProperty(jM2,"__esModule",{value:!0});jM2.ProxyTracerProvider=void 0;var Mn4=vG0(),Rn4=PM2(),On4=new Rn4.NoopTracerProvider;class SM2{getTracer(A,B,Q){var D;return(D=this.getDelegateTracer(A,B,Q))!==null&&D!==void 0?D:new Mn4.ProxyTracer(this,A,B,Q)}getDelegate(){var A;return(A=this._delegate)!==null&&A!==void 0?A:On4}setDelegate(A){this._delegate=A}getDelegateTracer(A,B,Q){var D;return(D=this._delegate)===null||D===void 0?void 0:D.getTracer(A,B,Q)}}jM2.ProxyTracerProvider=SM2});
var bh2=E((vh2)=>{Object.defineProperty(vh2,"__esModule",{value:!0});vh2.getMachineId=void 0;var c86=J1("process"),ko;vh2.getMachineId=ko;switch(c86.platform){case"darwin":vh2.getMachineId=ko=$h2().getMachineId;break;case"linux":vh2.getMachineId=ko=Lh2().getMachineId;break;case"freebsd":vh2.getMachineId=ko=Th2().getMachineId;break;case"win32":vh2.getMachineId=ko=kh2().getMachineId;break;default:vh2.getMachineId=ko=xh2().getMachineId}});
var bm2=E((xm2)=>{Object.defineProperty(xm2,"__esModule",{value:!0});xm2.createOtlpNetworkExportDelegate=void 0;var F76=CI0(),I76=KI0();function Y76(A,B,Q){return I76.createOtlpExportDelegate({transport:Q,serializer:B,promiseHandler:F76.createBoundedQueueExportPromiseHandler(A)},{timeout:A.timeoutMillis})}xm2.createOtlpNetworkExportDelegate=Y76});
var bu2=E((xu2)=>{Object.defineProperty(xu2,"__esModule",{value:!0});xu2.MeterProviderSharedState=void 0;var $36=cw(),q36=Cg2(),N36=yu2(),L36=r51();class _u2{resource;viewRegistry=new q36.ViewRegistry;metricCollectors=[];meterSharedStates=new Map;constructor(A){this.resource=A}getMeterSharedState(A){let B=$36.instrumentationScopeId(A),Q=this.meterSharedStates.get(B);if(Q==null)Q=new N36.MeterSharedState(this,A),this.meterSharedStates.set(B,Q);return Q}selectAggregations(A){let B=[];for(let Q of this.metricCollectors)B.push([Q,L36.toAggregation(Q.selectAggregation(A))]);return B}}xu2.MeterProviderSharedState=_u2});
var bv2=E((xv2)=>{Object.defineProperty(xv2,"__esModule",{value:!0});xv2.METRIC_SIGNALR_SERVER_CONNECTION_DURATION=xv2.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS=xv2.METRIC_KESTREL_UPGRADED_CONNECTIONS=xv2.METRIC_KESTREL_TLS_HANDSHAKE_DURATION=xv2.METRIC_KESTREL_REJECTED_CONNECTIONS=xv2.METRIC_KESTREL_QUEUED_REQUESTS=xv2.METRIC_KESTREL_QUEUED_CONNECTIONS=xv2.METRIC_KESTREL_CONNECTION_DURATION=xv2.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES=xv2.METRIC_KESTREL_ACTIVE_CONNECTIONS=xv2.METRIC_JVM_THREAD_COUNT=xv2.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC=xv2.METRIC_JVM_MEMORY_USED=xv2.METRIC_JVM_MEMORY_LIMIT=xv2.METRIC_JVM_MEMORY_COMMITTED=xv2.METRIC_JVM_GC_DURATION=xv2.METRIC_JVM_CPU_TIME=xv2.METRIC_JVM_CPU_RECENT_UTILIZATION=xv2.METRIC_JVM_CPU_COUNT=xv2.METRIC_JVM_CLASS_UNLOADED=xv2.METRIC_JVM_CLASS_LOADED=xv2.METRIC_JVM_CLASS_COUNT=xv2.METRIC_HTTP_SERVER_REQUEST_DURATION=xv2.METRIC_HTTP_CLIENT_REQUEST_DURATION=xv2.METRIC_DOTNET_TIMER_COUNT=xv2.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT=xv2.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT=xv2.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH=xv2.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET=xv2.METRIC_DOTNET_PROCESS_CPU_TIME=xv2.METRIC_DOTNET_PROCESS_CPU_COUNT=xv2.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS=xv2.METRIC_DOTNET_JIT_COMPILED_METHODS=xv2.METRIC_DOTNET_JIT_COMPILED_IL_SIZE=xv2.METRIC_DOTNET_JIT_COMPILATION_TIME=xv2.METRIC_DOTNET_GC_PAUSE_TIME=xv2.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE=xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE=xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE=xv2.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED=xv2.METRIC_DOTNET_GC_COLLECTIONS=xv2.METRIC_DOTNET_EXCEPTIONS=xv2.METRIC_DOTNET_ASSEMBLY_COUNT=xv2.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS=xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS=xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION=xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE=xv2.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS=xv2.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES=xv2.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS=void 0;xv2.METRIC_ASPNETCORE_DIAGNOSTICS_EXCEPTIONS="aspnetcore.diagnostics.exceptions";xv2.METRIC_ASPNETCORE_RATE_LIMITING_ACTIVE_REQUEST_LEASES="aspnetcore.rate_limiting.active_request_leases";xv2.METRIC_ASPNETCORE_RATE_LIMITING_QUEUED_REQUESTS="aspnetcore.rate_limiting.queued_requests";xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_TIME_IN_QUEUE="aspnetcore.rate_limiting.request.time_in_queue";xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUEST_LEASE_DURATION="aspnetcore.rate_limiting.request_lease.duration";xv2.METRIC_ASPNETCORE_RATE_LIMITING_REQUESTS="aspnetcore.rate_limiting.requests";xv2.METRIC_ASPNETCORE_ROUTING_MATCH_ATTEMPTS="aspnetcore.routing.match_attempts";xv2.METRIC_DOTNET_ASSEMBLY_COUNT="dotnet.assembly.count";xv2.METRIC_DOTNET_EXCEPTIONS="dotnet.exceptions";xv2.METRIC_DOTNET_GC_COLLECTIONS="dotnet.gc.collections";xv2.METRIC_DOTNET_GC_HEAP_TOTAL_ALLOCATED="dotnet.gc.heap.total_allocated";xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_FRAGMENTATION_SIZE="dotnet.gc.last_collection.heap.fragmentation.size";xv2.METRIC_DOTNET_GC_LAST_COLLECTION_HEAP_SIZE="dotnet.gc.last_collection.heap.size";xv2.METRIC_DOTNET_GC_LAST_COLLECTION_MEMORY_COMMITTED_SIZE="dotnet.gc.last_collection.memory.committed_size";xv2.METRIC_DOTNET_GC_PAUSE_TIME="dotnet.gc.pause.time";xv2.METRIC_DOTNET_JIT_COMPILATION_TIME="dotnet.jit.compilation.time";xv2.METRIC_DOTNET_JIT_COMPILED_IL_SIZE="dotnet.jit.compiled_il.size";xv2.METRIC_DOTNET_JIT_COMPILED_METHODS="dotnet.jit.compiled_methods";xv2.METRIC_DOTNET_MONITOR_LOCK_CONTENTIONS="dotnet.monitor.lock_contentions";xv2.METRIC_DOTNET_PROCESS_CPU_COUNT="dotnet.process.cpu.count";xv2.METRIC_DOTNET_PROCESS_CPU_TIME="dotnet.process.cpu.time";xv2.METRIC_DOTNET_PROCESS_MEMORY_WORKING_SET="dotnet.process.memory.working_set";xv2.METRIC_DOTNET_THREAD_POOL_QUEUE_LENGTH="dotnet.thread_pool.queue.length";xv2.METRIC_DOTNET_THREAD_POOL_THREAD_COUNT="dotnet.thread_pool.thread.count";xv2.METRIC_DOTNET_THREAD_POOL_WORK_ITEM_COUNT="dotnet.thread_pool.work_item.count";xv2.METRIC_DOTNET_TIMER_COUNT="dotnet.timer.count";xv2.METRIC_HTTP_CLIENT_REQUEST_DURATION="http.client.request.duration";xv2.METRIC_HTTP_SERVER_REQUEST_DURATION="http.server.request.duration";xv2.METRIC_JVM_CLASS_COUNT="jvm.class.count";xv2.METRIC_JVM_CLASS_LOADED="jvm.class.loaded";xv2.METRIC_JVM_CLASS_UNLOADED="jvm.class.unloaded";xv2.METRIC_JVM_CPU_COUNT="jvm.cpu.count";xv2.METRIC_JVM_CPU_RECENT_UTILIZATION="jvm.cpu.recent_utilization";xv2.METRIC_JVM_CPU_TIME="jvm.cpu.time";xv2.METRIC_JVM_GC_DURATION="jvm.gc.duration";xv2.METRIC_JVM_MEMORY_COMMITTED="jvm.memory.committed";xv2.METRIC_JVM_MEMORY_LIMIT="jvm.memory.limit";xv2.METRIC_JVM_MEMORY_USED="jvm.memory.used";xv2.METRIC_JVM_MEMORY_USED_AFTER_LAST_GC="jvm.memory.used_after_last_gc";xv2.METRIC_JVM_THREAD_COUNT="jvm.thread.count";xv2.METRIC_KESTREL_ACTIVE_CONNECTIONS="kestrel.active_connections";xv2.METRIC_KESTREL_ACTIVE_TLS_HANDSHAKES="kestrel.active_tls_handshakes";xv2.METRIC_KESTREL_CONNECTION_DURATION="kestrel.connection.duration";xv2.METRIC_KESTREL_QUEUED_CONNECTIONS="kestrel.queued_connections";xv2.METRIC_KESTREL_QUEUED_REQUESTS="kestrel.queued_requests";xv2.METRIC_KESTREL_REJECTED_CONNECTIONS="kestrel.rejected_connections";xv2.METRIC_KESTREL_TLS_HANDSHAKE_DURATION="kestrel.tls_handshake.duration";xv2.METRIC_KESTREL_UPGRADED_CONNECTIONS="kestrel.upgraded_connections";xv2.METRIC_SIGNALR_SERVER_ACTIVE_CONNECTIONS="signalr.server.active_connections";xv2.METRIC_SIGNALR_SERVER_CONNECTION_DURATION="signalr.server.connection.duration"});
var c51=E((JT2)=>{Object.defineProperty(JT2,"__esModule",{value:!0});JT2.isTracingSuppressed=JT2.unsuppressTracing=JT2.suppressTracing=void 0;var us4=ZQ(),UF0=us4.createContextKey("OpenTelemetry SDK Context Key SUPPRESS_TRACING");function ms4(A){return A.setValue(UF0,!0)}JT2.suppressTracing=ms4;function ds4(A){return A.deleteValue(UF0)}JT2.unsuppressTracing=ds4;function cs4(A){return A.getValue(UF0)===!0}JT2.isTracingSuppressed=cs4});
var cT2=E((mT2)=>{Object.defineProperty(mT2,"__esModule",{value:!0});mT2.getStringListFromEnv=mT2.getBooleanFromEnv=mT2.getStringFromEnv=mT2.getNumberFromEnv=void 0;var hT2=ZQ(),gT2=J1("util");function Ur4(A){let B=process.env[A];if(B==null||B.trim()==="")return;let Q=Number(B);if(isNaN(Q)){hT2.diag.warn(`Unknown value ${gT2.inspect(B)} for ${A}, expected a number, using defaults`);return}return Q}mT2.getNumberFromEnv=Ur4;function uT2(A){let B=process.env[A];if(B==null||B.trim()==="")return;return B}mT2.getStringFromEnv=uT2;function wr4(A){let B=process.env[A]?.trim().toLowerCase();if(B==null||B==="")return!1;if(B==="true")return!0;else if(B==="false")return!1;else return hT2.diag.warn(`Unknown value ${gT2.inspect(B)} for ${A}, expected 'true' or 'false', falling back to 'false' (default)`),!1}mT2.getBooleanFromEnv=wr4;function $r4(A){return uT2(A)?.split(",").map((B)=>B.trim()).filter((B)=>B!=="")}mT2.getStringListFromEnv=$r4});
var cf2=E((mf2)=>{Object.defineProperty(mf2,"__esModule",{value:!0});mf2.PeriodicExportingMetricReader=void 0;var mF0=ZQ(),o51=y3(),F86=uF0(),gf2=cw();class uf2 extends F86.MetricReader{_interval;_exporter;_exportInterval;_exportTimeout;constructor(A){super({aggregationSelector:A.exporter.selectAggregation?.bind(A.exporter),aggregationTemporalitySelector:A.exporter.selectAggregationTemporality?.bind(A.exporter),metricProducers:A.metricProducers});if(A.exportIntervalMillis!==void 0&&A.exportIntervalMillis<=0)throw Error("exportIntervalMillis must be greater than 0");if(A.exportTimeoutMillis!==void 0&&A.exportTimeoutMillis<=0)throw Error("exportTimeoutMillis must be greater than 0");if(A.exportTimeoutMillis!==void 0&&A.exportIntervalMillis!==void 0&&A.exportIntervalMillis<A.exportTimeoutMillis)throw Error("exportIntervalMillis must be greater than or equal to exportTimeoutMillis");this._exportInterval=A.exportIntervalMillis??60000,this._exportTimeout=A.exportTimeoutMillis??30000,this._exporter=A.exporter}async _runOnce(){try{await gf2.callWithTimeout(this._doRun(),this._exportTimeout)}catch(A){if(A instanceof gf2.TimeoutError){mF0.diag.error("Export took longer than %s milliseconds and timed out.",this._exportTimeout);return}o51.globalErrorHandler(A)}}async _doRun(){let{resourceMetrics:A,errors:B}=await this.collect({timeoutMillis:this._exportTimeout});if(B.length>0)mF0.diag.error("PeriodicExportingMetricReader: metrics collection errors",...B);if(A.resource.asyncAttributesPending)try{await A.resource.waitForAsyncAttributes?.()}catch(D){mF0.diag.debug("Error while resolving async portion of resource: ",D),o51.globalErrorHandler(D)}if(A.scopeMetrics.length===0)return;let Q=await o51.internal._export(this._exporter,A);if(Q.code!==o51.ExportResultCode.SUCCESS)throw new Error(`PeriodicExportingMetricReader: metrics export failed (error ${Q.error})`)}onInitialized(){this._interval=setInterval(()=>{this._runOnce()},this._exportInterval),o51.unrefTimer(this._interval)}async onForceFlush(){await this._runOnce(),await this._exporter.forceFlush()}async onShutdown(){if(this._interval)clearInterval(this._interval);await this.onForceFlush(),await this._exporter.shutdown()}}mf2.PeriodicExportingMetricReader=uf2});
var ch2=E((mh2)=>{Object.defineProperty(mh2,"__esModule",{value:!0});mh2.hostDetector=void 0;var eF0=GP(),gh2=J1("os"),n86=bh2(),a86=tF0();class uh2{detect(A){return{attributes:{[eF0.SEMRESATTRS_HOST_NAME]:gh2.hostname(),[eF0.SEMRESATTRS_HOST_ARCH]:a86.normalizeArch(gh2.arch()),[eF0.SEMRESATTRS_HOST_ID]:n86.getMachineId()}}}}mh2.hostDetector=new uh2});
var cv2=E((dL)=>{Object.defineProperty(dL,"__esModule",{value:!0});dL.unrefTimer=dL.SDK_INFO=dL.otperformance=dL._globalThis=dL.getStringListFromEnv=dL.getNumberFromEnv=dL.getBooleanFromEnv=dL.getStringFromEnv=void 0;var LO1=cT2();Object.defineProperty(dL,"getStringFromEnv",{enumerable:!0,get:function(){return LO1.getStringFromEnv}});Object.defineProperty(dL,"getBooleanFromEnv",{enumerable:!0,get:function(){return LO1.getBooleanFromEnv}});Object.defineProperty(dL,"getNumberFromEnv",{enumerable:!0,get:function(){return LO1.getNumberFromEnv}});Object.defineProperty(dL,"getStringListFromEnv",{enumerable:!0,get:function(){return LO1.getStringListFromEnv}});var F46=iT2();Object.defineProperty(dL,"_globalThis",{enumerable:!0,get:function(){return F46._globalThis}});var I46=sT2();Object.defineProperty(dL,"otperformance",{enumerable:!0,get:function(){return I46.otperformance}});var Y46=gv2();Object.defineProperty(dL,"SDK_INFO",{enumerable:!0,get:function(){return Y46.SDK_INFO}});var W46=dv2();Object.defineProperty(dL,"unrefTimer",{enumerable:!0,get:function(){return W46.unrefTimer}})});
var cw=E((qO2)=>{Object.defineProperty(qO2,"__esModule",{value:!0});qO2.equalsCaseInsensitive=qO2.binarySearchUB=qO2.setEquals=qO2.FlatMap=qO2.isPromiseAllSettledRejectionResult=qO2.PromiseAllSettled=qO2.callWithTimeout=qO2.TimeoutError=qO2.instrumentationScopeId=qO2.hashAttributes=qO2.isNotNullish=void 0;function la4(A){return A!==void 0&&A!==null}qO2.isNotNullish=la4;function pa4(A){let B=Object.keys(A);if(B.length===0)return"";return B=B.sort(),JSON.stringify(B.map((Q)=>[Q,A[Q]]))}qO2.hashAttributes=pa4;function ia4(A){return`${A.name}:${A.version??""}:${A.schemaUrl??""}`}qO2.instrumentationScopeId=ia4;class UO1 extends Error{constructor(A){super(A);Object.setPrototypeOf(this,UO1.prototype)}}qO2.TimeoutError=UO1;function na4(A,B){let Q,D=new Promise(function Z(G,F){Q=setTimeout(function I(){F(new UO1("Operation timed out."))},B)});return Promise.race([A,D]).then((Z)=>{return clearTimeout(Q),Z},(Z)=>{throw clearTimeout(Q),Z})}qO2.callWithTimeout=na4;async function aa4(A){return Promise.all(A.map(async(B)=>{try{return{status:"fulfilled",value:await B}}catch(Q){return{status:"rejected",reason:Q}}}))}qO2.PromiseAllSettled=aa4;function sa4(A){return A.status==="rejected"}qO2.isPromiseAllSettledRejectionResult=sa4;function ra4(A,B){let Q=[];return A.forEach((D)=>{Q.push(...B(D))}),Q}qO2.FlatMap=ra4;function oa4(A,B){if(A.size!==B.size)return!1;for(let Q of A)if(!B.has(Q))return!1;return!0}qO2.setEquals=oa4;function ta4(A,B){let Q=0,D=A.length-1,Z=A.length;while(D>=Q){let G=Q+Math.trunc((D-Q)/2);if(A[G]<B)Q=G+1;else Z=G,D=G-1}return Z}qO2.binarySearchUB=ta4;function ea4(A,B){return A.toLowerCase()===B.toLowerCase()}qO2.equalsCaseInsensitive=ea4});
var dI0=E((Ic2)=>{Object.defineProperty(Ic2,"__esModule",{value:!0});Ic2.createExportMetricsServiceRequest=Ic2.toMetric=Ic2.toScopeMetrics=Ic2.toResourceMetrics=void 0;var Qc2=ZQ(),go=f_(),ID6=QT1(),Y31=DT1();function Zc2(A,B){let Q=ID6.getOtlpEncoder(B);return{resource:Y31.createResource(A.resource),schemaUrl:void 0,scopeMetrics:Gc2(A.scopeMetrics,Q)}}Ic2.toResourceMetrics=Zc2;function Gc2(A,B){return Array.from(A.map((Q)=>({scope:Y31.createInstrumentationScope(Q.scope),metrics:Q.metrics.map((D)=>Fc2(D,B)),schemaUrl:Q.scope.schemaUrl})))}Ic2.toScopeMetrics=Gc2;function Fc2(A,B){let Q={name:A.descriptor.name,description:A.descriptor.description,unit:A.descriptor.unit},D=XD6(A.aggregationTemporality);switch(A.dataPointType){case go.DataPointType.SUM:Q.sum={aggregationTemporality:D,isMonotonic:A.isMonotonic,dataPoints:Dc2(A,B)};break;case go.DataPointType.GAUGE:Q.gauge={dataPoints:Dc2(A,B)};break;case go.DataPointType.HISTOGRAM:Q.histogram={aggregationTemporality:D,dataPoints:WD6(A,B)};break;case go.DataPointType.EXPONENTIAL_HISTOGRAM:Q.exponentialHistogram={aggregationTemporality:D,dataPoints:JD6(A,B)};break}return Q}Ic2.toMetric=Fc2;function YD6(A,B,Q){let D={attributes:Y31.toAttributes(A.attributes),startTimeUnixNano:Q.encodeHrTime(A.startTime),timeUnixNano:Q.encodeHrTime(A.endTime)};switch(B){case Qc2.ValueType.INT:D.asInt=A.value;break;case Qc2.ValueType.DOUBLE:D.asDouble=A.value;break}return D}function Dc2(A,B){return A.dataPoints.map((Q)=>{return YD6(Q,A.descriptor.valueType,B)})}function WD6(A,B){return A.dataPoints.map((Q)=>{let D=Q.value;return{attributes:Y31.toAttributes(Q.attributes),bucketCounts:D.buckets.counts,explicitBounds:D.buckets.boundaries,count:D.count,sum:D.sum,min:D.min,max:D.max,startTimeUnixNano:B.encodeHrTime(Q.startTime),timeUnixNano:B.encodeHrTime(Q.endTime)}})}function JD6(A,B){return A.dataPoints.map((Q)=>{let D=Q.value;return{attributes:Y31.toAttributes(Q.attributes),count:D.count,min:D.min,max:D.max,sum:D.sum,positive:{offset:D.positive.offset,bucketCounts:D.positive.bucketCounts},negative:{offset:D.negative.offset,bucketCounts:D.negative.bucketCounts},scale:D.scale,zeroCount:D.zeroCount,startTimeUnixNano:B.encodeHrTime(Q.startTime),timeUnixNano:B.encodeHrTime(Q.endTime)}})}function XD6(A){switch(A){case go.AggregationTemporality.DELTA:return 1;case go.AggregationTemporality.CUMULATIVE:return 2}}function VD6(A,B){return{resourceMetrics:A.map((Q)=>Zc2(Q,B))}}Ic2.createExportMetricsServiceRequest=VD6});
var dO1=E((Rg2)=>{Object.defineProperty(Rg2,"__esModule",{value:!0});Rg2.isObservableInstrument=Rg2.ObservableUpDownCounterInstrument=Rg2.ObservableGaugeInstrument=Rg2.ObservableCounterInstrument=Rg2.ObservableInstrument=Rg2.HistogramInstrument=Rg2.GaugeInstrument=Rg2.CounterInstrument=Rg2.UpDownCounterInstrument=Rg2.SyncInstrument=void 0;var xo=ZQ(),q56=y3();class vo{_writableMetricStorage;_descriptor;constructor(A,B){this._writableMetricStorage=A,this._descriptor=B}_record(A,B={},Q=xo.context.active()){if(typeof A!=="number"){xo.diag.warn(`non-number value provided to metric ${this._descriptor.name}: ${A}`);return}if(this._descriptor.valueType===xo.ValueType.INT&&!Number.isInteger(A)){if(xo.diag.warn(`INT value type cannot accept a floating-point value for ${this._descriptor.name}, ignoring the fractional digits.`),A=Math.trunc(A),!Number.isInteger(A))return}this._writableMetricStorage.record(A,B,Q,q56.millisToHrTime(Date.now()))}}Rg2.SyncInstrument=vo;class Ug2 extends vo{add(A,B,Q){this._record(A,B,Q)}}Rg2.UpDownCounterInstrument=Ug2;class wg2 extends vo{add(A,B,Q){if(A<0){xo.diag.warn(`negative value provided to counter ${this._descriptor.name}: ${A}`);return}this._record(A,B,Q)}}Rg2.CounterInstrument=wg2;class $g2 extends vo{record(A,B,Q){this._record(A,B,Q)}}Rg2.GaugeInstrument=$g2;class qg2 extends vo{record(A,B,Q){if(A<0){xo.diag.warn(`negative value provided to histogram ${this._descriptor.name}: ${A}`);return}this._record(A,B,Q)}}Rg2.HistogramInstrument=qg2;class bo{_observableRegistry;_metricStorages;_descriptor;constructor(A,B,Q){this._observableRegistry=Q,this._descriptor=A,this._metricStorages=B}addCallback(A){this._observableRegistry.addCallback(A,this)}removeCallback(A){this._observableRegistry.removeCallback(A,this)}}Rg2.ObservableInstrument=bo;class Ng2 extends bo{}Rg2.ObservableCounterInstrument=Ng2;class Lg2 extends bo{}Rg2.ObservableGaugeInstrument=Lg2;class Mg2 extends bo{}Rg2.ObservableUpDownCounterInstrument=Mg2;function N56(A){return A instanceof bo}Rg2.isObservableInstrument=N56});
var dv2=E((uv2)=>{Object.defineProperty(uv2,"__esModule",{value:!0});uv2.unrefTimer=void 0;function G46(A){A.unref()}uv2.unrefTimer=G46});
var eM2=E((oM2)=>{Object.defineProperty(oM2,"__esModule",{value:!0});oM2.context=void 0;var mn4=g51();oM2.context=mn4.ContextAPI.getInstance()});
var eN2=E((Iu)=>{var Zi4=Iu&&Iu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;Object.defineProperty(A,D,{enumerable:!0,get:function(){return B[Q]}})}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),Gi4=Iu&&Iu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))Zi4(B,A,Q)};Object.defineProperty(Iu,"__esModule",{value:!0});Gi4(tN2(),Iu)});
var eb2=E((ob2)=>{Object.defineProperty(ob2,"__esModule",{value:!0});ob2.diagLogLevelFromString=void 0;var FP=ZQ(),rb2={ALL:FP.DiagLogLevel.ALL,VERBOSE:FP.DiagLogLevel.VERBOSE,DEBUG:FP.DiagLogLevel.DEBUG,INFO:FP.DiagLogLevel.INFO,WARN:FP.DiagLogLevel.WARN,ERROR:FP.DiagLogLevel.ERROR,NONE:FP.DiagLogLevel.NONE};function L66(A){if(A==null)return;let B=rb2[A.toUpperCase()];if(B==null)return FP.diag.warn(`Unknown log level "${A}", expected one of ${Object.keys(rb2)}, using default`),FP.DiagLogLevel.INFO;return B}ob2.diagLogLevelFromString=L66});
var eh2=E((oh2)=>{Object.defineProperty(oh2,"__esModule",{value:!0});oh2.processDetector=void 0;var r86=ZQ(),IP=GP(),o86=J1("os");class rh2{detect(A){let B={[IP.SEMRESATTRS_PROCESS_PID]:process.pid,[IP.SEMRESATTRS_PROCESS_EXECUTABLE_NAME]:process.title,[IP.SEMRESATTRS_PROCESS_EXECUTABLE_PATH]:process.execPath,[IP.SEMRESATTRS_PROCESS_COMMAND_ARGS]:[process.argv[0],...process.execArgv,...process.argv.slice(1)],[IP.SEMRESATTRS_PROCESS_RUNTIME_VERSION]:process.versions.node,[IP.SEMRESATTRS_PROCESS_RUNTIME_NAME]:"nodejs",[IP.SEMRESATTRS_PROCESS_RUNTIME_DESCRIPTION]:"Node.js"};if(process.argv.length>1)B[IP.SEMRESATTRS_PROCESS_COMMAND]=process.argv[1];try{let Q=o86.userInfo();B[IP.SEMRESATTRS_PROCESS_OWNER]=Q.username}catch(Q){r86.diag.debug(`error obtaining process owner: ${Q}`)}return{attributes:B}}}oh2.processDetector=new rh2});
var f51=E((SL2)=>{Object.defineProperty(SL2,"__esModule",{value:!0});SL2.ROOT_CONTEXT=SL2.createContextKey=void 0;function ki4(A){return Symbol.for(A)}SL2.createContextKey=ki4;class JO1{constructor(A){let B=this;B._currentContext=A?new Map(A):new Map,B.getValue=(Q)=>B._currentContext.get(Q),B.setValue=(Q,D)=>{let Z=new JO1(B._currentContext);return Z._currentContext.set(Q,D),Z},B.deleteValue=(Q)=>{let D=new JO1(B._currentContext);return D._currentContext.delete(Q),D}}}SL2.ROOT_CONTEXT=new JO1});
var fM2=E((bM2)=>{Object.defineProperty(bM2,"__esModule",{value:!0});bM2.SpanStatusCode=void 0;var Sn4;(function(A){A[A.UNSET=0]="UNSET",A[A.OK=1]="OK",A[A.ERROR=2]="ERROR"})(Sn4=bM2.SpanStatusCode||(bM2.SpanStatusCode={}))});
var fR2=E((vR2)=>{Object.defineProperty(vR2,"__esModule",{value:!0});vR2.trace=void 0;var Ga4=xR2();vR2.trace=Ga4.TraceAPI.getInstance()});
var fT2=E((vT2)=>{Object.defineProperty(vT2,"__esModule",{value:!0});vT2.globalErrorHandler=vT2.setGlobalErrorHandler=void 0;var Kr4=LF0(),xT2=Kr4.loggingErrorHandler();function Hr4(A){xT2=A}vT2.setGlobalErrorHandler=Hr4;function zr4(A){try{xT2(A)}catch{}}vT2.globalErrorHandler=zr4});
var f_=E((TV)=>{Object.defineProperty(TV,"__esModule",{value:!0});TV.TimeoutError=TV.createDenyListAttributesProcessor=TV.createAllowListAttributesProcessor=TV.AggregationType=TV.MeterProvider=TV.ConsoleMetricExporter=TV.InMemoryMetricExporter=TV.PeriodicExportingMetricReader=TV.MetricReader=TV.InstrumentType=TV.DataPointType=TV.AggregationTemporality=void 0;var b36=EO1();Object.defineProperty(TV,"AggregationTemporality",{enumerable:!0,get:function(){return b36.AggregationTemporality}});var Wm2=__();Object.defineProperty(TV,"DataPointType",{enumerable:!0,get:function(){return Wm2.DataPointType}});Object.defineProperty(TV,"InstrumentType",{enumerable:!0,get:function(){return Wm2.InstrumentType}});var f36=uF0();Object.defineProperty(TV,"MetricReader",{enumerable:!0,get:function(){return f36.MetricReader}});var h36=cf2();Object.defineProperty(TV,"PeriodicExportingMetricReader",{enumerable:!0,get:function(){return h36.PeriodicExportingMetricReader}});var g36=af2();Object.defineProperty(TV,"InMemoryMetricExporter",{enumerable:!0,get:function(){return g36.InMemoryMetricExporter}});var u36=tf2();Object.defineProperty(TV,"ConsoleMetricExporter",{enumerable:!0,get:function(){return u36.ConsoleMetricExporter}});var m36=Ym2();Object.defineProperty(TV,"MeterProvider",{enumerable:!0,get:function(){return m36.MeterProvider}});var d36=r51();Object.defineProperty(TV,"AggregationType",{enumerable:!0,get:function(){return d36.AggregationType}});var Jm2=lO1();Object.defineProperty(TV,"createAllowListAttributesProcessor",{enumerable:!0,get:function(){return Jm2.createAllowListAttributesProcessor}});Object.defineProperty(TV,"createDenyListAttributesProcessor",{enumerable:!0,get:function(){return Jm2.createDenyListAttributesProcessor}});var c36=cw();Object.defineProperty(TV,"TimeoutError",{enumerable:!0,get:function(){return c36.TimeoutError}})});
var fb2=E((vb2)=>{Object.defineProperty(vb2,"__esModule",{value:!0});vb2.callWithTimeout=vb2.TimeoutError=void 0;class jO1 extends Error{constructor(A){super(A);Object.setPrototypeOf(this,jO1.prototype)}}vb2.TimeoutError=jO1;function U66(A,B){let Q,D=new Promise(function Z(G,F){Q=setTimeout(function I(){F(new jO1("Operation timed out."))},B)});return Promise.race([A,D]).then((Z)=>{return clearTimeout(Q),Z},(Z)=>{throw clearTimeout(Q),Z})}vb2.callWithTimeout=U66});
var g51=E((AM2)=>{Object.defineProperty(AM2,"__esModule",{value:!0});AM2.ContextAPI=void 0;var ni4=tL2(),MG0=Wu(),eL2=Ju(),RG0="context",ai4=new ni4.NoopContextManager;class OG0{constructor(){}static getInstance(){if(!this._instance)this._instance=new OG0;return this._instance}setGlobalContextManager(A){return MG0.registerGlobal(RG0,A,eL2.DiagAPI.instance())}active(){return this._getContextManager().active()}with(A,B,Q,...D){return this._getContextManager().with(A,B,Q,...D)}bind(A,B){return this._getContextManager().bind(A,B)}_getContextManager(){return MG0.getGlobal(RG0)||ai4}disable(){this._getContextManager().disable(),MG0.unregisterGlobal(RG0,eL2.DiagAPI.instance())}}AM2.ContextAPI=OG0});
var gF0=E((yf2)=>{Object.defineProperty(yf2,"__esModule",{value:!0});yf2.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR=yf2.DEFAULT_AGGREGATION_SELECTOR=void 0;var B86=EO1(),Q86=r51(),D86=(A)=>{return{type:Q86.AggregationType.DEFAULT}};yf2.DEFAULT_AGGREGATION_SELECTOR=D86;var Z86=(A)=>B86.AggregationTemporality.CUMULATIVE;yf2.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR=Z86});
var gv2=E((fv2)=>{Object.defineProperty(fv2,"__esModule",{value:!0});fv2.SDK_INFO=void 0;var Z46=tT2(),l51=GP();fv2.SDK_INFO={[l51.SEMRESATTRS_TELEMETRY_SDK_NAME]:"opentelemetry",[l51.SEMRESATTRS_PROCESS_RUNTIME_NAME]:"node",[l51.SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]:l51.TELEMETRYSDKLANGUAGEVALUES_NODEJS,[l51.SEMRESATTRS_TELEMETRY_SDK_VERSION]:Z46.VERSION}});
var hO1=E((zh2)=>{Object.defineProperty(zh2,"__esModule",{value:!0});zh2.execAsync=void 0;var T86=J1("child_process"),P86=J1("util");zh2.execAsync=P86.promisify(T86.exec)});
var hc2=E((bc2)=>{Object.defineProperty(bc2,"__esModule",{value:!0});bc2.VERSION=void 0;bc2.VERSION="0.200.0"});
var hd2=E((bd2)=>{Object.defineProperty(bd2,"__esModule",{value:!0});bd2.hexToBinary=void 0;function vd2(A){if(A>=48&&A<=57)return A-48;if(A>=97&&A<=102)return A-87;return A-55}function x76(A){let B=new Uint8Array(A.length/2),Q=0;for(let D=0;D<A.length;D+=2){let Z=vd2(A.charCodeAt(D)),G=vd2(A.charCodeAt(D+1));B[Q++]=Z<<4|G}return B}bd2.hexToBinary=x76});
var iL2=E((pL2)=>{Object.defineProperty(pL2,"__esModule",{value:!0});pL2.ValueType=void 0;var li4;(function(A){A[A.INT=0]="INT",A[A.DOUBLE=1]="DOUBLE"})(li4=pL2.ValueType||(pL2.ValueType={}))});
var iO2=E((lO2)=>{Object.defineProperty(lO2,"__esModule",{value:!0});lO2.ExponentMapping=void 0;var Po=zF0(),Ps4=wO1(),dO2=$O1();class cO2{_shift;constructor(A){this._shift=-A}mapToIndex(A){if(A<Po.MIN_VALUE)return this._minNormalLowerBoundaryIndex();let B=Po.getNormalBase2(A),Q=this._rightShift(Po.getSignificand(A)-1,Po.SIGNIFICAND_WIDTH);return B+Q>>this._shift}lowerBoundary(A){let B=this._minNormalLowerBoundaryIndex();if(A<B)throw new dO2.MappingError(`underflow: ${A} is < minimum lower boundary: ${B}`);let Q=this._maxNormalLowerBoundaryIndex();if(A>Q)throw new dO2.MappingError(`overflow: ${A} is > maximum lower boundary: ${Q}`);return Ps4.ldexp(1,A<<this._shift)}get scale(){if(this._shift===0)return 0;return-this._shift}_minNormalLowerBoundaryIndex(){let A=Po.MIN_NORMAL_EXPONENT>>this._shift;if(this._shift<2)A--;return A}_maxNormalLowerBoundaryIndex(){return Po.MAX_NORMAL_EXPONENT>>this._shift}_rightShift(A,B){return Math.floor(A*Math.pow(2,-B))}}lO2.ExponentMapping=cO2});
var iT2=E((lT2)=>{Object.defineProperty(lT2,"__esModule",{value:!0});lT2._globalThis=void 0;lT2._globalThis=typeof globalThis==="object"?globalThis:global});
var im2=E((pm2)=>{var sO1=pm2;sO1.length=function A(B){var Q=B.length;if(!Q)return 0;var D=0;while(--Q%4>1&&B.charAt(Q)==="=")++D;return Math.ceil(B.length*3)/4-D};var ho=new Array(64),lm2=new Array(123);for(JE=0;JE<64;)lm2[ho[JE]=JE<26?JE+65:JE<52?JE+71:JE<62?JE-4:JE-59|43]=JE++;var JE;sO1.encode=function A(B,Q,D){var Z=null,G=[],F=0,I=0,Y;while(Q<D){var W=B[Q++];switch(I){case 0:G[F++]=ho[W>>2],Y=(W&3)<<4,I=1;break;case 1:G[F++]=ho[Y|W>>4],Y=(W&15)<<2,I=2;break;case 2:G[F++]=ho[Y|W>>6],G[F++]=ho[W&63],I=0;break}if(F>8191)(Z||(Z=[])).push(String.fromCharCode.apply(String,G)),F=0}if(I){if(G[F++]=ho[Y],G[F++]=61,I===1)G[F++]=61}if(Z){if(F)Z.push(String.fromCharCode.apply(String,G.slice(0,F)));return Z.join("")}return String.fromCharCode.apply(String,G.slice(0,F))};var cm2="invalid encoding";sO1.decode=function A(B,Q,D){var Z=D,G=0,F;for(var I=0;I<B.length;){var Y=B.charCodeAt(I++);if(Y===61&&G>1)break;if((Y=lm2[Y])===void 0)throw Error(cm2);switch(G){case 0:F=Y,G=1;break;case 1:Q[D++]=F<<2|(Y&48)>>4,F=Y,G=2;break;case 2:Q[D++]=(F&15)<<4|(Y&60)>>2,F=Y,G=3;break;case 3:Q[D++]=(F&3)<<6|Y,G=0;break}}if(G===1)throw Error(cm2);return D-Z};sO1.test=function A(B){return/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(B)}});
var jc2=E((Pc2)=>{Object.defineProperty(Pc2,"__esModule",{value:!0});Pc2.JsonMetricsSerializer=void 0;var bD6=dI0();Pc2.JsonMetricsSerializer={serializeRequest:(A)=>{let B=bD6.createExportMetricsServiceRequest([A],{useLongBits:!1});return new TextEncoder().encode(JSON.stringify(B))},deserializeResponse:(A)=>{return JSON.parse(new TextDecoder().decode(A))}}});
var jg2=E((Pg2)=>{Object.defineProperty(Pg2,"__esModule",{value:!0});Pg2.Meter=void 0;var Lu=A31(),Mu=dO1(),Ru=__();class Tg2{_meterSharedState;constructor(A){this._meterSharedState=A}createGauge(A,B){let Q=Lu.createInstrumentDescriptor(A,Ru.InstrumentType.GAUGE,B),D=this._meterSharedState.registerMetricStorage(Q);return new Mu.GaugeInstrument(D,Q)}createHistogram(A,B){let Q=Lu.createInstrumentDescriptor(A,Ru.InstrumentType.HISTOGRAM,B),D=this._meterSharedState.registerMetricStorage(Q);return new Mu.HistogramInstrument(D,Q)}createCounter(A,B){let Q=Lu.createInstrumentDescriptor(A,Ru.InstrumentType.COUNTER,B),D=this._meterSharedState.registerMetricStorage(Q);return new Mu.CounterInstrument(D,Q)}createUpDownCounter(A,B){let Q=Lu.createInstrumentDescriptor(A,Ru.InstrumentType.UP_DOWN_COUNTER,B),D=this._meterSharedState.registerMetricStorage(Q);return new Mu.UpDownCounterInstrument(D,Q)}createObservableGauge(A,B){let Q=Lu.createInstrumentDescriptor(A,Ru.InstrumentType.OBSERVABLE_GAUGE,B),D=this._meterSharedState.registerAsyncMetricStorage(Q);return new Mu.ObservableGaugeInstrument(Q,D,this._meterSharedState.observableRegistry)}createObservableCounter(A,B){let Q=Lu.createInstrumentDescriptor(A,Ru.InstrumentType.OBSERVABLE_COUNTER,B),D=this._meterSharedState.registerAsyncMetricStorage(Q);return new Mu.ObservableCounterInstrument(Q,D,this._meterSharedState.observableRegistry)}createObservableUpDownCounter(A,B){let Q=Lu.createInstrumentDescriptor(A,Ru.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER,B),D=this._meterSharedState.registerAsyncMetricStorage(Q);return new Mu.ObservableUpDownCounterInstrument(Q,D,this._meterSharedState.observableRegistry)}addBatchObservableCallback(A,B){this._meterSharedState.observableRegistry.addBatchCallback(A,B)}removeBatchObservableCallback(A,B){this._meterSharedState.observableRegistry.removeBatchCallback(A,B)}}Pg2.Meter=Tg2});
var jl2=E((Pl2)=>{Object.defineProperty(Pl2,"__esModule",{value:!0});Pl2.OTLPMetricExporter=void 0;var iZ6=EI0(),nZ6=ju(),aZ6=hc2(),Ol2=uo(),sZ6={"User-Agent":`OTel-OTLP-Exporter-JavaScript/${aZ6.VERSION}`};class Tl2 extends iZ6.OTLPMetricExporterBase{constructor(A){super(Ol2.createOtlpHttpExportDelegate(Ol2.convertLegacyHttpOptions(A??{},"METRICS","v1/metrics",{...sZ6,"Content-Type":"application/json"}),nZ6.JsonMetricsSerializer),A)}}Pl2.OTLPMetricExporter=Tl2});
var ju=E((m_)=>{Object.defineProperty(m_,"__esModule",{value:!0});m_.JsonTraceSerializer=m_.JsonMetricsSerializer=m_.JsonLogsSerializer=m_.ProtobufTraceSerializer=m_.ProtobufMetricsSerializer=m_.ProtobufLogsSerializer=void 0;var dD6=Bc2();Object.defineProperty(m_,"ProtobufLogsSerializer",{enumerable:!0,get:function(){return dD6.ProtobufLogsSerializer}});var cD6=Cc2();Object.defineProperty(m_,"ProtobufMetricsSerializer",{enumerable:!0,get:function(){return cD6.ProtobufMetricsSerializer}});var lD6=Lc2();Object.defineProperty(m_,"ProtobufTraceSerializer",{enumerable:!0,get:function(){return lD6.ProtobufTraceSerializer}});var pD6=Tc2();Object.defineProperty(m_,"JsonLogsSerializer",{enumerable:!0,get:function(){return pD6.JsonLogsSerializer}});var iD6=kc2();Object.defineProperty(m_,"JsonMetricsSerializer",{enumerable:!0,get:function(){return iD6.JsonMetricsSerializer}});var nD6=vc2();Object.defineProperty(m_,"JsonTraceSerializer",{enumerable:!0,get:function(){return nD6.JsonTraceSerializer}})});
var kG0=E((XM2)=>{Object.defineProperty(XM2,"__esModule",{value:!0});XM2.getSpanContext=XM2.setSpanContext=XM2.deleteSpan=XM2.setSpan=XM2.getActiveSpan=XM2.getSpan=void 0;var ti4=f51(),ei4=VO1(),An4=g51(),SG0=ti4.createContextKey("OpenTelemetry Context Key SPAN");function jG0(A){return A.getValue(SG0)||void 0}XM2.getSpan=jG0;function Bn4(){return jG0(An4.ContextAPI.getInstance().active())}XM2.getActiveSpan=Bn4;function JM2(A,B){return A.setValue(SG0,B)}XM2.setSpan=JM2;function Qn4(A){return A.deleteValue(SG0)}XM2.deleteSpan=Qn4;function Dn4(A,B){return JM2(A,new ei4.NonRecordingSpan(B))}XM2.setSpanContext=Dn4;function Zn4(A){var B;return(B=jG0(A))===null||B===void 0?void 0:B.spanContext()}XM2.getSpanContext=Zn4});
var kO2=E((SO2)=>{Object.defineProperty(SO2,"__esModule",{value:!0});SO2.HistogramAggregator=SO2.HistogramAccumulation=void 0;var Vs4=To(),u51=__(),Cs4=cw();function Ks4(A){let B=A.map(()=>0);return B.push(0),{buckets:{boundaries:A,counts:B},sum:0,count:0,hasMinMax:!1,min:1/0,max:-1/0}}class m51{startTime;_boundaries;_recordMinMax;_current;constructor(A,B,Q=!0,D=Ks4(B)){this.startTime=A,this._boundaries=B,this._recordMinMax=Q,this._current=D}record(A){if(Number.isNaN(A))return;if(this._current.count+=1,this._current.sum+=A,this._recordMinMax)this._current.min=Math.min(A,this._current.min),this._current.max=Math.max(A,this._current.max),this._current.hasMinMax=!0;let B=Cs4.binarySearchUB(this._boundaries,A);this._current.buckets.counts[B]+=1}setStartTime(A){this.startTime=A}toPointValue(){return this._current}}SO2.HistogramAccumulation=m51;class PO2{_boundaries;_recordMinMax;kind=Vs4.AggregatorKind.HISTOGRAM;constructor(A,B){this._boundaries=A,this._recordMinMax=B}createAccumulation(A){return new m51(A,this._boundaries,this._recordMinMax)}merge(A,B){let Q=A.toPointValue(),D=B.toPointValue(),Z=Q.buckets.counts,G=D.buckets.counts,F=new Array(Z.length);for(let W=0;W<Z.length;W++)F[W]=Z[W]+G[W];let I=1/0,Y=-1/0;if(this._recordMinMax){if(Q.hasMinMax&&D.hasMinMax)I=Math.min(Q.min,D.min),Y=Math.max(Q.max,D.max);else if(Q.hasMinMax)I=Q.min,Y=Q.max;else if(D.hasMinMax)I=D.min,Y=D.max}return new m51(A.startTime,Q.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:Q.buckets.boundaries,counts:F},count:Q.count+D.count,sum:Q.sum+D.sum,hasMinMax:this._recordMinMax&&(Q.hasMinMax||D.hasMinMax),min:I,max:Y})}diff(A,B){let Q=A.toPointValue(),D=B.toPointValue(),Z=Q.buckets.counts,G=D.buckets.counts,F=new Array(Z.length);for(let I=0;I<Z.length;I++)F[I]=G[I]-Z[I];return new m51(B.startTime,Q.buckets.boundaries,this._recordMinMax,{buckets:{boundaries:Q.buckets.boundaries,counts:F},count:D.count-Q.count,sum:D.sum-Q.sum,hasMinMax:!1,min:1/0,max:-1/0})}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:u51.DataPointType.HISTOGRAM,dataPoints:Q.map(([Z,G])=>{let F=G.toPointValue(),I=A.type===u51.InstrumentType.GAUGE||A.type===u51.InstrumentType.UP_DOWN_COUNTER||A.type===u51.InstrumentType.OBSERVABLE_GAUGE||A.type===u51.InstrumentType.OBSERVABLE_UP_DOWN_COUNTER;return{attributes:Z,startTime:G.startTime,endTime:D,value:{min:F.hasMinMax?F.min:void 0,max:F.hasMinMax?F.max:void 0,sum:!I?F.sum:void 0,buckets:F.buckets,count:F.count}}})}}}SO2.HistogramAggregator=PO2});
var kT2=E((ST2)=>{Object.defineProperty(ST2,"__esModule",{value:!0});ST2.isAttributeValue=ST2.isAttributeKey=ST2.sanitizeAttributes=void 0;var RT2=ZQ();function Fr4(A){let B={};if(typeof A!=="object"||A==null)return B;for(let[Q,D]of Object.entries(A)){if(!OT2(Q)){RT2.diag.warn(`Invalid attribute key: ${Q}`);continue}if(!TT2(D)){RT2.diag.warn(`Invalid attribute value set for key: ${Q}`);continue}if(Array.isArray(D))B[Q]=D.slice();else B[Q]=D}return B}ST2.sanitizeAttributes=Fr4;function OT2(A){return typeof A==="string"&&A.length>0}ST2.isAttributeKey=OT2;function TT2(A){if(A==null)return!0;if(Array.isArray(A))return Ir4(A);return PT2(A)}ST2.isAttributeValue=TT2;function Ir4(A){let B;for(let Q of A){if(Q==null)continue;if(!B){if(PT2(Q)){B=typeof Q;continue}return!1}if(typeof Q===B)continue;return!1}return!0}function PT2(A){switch(typeof A){case"number":case"boolean":case"string":return!0}return!1}});
var kc2=E((nI0)=>{Object.defineProperty(nI0,"__esModule",{value:!0});nI0.JsonMetricsSerializer=void 0;var fD6=jc2();Object.defineProperty(nI0,"JsonMetricsSerializer",{enumerable:!0,get:function(){return fD6.JsonMetricsSerializer}})});
var kh2=E((Sh2)=>{Object.defineProperty(Sh2,"__esModule",{value:!0});Sh2.getMachineId=void 0;var Ph2=J1("process"),h86=hO1(),g86=ZQ();async function u86(){let B="%windir%\\System32\\REG.exe";if(Ph2.arch==="ia32"&&"PROCESSOR_ARCHITEW6432"in Ph2.env)B="%windir%\\sysnative\\cmd.exe /c "+B;try{let D=(await h86.execAsync(`${B} QUERY HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid`)).stdout.split("REG_SZ");if(D.length===2)return D[1].trim()}catch(Q){g86.diag.debug(`error reading machine id: ${Q}`)}return}Sh2.getMachineId=u86});
var kl2=E((oI0)=>{Object.defineProperty(oI0,"__esModule",{value:!0});oI0.OTLPMetricExporter=void 0;var rZ6=jl2();Object.defineProperty(oI0,"OTLPMetricExporter",{enumerable:!0,get:function(){return rZ6.OTLPMetricExporter}})});
var lI0=E((Ec2)=>{Object.defineProperty(Ec2,"__esModule",{value:!0});Ec2.createExportTraceServiceRequest=Ec2.toOtlpSpanEvent=Ec2.toOtlpLink=Ec2.sdkSpanToOtlpSpan=void 0;var W31=DT1(),qD6=QT1();function Kc2(A,B){let Q=A.spanContext(),D=A.status,Z=A.parentSpanContext?.spanId?B.encodeSpanContext(A.parentSpanContext?.spanId):void 0;return{traceId:B.encodeSpanContext(Q.traceId),spanId:B.encodeSpanContext(Q.spanId),parentSpanId:Z,traceState:Q.traceState?.serialize(),name:A.name,kind:A.kind==null?0:A.kind+1,startTimeUnixNano:B.encodeHrTime(A.startTime),endTimeUnixNano:B.encodeHrTime(A.endTime),attributes:W31.toAttributes(A.attributes),droppedAttributesCount:A.droppedAttributesCount,events:A.events.map((G)=>zc2(G,B)),droppedEventsCount:A.droppedEventsCount,status:{code:D.code,message:D.message},links:A.links.map((G)=>Hc2(G,B)),droppedLinksCount:A.droppedLinksCount}}Ec2.sdkSpanToOtlpSpan=Kc2;function Hc2(A,B){return{attributes:A.attributes?W31.toAttributes(A.attributes):[],spanId:B.encodeSpanContext(A.context.spanId),traceId:B.encodeSpanContext(A.context.traceId),traceState:A.context.traceState?.serialize(),droppedAttributesCount:A.droppedAttributesCount||0}}Ec2.toOtlpLink=Hc2;function zc2(A,B){return{attributes:A.attributes?W31.toAttributes(A.attributes):[],name:A.name,timeUnixNano:B.encodeHrTime(A.time),droppedAttributesCount:A.droppedAttributesCount||0}}Ec2.toOtlpSpanEvent=zc2;function ND6(A,B){let Q=qD6.getOtlpEncoder(B);return{resourceSpans:MD6(A,Q)}}Ec2.createExportTraceServiceRequest=ND6;function LD6(A){let B=new Map;for(let Q of A){let D=B.get(Q.resource);if(!D)D=new Map,B.set(Q.resource,D);let Z=`${Q.instrumentationScope.name}@${Q.instrumentationScope.version||""}:${Q.instrumentationScope.schemaUrl||""}`,G=D.get(Z);if(!G)G=[],D.set(Z,G);G.push(Q)}return B}function MD6(A,B){let Q=LD6(A),D=[],Z=Q.entries(),G=Z.next();while(!G.done){let[F,I]=G.value,Y=[],W=I.values(),J=W.next();while(!J.done){let V=J.value;if(V.length>0){let C=V.map((K)=>Kc2(K,B));Y.push({scope:W31.createInstrumentationScope(V[0].instrumentationScope),spans:C,schemaUrl:V[0].instrumentationScope.schemaUrl})}J=W.next()}let X={resource:W31.createResource(F),scopeSpans:Y,schemaUrl:void 0};D.push(X),G=Z.next()}return D}});
var lO1=E((Tu2)=>{Object.defineProperty(Tu2,"__esModule",{value:!0});Tu2.createDenyListAttributesProcessor=Tu2.createAllowListAttributesProcessor=Tu2.createMultiAttributesProcessor=Tu2.createNoopAttributesProcessor=void 0;class Lu2{process(A,B){return A}}class Mu2{_processors;constructor(A){this._processors=A}process(A,B){let Q=A;for(let D of this._processors)Q=D.process(Q,B);return Q}}class Ru2{_allowedAttributeNames;constructor(A){this._allowedAttributeNames=A}process(A,B){let Q={};return Object.keys(A).filter((D)=>this._allowedAttributeNames.includes(D)).forEach((D)=>Q[D]=A[D]),Q}}class Ou2{_deniedAttributeNames;constructor(A){this._deniedAttributeNames=A}process(A,B){let Q={};return Object.keys(A).filter((D)=>!this._deniedAttributeNames.includes(D)).forEach((D)=>Q[D]=A[D]),Q}}function D36(){return I36}Tu2.createNoopAttributesProcessor=D36;function Z36(A){return new Mu2(A)}Tu2.createMultiAttributesProcessor=Z36;function G36(A){return new Ru2(A)}Tu2.createAllowListAttributesProcessor=G36;function F36(A){return new Ou2(A)}Tu2.createDenyListAttributesProcessor=F36;var I36=new Lu2});
var mO1=E((WE)=>{Object.defineProperty(WE,"__esModule",{value:!0});WE.defaultServiceName=WE.emptyResource=WE.defaultResource=WE.resourceFromAttributes=WE.serviceInstanceIdDetector=WE.processDetector=WE.osDetector=WE.hostDetector=WE.envDetector=WE.detectResources=void 0;var J56=Jh2();Object.defineProperty(WE,"detectResources",{enumerable:!0,get:function(){return J56.detectResources}});var e51=Wg2();Object.defineProperty(WE,"envDetector",{enumerable:!0,get:function(){return e51.envDetector}});Object.defineProperty(WE,"hostDetector",{enumerable:!0,get:function(){return e51.hostDetector}});Object.defineProperty(WE,"osDetector",{enumerable:!0,get:function(){return e51.osDetector}});Object.defineProperty(WE,"processDetector",{enumerable:!0,get:function(){return e51.processDetector}});Object.defineProperty(WE,"serviceInstanceIdDetector",{enumerable:!0,get:function(){return e51.serviceInstanceIdDetector}});var BI0=sF0();Object.defineProperty(WE,"resourceFromAttributes",{enumerable:!0,get:function(){return BI0.resourceFromAttributes}});Object.defineProperty(WE,"defaultResource",{enumerable:!0,get:function(){return BI0.defaultResource}});Object.defineProperty(WE,"emptyResource",{enumerable:!0,get:function(){return BI0.emptyResource}});var X56=pF0();Object.defineProperty(WE,"defaultServiceName",{enumerable:!0,get:function(){return X56.defaultServiceName}})});
var mb2=E((gb2)=>{Object.defineProperty(gb2,"__esModule",{value:!0});gb2.isUrlIgnored=gb2.urlMatches=void 0;function hb2(A,B){if(typeof B==="string")return A===B;else return!!A.match(B)}gb2.urlMatches=hb2;function $66(A,B){if(!B)return!1;for(let Q of B)if(hb2(A,Q))return!0;return!1}gb2.isUrlIgnored=$66});
var mc2=E((gc2)=>{Object.defineProperty(gc2,"__esModule",{value:!0});gc2.parseRetryAfterToMills=gc2.isExportRetryable=void 0;function sD6(A){return[429,502,503,504].includes(A)}gc2.isExportRetryable=sD6;function rD6(A){if(A==null)return;let B=Number.parseInt(A,10);if(Number.isInteger(B))return B>0?B*1000:-1;let Q=new Date(A).getTime()-Date.now();if(Q>=0)return Q;return 0}gc2.parseRetryAfterToMills=rD6});
var ml2=E((eI0)=>{Object.defineProperty(eI0,"__esModule",{value:!0});eI0.OTLPMetricExporter=void 0;var FG6=ul2();Object.defineProperty(eI0,"OTLPMetricExporter",{enumerable:!0,get:function(){return FG6.OTLPMetricExporter}})});
var nM2=E((pM2)=>{Object.defineProperty(pM2,"__esModule",{value:!0});pM2.TraceStateImpl=void 0;var mM2=uM2(),dM2=32,hn4=512,cM2=",",lM2="=";class mG0{constructor(A){if(this._internalState=new Map,A)this._parse(A)}set(A,B){let Q=this._clone();if(Q._internalState.has(A))Q._internalState.delete(A);return Q._internalState.set(A,B),Q}unset(A){let B=this._clone();return B._internalState.delete(A),B}get(A){return this._internalState.get(A)}serialize(){return this._keys().reduce((A,B)=>{return A.push(B+lM2+this.get(B)),A},[]).join(cM2)}_parse(A){if(A.length>hn4)return;if(this._internalState=A.split(cM2).reverse().reduce((B,Q)=>{let D=Q.trim(),Z=D.indexOf(lM2);if(Z!==-1){let G=D.slice(0,Z),F=D.slice(Z+1,Q.length);if(mM2.validateKey(G)&&mM2.validateValue(F))B.set(G,F)}return B},new Map),this._internalState.size>dM2)this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,dM2))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let A=new mG0;return A._internalState=new Map(this._internalState),A}}pM2.TraceStateImpl=mG0});
var nO1=E((Em2)=>{Object.defineProperty(Em2,"__esModule",{value:!0});Em2.OTLPExporterError=void 0;class zm2 extends Error{code;name="OTLPExporterError";data;constructor(A,B,Q){super(A);this.data=Q,this.code=B}}Em2.OTLPExporterError=zm2});
var ov2=E((rv2)=>{Object.defineProperty(rv2,"__esModule",{value:!0});rv2.ExportResultCode=void 0;var y46;(function(A){A[A.SUCCESS=0]="SUCCESS",A[A.FAILED=1]="FAILED"})(y46=rv2.ExportResultCode||(rv2.ExportResultCode={}))});
var oy2=E((ly2)=>{Object.defineProperty(ly2,"__esModule",{value:!0});ly2.SEMATTRS_NET_HOST_CARRIER_ICC=ly2.SEMATTRS_NET_HOST_CARRIER_MNC=ly2.SEMATTRS_NET_HOST_CARRIER_MCC=ly2.SEMATTRS_NET_HOST_CARRIER_NAME=ly2.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE=ly2.SEMATTRS_NET_HOST_CONNECTION_TYPE=ly2.SEMATTRS_NET_HOST_NAME=ly2.SEMATTRS_NET_HOST_PORT=ly2.SEMATTRS_NET_HOST_IP=ly2.SEMATTRS_NET_PEER_NAME=ly2.SEMATTRS_NET_PEER_PORT=ly2.SEMATTRS_NET_PEER_IP=ly2.SEMATTRS_NET_TRANSPORT=ly2.SEMATTRS_FAAS_INVOKED_REGION=ly2.SEMATTRS_FAAS_INVOKED_PROVIDER=ly2.SEMATTRS_FAAS_INVOKED_NAME=ly2.SEMATTRS_FAAS_COLDSTART=ly2.SEMATTRS_FAAS_CRON=ly2.SEMATTRS_FAAS_TIME=ly2.SEMATTRS_FAAS_DOCUMENT_NAME=ly2.SEMATTRS_FAAS_DOCUMENT_TIME=ly2.SEMATTRS_FAAS_DOCUMENT_OPERATION=ly2.SEMATTRS_FAAS_DOCUMENT_COLLECTION=ly2.SEMATTRS_FAAS_EXECUTION=ly2.SEMATTRS_FAAS_TRIGGER=ly2.SEMATTRS_EXCEPTION_ESCAPED=ly2.SEMATTRS_EXCEPTION_STACKTRACE=ly2.SEMATTRS_EXCEPTION_MESSAGE=ly2.SEMATTRS_EXCEPTION_TYPE=ly2.SEMATTRS_DB_SQL_TABLE=ly2.SEMATTRS_DB_MONGODB_COLLECTION=ly2.SEMATTRS_DB_REDIS_DATABASE_INDEX=ly2.SEMATTRS_DB_HBASE_NAMESPACE=ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC=ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID=ly2.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT=ly2.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE=ly2.SEMATTRS_DB_CASSANDRA_TABLE=ly2.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL=ly2.SEMATTRS_DB_CASSANDRA_PAGE_SIZE=ly2.SEMATTRS_DB_CASSANDRA_KEYSPACE=ly2.SEMATTRS_DB_MSSQL_INSTANCE_NAME=ly2.SEMATTRS_DB_OPERATION=ly2.SEMATTRS_DB_STATEMENT=ly2.SEMATTRS_DB_NAME=ly2.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME=ly2.SEMATTRS_DB_USER=ly2.SEMATTRS_DB_CONNECTION_STRING=ly2.SEMATTRS_DB_SYSTEM=ly2.SEMATTRS_AWS_LAMBDA_INVOKED_ARN=void 0;ly2.SEMATTRS_MESSAGING_DESTINATION_KIND=ly2.SEMATTRS_MESSAGING_DESTINATION=ly2.SEMATTRS_MESSAGING_SYSTEM=ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES=ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS=ly2.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT=ly2.SEMATTRS_AWS_DYNAMODB_COUNT=ly2.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS=ly2.SEMATTRS_AWS_DYNAMODB_SEGMENT=ly2.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD=ly2.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT=ly2.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE=ly2.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES=ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES=ly2.SEMATTRS_AWS_DYNAMODB_SELECT=ly2.SEMATTRS_AWS_DYNAMODB_INDEX_NAME=ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET=ly2.SEMATTRS_AWS_DYNAMODB_LIMIT=ly2.SEMATTRS_AWS_DYNAMODB_PROJECTION=ly2.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ=ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY=ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY=ly2.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS=ly2.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY=ly2.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES=ly2.SEMATTRS_HTTP_CLIENT_IP=ly2.SEMATTRS_HTTP_ROUTE=ly2.SEMATTRS_HTTP_SERVER_NAME=ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED=ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH=ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED=ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH=ly2.SEMATTRS_HTTP_USER_AGENT=ly2.SEMATTRS_HTTP_FLAVOR=ly2.SEMATTRS_HTTP_STATUS_CODE=ly2.SEMATTRS_HTTP_SCHEME=ly2.SEMATTRS_HTTP_HOST=ly2.SEMATTRS_HTTP_TARGET=ly2.SEMATTRS_HTTP_URL=ly2.SEMATTRS_HTTP_METHOD=ly2.SEMATTRS_CODE_LINENO=ly2.SEMATTRS_CODE_FILEPATH=ly2.SEMATTRS_CODE_NAMESPACE=ly2.SEMATTRS_CODE_FUNCTION=ly2.SEMATTRS_THREAD_NAME=ly2.SEMATTRS_THREAD_ID=ly2.SEMATTRS_ENDUSER_SCOPE=ly2.SEMATTRS_ENDUSER_ROLE=ly2.SEMATTRS_ENDUSER_ID=ly2.SEMATTRS_PEER_SERVICE=void 0;ly2.DBSYSTEMVALUES_FILEMAKER=ly2.DBSYSTEMVALUES_DERBY=ly2.DBSYSTEMVALUES_FIREBIRD=ly2.DBSYSTEMVALUES_ADABAS=ly2.DBSYSTEMVALUES_CACHE=ly2.DBSYSTEMVALUES_EDB=ly2.DBSYSTEMVALUES_FIRSTSQL=ly2.DBSYSTEMVALUES_INGRES=ly2.DBSYSTEMVALUES_HANADB=ly2.DBSYSTEMVALUES_MAXDB=ly2.DBSYSTEMVALUES_PROGRESS=ly2.DBSYSTEMVALUES_HSQLDB=ly2.DBSYSTEMVALUES_CLOUDSCAPE=ly2.DBSYSTEMVALUES_HIVE=ly2.DBSYSTEMVALUES_REDSHIFT=ly2.DBSYSTEMVALUES_POSTGRESQL=ly2.DBSYSTEMVALUES_DB2=ly2.DBSYSTEMVALUES_ORACLE=ly2.DBSYSTEMVALUES_MYSQL=ly2.DBSYSTEMVALUES_MSSQL=ly2.DBSYSTEMVALUES_OTHER_SQL=ly2.SemanticAttributes=ly2.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE=ly2.SEMATTRS_MESSAGE_COMPRESSED_SIZE=ly2.SEMATTRS_MESSAGE_ID=ly2.SEMATTRS_MESSAGE_TYPE=ly2.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE=ly2.SEMATTRS_RPC_JSONRPC_ERROR_CODE=ly2.SEMATTRS_RPC_JSONRPC_REQUEST_ID=ly2.SEMATTRS_RPC_JSONRPC_VERSION=ly2.SEMATTRS_RPC_GRPC_STATUS_CODE=ly2.SEMATTRS_RPC_METHOD=ly2.SEMATTRS_RPC_SERVICE=ly2.SEMATTRS_RPC_SYSTEM=ly2.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE=ly2.SEMATTRS_MESSAGING_KAFKA_PARTITION=ly2.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID=ly2.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP=ly2.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY=ly2.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY=ly2.SEMATTRS_MESSAGING_CONSUMER_ID=ly2.SEMATTRS_MESSAGING_OPERATION=ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES=ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES=ly2.SEMATTRS_MESSAGING_CONVERSATION_ID=ly2.SEMATTRS_MESSAGING_MESSAGE_ID=ly2.SEMATTRS_MESSAGING_URL=ly2.SEMATTRS_MESSAGING_PROTOCOL_VERSION=ly2.SEMATTRS_MESSAGING_PROTOCOL=ly2.SEMATTRS_MESSAGING_TEMP_DESTINATION=void 0;ly2.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD=ly2.FaasDocumentOperationValues=ly2.FAASDOCUMENTOPERATIONVALUES_DELETE=ly2.FAASDOCUMENTOPERATIONVALUES_EDIT=ly2.FAASDOCUMENTOPERATIONVALUES_INSERT=ly2.FaasTriggerValues=ly2.FAASTRIGGERVALUES_OTHER=ly2.FAASTRIGGERVALUES_TIMER=ly2.FAASTRIGGERVALUES_PUBSUB=ly2.FAASTRIGGERVALUES_HTTP=ly2.FAASTRIGGERVALUES_DATASOURCE=ly2.DbCassandraConsistencyLevelValues=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ANY=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_THREE=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_TWO=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ONE=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM=ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ALL=ly2.DbSystemValues=ly2.DBSYSTEMVALUES_COCKROACHDB=ly2.DBSYSTEMVALUES_MEMCACHED=ly2.DBSYSTEMVALUES_ELASTICSEARCH=ly2.DBSYSTEMVALUES_GEODE=ly2.DBSYSTEMVALUES_NEO4J=ly2.DBSYSTEMVALUES_DYNAMODB=ly2.DBSYSTEMVALUES_COSMOSDB=ly2.DBSYSTEMVALUES_COUCHDB=ly2.DBSYSTEMVALUES_COUCHBASE=ly2.DBSYSTEMVALUES_REDIS=ly2.DBSYSTEMVALUES_MONGODB=ly2.DBSYSTEMVALUES_HBASE=ly2.DBSYSTEMVALUES_CASSANDRA=ly2.DBSYSTEMVALUES_COLDFUSION=ly2.DBSYSTEMVALUES_H2=ly2.DBSYSTEMVALUES_VERTICA=ly2.DBSYSTEMVALUES_TERADATA=ly2.DBSYSTEMVALUES_SYBASE=ly2.DBSYSTEMVALUES_SQLITE=ly2.DBSYSTEMVALUES_POINTBASE=ly2.DBSYSTEMVALUES_PERVASIVE=ly2.DBSYSTEMVALUES_NETEZZA=ly2.DBSYSTEMVALUES_MARIADB=ly2.DBSYSTEMVALUES_INTERBASE=ly2.DBSYSTEMVALUES_INSTANTDB=ly2.DBSYSTEMVALUES_INFORMIX=void 0;ly2.MESSAGINGOPERATIONVALUES_RECEIVE=ly2.MessagingDestinationKindValues=ly2.MESSAGINGDESTINATIONKINDVALUES_TOPIC=ly2.MESSAGINGDESTINATIONKINDVALUES_QUEUE=ly2.HttpFlavorValues=ly2.HTTPFLAVORVALUES_QUIC=ly2.HTTPFLAVORVALUES_SPDY=ly2.HTTPFLAVORVALUES_HTTP_2_0=ly2.HTTPFLAVORVALUES_HTTP_1_1=ly2.HTTPFLAVORVALUES_HTTP_1_0=ly2.NetHostConnectionSubtypeValues=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NR=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GSM=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE=ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS=ly2.NetHostConnectionTypeValues=ly2.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN=ly2.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE=ly2.NETHOSTCONNECTIONTYPEVALUES_CELL=ly2.NETHOSTCONNECTIONTYPEVALUES_WIRED=ly2.NETHOSTCONNECTIONTYPEVALUES_WIFI=ly2.NetTransportValues=ly2.NETTRANSPORTVALUES_OTHER=ly2.NETTRANSPORTVALUES_INPROC=ly2.NETTRANSPORTVALUES_PIPE=ly2.NETTRANSPORTVALUES_UNIX=ly2.NETTRANSPORTVALUES_IP=ly2.NETTRANSPORTVALUES_IP_UDP=ly2.NETTRANSPORTVALUES_IP_TCP=ly2.FaasInvokedProviderValues=ly2.FAASINVOKEDPROVIDERVALUES_GCP=ly2.FAASINVOKEDPROVIDERVALUES_AZURE=ly2.FAASINVOKEDPROVIDERVALUES_AWS=void 0;ly2.MessageTypeValues=ly2.MESSAGETYPEVALUES_RECEIVED=ly2.MESSAGETYPEVALUES_SENT=ly2.RpcGrpcStatusCodeValues=ly2.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED=ly2.RPCGRPCSTATUSCODEVALUES_DATA_LOSS=ly2.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE=ly2.RPCGRPCSTATUSCODEVALUES_INTERNAL=ly2.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED=ly2.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE=ly2.RPCGRPCSTATUSCODEVALUES_ABORTED=ly2.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION=ly2.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED=ly2.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED=ly2.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS=ly2.RPCGRPCSTATUSCODEVALUES_NOT_FOUND=ly2.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED=ly2.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT=ly2.RPCGRPCSTATUSCODEVALUES_UNKNOWN=ly2.RPCGRPCSTATUSCODEVALUES_CANCELLED=ly2.RPCGRPCSTATUSCODEVALUES_OK=ly2.MessagingOperationValues=ly2.MESSAGINGOPERATIONVALUES_PROCESS=void 0;var IE=MF0(),BP2="aws.lambda.invoked_arn",QP2="db.system",DP2="db.connection_string",ZP2="db.user",GP2="db.jdbc.driver_classname",FP2="db.name",IP2="db.statement",YP2="db.operation",WP2="db.mssql.instance_name",JP2="db.cassandra.keyspace",XP2="db.cassandra.page_size",VP2="db.cassandra.consistency_level",CP2="db.cassandra.table",KP2="db.cassandra.idempotence",HP2="db.cassandra.speculative_execution_count",zP2="db.cassandra.coordinator.id",EP2="db.cassandra.coordinator.dc",UP2="db.hbase.namespace",wP2="db.redis.database_index",$P2="db.mongodb.collection",qP2="db.sql.table",NP2="exception.type",LP2="exception.message",MP2="exception.stacktrace",RP2="exception.escaped",OP2="faas.trigger",TP2="faas.execution",PP2="faas.document.collection",SP2="faas.document.operation",jP2="faas.document.time",kP2="faas.document.name",yP2="faas.time",_P2="faas.cron",xP2="faas.coldstart",vP2="faas.invoked_name",bP2="faas.invoked_provider",fP2="faas.invoked_region",hP2="net.transport",gP2="net.peer.ip",uP2="net.peer.port",mP2="net.peer.name",dP2="net.host.ip",cP2="net.host.port",lP2="net.host.name",pP2="net.host.connection.type",iP2="net.host.connection.subtype",nP2="net.host.carrier.name",aP2="net.host.carrier.mcc",sP2="net.host.carrier.mnc",rP2="net.host.carrier.icc",oP2="peer.service",tP2="enduser.id",eP2="enduser.role",AS2="enduser.scope",BS2="thread.id",QS2="thread.name",DS2="code.function",ZS2="code.namespace",GS2="code.filepath",FS2="code.lineno",IS2="http.method",YS2="http.url",WS2="http.target",JS2="http.host",XS2="http.scheme",VS2="http.status_code",CS2="http.flavor",KS2="http.user_agent",HS2="http.request_content_length",zS2="http.request_content_length_uncompressed",ES2="http.response_content_length",US2="http.response_content_length_uncompressed",wS2="http.server_name",$S2="http.route",qS2="http.client_ip",NS2="aws.dynamodb.table_names",LS2="aws.dynamodb.consumed_capacity",MS2="aws.dynamodb.item_collection_metrics",RS2="aws.dynamodb.provisioned_read_capacity",OS2="aws.dynamodb.provisioned_write_capacity",TS2="aws.dynamodb.consistent_read",PS2="aws.dynamodb.projection",SS2="aws.dynamodb.limit",jS2="aws.dynamodb.attributes_to_get",kS2="aws.dynamodb.index_name",yS2="aws.dynamodb.select",_S2="aws.dynamodb.global_secondary_indexes",xS2="aws.dynamodb.local_secondary_indexes",vS2="aws.dynamodb.exclusive_start_table",bS2="aws.dynamodb.table_count",fS2="aws.dynamodb.scan_forward",hS2="aws.dynamodb.segment",gS2="aws.dynamodb.total_segments",uS2="aws.dynamodb.count",mS2="aws.dynamodb.scanned_count",dS2="aws.dynamodb.attribute_definitions",cS2="aws.dynamodb.global_secondary_index_updates",lS2="messaging.system",pS2="messaging.destination",iS2="messaging.destination_kind",nS2="messaging.temp_destination",aS2="messaging.protocol",sS2="messaging.protocol_version",rS2="messaging.url",oS2="messaging.message_id",tS2="messaging.conversation_id",eS2="messaging.message_payload_size_bytes",Aj2="messaging.message_payload_compressed_size_bytes",Bj2="messaging.operation",Qj2="messaging.consumer_id",Dj2="messaging.rabbitmq.routing_key",Zj2="messaging.kafka.message_key",Gj2="messaging.kafka.consumer_group",Fj2="messaging.kafka.client_id",Ij2="messaging.kafka.partition",Yj2="messaging.kafka.tombstone",Wj2="rpc.system",Jj2="rpc.service",Xj2="rpc.method",Vj2="rpc.grpc.status_code",Cj2="rpc.jsonrpc.version",Kj2="rpc.jsonrpc.request_id",Hj2="rpc.jsonrpc.error_code",zj2="rpc.jsonrpc.error_message",Ej2="message.type",Uj2="message.id",wj2="message.compressed_size",$j2="message.uncompressed_size";ly2.SEMATTRS_AWS_LAMBDA_INVOKED_ARN=BP2;ly2.SEMATTRS_DB_SYSTEM=QP2;ly2.SEMATTRS_DB_CONNECTION_STRING=DP2;ly2.SEMATTRS_DB_USER=ZP2;ly2.SEMATTRS_DB_JDBC_DRIVER_CLASSNAME=GP2;ly2.SEMATTRS_DB_NAME=FP2;ly2.SEMATTRS_DB_STATEMENT=IP2;ly2.SEMATTRS_DB_OPERATION=YP2;ly2.SEMATTRS_DB_MSSQL_INSTANCE_NAME=WP2;ly2.SEMATTRS_DB_CASSANDRA_KEYSPACE=JP2;ly2.SEMATTRS_DB_CASSANDRA_PAGE_SIZE=XP2;ly2.SEMATTRS_DB_CASSANDRA_CONSISTENCY_LEVEL=VP2;ly2.SEMATTRS_DB_CASSANDRA_TABLE=CP2;ly2.SEMATTRS_DB_CASSANDRA_IDEMPOTENCE=KP2;ly2.SEMATTRS_DB_CASSANDRA_SPECULATIVE_EXECUTION_COUNT=HP2;ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_ID=zP2;ly2.SEMATTRS_DB_CASSANDRA_COORDINATOR_DC=EP2;ly2.SEMATTRS_DB_HBASE_NAMESPACE=UP2;ly2.SEMATTRS_DB_REDIS_DATABASE_INDEX=wP2;ly2.SEMATTRS_DB_MONGODB_COLLECTION=$P2;ly2.SEMATTRS_DB_SQL_TABLE=qP2;ly2.SEMATTRS_EXCEPTION_TYPE=NP2;ly2.SEMATTRS_EXCEPTION_MESSAGE=LP2;ly2.SEMATTRS_EXCEPTION_STACKTRACE=MP2;ly2.SEMATTRS_EXCEPTION_ESCAPED=RP2;ly2.SEMATTRS_FAAS_TRIGGER=OP2;ly2.SEMATTRS_FAAS_EXECUTION=TP2;ly2.SEMATTRS_FAAS_DOCUMENT_COLLECTION=PP2;ly2.SEMATTRS_FAAS_DOCUMENT_OPERATION=SP2;ly2.SEMATTRS_FAAS_DOCUMENT_TIME=jP2;ly2.SEMATTRS_FAAS_DOCUMENT_NAME=kP2;ly2.SEMATTRS_FAAS_TIME=yP2;ly2.SEMATTRS_FAAS_CRON=_P2;ly2.SEMATTRS_FAAS_COLDSTART=xP2;ly2.SEMATTRS_FAAS_INVOKED_NAME=vP2;ly2.SEMATTRS_FAAS_INVOKED_PROVIDER=bP2;ly2.SEMATTRS_FAAS_INVOKED_REGION=fP2;ly2.SEMATTRS_NET_TRANSPORT=hP2;ly2.SEMATTRS_NET_PEER_IP=gP2;ly2.SEMATTRS_NET_PEER_PORT=uP2;ly2.SEMATTRS_NET_PEER_NAME=mP2;ly2.SEMATTRS_NET_HOST_IP=dP2;ly2.SEMATTRS_NET_HOST_PORT=cP2;ly2.SEMATTRS_NET_HOST_NAME=lP2;ly2.SEMATTRS_NET_HOST_CONNECTION_TYPE=pP2;ly2.SEMATTRS_NET_HOST_CONNECTION_SUBTYPE=iP2;ly2.SEMATTRS_NET_HOST_CARRIER_NAME=nP2;ly2.SEMATTRS_NET_HOST_CARRIER_MCC=aP2;ly2.SEMATTRS_NET_HOST_CARRIER_MNC=sP2;ly2.SEMATTRS_NET_HOST_CARRIER_ICC=rP2;ly2.SEMATTRS_PEER_SERVICE=oP2;ly2.SEMATTRS_ENDUSER_ID=tP2;ly2.SEMATTRS_ENDUSER_ROLE=eP2;ly2.SEMATTRS_ENDUSER_SCOPE=AS2;ly2.SEMATTRS_THREAD_ID=BS2;ly2.SEMATTRS_THREAD_NAME=QS2;ly2.SEMATTRS_CODE_FUNCTION=DS2;ly2.SEMATTRS_CODE_NAMESPACE=ZS2;ly2.SEMATTRS_CODE_FILEPATH=GS2;ly2.SEMATTRS_CODE_LINENO=FS2;ly2.SEMATTRS_HTTP_METHOD=IS2;ly2.SEMATTRS_HTTP_URL=YS2;ly2.SEMATTRS_HTTP_TARGET=WS2;ly2.SEMATTRS_HTTP_HOST=JS2;ly2.SEMATTRS_HTTP_SCHEME=XS2;ly2.SEMATTRS_HTTP_STATUS_CODE=VS2;ly2.SEMATTRS_HTTP_FLAVOR=CS2;ly2.SEMATTRS_HTTP_USER_AGENT=KS2;ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH=HS2;ly2.SEMATTRS_HTTP_REQUEST_CONTENT_LENGTH_UNCOMPRESSED=zS2;ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH=ES2;ly2.SEMATTRS_HTTP_RESPONSE_CONTENT_LENGTH_UNCOMPRESSED=US2;ly2.SEMATTRS_HTTP_SERVER_NAME=wS2;ly2.SEMATTRS_HTTP_ROUTE=$S2;ly2.SEMATTRS_HTTP_CLIENT_IP=qS2;ly2.SEMATTRS_AWS_DYNAMODB_TABLE_NAMES=NS2;ly2.SEMATTRS_AWS_DYNAMODB_CONSUMED_CAPACITY=LS2;ly2.SEMATTRS_AWS_DYNAMODB_ITEM_COLLECTION_METRICS=MS2;ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_READ_CAPACITY=RS2;ly2.SEMATTRS_AWS_DYNAMODB_PROVISIONED_WRITE_CAPACITY=OS2;ly2.SEMATTRS_AWS_DYNAMODB_CONSISTENT_READ=TS2;ly2.SEMATTRS_AWS_DYNAMODB_PROJECTION=PS2;ly2.SEMATTRS_AWS_DYNAMODB_LIMIT=SS2;ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTES_TO_GET=jS2;ly2.SEMATTRS_AWS_DYNAMODB_INDEX_NAME=kS2;ly2.SEMATTRS_AWS_DYNAMODB_SELECT=yS2;ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEXES=_S2;ly2.SEMATTRS_AWS_DYNAMODB_LOCAL_SECONDARY_INDEXES=xS2;ly2.SEMATTRS_AWS_DYNAMODB_EXCLUSIVE_START_TABLE=vS2;ly2.SEMATTRS_AWS_DYNAMODB_TABLE_COUNT=bS2;ly2.SEMATTRS_AWS_DYNAMODB_SCAN_FORWARD=fS2;ly2.SEMATTRS_AWS_DYNAMODB_SEGMENT=hS2;ly2.SEMATTRS_AWS_DYNAMODB_TOTAL_SEGMENTS=gS2;ly2.SEMATTRS_AWS_DYNAMODB_COUNT=uS2;ly2.SEMATTRS_AWS_DYNAMODB_SCANNED_COUNT=mS2;ly2.SEMATTRS_AWS_DYNAMODB_ATTRIBUTE_DEFINITIONS=dS2;ly2.SEMATTRS_AWS_DYNAMODB_GLOBAL_SECONDARY_INDEX_UPDATES=cS2;ly2.SEMATTRS_MESSAGING_SYSTEM=lS2;ly2.SEMATTRS_MESSAGING_DESTINATION=pS2;ly2.SEMATTRS_MESSAGING_DESTINATION_KIND=iS2;ly2.SEMATTRS_MESSAGING_TEMP_DESTINATION=nS2;ly2.SEMATTRS_MESSAGING_PROTOCOL=aS2;ly2.SEMATTRS_MESSAGING_PROTOCOL_VERSION=sS2;ly2.SEMATTRS_MESSAGING_URL=rS2;ly2.SEMATTRS_MESSAGING_MESSAGE_ID=oS2;ly2.SEMATTRS_MESSAGING_CONVERSATION_ID=tS2;ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_SIZE_BYTES=eS2;ly2.SEMATTRS_MESSAGING_MESSAGE_PAYLOAD_COMPRESSED_SIZE_BYTES=Aj2;ly2.SEMATTRS_MESSAGING_OPERATION=Bj2;ly2.SEMATTRS_MESSAGING_CONSUMER_ID=Qj2;ly2.SEMATTRS_MESSAGING_RABBITMQ_ROUTING_KEY=Dj2;ly2.SEMATTRS_MESSAGING_KAFKA_MESSAGE_KEY=Zj2;ly2.SEMATTRS_MESSAGING_KAFKA_CONSUMER_GROUP=Gj2;ly2.SEMATTRS_MESSAGING_KAFKA_CLIENT_ID=Fj2;ly2.SEMATTRS_MESSAGING_KAFKA_PARTITION=Ij2;ly2.SEMATTRS_MESSAGING_KAFKA_TOMBSTONE=Yj2;ly2.SEMATTRS_RPC_SYSTEM=Wj2;ly2.SEMATTRS_RPC_SERVICE=Jj2;ly2.SEMATTRS_RPC_METHOD=Xj2;ly2.SEMATTRS_RPC_GRPC_STATUS_CODE=Vj2;ly2.SEMATTRS_RPC_JSONRPC_VERSION=Cj2;ly2.SEMATTRS_RPC_JSONRPC_REQUEST_ID=Kj2;ly2.SEMATTRS_RPC_JSONRPC_ERROR_CODE=Hj2;ly2.SEMATTRS_RPC_JSONRPC_ERROR_MESSAGE=zj2;ly2.SEMATTRS_MESSAGE_TYPE=Ej2;ly2.SEMATTRS_MESSAGE_ID=Uj2;ly2.SEMATTRS_MESSAGE_COMPRESSED_SIZE=wj2;ly2.SEMATTRS_MESSAGE_UNCOMPRESSED_SIZE=$j2;ly2.SemanticAttributes=IE.createConstMap([BP2,QP2,DP2,ZP2,GP2,FP2,IP2,YP2,WP2,JP2,XP2,VP2,CP2,KP2,HP2,zP2,EP2,UP2,wP2,$P2,qP2,NP2,LP2,MP2,RP2,OP2,TP2,PP2,SP2,jP2,kP2,yP2,_P2,xP2,vP2,bP2,fP2,hP2,gP2,uP2,mP2,dP2,cP2,lP2,pP2,iP2,nP2,aP2,sP2,rP2,oP2,tP2,eP2,AS2,BS2,QS2,DS2,ZS2,GS2,FS2,IS2,YS2,WS2,JS2,XS2,VS2,CS2,KS2,HS2,zS2,ES2,US2,wS2,$S2,qS2,NS2,LS2,MS2,RS2,OS2,TS2,PS2,SS2,jS2,kS2,yS2,_S2,xS2,vS2,bS2,fS2,hS2,gS2,uS2,mS2,dS2,cS2,lS2,pS2,iS2,nS2,aS2,sS2,rS2,oS2,tS2,eS2,Aj2,Bj2,Qj2,Dj2,Zj2,Gj2,Fj2,Ij2,Yj2,Wj2,Jj2,Xj2,Vj2,Cj2,Kj2,Hj2,zj2,Ej2,Uj2,wj2,$j2]);var qj2="other_sql",Nj2="mssql",Lj2="mysql",Mj2="oracle",Rj2="db2",Oj2="postgresql",Tj2="redshift",Pj2="hive",Sj2="cloudscape",jj2="hsqldb",kj2="progress",yj2="maxdb",_j2="hanadb",xj2="ingres",vj2="firstsql",bj2="edb",fj2="cache",hj2="adabas",gj2="firebird",uj2="derby",mj2="filemaker",dj2="informix",cj2="instantdb",lj2="interbase",pj2="mariadb",ij2="netezza",nj2="pervasive",aj2="pointbase",sj2="sqlite",rj2="sybase",oj2="teradata",tj2="vertica",ej2="h2",Ak2="coldfusion",Bk2="cassandra",Qk2="hbase",Dk2="mongodb",Zk2="redis",Gk2="couchbase",Fk2="couchdb",Ik2="cosmosdb",Yk2="dynamodb",Wk2="neo4j",Jk2="geode",Xk2="elasticsearch",Vk2="memcached",Ck2="cockroachdb";ly2.DBSYSTEMVALUES_OTHER_SQL=qj2;ly2.DBSYSTEMVALUES_MSSQL=Nj2;ly2.DBSYSTEMVALUES_MYSQL=Lj2;ly2.DBSYSTEMVALUES_ORACLE=Mj2;ly2.DBSYSTEMVALUES_DB2=Rj2;ly2.DBSYSTEMVALUES_POSTGRESQL=Oj2;ly2.DBSYSTEMVALUES_REDSHIFT=Tj2;ly2.DBSYSTEMVALUES_HIVE=Pj2;ly2.DBSYSTEMVALUES_CLOUDSCAPE=Sj2;ly2.DBSYSTEMVALUES_HSQLDB=jj2;ly2.DBSYSTEMVALUES_PROGRESS=kj2;ly2.DBSYSTEMVALUES_MAXDB=yj2;ly2.DBSYSTEMVALUES_HANADB=_j2;ly2.DBSYSTEMVALUES_INGRES=xj2;ly2.DBSYSTEMVALUES_FIRSTSQL=vj2;ly2.DBSYSTEMVALUES_EDB=bj2;ly2.DBSYSTEMVALUES_CACHE=fj2;ly2.DBSYSTEMVALUES_ADABAS=hj2;ly2.DBSYSTEMVALUES_FIREBIRD=gj2;ly2.DBSYSTEMVALUES_DERBY=uj2;ly2.DBSYSTEMVALUES_FILEMAKER=mj2;ly2.DBSYSTEMVALUES_INFORMIX=dj2;ly2.DBSYSTEMVALUES_INSTANTDB=cj2;ly2.DBSYSTEMVALUES_INTERBASE=lj2;ly2.DBSYSTEMVALUES_MARIADB=pj2;ly2.DBSYSTEMVALUES_NETEZZA=ij2;ly2.DBSYSTEMVALUES_PERVASIVE=nj2;ly2.DBSYSTEMVALUES_POINTBASE=aj2;ly2.DBSYSTEMVALUES_SQLITE=sj2;ly2.DBSYSTEMVALUES_SYBASE=rj2;ly2.DBSYSTEMVALUES_TERADATA=oj2;ly2.DBSYSTEMVALUES_VERTICA=tj2;ly2.DBSYSTEMVALUES_H2=ej2;ly2.DBSYSTEMVALUES_COLDFUSION=Ak2;ly2.DBSYSTEMVALUES_CASSANDRA=Bk2;ly2.DBSYSTEMVALUES_HBASE=Qk2;ly2.DBSYSTEMVALUES_MONGODB=Dk2;ly2.DBSYSTEMVALUES_REDIS=Zk2;ly2.DBSYSTEMVALUES_COUCHBASE=Gk2;ly2.DBSYSTEMVALUES_COUCHDB=Fk2;ly2.DBSYSTEMVALUES_COSMOSDB=Ik2;ly2.DBSYSTEMVALUES_DYNAMODB=Yk2;ly2.DBSYSTEMVALUES_NEO4J=Wk2;ly2.DBSYSTEMVALUES_GEODE=Jk2;ly2.DBSYSTEMVALUES_ELASTICSEARCH=Xk2;ly2.DBSYSTEMVALUES_MEMCACHED=Vk2;ly2.DBSYSTEMVALUES_COCKROACHDB=Ck2;ly2.DbSystemValues=IE.createConstMap([qj2,Nj2,Lj2,Mj2,Rj2,Oj2,Tj2,Pj2,Sj2,jj2,kj2,yj2,_j2,xj2,vj2,bj2,fj2,hj2,gj2,uj2,mj2,dj2,cj2,lj2,pj2,ij2,nj2,aj2,sj2,rj2,oj2,tj2,ej2,Ak2,Bk2,Qk2,Dk2,Zk2,Gk2,Fk2,Ik2,Yk2,Wk2,Jk2,Xk2,Vk2,Ck2]);var Kk2="all",Hk2="each_quorum",zk2="quorum",Ek2="local_quorum",Uk2="one",wk2="two",$k2="three",qk2="local_one",Nk2="any",Lk2="serial",Mk2="local_serial";ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ALL=Kk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_EACH_QUORUM=Hk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_QUORUM=zk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_QUORUM=Ek2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ONE=Uk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_TWO=wk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_THREE=$k2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_ONE=qk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_ANY=Nk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_SERIAL=Lk2;ly2.DBCASSANDRACONSISTENCYLEVELVALUES_LOCAL_SERIAL=Mk2;ly2.DbCassandraConsistencyLevelValues=IE.createConstMap([Kk2,Hk2,zk2,Ek2,Uk2,wk2,$k2,qk2,Nk2,Lk2,Mk2]);var Rk2="datasource",Ok2="http",Tk2="pubsub",Pk2="timer",Sk2="other";ly2.FAASTRIGGERVALUES_DATASOURCE=Rk2;ly2.FAASTRIGGERVALUES_HTTP=Ok2;ly2.FAASTRIGGERVALUES_PUBSUB=Tk2;ly2.FAASTRIGGERVALUES_TIMER=Pk2;ly2.FAASTRIGGERVALUES_OTHER=Sk2;ly2.FaasTriggerValues=IE.createConstMap([Rk2,Ok2,Tk2,Pk2,Sk2]);var jk2="insert",kk2="edit",yk2="delete";ly2.FAASDOCUMENTOPERATIONVALUES_INSERT=jk2;ly2.FAASDOCUMENTOPERATIONVALUES_EDIT=kk2;ly2.FAASDOCUMENTOPERATIONVALUES_DELETE=yk2;ly2.FaasDocumentOperationValues=IE.createConstMap([jk2,kk2,yk2]);var _k2="alibaba_cloud",xk2="aws",vk2="azure",bk2="gcp";ly2.FAASINVOKEDPROVIDERVALUES_ALIBABA_CLOUD=_k2;ly2.FAASINVOKEDPROVIDERVALUES_AWS=xk2;ly2.FAASINVOKEDPROVIDERVALUES_AZURE=vk2;ly2.FAASINVOKEDPROVIDERVALUES_GCP=bk2;ly2.FaasInvokedProviderValues=IE.createConstMap([_k2,xk2,vk2,bk2]);var fk2="ip_tcp",hk2="ip_udp",gk2="ip",uk2="unix",mk2="pipe",dk2="inproc",ck2="other";ly2.NETTRANSPORTVALUES_IP_TCP=fk2;ly2.NETTRANSPORTVALUES_IP_UDP=hk2;ly2.NETTRANSPORTVALUES_IP=gk2;ly2.NETTRANSPORTVALUES_UNIX=uk2;ly2.NETTRANSPORTVALUES_PIPE=mk2;ly2.NETTRANSPORTVALUES_INPROC=dk2;ly2.NETTRANSPORTVALUES_OTHER=ck2;ly2.NetTransportValues=IE.createConstMap([fk2,hk2,gk2,uk2,mk2,dk2,ck2]);var lk2="wifi",pk2="wired",ik2="cell",nk2="unavailable",ak2="unknown";ly2.NETHOSTCONNECTIONTYPEVALUES_WIFI=lk2;ly2.NETHOSTCONNECTIONTYPEVALUES_WIRED=pk2;ly2.NETHOSTCONNECTIONTYPEVALUES_CELL=ik2;ly2.NETHOSTCONNECTIONTYPEVALUES_UNAVAILABLE=nk2;ly2.NETHOSTCONNECTIONTYPEVALUES_UNKNOWN=ak2;ly2.NetHostConnectionTypeValues=IE.createConstMap([lk2,pk2,ik2,nk2,ak2]);var sk2="gprs",rk2="edge",ok2="umts",tk2="cdma",ek2="evdo_0",Ay2="evdo_a",By2="cdma2000_1xrtt",Qy2="hsdpa",Dy2="hsupa",Zy2="hspa",Gy2="iden",Fy2="evdo_b",Iy2="lte",Yy2="ehrpd",Wy2="hspap",Jy2="gsm",Xy2="td_scdma",Vy2="iwlan",Cy2="nr",Ky2="nrnsa",Hy2="lte_ca";ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GPRS=sk2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EDGE=rk2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_UMTS=ok2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA=tk2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_0=ek2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_A=Ay2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_CDMA2000_1XRTT=By2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSDPA=Qy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSUPA=Dy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPA=Zy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IDEN=Gy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EVDO_B=Fy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE=Iy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_EHRPD=Yy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_HSPAP=Wy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_GSM=Jy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_TD_SCDMA=Xy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_IWLAN=Vy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NR=Cy2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_NRNSA=Ky2;ly2.NETHOSTCONNECTIONSUBTYPEVALUES_LTE_CA=Hy2;ly2.NetHostConnectionSubtypeValues=IE.createConstMap([sk2,rk2,ok2,tk2,ek2,Ay2,By2,Qy2,Dy2,Zy2,Gy2,Fy2,Iy2,Yy2,Wy2,Jy2,Xy2,Vy2,Cy2,Ky2,Hy2]);var zy2="1.0",Ey2="1.1",Uy2="2.0",wy2="SPDY",$y2="QUIC";ly2.HTTPFLAVORVALUES_HTTP_1_0=zy2;ly2.HTTPFLAVORVALUES_HTTP_1_1=Ey2;ly2.HTTPFLAVORVALUES_HTTP_2_0=Uy2;ly2.HTTPFLAVORVALUES_SPDY=wy2;ly2.HTTPFLAVORVALUES_QUIC=$y2;ly2.HttpFlavorValues={HTTP_1_0:zy2,HTTP_1_1:Ey2,HTTP_2_0:Uy2,SPDY:wy2,QUIC:$y2};var qy2="queue",Ny2="topic";ly2.MESSAGINGDESTINATIONKINDVALUES_QUEUE=qy2;ly2.MESSAGINGDESTINATIONKINDVALUES_TOPIC=Ny2;ly2.MessagingDestinationKindValues=IE.createConstMap([qy2,Ny2]);var Ly2="receive",My2="process";ly2.MESSAGINGOPERATIONVALUES_RECEIVE=Ly2;ly2.MESSAGINGOPERATIONVALUES_PROCESS=My2;ly2.MessagingOperationValues=IE.createConstMap([Ly2,My2]);var Ry2=0,Oy2=1,Ty2=2,Py2=3,Sy2=4,jy2=5,ky2=6,yy2=7,_y2=8,xy2=9,vy2=10,by2=11,fy2=12,hy2=13,gy2=14,uy2=15,my2=16;ly2.RPCGRPCSTATUSCODEVALUES_OK=Ry2;ly2.RPCGRPCSTATUSCODEVALUES_CANCELLED=Oy2;ly2.RPCGRPCSTATUSCODEVALUES_UNKNOWN=Ty2;ly2.RPCGRPCSTATUSCODEVALUES_INVALID_ARGUMENT=Py2;ly2.RPCGRPCSTATUSCODEVALUES_DEADLINE_EXCEEDED=Sy2;ly2.RPCGRPCSTATUSCODEVALUES_NOT_FOUND=jy2;ly2.RPCGRPCSTATUSCODEVALUES_ALREADY_EXISTS=ky2;ly2.RPCGRPCSTATUSCODEVALUES_PERMISSION_DENIED=yy2;ly2.RPCGRPCSTATUSCODEVALUES_RESOURCE_EXHAUSTED=_y2;ly2.RPCGRPCSTATUSCODEVALUES_FAILED_PRECONDITION=xy2;ly2.RPCGRPCSTATUSCODEVALUES_ABORTED=vy2;ly2.RPCGRPCSTATUSCODEVALUES_OUT_OF_RANGE=by2;ly2.RPCGRPCSTATUSCODEVALUES_UNIMPLEMENTED=fy2;ly2.RPCGRPCSTATUSCODEVALUES_INTERNAL=hy2;ly2.RPCGRPCSTATUSCODEVALUES_UNAVAILABLE=gy2;ly2.RPCGRPCSTATUSCODEVALUES_DATA_LOSS=uy2;ly2.RPCGRPCSTATUSCODEVALUES_UNAUTHENTICATED=my2;ly2.RpcGrpcStatusCodeValues={OK:Ry2,CANCELLED:Oy2,UNKNOWN:Ty2,INVALID_ARGUMENT:Py2,DEADLINE_EXCEEDED:Sy2,NOT_FOUND:jy2,ALREADY_EXISTS:ky2,PERMISSION_DENIED:yy2,RESOURCE_EXHAUSTED:_y2,FAILED_PRECONDITION:xy2,ABORTED:vy2,OUT_OF_RANGE:by2,UNIMPLEMENTED:fy2,INTERNAL:hy2,UNAVAILABLE:gy2,DATA_LOSS:uy2,UNAUTHENTICATED:my2};var dy2="SENT",cy2="RECEIVED";ly2.MESSAGETYPEVALUES_SENT=dy2;ly2.MESSAGETYPEVALUES_RECEIVED=cy2;ly2.MessageTypeValues=IE.createConstMap([dy2,cy2])});
var pF0=E((lF0)=>{Object.defineProperty(lF0,"__esModule",{value:!0});lF0.defaultServiceName=void 0;var X86=Qh2();Object.defineProperty(lF0,"defaultServiceName",{enumerable:!0,get:function(){return X86.defaultServiceName}})});
var pL=E((NI0)=>{var O9=NI0;O9.asPromise=UI0();O9.base64=im2();O9.EventEmitter=am2();O9.float=Bd2();O9.inquire=$I0();O9.utf8=Zd2();O9.pool=Fd2();O9.LongBits=Yd2();O9.isNode=Boolean(typeof global!=="undefined"&&global&&global.process&&global.process.versions&&global.process.versions.node);O9.global=O9.isNode&&global||typeof window!=="undefined"&&window||typeof self!=="undefined"&&self||NI0;O9.emptyArray=Object.freeze?Object.freeze([]):[];O9.emptyObject=Object.freeze?Object.freeze({}):{};O9.isInteger=Number.isInteger||function A(B){return typeof B==="number"&&isFinite(B)&&Math.floor(B)===B};O9.isString=function A(B){return typeof B==="string"||B instanceof String};O9.isObject=function A(B){return B&&typeof B==="object"};O9.isset=O9.isSet=function A(B,Q){var D=B[Q];if(D!=null&&B.hasOwnProperty(Q))return typeof D!=="object"||(Array.isArray(D)?D.length:Object.keys(D).length)>0;return!1};O9.Buffer=function(){try{var A=O9.inquire("buffer").Buffer;return A.prototype.utf8Write?A:null}catch(B){return null}}();O9._Buffer_from=null;O9._Buffer_allocUnsafe=null;O9.newBuffer=function A(B){return typeof B==="number"?O9.Buffer?O9._Buffer_allocUnsafe(B):new O9.Array(B):O9.Buffer?O9._Buffer_from(B):typeof Uint8Array==="undefined"?B:new Uint8Array(B)};O9.Array=typeof Uint8Array!=="undefined"?Uint8Array:Array;O9.Long=O9.global.dcodeIO&&O9.global.dcodeIO.Long||O9.global.Long||O9.inquire("long");O9.key2Re=/^true|false|0|1$/;O9.key32Re=/^-?(?:0|[1-9][0-9]*)$/;O9.key64Re=/^(?:[\\x00-\\xff]{8}|-?(?:0|[1-9][0-9]*))$/;O9.longToHash=function A(B){return B?O9.LongBits.from(B).toHash():O9.LongBits.zeroHash};O9.longFromHash=function A(B,Q){var D=O9.LongBits.fromHash(B);if(O9.Long)return O9.Long.fromBits(D.lo,D.hi,Q);return D.toNumber(Boolean(Q))};function Wd2(A,B,Q){for(var D=Object.keys(B),Z=0;Z<D.length;++Z)if(A[D[Z]]===void 0||!Q)A[D[Z]]=B[D[Z]];return A}O9.merge=Wd2;O9.lcFirst=function A(B){return B.charAt(0).toLowerCase()+B.substring(1)};function Jd2(A){function B(Q,D){if(!(this instanceof B))return new B(Q,D);if(Object.defineProperty(this,"message",{get:function(){return Q}}),Error.captureStackTrace)Error.captureStackTrace(this,B);else Object.defineProperty(this,"stack",{value:new Error().stack||""});if(D)Wd2(this,D)}return B.prototype=Object.create(Error.prototype,{constructor:{value:B,writable:!0,enumerable:!1,configurable:!0},name:{get:function Q(){return A},set:void 0,enumerable:!1,configurable:!0},toString:{value:function Q(){return this.name+": "+this.message},writable:!0,enumerable:!1,configurable:!0}}),B}O9.newError=Jd2;O9.ProtocolError=Jd2("ProtocolError");O9.oneOfGetter=function A(B){var Q={};for(var D=0;D<B.length;++D)Q[B[D]]=1;return function(){for(var Z=Object.keys(this),G=Z.length-1;G>-1;--G)if(Q[Z[G]]===1&&this[Z[G]]!==void 0&&this[Z[G]]!==null)return Z[G]}};O9.oneOfSetter=function A(B){return function(Q){for(var D=0;D<B.length;++D)if(B[D]!==Q)delete this[B[D]]}};O9.toJSONOptions={longs:String,enums:String,bytes:String,json:!0};O9._configure=function(){var A=O9.Buffer;if(!A){O9._Buffer_from=O9._Buffer_allocUnsafe=null;return}O9._Buffer_from=A.from!==Uint8Array.from&&A.from||function B(Q,D){return new A(Q,D)},O9._Buffer_allocUnsafe=A.allocUnsafe||function B(Q){return new A(Q)}}});
var pO1=E((du2)=>{Object.defineProperty(du2,"__esModule",{value:!0});du2.ExactPredicate=du2.PatternPredicate=void 0;var R36=/[\^$\\.+?()[\]{}|]/g;class YI0{_matchAll;_regexp;constructor(A){if(A==="*")this._matchAll=!0,this._regexp=/.*/;else this._matchAll=!1,this._regexp=new RegExp(YI0.escapePattern(A))}match(A){if(this._matchAll)return!0;return this._regexp.test(A)}static escapePattern(A){return`^${A.replace(R36,"\\$&").replace("*",".*")}$`}static hasWildcard(A){return A.includes("*")}}du2.PatternPredicate=YI0;class mu2{_matchAll;_pattern;constructor(A){this._matchAll=A===void 0,this._pattern=A}match(A){if(this._matchAll)return!0;if(A===this._pattern)return!0;return!1}}du2.ExactPredicate=mu2});
var pb2=E((cb2)=>{Object.defineProperty(cb2,"__esModule",{value:!0});cb2.Deferred=void 0;class db2{_promise;_resolve;_reject;constructor(){this._promise=new Promise((A,B)=>{this._resolve=A,this._reject=B})}get promise(){return this._promise}resolve(A){this._resolve(A)}reject(A){this._reject(A)}}cb2.Deferred=db2});
var pg2=E((cg2)=>{Object.defineProperty(cg2,"__esModule",{value:!0});cg2.AsyncMetricStorage=void 0;var g56=QI0(),u56=GI0(),m56=FI0(),d56=B31();class dg2 extends g56.MetricStorage{_attributesProcessor;_aggregationCardinalityLimit;_deltaMetricStorage;_temporalMetricStorage;constructor(A,B,Q,D,Z){super(A);this._attributesProcessor=Q,this._aggregationCardinalityLimit=Z,this._deltaMetricStorage=new u56.DeltaMetricProcessor(B,this._aggregationCardinalityLimit),this._temporalMetricStorage=new m56.TemporalMetricProcessor(B,D)}record(A,B){let Q=new d56.AttributeHashMap;Array.from(A.entries()).forEach(([D,Z])=>{Q.set(this._attributesProcessor.process(D),Z)}),this._deltaMetricStorage.batchCumulate(Q,B)}collect(A,B){let Q=this._deltaMetricStorage.collect();return this._temporalMetricStorage.buildMetrics(A,this._instrumentDescriptor,Q,B)}}cg2.AsyncMetricStorage=dg2});
var qG0=E((vL2)=>{Object.defineProperty(vL2,"__esModule",{value:!0});vL2.createNoopMeter=vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=vL2.NOOP_OBSERVABLE_GAUGE_METRIC=vL2.NOOP_OBSERVABLE_COUNTER_METRIC=vL2.NOOP_UP_DOWN_COUNTER_METRIC=vL2.NOOP_HISTOGRAM_METRIC=vL2.NOOP_GAUGE_METRIC=vL2.NOOP_COUNTER_METRIC=vL2.NOOP_METER=vL2.NoopObservableUpDownCounterMetric=vL2.NoopObservableGaugeMetric=vL2.NoopObservableCounterMetric=vL2.NoopObservableMetric=vL2.NoopHistogramMetric=vL2.NoopGaugeMetric=vL2.NoopUpDownCounterMetric=vL2.NoopCounterMetric=vL2.NoopMetric=vL2.NoopMeter=void 0;class CG0{constructor(){}createGauge(A,B){return vL2.NOOP_GAUGE_METRIC}createHistogram(A,B){return vL2.NOOP_HISTOGRAM_METRIC}createCounter(A,B){return vL2.NOOP_COUNTER_METRIC}createUpDownCounter(A,B){return vL2.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(A,B){return vL2.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(A,B){return vL2.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(A,B){return vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(A,B){}removeBatchObservableCallback(A){}}vL2.NoopMeter=CG0;class Ro{}vL2.NoopMetric=Ro;class KG0 extends Ro{add(A,B){}}vL2.NoopCounterMetric=KG0;class HG0 extends Ro{add(A,B){}}vL2.NoopUpDownCounterMetric=HG0;class zG0 extends Ro{record(A,B){}}vL2.NoopGaugeMetric=zG0;class EG0 extends Ro{record(A,B){}}vL2.NoopHistogramMetric=EG0;class h51{addCallback(A){}removeCallback(A){}}vL2.NoopObservableMetric=h51;class UG0 extends h51{}vL2.NoopObservableCounterMetric=UG0;class wG0 extends h51{}vL2.NoopObservableGaugeMetric=wG0;class $G0 extends h51{}vL2.NoopObservableUpDownCounterMetric=$G0;vL2.NOOP_METER=new CG0;vL2.NOOP_COUNTER_METRIC=new KG0;vL2.NOOP_GAUGE_METRIC=new zG0;vL2.NOOP_HISTOGRAM_METRIC=new EG0;vL2.NOOP_UP_DOWN_COUNTER_METRIC=new HG0;vL2.NOOP_OBSERVABLE_COUNTER_METRIC=new UG0;vL2.NOOP_OBSERVABLE_GAUGE_METRIC=new wG0;vL2.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new $G0;function _i4(){return vL2.NOOP_METER}vL2.createNoopMeter=_i4});
var qf2=E((lw)=>{Object.defineProperty(lw,"__esModule",{value:!0});lw.SumAggregator=lw.SumAccumulation=lw.LastValueAggregator=lw.LastValueAccumulation=lw.ExponentialHistogramAggregator=lw.ExponentialHistogramAccumulation=lw.HistogramAggregator=lw.HistogramAccumulation=lw.DropAggregator=void 0;var c66=TO2();Object.defineProperty(lw,"DropAggregator",{enumerable:!0,get:function(){return c66.DropAggregator}});var Ef2=kO2();Object.defineProperty(lw,"HistogramAccumulation",{enumerable:!0,get:function(){return Ef2.HistogramAccumulation}});Object.defineProperty(lw,"HistogramAggregator",{enumerable:!0,get:function(){return Ef2.HistogramAggregator}});var Uf2=WT2();Object.defineProperty(lw,"ExponentialHistogramAccumulation",{enumerable:!0,get:function(){return Uf2.ExponentialHistogramAccumulation}});Object.defineProperty(lw,"ExponentialHistogramAggregator",{enumerable:!0,get:function(){return Uf2.ExponentialHistogramAggregator}});var wf2=Vf2();Object.defineProperty(lw,"LastValueAccumulation",{enumerable:!0,get:function(){return wf2.LastValueAccumulation}});Object.defineProperty(lw,"LastValueAggregator",{enumerable:!0,get:function(){return wf2.LastValueAggregator}});var $f2=zf2();Object.defineProperty(lw,"SumAccumulation",{enumerable:!0,get:function(){return $f2.SumAccumulation}});Object.defineProperty(lw,"SumAggregator",{enumerable:!0,get:function(){return $f2.SumAggregator}})});
var ql2=E((wl2)=>{Object.defineProperty(wl2,"__esModule",{value:!0});wl2.getHttpConfigurationFromEnvironment=void 0;var GT1=y3(),rI0=ZQ(),SZ6=sI0(),jZ6=Z31();function kZ6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_HEADERS`]?.trim(),Q=process.env.OTEL_EXPORTER_OTLP_HEADERS?.trim(),D=GT1.parseKeyPairsIntoRecord(B),Z=GT1.parseKeyPairsIntoRecord(Q);if(Object.keys(D).length===0&&Object.keys(Z).length===0)return;return Object.assign({},GT1.parseKeyPairsIntoRecord(Q),GT1.parseKeyPairsIntoRecord(B))}function yZ6(A){try{return new URL(A).toString()}catch{rI0.diag.warn(`Configuration: Could not parse environment-provided export URL: '${A}', falling back to undefined`);return}}function _Z6(A,B){try{new URL(A)}catch{rI0.diag.warn(`Configuration: Could not parse environment-provided export URL: '${A}', falling back to undefined`);return}if(!A.endsWith("/"))A=A+"/";A+=B;try{new URL(A)}catch{rI0.diag.warn(`Configuration: Provided URL appended with '${B}' is not a valid URL, using 'undefined' instead of '${A}'`);return}return A}function xZ6(A){let B=process.env.OTEL_EXPORTER_OTLP_ENDPOINT?.trim();if(B==null||B==="")return;return _Z6(B,A)}function vZ6(A){let B=process.env[`OTEL_EXPORTER_OTLP_${A}_ENDPOINT`]?.trim();if(B==null||B==="")return;return yZ6(B)}function bZ6(A,B){return{...SZ6.getSharedConfigurationFromEnvironment(A),url:vZ6(A)??xZ6(B),headers:jZ6.wrapStaticHeadersInFunction(kZ6(A))}}wl2.getHttpConfigurationFromEnvironment=bZ6});
var r51=E((jf2)=>{Object.defineProperty(jf2,"__esModule",{value:!0});jf2.toAggregation=jf2.AggregationType=void 0;var wu=Pf2(),$u;(function(A){A[A.DEFAULT=0]="DEFAULT",A[A.DROP=1]="DROP",A[A.SUM=2]="SUM",A[A.LAST_VALUE=3]="LAST_VALUE",A[A.EXPLICIT_BUCKET_HISTOGRAM=4]="EXPLICIT_BUCKET_HISTOGRAM",A[A.EXPONENTIAL_HISTOGRAM=5]="EXPONENTIAL_HISTOGRAM"})($u=jf2.AggregationType||(jf2.AggregationType={}));function A86(A){switch(A.type){case $u.DEFAULT:return wu.DEFAULT_AGGREGATION;case $u.DROP:return wu.DROP_AGGREGATION;case $u.SUM:return wu.SUM_AGGREGATION;case $u.LAST_VALUE:return wu.LAST_VALUE_AGGREGATION;case $u.EXPONENTIAL_HISTOGRAM:{let B=A;return new wu.ExponentialHistogramAggregation(B.options?.maxSize,B.options?.recordMinMax)}case $u.EXPLICIT_BUCKET_HISTOGRAM:{let B=A;if(B.options==null)return wu.HISTOGRAM_AGGREGATION;else return new wu.ExplicitBucketHistogramAggregation(B.options?.boundaries,B.options?.recordMinMax)}default:throw new Error("Unsupported Aggregation")}}jf2.toAggregation=A86});
var rM2=E((aM2)=>{Object.defineProperty(aM2,"__esModule",{value:!0});aM2.createTraceState=void 0;var gn4=nM2();function un4(A){return new gn4.TraceStateImpl(A)}aM2.createTraceState=un4});
var sF0=E((Fh2)=>{Object.defineProperty(Fh2,"__esModule",{value:!0});Fh2.defaultResource=Fh2.emptyResource=Fh2.resourceFromDetectedResource=Fh2.resourceFromAttributes=void 0;var iF0=ZQ(),nF0=y3(),qu=GP(),z86=pF0(),fO1=Gh2();class t51{_rawAttributes;_asyncAttributesPending=!1;_memoizedAttributes;static FromAttributeList(A){let B=new t51({});return B._rawAttributes=A,B._asyncAttributesPending=A.filter(([Q,D])=>fO1.isPromiseLike(D)).length>0,B}constructor(A){let B=A.attributes??{};this._rawAttributes=Object.entries(B).map(([Q,D])=>{if(fO1.isPromiseLike(D))this._asyncAttributesPending=!0;return[Q,D]})}get asyncAttributesPending(){return this._asyncAttributesPending}async waitForAsyncAttributes(){if(!this.asyncAttributesPending)return;for(let A=0;A<this._rawAttributes.length;A++){let[B,Q]=this._rawAttributes[A];try{this._rawAttributes[A]=[B,fO1.isPromiseLike(Q)?await Q:Q]}catch(D){iF0.diag.debug("a resource's async attributes promise rejected: %s",D),this._rawAttributes[A]=[B,void 0]}}this._asyncAttributesPending=!1}get attributes(){if(this.asyncAttributesPending)iF0.diag.error("Accessing resource attributes before async attributes settled");if(this._memoizedAttributes)return this._memoizedAttributes;let A={};for(let[B,Q]of this._rawAttributes){if(fO1.isPromiseLike(Q)){iF0.diag.debug(`Unsettled resource attribute ${B} skipped`);continue}if(Q!=null)A[B]??=Q}if(!this._asyncAttributesPending)this._memoizedAttributes=A;return A}getRawAttributes(){return this._rawAttributes}merge(A){if(A==null)return this;return t51.FromAttributeList([...A.getRawAttributes(),...this.getRawAttributes()])}}function aF0(A){return t51.FromAttributeList(Object.entries(A))}Fh2.resourceFromAttributes=aF0;function E86(A){return new t51(A)}Fh2.resourceFromDetectedResource=E86;function U86(){return aF0({})}Fh2.emptyResource=U86;function w86(){return aF0({[qu.ATTR_SERVICE_NAME]:z86.defaultServiceName(),[qu.ATTR_TELEMETRY_SDK_LANGUAGE]:nF0.SDK_INFO[qu.ATTR_TELEMETRY_SDK_LANGUAGE],[qu.ATTR_TELEMETRY_SDK_NAME]:nF0.SDK_INFO[qu.ATTR_TELEMETRY_SDK_NAME],[qu.ATTR_TELEMETRY_SDK_VERSION]:nF0.SDK_INFO[qu.ATTR_TELEMETRY_SDK_VERSION]})}Fh2.defaultResource=w86});
var sI0=E((Jl2)=>{Object.defineProperty(Jl2,"__esModule",{value:!0});Jl2.getSharedConfigurationFromEnvironment=void 0;var Wl2=ZQ();function Il2(A){let B=process.env[A]?.trim();if(B!=null&&B!==""){let Q=Number(B);if(Number.isFinite(Q)&&Q>0)return Q;Wl2.diag.warn(`Configuration: ${A} is invalid, expected number greater than 0 (actual: ${B})`)}return}function UZ6(A){let B=Il2(`OTEL_EXPORTER_OTLP_${A}_TIMEOUT`),Q=Il2("OTEL_EXPORTER_OTLP_TIMEOUT");return B??Q}function Yl2(A){let B=process.env[A]?.trim();if(B==="")return;if(B==null||B==="none"||B==="gzip")return B;Wl2.diag.warn(`Configuration: ${A} is invalid, expected 'none' or 'gzip' (actual: '${B}')`);return}function wZ6(A){let B=Yl2(`OTEL_EXPORTER_OTLP_${A}_COMPRESSION`),Q=Yl2("OTEL_EXPORTER_OTLP_COMPRESSION");return B??Q}function $Z6(A){return{timeoutMillis:UZ6(A),compression:wZ6(A)}}Jl2.getSharedConfigurationFromEnvironment=$Z6});
var sT2=E((nT2)=>{Object.defineProperty(nT2,"__esModule",{value:!0});nT2.otperformance=void 0;var Mr4=J1("perf_hooks");nT2.otperformance=Mr4.performance});
var sb2=E((nb2)=>{Object.defineProperty(nb2,"__esModule",{value:!0});nb2.BindOnceFuture=void 0;var N66=pb2();class ib2{_callback;_that;_isCalled=!1;_deferred=new N66.Deferred;constructor(A,B){this._callback=A,this._that=B}get isCalled(){return this._isCalled}get promise(){return this._deferred.promise}call(...A){if(!this._isCalled){this._isCalled=!0;try{Promise.resolve(this._callback.call(this._that,...A)).then((B)=>this._deferred.resolve(B),(B)=>this._deferred.reject(B))}catch(B){this._deferred.reject(B)}}return this._deferred.promise}}nb2.BindOnceFuture=ib2});
var sh2=E((nh2)=>{Object.defineProperty(nh2,"__esModule",{value:!0});nh2.osDetector=void 0;var lh2=GP(),ph2=J1("os"),s86=tF0();class ih2{detect(A){return{attributes:{[lh2.SEMRESATTRS_OS_TYPE]:s86.normalizeType(ph2.platform()),[lh2.SEMRESATTRS_OS_VERSION]:ph2.release()}}}}nh2.osDetector=new ih2});
var sv2=E((nv2)=>{Object.defineProperty(nv2,"__esModule",{value:!0});nv2.addHrTimes=nv2.isTimeInput=nv2.isTimeInputHrTime=nv2.hrTimeToMicroseconds=nv2.hrTimeToMilliseconds=nv2.hrTimeToNanoseconds=nv2.hrTimeToTimeStamp=nv2.hrTimeDuration=nv2.timeInputToHrTime=nv2.hrTime=nv2.getTimeOrigin=nv2.millisToHrTime=void 0;var OF0=RF0(),lv2=9,V46=6,C46=Math.pow(10,V46),MO1=Math.pow(10,lv2);function p51(A){let B=A/1000,Q=Math.trunc(B),D=Math.round(A%1000*C46);return[Q,D]}nv2.millisToHrTime=p51;function TF0(){let A=OF0.otperformance.timeOrigin;if(typeof A!=="number"){let B=OF0.otperformance;A=B.timing&&B.timing.fetchStart}return A}nv2.getTimeOrigin=TF0;function pv2(A){let B=p51(TF0()),Q=p51(typeof A==="number"?A:OF0.otperformance.now());return iv2(B,Q)}nv2.hrTime=pv2;function K46(A){if(PF0(A))return A;else if(typeof A==="number")if(A<TF0())return pv2(A);else return p51(A);else if(A instanceof Date)return p51(A.getTime());else throw TypeError("Invalid input type")}nv2.timeInputToHrTime=K46;function H46(A,B){let Q=B[0]-A[0],D=B[1]-A[1];if(D<0)Q-=1,D+=MO1;return[Q,D]}nv2.hrTimeDuration=H46;function z46(A){let B=lv2,Q=`${"0".repeat(B)}${A[1]}Z`,D=Q.substring(Q.length-B-1);return new Date(A[0]*1000).toISOString().replace("000Z",D)}nv2.hrTimeToTimeStamp=z46;function E46(A){return A[0]*MO1+A[1]}nv2.hrTimeToNanoseconds=E46;function U46(A){return A[0]*1000+A[1]/1e6}nv2.hrTimeToMilliseconds=U46;function w46(A){return A[0]*1e6+A[1]/1000}nv2.hrTimeToMicroseconds=w46;function PF0(A){return Array.isArray(A)&&A.length===2&&typeof A[0]==="number"&&typeof A[1]==="number"}nv2.isTimeInputHrTime=PF0;function $46(A){return PF0(A)||typeof A==="number"||A instanceof Date}nv2.isTimeInput=$46;function iv2(A,B){let Q=[A[0]+B[0],A[1]+B[1]];if(Q[1]>=MO1)Q[1]-=MO1,Q[0]+=1;return Q}nv2.addHrTimes=iv2});
var tF0=E((fh2)=>{Object.defineProperty(fh2,"__esModule",{value:!0});fh2.normalizeType=fh2.normalizeArch=void 0;var l86=(A)=>{switch(A){case"arm":return"arm32";case"ppc":return"ppc32";case"x64":return"amd64";default:return A}};fh2.normalizeArch=l86;var p86=(A)=>{switch(A){case"sunos":return"solaris";case"win32":return"windows";default:return A}};fh2.normalizeType=p86});
var tL2=E((rL2)=>{Object.defineProperty(rL2,"__esModule",{value:!0});rL2.NoopContextManager=void 0;var ii4=f51();class sL2{active(){return ii4.ROOT_CONTEXT}with(A,B,Q,...D){return B.call(Q,...D)}bind(A,B){return B}enable(){return this}disable(){return this}}rL2.NoopContextManager=sL2});
var tN2=E((rN2)=>{Object.defineProperty(rN2,"__esModule",{value:!0});rN2._globalThis=void 0;rN2._globalThis=typeof globalThis==="object"?globalThis:global});
var tO1=E((Ay5,Kd2)=>{Kd2.exports=E8;var XE=pL(),LI0,oO1=XE.LongBits,Xd2=XE.base64,Vd2=XE.utf8;function F31(A,B,Q){this.fn=A,this.len=B,this.next=void 0,this.val=Q}function RI0(){}function P76(A){this.head=A.head,this.tail=A.tail,this.len=A.len,this.next=A.states}function E8(){this.len=0,this.head=new F31(RI0,0,0),this.tail=this.head,this.states=null}var Cd2=function A(){return XE.Buffer?function B(){return(E8.create=function Q(){return new LI0})()}:function B(){return new E8}};E8.create=Cd2();E8.alloc=function A(B){return new XE.Array(B)};if(XE.Array!==Array)E8.alloc=XE.pool(E8.alloc,XE.Array.prototype.subarray);E8.prototype._push=function A(B,Q,D){return this.tail=this.tail.next=new F31(B,Q,D),this.len+=Q,this};function OI0(A,B,Q){B[Q]=A&255}function S76(A,B,Q){while(A>127)B[Q++]=A&127|128,A>>>=7;B[Q]=A}function TI0(A,B){this.len=A,this.next=void 0,this.val=B}TI0.prototype=Object.create(F31.prototype);TI0.prototype.fn=S76;E8.prototype.uint32=function A(B){return this.len+=(this.tail=this.tail.next=new TI0((B=B>>>0)<128?1:B<16384?2:B<2097152?3:B<268435456?4:5,B)).len,this};E8.prototype.int32=function A(B){return B<0?this._push(PI0,10,oO1.fromNumber(B)):this.uint32(B)};E8.prototype.sint32=function A(B){return this.uint32((B<<1^B>>31)>>>0)};function PI0(A,B,Q){while(A.hi)B[Q++]=A.lo&127|128,A.lo=(A.lo>>>7|A.hi<<25)>>>0,A.hi>>>=7;while(A.lo>127)B[Q++]=A.lo&127|128,A.lo=A.lo>>>7;B[Q++]=A.lo}E8.prototype.uint64=function A(B){var Q=oO1.from(B);return this._push(PI0,Q.length(),Q)};E8.prototype.int64=E8.prototype.uint64;E8.prototype.sint64=function A(B){var Q=oO1.from(B).zzEncode();return this._push(PI0,Q.length(),Q)};E8.prototype.bool=function A(B){return this._push(OI0,1,B?1:0)};function MI0(A,B,Q){B[Q]=A&255,B[Q+1]=A>>>8&255,B[Q+2]=A>>>16&255,B[Q+3]=A>>>24}E8.prototype.fixed32=function A(B){return this._push(MI0,4,B>>>0)};E8.prototype.sfixed32=E8.prototype.fixed32;E8.prototype.fixed64=function A(B){var Q=oO1.from(B);return this._push(MI0,4,Q.lo)._push(MI0,4,Q.hi)};E8.prototype.sfixed64=E8.prototype.fixed64;E8.prototype.float=function A(B){return this._push(XE.float.writeFloatLE,4,B)};E8.prototype.double=function A(B){return this._push(XE.float.writeDoubleLE,8,B)};var j76=XE.Array.prototype.set?function A(B,Q,D){Q.set(B,D)}:function A(B,Q,D){for(var Z=0;Z<B.length;++Z)Q[D+Z]=B[Z]};E8.prototype.bytes=function A(B){var Q=B.length>>>0;if(!Q)return this._push(OI0,1,0);if(XE.isString(B)){var D=E8.alloc(Q=Xd2.length(B));Xd2.decode(B,D,0),B=D}return this.uint32(Q)._push(j76,Q,B)};E8.prototype.string=function A(B){var Q=Vd2.length(B);return Q?this.uint32(Q)._push(Vd2.write,Q,B):this._push(OI0,1,0)};E8.prototype.fork=function A(){return this.states=new P76(this),this.head=this.tail=new F31(RI0,0,0),this.len=0,this};E8.prototype.reset=function A(){if(this.states)this.head=this.states.head,this.tail=this.states.tail,this.len=this.states.len,this.states=this.states.next;else this.head=this.tail=new F31(RI0,0,0),this.len=0;return this};E8.prototype.ldelim=function A(){var B=this.head,Q=this.tail,D=this.len;if(this.reset().uint32(D),D)this.tail.next=B.next,this.tail=Q,this.len+=D;return this};E8.prototype.finish=function A(){var B=this.head.next,Q=this.constructor.alloc(this.len),D=0;while(B)B.fn(B.val,Q,D),D+=B.len,B=B.next;return Q};E8._configure=function(A){LI0=A,E8.create=Cd2(),LI0._configure()}});
var tO2=E((rO2)=>{Object.defineProperty(rO2,"__esModule",{value:!0});rO2.LogarithmMapping=void 0;var So=zF0(),nO2=wO1(),aO2=$O1();class sO2{_scale;_scaleFactor;_inverseFactor;constructor(A){this._scale=A,this._scaleFactor=nO2.ldexp(Math.LOG2E,A),this._inverseFactor=nO2.ldexp(Math.LN2,-A)}mapToIndex(A){if(A<=So.MIN_VALUE)return this._minNormalLowerBoundaryIndex()-1;if(So.getSignificand(A)===0)return(So.getNormalBase2(A)<<this._scale)-1;let B=Math.floor(Math.log(A)*this._scaleFactor),Q=this._maxNormalLowerBoundaryIndex();if(B>=Q)return Q;return B}lowerBoundary(A){let B=this._maxNormalLowerBoundaryIndex();if(A>=B){if(A===B)return 2*Math.exp((A-(1<<this._scale))/this._scaleFactor);throw new aO2.MappingError(`overflow: ${A} is > maximum lower boundary: ${B}`)}let Q=this._minNormalLowerBoundaryIndex();if(A<=Q){if(A===Q)return So.MIN_VALUE;else if(A===Q-1)return Math.exp((A+(1<<this._scale))/this._scaleFactor)/2;throw new aO2.MappingError(`overflow: ${A} is < minimum lower boundary: ${Q}`)}return Math.exp(A*this._inverseFactor)}get scale(){return this._scale}_minNormalLowerBoundaryIndex(){return So.MIN_NORMAL_EXPONENT<<this._scale}_maxNormalLowerBoundaryIndex(){return(So.MAX_NORMAL_EXPONENT+1<<this._scale)-1}}rO2.LogarithmMapping=sO2});
var tT2=E((rT2)=>{Object.defineProperty(rT2,"__esModule",{value:!0});rT2.VERSION=void 0;rT2.VERSION="2.0.0"});
var tc2=E((rc2)=>{Object.defineProperty(rc2,"__esModule",{value:!0});rc2.createHttpExporterTransport=void 0;class sc2{_parameters;_utils=null;constructor(A){this._parameters=A}async send(A,B){let{agent:Q,send:D}=this._loadUtils();return new Promise((Z)=>{D(this._parameters,Q,A,(G)=>{Z(G)},B)})}shutdown(){}_loadUtils(){let A=this._utils;if(A===null){let{sendWithHttp:B,createHttpAgent:Q}=ac2();A=this._utils={agent:Q(this._parameters.url,this._parameters.agentOptions),send:B}}return A}}function FZ6(A){return new sc2(A)}rc2.createHttpExporterTransport=FZ6});
var tf2=E((rf2)=>{Object.defineProperty(rf2,"__esModule",{value:!0});rf2.ConsoleMetricExporter=void 0;var sf2=y3(),I86=gF0();class dF0{_shutdown=!1;_temporalitySelector;constructor(A){this._temporalitySelector=A?.temporalitySelector??I86.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR}export(A,B){if(this._shutdown){setImmediate(B,{code:sf2.ExportResultCode.FAILED});return}return dF0._sendMetrics(A,B)}forceFlush(){return Promise.resolve()}selectAggregationTemporality(A){return this._temporalitySelector(A)}shutdown(){return this._shutdown=!0,Promise.resolve()}static _sendMetrics(A,B){for(let Q of A.scopeMetrics)for(let D of Q.metrics)console.dir({descriptor:D.descriptor,dataPointType:D.dataPointType,dataPoints:D.dataPoints},{depth:null});B({code:sf2.ExportResultCode.SUCCESS})}}rf2.ConsoleMetricExporter=dF0});
var tg2=E((rg2)=>{Object.defineProperty(rg2,"__esModule",{value:!0});rg2.getConflictResolutionRecipe=rg2.getDescriptionResolutionRecipe=rg2.getTypeConflictResolutionRecipe=rg2.getUnitConflictResolutionRecipe=rg2.getValueTypeConflictResolutionRecipe=rg2.getIncompatibilityDetails=void 0;function c56(A,B){let Q="";if(A.unit!==B.unit)Q+=`	- Unit '${A.unit}' does not match '${B.unit}'
`;if(A.type!==B.type)Q+=`	- Type '${A.type}' does not match '${B.type}'
`;if(A.valueType!==B.valueType)Q+=`	- Value Type '${A.valueType}' does not match '${B.valueType}'
`;if(A.description!==B.description)Q+=`	- Description '${A.description}' does not match '${B.description}'
`;return Q}rg2.getIncompatibilityDetails=c56;function ig2(A,B){return`	- use valueType '${A.valueType}' on instrument creation or use an instrument name other than '${B.name}'`}rg2.getValueTypeConflictResolutionRecipe=ig2;function ng2(A,B){return`	- use unit '${A.unit}' on instrument creation or use an instrument name other than '${B.name}'`}rg2.getUnitConflictResolutionRecipe=ng2;function ag2(A,B){let Q={name:B.name,type:B.type,unit:B.unit},D=JSON.stringify(Q);return`	- create a new view with a name other than '${A.name}' and InstrumentSelector '${D}'`}rg2.getTypeConflictResolutionRecipe=ag2;function sg2(A,B){let Q={name:B.name,type:B.type,unit:B.unit},D=JSON.stringify(Q);return`	- create a new view with a name other than '${A.name}' and InstrumentSelector '${D}'
        - OR - create a new view with the name ${A.name} and description '${A.description}' and InstrumentSelector ${D}
        - OR - create a new view with the name ${B.name} and description '${A.description}' and InstrumentSelector ${D}`}rg2.getDescriptionResolutionRecipe=sg2;function l56(A,B){if(A.valueType!==B.valueType)return ig2(A,B);if(A.unit!==B.unit)return ng2(A,B);if(A.type!==B.type)return ag2(A,B);if(A.description!==B.description)return sg2(A,B);return""}rg2.getConflictResolutionRecipe=l56});
var tu2=E((ru2)=>{Object.defineProperty(ru2,"__esModule",{value:!0});ru2.MeterSelector=void 0;var WI0=pO1();class su2{_nameFilter;_versionFilter;_schemaUrlFilter;constructor(A){this._nameFilter=new WI0.ExactPredicate(A?.name),this._versionFilter=new WI0.ExactPredicate(A?.version),this._schemaUrlFilter=new WI0.ExactPredicate(A?.schemaUrl)}getNameFilter(){return this._nameFilter}getVersionFilter(){return this._versionFilter}getSchemaUrlFilter(){return this._schemaUrlFilter}}ru2.MeterSelector=su2});
var ty2=E((Cu)=>{var L06=Cu&&Cu.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),M06=Cu&&Cu.__exportStar||function(A,B){for(var Q in A)if(Q!=="default"&&!Object.prototype.hasOwnProperty.call(B,Q))L06(B,A,Q)};Object.defineProperty(Cu,"__esModule",{value:!0});M06(oy2(),Cu)});
var uF0=E((ff2)=>{Object.defineProperty(ff2,"__esModule",{value:!0});ff2.MetricReader=void 0;var xf2=ZQ(),bO1=cw(),vf2=gF0();class bf2{_shutdown=!1;_metricProducers;_sdkMetricProducer;_aggregationTemporalitySelector;_aggregationSelector;_cardinalitySelector;constructor(A){this._aggregationSelector=A?.aggregationSelector??vf2.DEFAULT_AGGREGATION_SELECTOR,this._aggregationTemporalitySelector=A?.aggregationTemporalitySelector??vf2.DEFAULT_AGGREGATION_TEMPORALITY_SELECTOR,this._metricProducers=A?.metricProducers??[],this._cardinalitySelector=A?.cardinalitySelector}setMetricProducer(A){if(this._sdkMetricProducer)throw new Error("MetricReader can not be bound to a MeterProvider again.");this._sdkMetricProducer=A,this.onInitialized()}selectAggregation(A){return this._aggregationSelector(A)}selectAggregationTemporality(A){return this._aggregationTemporalitySelector(A)}selectCardinalityLimit(A){return this._cardinalitySelector?this._cardinalitySelector(A):2000}onInitialized(){}async collect(A){if(this._sdkMetricProducer===void 0)throw new Error("MetricReader is not bound to a MetricProducer");if(this._shutdown)throw new Error("MetricReader is shutdown");let[B,...Q]=await Promise.all([this._sdkMetricProducer.collect({timeoutMillis:A?.timeoutMillis}),...this._metricProducers.map((F)=>F.collect({timeoutMillis:A?.timeoutMillis}))]),D=B.errors.concat(bO1.FlatMap(Q,(F)=>F.errors)),Z=B.resourceMetrics.resource,G=B.resourceMetrics.scopeMetrics.concat(bO1.FlatMap(Q,(F)=>F.resourceMetrics.scopeMetrics));return{resourceMetrics:{resource:Z,scopeMetrics:G},errors:D}}async shutdown(A){if(this._shutdown){xf2.diag.error("Cannot call shutdown twice.");return}if(A?.timeoutMillis==null)await this.onShutdown();else await bO1.callWithTimeout(this.onShutdown(),A.timeoutMillis);this._shutdown=!0}async forceFlush(A){if(this._shutdown){xf2.diag.warn("Cannot forceFlush on already shutdown MetricReader.");return}if(A?.timeoutMillis==null){await this.onForceFlush();return}await bO1.callWithTimeout(this.onForceFlush(),A.timeoutMillis)}}ff2.MetricReader=bf2});
var uI0=E((sd2)=>{Object.defineProperty(sd2,"__esModule",{value:!0});sd2.toLogAttributes=sd2.createExportLogsServiceRequest=void 0;var s76=QT1(),ZT1=DT1();function r76(A,B){let Q=s76.getOtlpEncoder(B);return{resourceLogs:t76(A,Q)}}sd2.createExportLogsServiceRequest=r76;function o76(A){let B=new Map;for(let Q of A){let{resource:D,instrumentationScope:{name:Z,version:G="",schemaUrl:F=""}}=Q,I=B.get(D);if(!I)I=new Map,B.set(D,I);let Y=`${Z}@${G}:${F}`,W=I.get(Y);if(!W)W=[],I.set(Y,W);W.push(Q)}return B}function t76(A,B){let Q=o76(A);return Array.from(Q,([D,Z])=>({resource:ZT1.createResource(D),scopeLogs:Array.from(Z,([,G])=>{return{scope:ZT1.createInstrumentationScope(G[0].instrumentationScope),logRecords:G.map((F)=>e76(F,B)),schemaUrl:G[0].instrumentationScope.schemaUrl}}),schemaUrl:void 0}))}function e76(A,B){return{timeUnixNano:B.encodeHrTime(A.hrTime),observedTimeUnixNano:B.encodeHrTime(A.hrTimeObserved),severityNumber:AD6(A.severityNumber),severityText:A.severityText,body:ZT1.toAnyValue(A.body),attributes:ad2(A.attributes),droppedAttributesCount:A.droppedAttributesCount,flags:A.spanContext?.traceFlags,traceId:B.encodeOptionalSpanContext(A.spanContext?.traceId),spanId:B.encodeOptionalSpanContext(A.spanContext?.spanId)}}function AD6(A){return A}function ad2(A){return Object.keys(A).map((B)=>ZT1.toKeyValue(B,A[B]))}sd2.toLogAttributes=ad2});
var uM2=E((hM2)=>{Object.defineProperty(hM2,"__esModule",{value:!0});hM2.validateValue=hM2.validateKey=void 0;var uG0="[_0-9a-z-*/]",jn4=`[a-z]${uG0}{0,255}`,kn4=`[a-z0-9]${uG0}{0,240}@[a-z]${uG0}{0,13}`,yn4=new RegExp(`^(?:${jn4}|${kn4})$`),_n4=/^[ -~]{0,255}[!-~]$/,xn4=/,|=/;function vn4(A){return yn4.test(A)}hM2.validateKey=vn4;function bn4(A){return _n4.test(A)&&!xn4.test(A)}hM2.validateValue=bn4});
var ul2=E((hl2)=>{Object.defineProperty(hl2,"__esModule",{value:!0});hl2.OTLPMetricExporter=void 0;var DG6=IT1(),ZG6=ju(),GG6=vl2(),bl2=uo();class fl2 extends DG6.OTLPMetricExporterBase{constructor(A){super(bl2.createOtlpHttpExportDelegate(bl2.convertLegacyHttpOptions(A??{},"METRICS","v1/metrics",{"User-Agent":`OTel-OTLP-Exporter-JavaScript/${GG6.VERSION}`,"Content-Type":"application/x-protobuf"}),ZG6.ProtobufMetricsSerializer),A)}}hl2.OTLPMetricExporter=fl2});
var uo=E((J31)=>{Object.defineProperty(J31,"__esModule",{value:!0});J31.convertLegacyHttpOptions=J31.getSharedConfigurationFromEnvironment=J31.createOtlpHttpExportDelegate=void 0;var dZ6=Fl2();Object.defineProperty(J31,"createOtlpHttpExportDelegate",{enumerable:!0,get:function(){return dZ6.createOtlpHttpExportDelegate}});var cZ6=sI0();Object.defineProperty(J31,"getSharedConfigurationFromEnvironment",{enumerable:!0,get:function(){return cZ6.getSharedConfigurationFromEnvironment}});var lZ6=Rl2();Object.defineProperty(J31,"convertLegacyHttpOptions",{enumerable:!0,get:function(){return lZ6.convertLegacyHttpOptions}})});
var uu2=E((hu2)=>{Object.defineProperty(hu2,"__esModule",{value:!0});hu2.MetricCollector=void 0;var M36=y3();class fu2{_sharedState;_metricReader;constructor(A,B){this._sharedState=A,this._metricReader=B}async collect(A){let B=M36.millisToHrTime(Date.now()),Q=[],D=[],Z=Array.from(this._sharedState.meterSharedStates.values()).map(async(G)=>{let F=await G.collect(this,B,A);if(F?.scopeMetrics!=null)Q.push(F.scopeMetrics);if(F?.errors!=null)D.push(...F.errors)});return await Promise.all(Z),{resourceMetrics:{resource:this._sharedState.resource,scopeMetrics:Q},errors:D}}async forceFlush(A){await this._metricReader.forceFlush(A)}async shutdown(A){await this._metricReader.shutdown(A)}selectAggregationTemporality(A){return this._metricReader.selectAggregationTemporality(A)}selectAggregation(A){return this._metricReader.selectAggregation(A)}selectCardinalityLimit(A){return this._metricReader.selectCardinalityLimit?.(A)??2000}}hu2.MetricCollector=fu2});
var vG0=E((LM2)=>{Object.defineProperty(LM2,"__esModule",{value:!0});LM2.ProxyTracer=void 0;var qn4=xG0(),Nn4=new qn4.NoopTracer;class NM2{constructor(A,B,Q,D){this._provider=A,this.name=B,this.version=Q,this.options=D}startSpan(A,B,Q){return this._getTracer().startSpan(A,B,Q)}startActiveSpan(A,B,Q,D){let Z=this._getTracer();return Reflect.apply(Z.startActiveSpan,Z,arguments)}_getTracer(){if(this._delegate)return this._delegate;let A=this._provider.getDelegateTracer(this.name,this.version,this.options);if(!A)return Nn4;return this._delegate=A,this._delegate}}LM2.ProxyTracer=NM2});
var vM2=E((xM2)=>{Object.defineProperty(xM2,"__esModule",{value:!0});xM2.SpanKind=void 0;var Pn4;(function(A){A[A.INTERNAL=0]="INTERNAL",A[A.SERVER=1]="SERVER",A[A.CLIENT=2]="CLIENT",A[A.PRODUCER=3]="PRODUCER",A[A.CONSUMER=4]="CONSUMER"})(Pn4=xM2.SpanKind||(xM2.SpanKind={}))});
var vc2=E((aI0)=>{Object.defineProperty(aI0,"__esModule",{value:!0});aI0.JsonTraceSerializer=void 0;var uD6=xc2();Object.defineProperty(aI0,"JsonTraceSerializer",{enumerable:!0,get:function(){return uD6.JsonTraceSerializer}})});
var vl2=E((_l2)=>{Object.defineProperty(_l2,"__esModule",{value:!0});_l2.VERSION=void 0;_l2.VERSION="0.200.0"});
var wF0=E((VT2)=>{Object.defineProperty(VT2,"__esModule",{value:!0});VT2.BAGGAGE_MAX_TOTAL_LENGTH=VT2.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=VT2.BAGGAGE_MAX_NAME_VALUE_PAIRS=VT2.BAGGAGE_HEADER=VT2.BAGGAGE_ITEMS_SEPARATOR=VT2.BAGGAGE_PROPERTIES_SEPARATOR=VT2.BAGGAGE_KEY_PAIR_SEPARATOR=void 0;VT2.BAGGAGE_KEY_PAIR_SEPARATOR="=";VT2.BAGGAGE_PROPERTIES_SEPARATOR=";";VT2.BAGGAGE_ITEMS_SEPARATOR=",";VT2.BAGGAGE_HEADER="baggage";VT2.BAGGAGE_MAX_NAME_VALUE_PAIRS=180;VT2.BAGGAGE_MAX_PER_NAME_VALUE_PAIRS=4096;VT2.BAGGAGE_MAX_TOTAL_LENGTH=8192});
var wO1=E((fO2)=>{Object.defineProperty(fO2,"__esModule",{value:!0});fO2.nextGreaterSquare=fO2.ldexp=void 0;function Rs4(A,B){if(A===0||A===Number.POSITIVE_INFINITY||A===Number.NEGATIVE_INFINITY||Number.isNaN(A))return A;return A*Math.pow(2,B)}fO2.ldexp=Rs4;function Os4(A){return A--,A|=A>>1,A|=A>>2,A|=A>>4,A|=A>>8,A|=A>>16,A++,A}fO2.nextGreaterSquare=Os4});
var xG0=E(($M2)=>{Object.defineProperty($M2,"__esModule",{value:!0});$M2.NoopTracer=void 0;var Un4=g51(),UM2=kG0(),yG0=VO1(),wn4=CO1(),_G0=Un4.ContextAPI.getInstance();class wM2{startSpan(A,B,Q=_G0.active()){if(Boolean(B===null||B===void 0?void 0:B.root))return new yG0.NonRecordingSpan;let Z=Q&&UM2.getSpanContext(Q);if($n4(Z)&&wn4.isSpanContextValid(Z))return new yG0.NonRecordingSpan(Z);else return new yG0.NonRecordingSpan}startActiveSpan(A,B,Q,D){let Z,G,F;if(arguments.length<2)return;else if(arguments.length===2)F=B;else if(arguments.length===3)Z=B,F=Q;else Z=B,G=Q,F=D;let I=G!==null&&G!==void 0?G:_G0.active(),Y=this.startSpan(A,Z,I),W=UM2.setSpan(I,Y);return _G0.with(W,F,void 0,Y)}}$M2.NoopTracer=wM2;function $n4(A){return typeof A==="object"&&typeof A.spanId==="string"&&typeof A.traceId==="string"&&typeof A.traceFlags==="number"}});
var xI0=E((yd2)=>{var PV=yd2;PV.build="minimal";PV.Writer=tO1();PV.BufferWriter=Ed2();PV.Reader=AT1();PV.BufferReader=Od2();PV.util=pL();PV.rpc=yI0();PV.roots=_I0();PV.configure=kd2;function kd2(){PV.util._configure(),PV.Writer._configure(PV.BufferWriter),PV.Reader._configure(PV.BufferReader)}kd2()});
var xL2=E((yL2)=>{Object.defineProperty(yL2,"__esModule",{value:!0});yL2.DiagConsoleLogger=void 0;var VG0=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class kL2{constructor(){function A(B){return function(...Q){if(console){let D=console[B];if(typeof D!=="function")D=console.log;if(typeof D==="function")return D.apply(console,Q)}}}for(let B=0;B<VG0.length;B++)this[VG0[B].n]=A(VG0[B].c)}}yL2.DiagConsoleLogger=kL2});
var xO2=E((yO2)=>{Object.defineProperty(yO2,"__esModule",{value:!0});yO2.Buckets=void 0;class CF0{backing;indexBase;indexStart;indexEnd;constructor(A=new KF0,B=0,Q=0,D=0){this.backing=A,this.indexBase=B,this.indexStart=Q,this.indexEnd=D}get offset(){return this.indexStart}get length(){if(this.backing.length===0)return 0;if(this.indexEnd===this.indexStart&&this.at(0)===0)return 0;return this.indexEnd-this.indexStart+1}counts(){return Array.from({length:this.length},(A,B)=>this.at(B))}at(A){let B=this.indexBase-this.indexStart;if(A<B)A+=this.backing.length;return A-=B,this.backing.countAt(A)}incrementBucket(A,B){this.backing.increment(A,B)}decrementBucket(A,B){this.backing.decrement(A,B)}trim(){for(let A=0;A<this.length;A++)if(this.at(A)!==0){this.indexStart+=A;break}else if(A===this.length-1){this.indexStart=this.indexEnd=this.indexBase=0;return}for(let A=this.length-1;A>=0;A--)if(this.at(A)!==0){this.indexEnd-=this.length-A-1;break}this._rotate()}downscale(A){this._rotate();let B=1+this.indexEnd-this.indexStart,Q=1<<A,D=0,Z=0;for(let G=this.indexStart;G<=this.indexEnd;){let F=G%Q;if(F<0)F+=Q;for(let I=F;I<Q&&D<B;I++)this._relocateBucket(Z,D),D++,G++;Z++}this.indexStart>>=A,this.indexEnd>>=A,this.indexBase=this.indexStart}clone(){return new CF0(this.backing.clone(),this.indexBase,this.indexStart,this.indexEnd)}_rotate(){let A=this.indexBase-this.indexStart;if(A===0)return;else if(A>0)this.backing.reverse(0,this.backing.length),this.backing.reverse(0,A),this.backing.reverse(A,this.backing.length);else this.backing.reverse(0,this.backing.length),this.backing.reverse(0,this.backing.length+A);this.indexBase=this.indexStart}_relocateBucket(A,B){if(A===B)return;this.incrementBucket(A,this.backing.emptyBucket(B))}}yO2.Buckets=CF0;class KF0{_counts;constructor(A=[0]){this._counts=A}get length(){return this._counts.length}countAt(A){return this._counts[A]}growTo(A,B,Q){let D=new Array(A).fill(0);D.splice(Q,this._counts.length-B,...this._counts.slice(B)),D.splice(0,B,...this._counts.slice(0,B)),this._counts=D}reverse(A,B){let Q=Math.floor((A+B)/2)-A;for(let D=0;D<Q;D++){let Z=this._counts[A+D];this._counts[A+D]=this._counts[B-D-1],this._counts[B-D-1]=Z}}emptyBucket(A){let B=this._counts[A];return this._counts[A]=0,B}increment(A,B){this._counts[A]+=B}decrement(A,B){if(this._counts[A]>=B)this._counts[A]-=B;else this._counts[A]=0}clone(){return new KF0([...this._counts])}}});
var xR2=E((yR2)=>{Object.defineProperty(yR2,"__esModule",{value:!0});yR2.TraceAPI=void 0;var rG0=Wu(),SR2=bG0(),jR2=CO1(),Oo=kG0(),kR2=Ju(),oG0="trace";class tG0{constructor(){this._proxyTracerProvider=new SR2.ProxyTracerProvider,this.wrapSpanContext=jR2.wrapSpanContext,this.isSpanContextValid=jR2.isSpanContextValid,this.deleteSpan=Oo.deleteSpan,this.getSpan=Oo.getSpan,this.getActiveSpan=Oo.getActiveSpan,this.getSpanContext=Oo.getSpanContext,this.setSpan=Oo.setSpan,this.setSpanContext=Oo.setSpanContext}static getInstance(){if(!this._instance)this._instance=new tG0;return this._instance}setGlobalTracerProvider(A){let B=rG0.registerGlobal(oG0,this._proxyTracerProvider,kR2.DiagAPI.instance());if(B)this._proxyTracerProvider.setDelegate(A);return B}getTracerProvider(){return rG0.getGlobal(oG0)||this._proxyTracerProvider}getTracer(A,B){return this.getTracerProvider().getTracer(A,B)}disable(){rG0.unregisterGlobal(oG0,kR2.DiagAPI.instance()),this._proxyTracerProvider=new SR2.ProxyTracerProvider}}yR2.TraceAPI=tG0});
var xb2=E((yb2)=>{Object.defineProperty(yb2,"__esModule",{value:!0});yb2.merge=void 0;var Pb2=Tb2(),H66=20;function z66(...A){let B=A.shift(),Q=new WeakMap;while(A.length>0)B=jb2(B,A.shift(),0,Q);return B}yb2.merge=z66;function xF0(A){if(SO1(A))return A.slice();return A}function jb2(A,B,Q=0,D){let Z;if(Q>H66)return;if(Q++,PO1(A)||PO1(B)||kb2(B))Z=xF0(B);else if(SO1(A)){if(Z=A.slice(),SO1(B))for(let G=0,F=B.length;G<F;G++)Z.push(xF0(B[G]));else if(i51(B)){let G=Object.keys(B);for(let F=0,I=G.length;F<I;F++){let Y=G[F];Z[Y]=xF0(B[Y])}}}else if(i51(A))if(i51(B)){if(!E66(A,B))return B;Z=Object.assign({},A);let G=Object.keys(B);for(let F=0,I=G.length;F<I;F++){let Y=G[F],W=B[Y];if(PO1(W))if(typeof W==="undefined")delete Z[Y];else Z[Y]=W;else{let J=Z[Y],X=W;if(Sb2(A,Y,D)||Sb2(B,Y,D))delete Z[Y];else{if(i51(J)&&i51(X)){let V=D.get(J)||[],C=D.get(X)||[];V.push({obj:A,key:Y}),C.push({obj:B,key:Y}),D.set(J,V),D.set(X,C)}Z[Y]=jb2(Z[Y],W,Q,D)}}}}else Z=B;return Z}function Sb2(A,B,Q){let D=Q.get(A[B])||[];for(let Z=0,G=D.length;Z<G;Z++){let F=D[Z];if(F.key===B&&F.obj===A)return!0}return!1}function SO1(A){return Array.isArray(A)}function kb2(A){return typeof A==="function"}function i51(A){return!PO1(A)&&!SO1(A)&&!kb2(A)&&typeof A==="object"}function PO1(A){return typeof A==="string"||typeof A==="number"||typeof A==="boolean"||typeof A==="undefined"||A instanceof Date||A instanceof RegExp||A===null}function E66(A,B){if(!Pb2.isPlainObject(A)||!Pb2.isPlainObject(B))return!1;return!0}});
var xc2=E((yc2)=>{Object.defineProperty(yc2,"__esModule",{value:!0});yc2.JsonTraceSerializer=void 0;var gD6=lI0();yc2.JsonTraceSerializer={serializeRequest:(A)=>{let B=gD6.createExportTraceServiceRequest(A,{useHex:!0,useLongBits:!1});return new TextEncoder().encode(JSON.stringify(B))},deserializeResponse:(A)=>{return JSON.parse(new TextDecoder().decode(A))}}});
var xh2=E((yh2)=>{Object.defineProperty(yh2,"__esModule",{value:!0});yh2.getMachineId=void 0;var m86=ZQ();async function d86(){m86.diag.debug("could not read machine-id: unsupported platform");return}yh2.getMachineId=d86});
var y3=E((r9)=>{Object.defineProperty(r9,"__esModule",{value:!0});r9.internal=r9.diagLogLevelFromString=r9.BindOnceFuture=r9.urlMatches=r9.isUrlIgnored=r9.callWithTimeout=r9.TimeoutError=r9.merge=r9.TraceState=r9.unsuppressTracing=r9.suppressTracing=r9.isTracingSuppressed=r9.setRPCMetadata=r9.getRPCMetadata=r9.deleteRPCMetadata=r9.RPCType=r9.parseTraceParent=r9.W3CTraceContextPropagator=r9.TRACE_STATE_HEADER=r9.TRACE_PARENT_HEADER=r9.CompositePropagator=r9.unrefTimer=r9.otperformance=r9.getStringListFromEnv=r9.getNumberFromEnv=r9.getBooleanFromEnv=r9.getStringFromEnv=r9._globalThis=r9.SDK_INFO=r9.parseKeyPairsIntoRecord=r9.ExportResultCode=r9.timeInputToHrTime=r9.millisToHrTime=r9.isTimeInputHrTime=r9.isTimeInput=r9.hrTimeToTimeStamp=r9.hrTimeToNanoseconds=r9.hrTimeToMilliseconds=r9.hrTimeToMicroseconds=r9.hrTimeDuration=r9.hrTime=r9.getTimeOrigin=r9.addHrTimes=r9.loggingErrorHandler=r9.setGlobalErrorHandler=r9.globalErrorHandler=r9.sanitizeAttributes=r9.isAttributeValue=r9.AnchoredClock=r9.W3CBaggagePropagator=void 0;var O66=$T2();Object.defineProperty(r9,"W3CBaggagePropagator",{enumerable:!0,get:function(){return O66.W3CBaggagePropagator}});var T66=MT2();Object.defineProperty(r9,"AnchoredClock",{enumerable:!0,get:function(){return T66.AnchoredClock}});var Zf2=kT2();Object.defineProperty(r9,"isAttributeValue",{enumerable:!0,get:function(){return Zf2.isAttributeValue}});Object.defineProperty(r9,"sanitizeAttributes",{enumerable:!0,get:function(){return Zf2.sanitizeAttributes}});var Gf2=fT2();Object.defineProperty(r9,"globalErrorHandler",{enumerable:!0,get:function(){return Gf2.globalErrorHandler}});Object.defineProperty(r9,"setGlobalErrorHandler",{enumerable:!0,get:function(){return Gf2.setGlobalErrorHandler}});var P66=LF0();Object.defineProperty(r9,"loggingErrorHandler",{enumerable:!0,get:function(){return P66.loggingErrorHandler}});var YE=sv2();Object.defineProperty(r9,"addHrTimes",{enumerable:!0,get:function(){return YE.addHrTimes}});Object.defineProperty(r9,"getTimeOrigin",{enumerable:!0,get:function(){return YE.getTimeOrigin}});Object.defineProperty(r9,"hrTime",{enumerable:!0,get:function(){return YE.hrTime}});Object.defineProperty(r9,"hrTimeDuration",{enumerable:!0,get:function(){return YE.hrTimeDuration}});Object.defineProperty(r9,"hrTimeToMicroseconds",{enumerable:!0,get:function(){return YE.hrTimeToMicroseconds}});Object.defineProperty(r9,"hrTimeToMilliseconds",{enumerable:!0,get:function(){return YE.hrTimeToMilliseconds}});Object.defineProperty(r9,"hrTimeToNanoseconds",{enumerable:!0,get:function(){return YE.hrTimeToNanoseconds}});Object.defineProperty(r9,"hrTimeToTimeStamp",{enumerable:!0,get:function(){return YE.hrTimeToTimeStamp}});Object.defineProperty(r9,"isTimeInput",{enumerable:!0,get:function(){return YE.isTimeInput}});Object.defineProperty(r9,"isTimeInputHrTime",{enumerable:!0,get:function(){return YE.isTimeInputHrTime}});Object.defineProperty(r9,"millisToHrTime",{enumerable:!0,get:function(){return YE.millisToHrTime}});Object.defineProperty(r9,"timeInputToHrTime",{enumerable:!0,get:function(){return YE.timeInputToHrTime}});var S66=ov2();Object.defineProperty(r9,"ExportResultCode",{enumerable:!0,get:function(){return S66.ExportResultCode}});var j66=$F0();Object.defineProperty(r9,"parseKeyPairsIntoRecord",{enumerable:!0,get:function(){return j66.parseKeyPairsIntoRecord}});var v_=RF0();Object.defineProperty(r9,"SDK_INFO",{enumerable:!0,get:function(){return v_.SDK_INFO}});Object.defineProperty(r9,"_globalThis",{enumerable:!0,get:function(){return v_._globalThis}});Object.defineProperty(r9,"getStringFromEnv",{enumerable:!0,get:function(){return v_.getStringFromEnv}});Object.defineProperty(r9,"getBooleanFromEnv",{enumerable:!0,get:function(){return v_.getBooleanFromEnv}});Object.defineProperty(r9,"getNumberFromEnv",{enumerable:!0,get:function(){return v_.getNumberFromEnv}});Object.defineProperty(r9,"getStringListFromEnv",{enumerable:!0,get:function(){return v_.getStringListFromEnv}});Object.defineProperty(r9,"otperformance",{enumerable:!0,get:function(){return v_.otperformance}});Object.defineProperty(r9,"unrefTimer",{enumerable:!0,get:function(){return v_.unrefTimer}});var k66=Qb2();Object.defineProperty(r9,"CompositePropagator",{enumerable:!0,get:function(){return k66.CompositePropagator}});var kO1=zb2();Object.defineProperty(r9,"TRACE_PARENT_HEADER",{enumerable:!0,get:function(){return kO1.TRACE_PARENT_HEADER}});Object.defineProperty(r9,"TRACE_STATE_HEADER",{enumerable:!0,get:function(){return kO1.TRACE_STATE_HEADER}});Object.defineProperty(r9,"W3CTraceContextPropagator",{enumerable:!0,get:function(){return kO1.W3CTraceContextPropagator}});Object.defineProperty(r9,"parseTraceParent",{enumerable:!0,get:function(){return kO1.parseTraceParent}});var yO1=$b2();Object.defineProperty(r9,"RPCType",{enumerable:!0,get:function(){return yO1.RPCType}});Object.defineProperty(r9,"deleteRPCMetadata",{enumerable:!0,get:function(){return yO1.deleteRPCMetadata}});Object.defineProperty(r9,"getRPCMetadata",{enumerable:!0,get:function(){return yO1.getRPCMetadata}});Object.defineProperty(r9,"setRPCMetadata",{enumerable:!0,get:function(){return yO1.setRPCMetadata}});var vF0=c51();Object.defineProperty(r9,"isTracingSuppressed",{enumerable:!0,get:function(){return vF0.isTracingSuppressed}});Object.defineProperty(r9,"suppressTracing",{enumerable:!0,get:function(){return vF0.suppressTracing}});Object.defineProperty(r9,"unsuppressTracing",{enumerable:!0,get:function(){return vF0.unsuppressTracing}});var y66=yF0();Object.defineProperty(r9,"TraceState",{enumerable:!0,get:function(){return y66.TraceState}});var _66=xb2();Object.defineProperty(r9,"merge",{enumerable:!0,get:function(){return _66.merge}});var Ff2=fb2();Object.defineProperty(r9,"TimeoutError",{enumerable:!0,get:function(){return Ff2.TimeoutError}});Object.defineProperty(r9,"callWithTimeout",{enumerable:!0,get:function(){return Ff2.callWithTimeout}});var If2=mb2();Object.defineProperty(r9,"isUrlIgnored",{enumerable:!0,get:function(){return If2.isUrlIgnored}});Object.defineProperty(r9,"urlMatches",{enumerable:!0,get:function(){return If2.urlMatches}});var x66=sb2();Object.defineProperty(r9,"BindOnceFuture",{enumerable:!0,get:function(){return x66.BindOnceFuture}});var v66=eb2();Object.defineProperty(r9,"diagLogLevelFromString",{enumerable:!0,get:function(){return v66.diagLogLevelFromString}});var b66=Df2();r9.internal={_export:b66._export}});
var yF0=E((Jb2)=>{Object.defineProperty(Jb2,"__esModule",{value:!0});Jb2.TraceState=void 0;var Fb2=Gb2(),Ib2=32,m46=512,Yb2=",",Wb2="=";class kF0{_internalState=new Map;constructor(A){if(A)this._parse(A)}set(A,B){let Q=this._clone();if(Q._internalState.has(A))Q._internalState.delete(A);return Q._internalState.set(A,B),Q}unset(A){let B=this._clone();return B._internalState.delete(A),B}get(A){return this._internalState.get(A)}serialize(){return this._keys().reduce((A,B)=>{return A.push(B+Wb2+this.get(B)),A},[]).join(Yb2)}_parse(A){if(A.length>m46)return;if(this._internalState=A.split(Yb2).reverse().reduce((B,Q)=>{let D=Q.trim(),Z=D.indexOf(Wb2);if(Z!==-1){let G=D.slice(0,Z),F=D.slice(Z+1,Q.length);if(Fb2.validateKey(G)&&Fb2.validateValue(F))B.set(G,F)}return B},new Map),this._internalState.size>Ib2)this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,Ib2))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let A=new kF0;return A._internalState=new Map(this._internalState),A}}Jb2.TraceState=kF0});
var yI0=E((Sd2)=>{var _76=Sd2;_76.Service=Pd2()});
var yl2=E((tI0)=>{Object.defineProperty(tI0,"__esModule",{value:!0});tI0.OTLPMetricExporter=void 0;var tZ6=kl2();Object.defineProperty(tI0,"OTLPMetricExporter",{enumerable:!0,get:function(){return tZ6.OTLPMetricExporter}})});
var yu2=E((ju2)=>{Object.defineProperty(ju2,"__esModule",{value:!0});ju2.MeterSharedState=void 0;var X36=A31(),V36=jg2(),C36=cw(),K36=pg2(),H36=Qu2(),z36=Fu2(),E36=Uu2(),U36=Nu2(),w36=lO1();class Su2{_meterProviderSharedState;_instrumentationScope;metricStorageRegistry=new H36.MetricStorageRegistry;observableRegistry=new E36.ObservableRegistry;meter;constructor(A,B){this._meterProviderSharedState=A,this._instrumentationScope=B,this.meter=new V36.Meter(this)}registerMetricStorage(A){let B=this._registerMetricStorage(A,U36.SyncMetricStorage);if(B.length===1)return B[0];return new z36.MultiMetricStorage(B)}registerAsyncMetricStorage(A){return this._registerMetricStorage(A,K36.AsyncMetricStorage)}async collect(A,B,Q){let D=await this.observableRegistry.observe(B,Q?.timeoutMillis),Z=this.metricStorageRegistry.getStorages(A);if(Z.length===0)return null;let G=Z.map((F)=>{return F.collect(A,B)}).filter(C36.isNotNullish);if(G.length===0)return{errors:D};return{scopeMetrics:{scope:this._instrumentationScope,metrics:G},errors:D}}_registerMetricStorage(A,B){let D=this._meterProviderSharedState.viewRegistry.findViews(A,this._instrumentationScope).map((Z)=>{let G=X36.createInstrumentDescriptorWithView(Z,A),F=this.metricStorageRegistry.findOrUpdateCompatibleStorage(G);if(F!=null)return F;let I=Z.aggregation.createAggregator(G),Y=new B(G,I,Z.attributesProcessor,this._meterProviderSharedState.metricCollectors,Z.aggregationCardinalityLimit);return this.metricStorageRegistry.register(Y),Y});if(D.length===0){let G=this._meterProviderSharedState.selectAggregations(A.type).map(([F,I])=>{let Y=this.metricStorageRegistry.findOrUpdateCompatibleCollectorStorage(F,A);if(Y!=null)return Y;let W=I.createAggregator(A),J=F.selectCardinalityLimit(A.type),X=new B(A,W,w36.createNoopAttributesProcessor(),[F],J);return this.metricStorageRegistry.registerForCollector(F,X),X});D=D.concat(G)}return D}}ju2.MeterSharedState=Su2});
var zF0=E((vO2)=>{Object.defineProperty(vO2,"__esModule",{value:!0});vO2.getSignificand=vO2.getNormalBase2=vO2.MIN_VALUE=vO2.MAX_NORMAL_EXPONENT=vO2.MIN_NORMAL_EXPONENT=vO2.SIGNIFICAND_WIDTH=void 0;vO2.SIGNIFICAND_WIDTH=52;var zs4=**********,Es4=1048575,HF0=1023;vO2.MIN_NORMAL_EXPONENT=-HF0+1;vO2.MAX_NORMAL_EXPONENT=HF0;vO2.MIN_VALUE=Math.pow(2,-1022);function Us4(A){let B=new DataView(new ArrayBuffer(8));return B.setFloat64(0,A),((B.getUint32(0)&zs4)>>20)-HF0}vO2.getNormalBase2=Us4;function ws4(A){let B=new DataView(new ArrayBuffer(8));B.setFloat64(0,A);let Q=B.getUint32(0),D=B.getUint32(4);return(Q&Es4)*Math.pow(2,32)+D}vO2.getSignificand=ws4});
var zR2=E((KR2)=>{Object.defineProperty(KR2,"__esModule",{value:!0});KR2.NoopTextMapPropagator=void 0;class CR2{inject(A,B){}extract(A,B){return A}fields(){return[]}}KR2.NoopTextMapPropagator=CR2});
var zb2=E((Kb2)=>{Object.defineProperty(Kb2,"__esModule",{value:!0});Kb2.W3CTraceContextPropagator=Kb2.parseTraceParent=Kb2.TRACE_STATE_HEADER=Kb2.TRACE_PARENT_HEADER=void 0;var RO1=ZQ(),d46=c51(),c46=yF0();Kb2.TRACE_PARENT_HEADER="traceparent";Kb2.TRACE_STATE_HEADER="tracestate";var l46="00",p46="(?!ff)[\\da-f]{2}",i46="(?![0]{32})[\\da-f]{32}",n46="(?![0]{16})[\\da-f]{16}",a46="[\\da-f]{2}",s46=new RegExp(`^\\s?(${p46})-(${i46})-(${n46})-(${a46})(-.*)?\\s?$`);function Vb2(A){let B=s46.exec(A);if(!B)return null;if(B[1]==="00"&&B[5])return null;return{traceId:B[2],spanId:B[3],traceFlags:parseInt(B[4],16)}}Kb2.parseTraceParent=Vb2;class Cb2{inject(A,B,Q){let D=RO1.trace.getSpanContext(A);if(!D||d46.isTracingSuppressed(A)||!RO1.isSpanContextValid(D))return;let Z=`${l46}-${D.traceId}-${D.spanId}-0${Number(D.traceFlags||RO1.TraceFlags.NONE).toString(16)}`;if(Q.set(B,Kb2.TRACE_PARENT_HEADER,Z),D.traceState)Q.set(B,Kb2.TRACE_STATE_HEADER,D.traceState.serialize())}extract(A,B,Q){let D=Q.get(B,Kb2.TRACE_PARENT_HEADER);if(!D)return A;let Z=Array.isArray(D)?D[0]:D;if(typeof Z!=="string")return A;let G=Vb2(Z);if(!G)return A;G.isRemote=!0;let F=Q.get(B,Kb2.TRACE_STATE_HEADER);if(F){let I=Array.isArray(F)?F.join(","):F;G.traceState=new c46.TraceState(typeof I==="string"?I:void 0)}return RO1.trace.setSpanContext(A,G)}fields(){return[Kb2.TRACE_PARENT_HEADER,Kb2.TRACE_STATE_HEADER]}}Kb2.W3CTraceContextPropagator=Cb2});
var zf2=E((Kf2)=>{Object.defineProperty(Kf2,"__esModule",{value:!0});Kf2.SumAggregator=Kf2.SumAccumulation=void 0;var u66=To(),m66=__();class Eu{startTime;monotonic;_current;reset;constructor(A,B,Q=0,D=!1){this.startTime=A,this.monotonic=B,this._current=Q,this.reset=D}record(A){if(this.monotonic&&A<0)return;this._current+=A}setStartTime(A){this.startTime=A}toPointValue(){return this._current}}Kf2.SumAccumulation=Eu;class Cf2{monotonic;kind=u66.AggregatorKind.SUM;constructor(A){this.monotonic=A}createAccumulation(A){return new Eu(A,this.monotonic)}merge(A,B){let Q=A.toPointValue(),D=B.toPointValue();if(B.reset)return new Eu(B.startTime,this.monotonic,D,B.reset);return new Eu(A.startTime,this.monotonic,Q+D)}diff(A,B){let Q=A.toPointValue(),D=B.toPointValue();if(this.monotonic&&Q>D)return new Eu(B.startTime,this.monotonic,D,!0);return new Eu(B.startTime,this.monotonic,D-Q)}toMetricData(A,B,Q,D){return{descriptor:A,aggregationTemporality:B,dataPointType:m66.DataPointType.SUM,dataPoints:Q.map(([Z,G])=>{return{attributes:Z,startTime:G.startTime,endTime:D,value:G.toPointValue()}}),isMonotonic:this.monotonic}}}Kf2.SumAggregator=Cf2});

module.exports = ml2;
