// Package extracted with entry point: p5B

var $5B=E((U5B)=>{Object.defineProperty(U5B,"__esModule",{value:!0});U5B.toUtf8=U5B.fromUtf8=void 0;var uS6=(A)=>{let B=[];for(let Q=0,D=A.length;Q<D;Q++){let Z=A.charCodeAt(Q);if(Z<128)B.push(Z);else if(Z<2048)B.push(Z>>6|192,Z&63|128);else if(Q+1<A.length&&(Z&64512)===55296&&(A.charCodeAt(Q+1)&64512)===56320){let G=65536+((Z&1023)<<10)+(A.charCodeAt(++Q)&1023);B.push(G>>18|240,G>>12&63|128,G>>6&63|128,G&63|128)}else B.push(Z>>12|224,Z>>6&63|128,Z&63|128)}return Uint8Array.from(B)};U5B.fromUtf8=uS6;var mS6=(A)=>{let B="";for(let Q=0,D=A.length;Q<D;Q++){let Z=A[Q];if(Z<128)B+=String.fromCharCode(Z);else if(192<=Z&&Z<224){let G=A[++Q];B+=String.fromCharCode((Z&31)<<6|G&63)}else if(240<=Z&&Z<365){let F="%"+[Z,A[++Q],A[++Q],A[++Q]].map((I)=>I.toString(16)).join("%");B+=decodeURIComponent(F)}else B+=String.fromCharCode((Z&15)<<12|(A[++Q]&63)<<6|A[++Q]&63)}return B};U5B.toUtf8=mS6});
var DK0=E((C5B)=>{Object.defineProperty(C5B,"__esModule",{value:!0});C5B.MAX_HASHABLE_LENGTH=C5B.INIT=C5B.KEY=C5B.DIGEST_LENGTH=C5B.BLOCK_SIZE=void 0;C5B.BLOCK_SIZE=64;C5B.DIGEST_LENGTH=32;C5B.KEY=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]);C5B.INIT=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];C5B.MAX_HASHABLE_LENGTH=Math.pow(2,53)-1});
var E5B=E((H5B)=>{Object.defineProperty(H5B,"__esModule",{value:!0});H5B.RawSha256=void 0;var jE=DK0(),gS6=function(){function A(){this.state=Int32Array.from(jE.INIT),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return A.prototype.update=function(B){if(this.finished)throw new Error("Attempted to update an already finished hash.");var Q=0,D=B.byteLength;if(this.bytesHashed+=D,this.bytesHashed*8>jE.MAX_HASHABLE_LENGTH)throw new Error("Cannot hash more than 2^53 - 1 bits");while(D>0)if(this.buffer[this.bufferLength++]=B[Q++],D--,this.bufferLength===jE.BLOCK_SIZE)this.hashBuffer(),this.bufferLength=0},A.prototype.digest=function(){if(!this.finished){var B=this.bytesHashed*8,Q=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),D=this.bufferLength;if(Q.setUint8(this.bufferLength++,128),D%jE.BLOCK_SIZE>=jE.BLOCK_SIZE-8){for(var Z=this.bufferLength;Z<jE.BLOCK_SIZE;Z++)Q.setUint8(Z,0);this.hashBuffer(),this.bufferLength=0}for(var Z=this.bufferLength;Z<jE.BLOCK_SIZE-8;Z++)Q.setUint8(Z,0);Q.setUint32(jE.BLOCK_SIZE-8,Math.floor(B/4294967296),!0),Q.setUint32(jE.BLOCK_SIZE-4,B),this.hashBuffer(),this.finished=!0}var G=new Uint8Array(jE.DIGEST_LENGTH);for(var Z=0;Z<8;Z++)G[Z*4]=this.state[Z]>>>24&255,G[Z*4+1]=this.state[Z]>>>16&255,G[Z*4+2]=this.state[Z]>>>8&255,G[Z*4+3]=this.state[Z]>>>0&255;return G},A.prototype.hashBuffer=function(){var B=this,Q=B.buffer,D=B.state,Z=D[0],G=D[1],F=D[2],I=D[3],Y=D[4],W=D[5],J=D[6],X=D[7];for(var V=0;V<jE.BLOCK_SIZE;V++){if(V<16)this.temp[V]=(Q[V*4]&255)<<24|(Q[V*4+1]&255)<<16|(Q[V*4+2]&255)<<8|Q[V*4+3]&255;else{var C=this.temp[V-2],K=(C>>>17|C<<15)^(C>>>19|C<<13)^C>>>10;C=this.temp[V-15];var H=(C>>>7|C<<25)^(C>>>18|C<<14)^C>>>3;this.temp[V]=(K+this.temp[V-7]|0)+(H+this.temp[V-16]|0)}var z=(((Y>>>6|Y<<26)^(Y>>>11|Y<<21)^(Y>>>25|Y<<7))+(Y&W^~Y&J)|0)+(X+(jE.KEY[V]+this.temp[V]|0)|0)|0,$=((Z>>>2|Z<<30)^(Z>>>13|Z<<19)^(Z>>>22|Z<<10))+(Z&G^Z&F^G&F)|0;X=J,J=W,W=Y,Y=I+z|0,I=F,F=G,G=Z,Z=z+$|0}D[0]+=Z,D[1]+=G,D[2]+=F,D[3]+=I,D[4]+=Y,D[5]+=W,D[6]+=J,D[7]+=X},A}();H5B.RawSha256=gS6});
var L5B=E((q5B)=>{Object.defineProperty(q5B,"__esModule",{value:!0});q5B.toUtf8=q5B.fromUtf8=void 0;function cS6(A){return new TextEncoder().encode(A)}q5B.fromUtf8=cS6;function lS6(A){return new TextDecoder("utf-8").decode(A)}q5B.toUtf8=lS6});
var QK0=E((tn5,nj1)=>{/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */var n8B,a8B,s8B,r8B,o8B,t8B,e8B,A5B,B5B,ij1,BK0,Q5B,D5B,Ie,Z5B,G5B,F5B,I5B,Y5B,W5B,J5B,X5B,V5B;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof nj1==="object"&&typeof tn5==="object")A(Q(B,Q(tn5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(Q,D){Q.__proto__=D}||function(Q,D){for(var Z in D)if(D.hasOwnProperty(Z))Q[Z]=D[Z]};n8B=function(Q,D){B(Q,D);function Z(){this.constructor=Q}Q.prototype=D===null?Object.create(D):(Z.prototype=D.prototype,new Z)},a8B=Object.assign||function(Q){for(var D,Z=1,G=arguments.length;Z<G;Z++){D=arguments[Z];for(var F in D)if(Object.prototype.hasOwnProperty.call(D,F))Q[F]=D[F]}return Q},s8B=function(Q,D){var Z={};for(var G in Q)if(Object.prototype.hasOwnProperty.call(Q,G)&&D.indexOf(G)<0)Z[G]=Q[G];if(Q!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var F=0,G=Object.getOwnPropertySymbols(Q);F<G.length;F++)if(D.indexOf(G[F])<0&&Object.prototype.propertyIsEnumerable.call(Q,G[F]))Z[G[F]]=Q[G[F]]}return Z},r8B=function(Q,D,Z,G){var F=arguments.length,I=F<3?D:G===null?G=Object.getOwnPropertyDescriptor(D,Z):G,Y;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")I=Reflect.decorate(Q,D,Z,G);else for(var W=Q.length-1;W>=0;W--)if(Y=Q[W])I=(F<3?Y(I):F>3?Y(D,Z,I):Y(D,Z))||I;return F>3&&I&&Object.defineProperty(D,Z,I),I},o8B=function(Q,D){return function(Z,G){D(Z,G,Q)}},t8B=function(Q,D){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(Q,D)},e8B=function(Q,D,Z,G){function F(I){return I instanceof Z?I:new Z(function(Y){Y(I)})}return new(Z||(Z=Promise))(function(I,Y){function W(V){try{X(G.next(V))}catch(C){Y(C)}}function J(V){try{X(G.throw(V))}catch(C){Y(C)}}function X(V){V.done?I(V.value):F(V.value).then(W,J)}X((G=G.apply(Q,D||[])).next())})},A5B=function(Q,D){var Z={label:0,sent:function(){if(I[0]&1)throw I[1];return I[1]},trys:[],ops:[]},G,F,I,Y;return Y={next:W(0),throw:W(1),return:W(2)},typeof Symbol==="function"&&(Y[Symbol.iterator]=function(){return this}),Y;function W(X){return function(V){return J([X,V])}}function J(X){if(G)throw new TypeError("Generator is already executing.");while(Z)try{if(G=1,F&&(I=X[0]&2?F.return:X[0]?F.throw||((I=F.return)&&I.call(F),0):F.next)&&!(I=I.call(F,X[1])).done)return I;if(F=0,I)X=[X[0]&2,I.value];switch(X[0]){case 0:case 1:I=X;break;case 4:return Z.label++,{value:X[1],done:!1};case 5:Z.label++,F=X[1],X=[0];continue;case 7:X=Z.ops.pop(),Z.trys.pop();continue;default:if((I=Z.trys,!(I=I.length>0&&I[I.length-1]))&&(X[0]===6||X[0]===2)){Z=0;continue}if(X[0]===3&&(!I||X[1]>I[0]&&X[1]<I[3])){Z.label=X[1];break}if(X[0]===6&&Z.label<I[1]){Z.label=I[1],I=X;break}if(I&&Z.label<I[2]){Z.label=I[2],Z.ops.push(X);break}if(I[2])Z.ops.pop();Z.trys.pop();continue}X=D.call(Q,Z)}catch(V){X=[6,V],F=0}finally{G=I=0}if(X[0]&5)throw X[1];return{value:X[0]?X[1]:void 0,done:!0}}},V5B=function(Q,D,Z,G){if(G===void 0)G=Z;Q[G]=D[Z]},B5B=function(Q,D){for(var Z in Q)if(Z!=="default"&&!D.hasOwnProperty(Z))D[Z]=Q[Z]},ij1=function(Q){var D=typeof Symbol==="function"&&Symbol.iterator,Z=D&&Q[D],G=0;if(Z)return Z.call(Q);if(Q&&typeof Q.length==="number")return{next:function(){if(Q&&G>=Q.length)Q=void 0;return{value:Q&&Q[G++],done:!Q}}};throw new TypeError(D?"Object is not iterable.":"Symbol.iterator is not defined.")},BK0=function(Q,D){var Z=typeof Symbol==="function"&&Q[Symbol.iterator];if(!Z)return Q;var G=Z.call(Q),F,I=[],Y;try{while((D===void 0||D-- >0)&&!(F=G.next()).done)I.push(F.value)}catch(W){Y={error:W}}finally{try{if(F&&!F.done&&(Z=G.return))Z.call(G)}finally{if(Y)throw Y.error}}return I},Q5B=function(){for(var Q=[],D=0;D<arguments.length;D++)Q=Q.concat(BK0(arguments[D]));return Q},D5B=function(){for(var Q=0,D=0,Z=arguments.length;D<Z;D++)Q+=arguments[D].length;for(var G=Array(Q),F=0,D=0;D<Z;D++)for(var I=arguments[D],Y=0,W=I.length;Y<W;Y++,F++)G[F]=I[Y];return G},Ie=function(Q){return this instanceof Ie?(this.v=Q,this):new Ie(Q)},Z5B=function(Q,D,Z){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var G=Z.apply(Q,D||[]),F,I=[];return F={},Y("next"),Y("throw"),Y("return"),F[Symbol.asyncIterator]=function(){return this},F;function Y(K){if(G[K])F[K]=function(H){return new Promise(function(z,$){I.push([K,H,z,$])>1||W(K,H)})}}function W(K,H){try{J(G[K](H))}catch(z){C(I[0][3],z)}}function J(K){K.value instanceof Ie?Promise.resolve(K.value.v).then(X,V):C(I[0][2],K)}function X(K){W("next",K)}function V(K){W("throw",K)}function C(K,H){if(K(H),I.shift(),I.length)W(I[0][0],I[0][1])}},G5B=function(Q){var D,Z;return D={},G("next"),G("throw",function(F){throw F}),G("return"),D[Symbol.iterator]=function(){return this},D;function G(F,I){D[F]=Q[F]?function(Y){return(Z=!Z)?{value:Ie(Q[F](Y)),done:F==="return"}:I?I(Y):Y}:I}},F5B=function(Q){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var D=Q[Symbol.asyncIterator],Z;return D?D.call(Q):(Q=typeof ij1==="function"?ij1(Q):Q[Symbol.iterator](),Z={},G("next"),G("throw"),G("return"),Z[Symbol.asyncIterator]=function(){return this},Z);function G(I){Z[I]=Q[I]&&function(Y){return new Promise(function(W,J){Y=Q[I](Y),F(W,J,Y.done,Y.value)})}}function F(I,Y,W,J){Promise.resolve(J).then(function(X){I({value:X,done:W})},Y)}},I5B=function(Q,D){if(Object.defineProperty)Object.defineProperty(Q,"raw",{value:D});else Q.raw=D;return Q},Y5B=function(Q){if(Q&&Q.__esModule)return Q;var D={};if(Q!=null){for(var Z in Q)if(Object.hasOwnProperty.call(Q,Z))D[Z]=Q[Z]}return D.default=Q,D},W5B=function(Q){return Q&&Q.__esModule?Q:{default:Q}},J5B=function(Q,D){if(!D.has(Q))throw new TypeError("attempted to get private field on non-instance");return D.get(Q)},X5B=function(Q,D,Z){if(!D.has(Q))throw new TypeError("attempted to set private field on non-instance");return D.set(Q,Z),Z},A("__extends",n8B),A("__assign",a8B),A("__rest",s8B),A("__decorate",r8B),A("__param",o8B),A("__metadata",t8B),A("__awaiter",e8B),A("__generator",A5B),A("__exportStar",B5B),A("__createBinding",V5B),A("__values",ij1),A("__read",BK0),A("__spread",Q5B),A("__spreadArrays",D5B),A("__await",Ie),A("__asyncGenerator",Z5B),A("__asyncDelegator",G5B),A("__asyncValues",F5B),A("__makeTemplateObject",I5B),A("__importStar",Y5B),A("__importDefault",W5B),A("__classPrivateFieldGet",J5B),A("__classPrivateFieldSet",X5B)})});
var ZK0=E((O5B)=>{Object.defineProperty(O5B,"__esModule",{value:!0});O5B.toUtf8=O5B.fromUtf8=void 0;var M5B=$5B(),R5B=L5B(),iS6=(A)=>typeof TextEncoder==="function"?R5B.fromUtf8(A):M5B.fromUtf8(A);O5B.fromUtf8=iS6;var nS6=(A)=>typeof TextDecoder==="function"?R5B.toUtf8(A):M5B.toUtf8(A);O5B.toUtf8=nS6});
var _5B=E((k5B)=>{Object.defineProperty(k5B,"__esModule",{value:!0});k5B.isEmptyData=void 0;function tS6(A){if(typeof A==="string")return A.length===0;return A.byteLength===0}k5B.isEmptyData=tS6});
var b5B=E((x5B)=>{Object.defineProperty(x5B,"__esModule",{value:!0});x5B.numToUint8=void 0;function eS6(A){return new Uint8Array([(A&4278190080)>>24,(A&16711680)>>16,(A&65280)>>8,A&255])}x5B.numToUint8=eS6});
var g5B=E((f5B)=>{Object.defineProperty(f5B,"__esModule",{value:!0});f5B.uint32ArrayFrom=void 0;function Aj6(A){if(!Uint32Array.from){var B=new Uint32Array(A.length),Q=0;while(Q<A.length)B[Q]=A[Q],Q+=1;return B}return Uint32Array.from(A)}f5B.uint32ArrayFrom=Aj6});
var j5B=E((P5B)=>{Object.defineProperty(P5B,"__esModule",{value:!0});P5B.convertToBuffer=void 0;var sS6=ZK0(),rS6=typeof Buffer!=="undefined"&&Buffer.from?function(A){return Buffer.from(A,"utf8")}:sS6.fromUtf8;function oS6(A){if(A instanceof Uint8Array)return A;if(typeof A==="string")return rS6(A);if(ArrayBuffer.isView(A))return new Uint8Array(A.buffer,A.byteOffset,A.byteLength/Uint8Array.BYTES_PER_ELEMENT);return new Uint8Array(A)}P5B.convertToBuffer=oS6});
var l5B=E((d5B)=>{Object.defineProperty(d5B,"__esModule",{value:!0});d5B.Sha256=void 0;var m5B=QK0(),sj1=DK0(),aj1=E5B(),GK0=u5B(),Fj6=function(){function A(B){this.secret=B,this.hash=new aj1.RawSha256,this.reset()}return A.prototype.update=function(B){if(GK0.isEmptyData(B)||this.error)return;try{this.hash.update(GK0.convertToBuffer(B))}catch(Q){this.error=Q}},A.prototype.digestSync=function(){if(this.error)throw this.error;if(this.outer){if(!this.outer.finished)this.outer.update(this.hash.digest());return this.outer.digest()}return this.hash.digest()},A.prototype.digest=function(){return m5B.__awaiter(this,void 0,void 0,function(){return m5B.__generator(this,function(B){return[2,this.digestSync()]})})},A.prototype.reset=function(){if(this.hash=new aj1.RawSha256,this.secret){this.outer=new aj1.RawSha256;var B=Ij6(this.secret),Q=new Uint8Array(sj1.BLOCK_SIZE);Q.set(B);for(var D=0;D<sj1.BLOCK_SIZE;D++)B[D]^=54,Q[D]^=92;this.hash.update(B),this.outer.update(Q);for(var D=0;D<B.byteLength;D++)B[D]=0}},A}();d5B.Sha256=Fj6;function Ij6(A){var B=GK0.convertToBuffer(A);if(B.byteLength>sj1.BLOCK_SIZE){var Q=new aj1.RawSha256;Q.update(B),B=Q.digest()}var D=new Uint8Array(sj1.BLOCK_SIZE);return D.set(B),D}});
var p5B=E((FK0)=>{Object.defineProperty(FK0,"__esModule",{value:!0});var Yj6=QK0();Yj6.__exportStar(l5B(),FK0)});
var u5B=E((Ye)=>{Object.defineProperty(Ye,"__esModule",{value:!0});Ye.uint32ArrayFrom=Ye.numToUint8=Ye.isEmptyData=Ye.convertToBuffer=void 0;var Bj6=j5B();Object.defineProperty(Ye,"convertToBuffer",{enumerable:!0,get:function(){return Bj6.convertToBuffer}});var Qj6=_5B();Object.defineProperty(Ye,"isEmptyData",{enumerable:!0,get:function(){return Qj6.isEmptyData}});var Dj6=b5B();Object.defineProperty(Ye,"numToUint8",{enumerable:!0,get:function(){return Dj6.numToUint8}});var Zj6=g5B();Object.defineProperty(Ye,"uint32ArrayFrom",{enumerable:!0,get:function(){return Zj6.uint32ArrayFrom}})});

module.exports = p5B;
