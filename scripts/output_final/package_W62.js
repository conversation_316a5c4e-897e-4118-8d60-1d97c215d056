// Package extracted with entry point: W62

var $oA=E((pJ5,woA)=>{var{defineProperty:Cq1,getOwnPropertyDescriptor:E94,getOwnPropertyNames:U94}=Object,w94=Object.prototype.hasOwnProperty,HQ0=(A,B)=>Cq1(A,"name",{value:B,configurable:!0}),$94=(A,B)=>{for(var Q in B)Cq1(A,Q,{get:B[Q],enumerable:!0})},q94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of U94(B))if(!w94.call(A,Z)&&Z!==Q)Cq1(A,Z,{get:()=>B[Z],enumerable:!(D=E94(B,Z))||D.enumerable})}return A},N94=(A)=>q94(Cq1({},"__esModule",{value:!0}),A),EoA={};$94(EoA,{escapeUri:()=>UoA,escapeUriPath:()=>M94});woA.exports=N94(EoA);var UoA=HQ0((A)=>encodeURIComponent(A).replace(/[!'()*]/g,L94),"escapeUri"),L94=HQ0((A)=>`%${A.charCodeAt(0).toString(16).toUpperCase()}`,"hexEncode"),M94=HQ0((A)=>A.split("/").map(UoA).join("/"),"escapeUriPath")});
var AeA=E((PX5,I64)=>{I64.exports={name:"@aws-sdk/client-bedrock",description:"AWS SDK for JavaScript Bedrock Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-bedrock","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo bedrock"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/credential-provider-node":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/token-providers":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0","@types/uuid":"^9.0.1",tslib:"^2.6.2",uuid:"^9.0.1"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-bedrock",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-bedrock"}}});
var B22=E((eA2)=>{Object.defineProperty(eA2,"__esModule",{value:!0});eA2.getRuntimeConfig=void 0;var _74=Hg(),x74=_74.__importDefault(C40()),j40=YI(),rA2=z61(),iq1=V4(),v74=VB(),b74=jG(),oA2=v4(),Ng=QD(),tA2=S3(),f74=kG(),h74=hZ(),g74=sA2(),u74=J6(),m74=yG(),d74=J6(),c74=(A)=>{d74.emitWarningIfUnsupportedVersion(process.version);let B=m74.resolveDefaultsModeConfig(A),Q=()=>B().then(u74.loadConfigsForDefaultMode),D=g74.getRuntimeConfig(A);j40.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Ng.loadConfig(j40.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??f74.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??rA2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:x74.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4")||(async(F)=>await A.credentialDefaultProvider(F?.__config||{})()),signer:new j40.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new v74.NoAuthSigner}],maxAttempts:A?.maxAttempts??Ng.loadConfig(oA2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Ng.loadConfig(iq1.NODE_REGION_CONFIG_OPTIONS,{...iq1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:tA2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Ng.loadConfig({...oA2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||h74.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??b74.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??tA2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Ng.loadConfig(iq1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Ng.loadConfig(iq1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Ng.loadConfig(rA2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};eA2.getRuntimeConfig=c74});
var BB2=E((e22)=>{Object.defineProperty(e22,"__esModule",{value:!0});e22.fromTokenFile=void 0;var wZ4=Tw(),$Z4=eB(),qZ4=J1("fs"),NZ4=C60(),t22="AWS_WEB_IDENTITY_TOKEN_FILE",LZ4="AWS_ROLE_ARN",MZ4="AWS_ROLE_SESSION_NAME",RZ4=(A={})=>async()=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let B=A?.webIdentityTokenFile??process.env[t22],Q=A?.roleArn??process.env[LZ4],D=A?.roleSessionName??process.env[MZ4];if(!B||!Q)throw new $Z4.CredentialsProviderError("Web identity configuration not specified",{logger:A.logger});let Z=await NZ4.fromWebToken({...A,webIdentityToken:qZ4.readFileSync(B,{encoding:"ascii"}),roleArn:Q,roleSessionName:D})();if(B===process.env[t22])wZ4.setCredentialFeature(Z,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h");return Z};e22.fromTokenFile=RZ4});
var Bs=E((OX5,otA)=>{var{defineProperty:Tq1,getOwnPropertyDescriptor:g44,getOwnPropertyNames:u44}=Object,m44=Object.prototype.hasOwnProperty,NT=(A,B)=>Tq1(A,"name",{value:B,configurable:!0}),d44=(A,B)=>{for(var Q in B)Tq1(A,Q,{get:B[Q],enumerable:!0})},c44=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of u44(B))if(!m44.call(A,Z)&&Z!==Q)Tq1(A,Z,{get:()=>B[Z],enumerable:!(D=g44(B,Z))||D.enumerable})}return A},l44=(A)=>c44(Tq1({},"__esModule",{value:!0}),A),ctA={};d44(ctA,{DEFAULT_UA_APP_ID:()=>ltA,getUserAgentMiddlewareOptions:()=>rtA,getUserAgentPlugin:()=>t44,resolveUserAgentConfig:()=>itA,userAgentMiddleware:()=>stA});otA.exports=l44(ctA);var p44=VB(),ltA=void 0;function ptA(A){if(A===void 0)return!0;return typeof A==="string"&&A.length<=50}NT(ptA,"isValidUserAgentAppId");function itA(A){let B=p44.normalizeProvider(A.userAgentAppId??ltA),{customUserAgent:Q}=A;return Object.assign(A,{customUserAgent:typeof Q==="string"?[[Q]]:Q,userAgentAppId:NT(async()=>{let D=await B();if(!ptA(D)){let Z=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console:A.logger;if(typeof D!=="string")Z?.warn("userAgentAppId must be a string or undefined.");else if(D.length>50)Z?.warn("The provided userAgentAppId exceeds the maximum length of 50 characters.")}return D},"userAgentAppId")})}NT(itA,"resolveUserAgentConfig");var i44=sa(),n44=CV(),YL=YI(),a44=/\d{12}\.ddb/;async function ntA(A,B,Q){if(Q.request?.headers?.["smithy-protocol"]==="rpc-v2-cbor")YL.setFeature(A,"PROTOCOL_RPC_V2_CBOR","M");if(typeof B.retryStrategy==="function"){let G=await B.retryStrategy();if(typeof G.acquireInitialRetryToken==="function")if(G.constructor?.name?.includes("Adaptive"))YL.setFeature(A,"RETRY_MODE_ADAPTIVE","F");else YL.setFeature(A,"RETRY_MODE_STANDARD","E");else YL.setFeature(A,"RETRY_MODE_LEGACY","D")}if(typeof B.accountIdEndpointMode==="function"){let G=A.endpointV2;if(String(G?.url?.hostname).match(a44))YL.setFeature(A,"ACCOUNT_ID_ENDPOINT","O");switch(await B.accountIdEndpointMode?.()){case"disabled":YL.setFeature(A,"ACCOUNT_ID_MODE_DISABLED","Q");break;case"preferred":YL.setFeature(A,"ACCOUNT_ID_MODE_PREFERRED","P");break;case"required":YL.setFeature(A,"ACCOUNT_ID_MODE_REQUIRED","R");break}}let Z=A.__smithy_context?.selectedHttpAuthScheme?.identity;if(Z?.$source){let G=Z;if(G.accountId)YL.setFeature(A,"RESOLVED_ACCOUNT_ID","T");for(let[F,I]of Object.entries(G.$source??{}))YL.setFeature(A,F,I)}}NT(ntA,"checkFeatures");var utA="user-agent",aQ0="x-amz-user-agent",mtA=" ",sQ0="/",s44=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g,r44=/[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g,dtA="-",o44=1024;function atA(A){let B="";for(let Q in A){let D=A[Q];if(B.length+D.length+1<=o44){if(B.length)B+=","+D;else B+=D;continue}break}return B}NT(atA,"encodeFeatures");var stA=NT((A)=>(B,Q)=>async(D)=>{let{request:Z}=D;if(!n44.HttpRequest.isInstance(Z))return B(D);let{headers:G}=Z,F=Q?.userAgent?.map(Oq1)||[],I=(await A.defaultUserAgentProvider()).map(Oq1);await ntA(Q,A,D);let Y=Q;I.push(`m/${atA(Object.assign({},Q.__smithy_context?.features,Y.__aws_sdk_context?.features))}`);let W=A?.customUserAgent?.map(Oq1)||[],J=await A.userAgentAppId();if(J)I.push(Oq1([`app/${J}`]));let X=i44.getUserAgentPrefix(),V=(X?[X]:[]).concat([...I,...F,...W]).join(mtA),C=[...I.filter((K)=>K.startsWith("aws-sdk-")),...W].join(mtA);if(A.runtime!=="browser"){if(C)G[aQ0]=G[aQ0]?`${G[utA]} ${C}`:C;G[utA]=V}else G[aQ0]=V;return B({...D,request:Z})},"userAgentMiddleware"),Oq1=NT((A)=>{let B=A[0].split(sQ0).map((F)=>F.replace(s44,dtA)).join(sQ0),Q=A[1]?.replace(r44,dtA),D=B.indexOf(sQ0),Z=B.substring(0,D),G=B.substring(D+1);if(Z==="api")G=G.toLowerCase();return[Z,G,Q].filter((F)=>F&&F.length>0).reduce((F,I,Y)=>{switch(Y){case 0:return I;case 1:return`${F}/${I}`;default:return`${F}#${I}`}},"")},"escapeUserAgent"),rtA={name:"getUserAgentMiddleware",step:"build",priority:"low",tags:["SET_USER_AGENT","USER_AGENT"],override:!0},t44=NT((A)=>({applyToStack:NT((B)=>{B.add(stA(A),rtA)},"applyToStack")}),"getUserAgentPlugin")});
var C40=E((oX5,j54)=>{j54.exports={name:"@aws-sdk/nested-clients",version:"3.840.0",description:"Nested clients for AWS SDK packages.",main:"./dist-cjs/index.js",module:"./dist-es/index.js",types:"./dist-types/index.d.ts",scripts:{build:"yarn lint && concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline nested-clients","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo",lint:"node ../../scripts/validation/submodules-linter.js --pkg nested-clients",test:"yarn g:vitest run","test:watch":"yarn g:vitest watch"},engines:{node:">=18.0.0"},author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["./sso-oidc.d.ts","./sso-oidc.js","./sts.d.ts","./sts.js","dist-*/**"],browser:{"./dist-es/submodules/sso-oidc/runtimeConfig":"./dist-es/submodules/sso-oidc/runtimeConfig.browser","./dist-es/submodules/sts/runtimeConfig":"./dist-es/submodules/sts/runtimeConfig.browser"},"react-native":{},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/packages/nested-clients",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"packages/nested-clients"},exports:{"./sso-oidc":{types:"./dist-types/submodules/sso-oidc/index.d.ts",module:"./dist-es/submodules/sso-oidc/index.js",node:"./dist-cjs/submodules/sso-oidc/index.js",import:"./dist-es/submodules/sso-oidc/index.js",require:"./dist-cjs/submodules/sso-oidc/index.js"},"./sts":{types:"./dist-types/submodules/sts/index.d.ts",module:"./dist-es/submodules/sts/index.js",node:"./dist-cjs/submodules/sts/index.js",import:"./dist-es/submodules/sts/index.js",require:"./dist-cjs/submodules/sts/index.js"}}}});
var C60=E((XL)=>{var HZ4=XL&&XL.__createBinding||(Object.create?function(A,B,Q,D){if(D===void 0)D=Q;var Z=Object.getOwnPropertyDescriptor(B,Q);if(!Z||("get"in Z?!B.__esModule:Z.writable||Z.configurable))Z={enumerable:!0,get:function(){return B[Q]}};Object.defineProperty(A,D,Z)}:function(A,B,Q,D){if(D===void 0)D=Q;A[D]=B[Q]}),zZ4=XL&&XL.__setModuleDefault||(Object.create?function(A,B){Object.defineProperty(A,"default",{enumerable:!0,value:B})}:function(A,B){A.default=B}),EZ4=XL&&XL.__importStar||function(){var A=function(B){return A=Object.getOwnPropertyNames||function(Q){var D=[];for(var Z in Q)if(Object.prototype.hasOwnProperty.call(Q,Z))D[D.length]=Z;return D},A(B)};return function(B){if(B&&B.__esModule)return B;var Q={};if(B!=null){for(var D=A(B),Z=0;Z<D.length;Z++)if(D[Z]!=="default")HZ4(Q,B,D[Z])}return zZ4(Q,B),Q}}();Object.defineProperty(XL,"__esModule",{value:!0});XL.fromWebToken=void 0;var UZ4=(A)=>async(B)=>{A.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:Q,roleSessionName:D,webIdentityToken:Z,providerId:G,policyArns:F,policy:I,durationSeconds:Y}=A,{roleAssumerWithWebIdentity:W}=A;if(!W){let{getDefaultRoleAssumerWithWebIdentity:J}=await Promise.resolve().then(()=>EZ4(W60()));W=J({...A.clientConfig,credentialProviderLogger:A.logger,parentClientConfig:{...B?.callerClientConfig,...A.parentClientConfig}},A.clientPlugins)}return W({RoleArn:Q,RoleSessionName:D??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:Z,ProviderId:G,PolicyArns:F,Policy:I,DurationSeconds:Y})};XL.fromWebToken=UZ4});
var CV=E((xJ5,XrA)=>{var{defineProperty:Aq1,getOwnPropertyDescriptor:WB4,getOwnPropertyNames:JB4}=Object,XB4=Object.prototype.hasOwnProperty,uy=(A,B)=>Aq1(A,"name",{value:B,configurable:!0}),VB4=(A,B)=>{for(var Q in B)Aq1(A,Q,{get:B[Q],enumerable:!0})},CB4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of JB4(B))if(!XB4.call(A,Z)&&Z!==Q)Aq1(A,Z,{get:()=>B[Z],enumerable:!(D=WB4(B,Z))||D.enumerable})}return A},KB4=(A)=>CB4(Aq1({},"__esModule",{value:!0}),A),IrA={};VB4(IrA,{Field:()=>EB4,Fields:()=>UB4,HttpRequest:()=>wB4,HttpResponse:()=>$B4,IHttpRequest:()=>YrA.HttpRequest,getHttpHandlerExtensionConfiguration:()=>HB4,isValidHostname:()=>JrA,resolveHttpHandlerRuntimeConfig:()=>zB4});XrA.exports=KB4(IrA);var HB4=uy((A)=>{return{setHttpHandler(B){A.httpHandler=B},httpHandler(){return A.httpHandler},updateHttpClientConfig(B,Q){A.httpHandler?.updateHttpClientConfig(B,Q)},httpHandlerConfigs(){return A.httpHandler.httpHandlerConfigs()}}},"getHttpHandlerExtensionConfiguration"),zB4=uy((A)=>{return{httpHandler:A.httpHandler()}},"resolveHttpHandlerRuntimeConfig"),YrA=JQ0(),EB4=class{static{uy(this,"Field")}constructor({name:A,kind:B=YrA.FieldPosition.HEADER,values:Q=[]}){this.name=A,this.kind=B,this.values=Q}add(A){this.values.push(A)}set(A){this.values=A}remove(A){this.values=this.values.filter((B)=>B!==A)}toString(){return this.values.map((A)=>A.includes(",")||A.includes(" ")?`"${A}"`:A).join(", ")}get(){return this.values}},UB4=class{constructor({fields:A=[],encoding:B="utf-8"}){this.entries={},A.forEach(this.setField.bind(this)),this.encoding=B}static{uy(this,"Fields")}setField(A){this.entries[A.name.toLowerCase()]=A}getField(A){return this.entries[A.toLowerCase()]}removeField(A){delete this.entries[A.toLowerCase()]}getByType(A){return Object.values(this.entries).filter((B)=>B.kind===A)}},wB4=class A{static{uy(this,"HttpRequest")}constructor(B){this.method=B.method||"GET",this.hostname=B.hostname||"localhost",this.port=B.port,this.query=B.query||{},this.headers=B.headers||{},this.body=B.body,this.protocol=B.protocol?B.protocol.slice(-1)!==":"?`${B.protocol}:`:B.protocol:"https:",this.path=B.path?B.path.charAt(0)!=="/"?`/${B.path}`:B.path:"/",this.username=B.username,this.password=B.password,this.fragment=B.fragment}static clone(B){let Q=new A({...B,headers:{...B.headers}});if(Q.query)Q.query=WrA(Q.query);return Q}static isInstance(B){if(!B)return!1;let Q=B;return"method"in Q&&"protocol"in Q&&"hostname"in Q&&"path"in Q&&typeof Q.query==="object"&&typeof Q.headers==="object"}clone(){return A.clone(this)}};function WrA(A){return Object.keys(A).reduce((B,Q)=>{let D=A[Q];return{...B,[Q]:Array.isArray(D)?[...D]:D}},{})}uy(WrA,"cloneQuery");var $B4=class{static{uy(this,"HttpResponse")}constructor(A){this.statusCode=A.statusCode,this.reason=A.reason,this.headers=A.headers||{},this.body=A.body}static isInstance(A){if(!A)return!1;let B=A;return typeof B.statusCode==="number"&&typeof B.headers==="object"}};function JrA(A){return/^[a-z0-9][a-z0-9\.\-]*[a-z0-9]$/.test(A)}uy(JrA,"isValidHostname")});
var CeA=E((VeA)=>{Object.defineProperty(VeA,"__esModule",{value:!0});VeA.createGetRequest=O64;VeA.getCredentials=T64;var A40=eB(),L64=CV(),M64=J6(),R64=Fy();function O64(A){return new L64.HttpRequest({protocol:A.protocol,hostname:A.hostname,port:Number(A.port),path:A.pathname,query:Array.from(A.searchParams.entries()).reduce((B,[Q,D])=>{return B[Q]=D,B},{}),fragment:A.hash})}async function T64(A,B){let D=await R64.sdkStreamMixin(A.body).transformToString();if(A.statusCode===200){let Z=JSON.parse(D);if(typeof Z.AccessKeyId!=="string"||typeof Z.SecretAccessKey!=="string"||typeof Z.Token!=="string"||typeof Z.Expiration!=="string")throw new A40.CredentialsProviderError("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:B});return{accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.Token,expiration:M64.parseRfc3339DateTime(Z.Expiration)}}if(A.statusCode>=400&&A.statusCode<500){let Z={};try{Z=JSON.parse(D)}catch(G){}throw Object.assign(new A40.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B}),{Code:Z.Code,Message:Z.Message})}throw new A40.CredentialsProviderError(`Server responded with status: ${A.statusCode}`,{logger:B})}});
var CtA=E((XtA)=>{Object.defineProperty(XtA,"__esModule",{value:!0});XtA.toBase64=void 0;var KQ4=AD(),HQ4=cB(),zQ4=(A)=>{let B;if(typeof A==="string")B=HQ4.fromUtf8(A);else B=A;if(typeof B!=="object"||typeof B.byteOffset!=="number"||typeof B.byteLength!=="number")throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");return KQ4.fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength).toString("base64")};XtA.toBase64=zQ4});
var Hg=E((dJ5,Wq1)=>{var vrA,brA,frA,hrA,grA,urA,mrA,drA,crA,lrA,prA,irA,nrA,Iq1,CQ0,arA,srA,rrA,ra,orA,trA,erA,AoA,BoA,QoA,DoA,ZoA,GoA,Yq1,FoA,IoA,YoA;(function(A){var B=typeof global==="object"?global:typeof self==="object"?self:typeof this==="object"?this:{};if(typeof define==="function"&&define.amd)define("tslib",["exports"],function(D){A(Q(B,Q(D)))});else if(typeof Wq1==="object"&&typeof dJ5==="object")A(Q(B,Q(dJ5)));else A(Q(B));function Q(D,Z){if(D!==B)if(typeof Object.create==="function")Object.defineProperty(D,"__esModule",{value:!0});else D.__esModule=!0;return function(G,F){return D[G]=Z?Z(G,F):F}}})(function(A){var B=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(G,F){G.__proto__=F}||function(G,F){for(var I in F)if(Object.prototype.hasOwnProperty.call(F,I))G[I]=F[I]};vrA=function(G,F){if(typeof F!=="function"&&F!==null)throw new TypeError("Class extends value "+String(F)+" is not a constructor or null");B(G,F);function I(){this.constructor=G}G.prototype=F===null?Object.create(F):(I.prototype=F.prototype,new I)},brA=Object.assign||function(G){for(var F,I=1,Y=arguments.length;I<Y;I++){F=arguments[I];for(var W in F)if(Object.prototype.hasOwnProperty.call(F,W))G[W]=F[W]}return G},frA=function(G,F){var I={};for(var Y in G)if(Object.prototype.hasOwnProperty.call(G,Y)&&F.indexOf(Y)<0)I[Y]=G[Y];if(G!=null&&typeof Object.getOwnPropertySymbols==="function"){for(var W=0,Y=Object.getOwnPropertySymbols(G);W<Y.length;W++)if(F.indexOf(Y[W])<0&&Object.prototype.propertyIsEnumerable.call(G,Y[W]))I[Y[W]]=G[Y[W]]}return I},hrA=function(G,F,I,Y){var W=arguments.length,J=W<3?F:Y===null?Y=Object.getOwnPropertyDescriptor(F,I):Y,X;if(typeof Reflect==="object"&&typeof Reflect.decorate==="function")J=Reflect.decorate(G,F,I,Y);else for(var V=G.length-1;V>=0;V--)if(X=G[V])J=(W<3?X(J):W>3?X(F,I,J):X(F,I))||J;return W>3&&J&&Object.defineProperty(F,I,J),J},grA=function(G,F){return function(I,Y){F(I,Y,G)}},urA=function(G,F,I,Y,W,J){function X(T){if(T!==void 0&&typeof T!=="function")throw new TypeError("Function expected");return T}var V=Y.kind,C=V==="getter"?"get":V==="setter"?"set":"value",K=!F&&G?Y.static?G:G.prototype:null,H=F||(K?Object.getOwnPropertyDescriptor(K,Y.name):{}),z,$=!1;for(var L=I.length-1;L>=0;L--){var N={};for(var O in Y)N[O]=O==="access"?{}:Y[O];for(var O in Y.access)N.access[O]=Y.access[O];N.addInitializer=function(T){if($)throw new TypeError("Cannot add initializers after decoration has completed");J.push(X(T||null))};var R=I[L](V==="accessor"?{get:H.get,set:H.set}:H[C],N);if(V==="accessor"){if(R===void 0)continue;if(R===null||typeof R!=="object")throw new TypeError("Object expected");if(z=X(R.get))H.get=z;if(z=X(R.set))H.set=z;if(z=X(R.init))W.unshift(z)}else if(z=X(R))if(V==="field")W.unshift(z);else H[C]=z}if(K)Object.defineProperty(K,Y.name,H);$=!0},mrA=function(G,F,I){var Y=arguments.length>2;for(var W=0;W<F.length;W++)I=Y?F[W].call(G,I):F[W].call(G);return Y?I:void 0},drA=function(G){return typeof G==="symbol"?G:"".concat(G)},crA=function(G,F,I){if(typeof F==="symbol")F=F.description?"[".concat(F.description,"]"):"";return Object.defineProperty(G,"name",{configurable:!0,value:I?"".concat(I," ",F):F})},lrA=function(G,F){if(typeof Reflect==="object"&&typeof Reflect.metadata==="function")return Reflect.metadata(G,F)},prA=function(G,F,I,Y){function W(J){return J instanceof I?J:new I(function(X){X(J)})}return new(I||(I=Promise))(function(J,X){function V(H){try{K(Y.next(H))}catch(z){X(z)}}function C(H){try{K(Y.throw(H))}catch(z){X(z)}}function K(H){H.done?J(H.value):W(H.value).then(V,C)}K((Y=Y.apply(G,F||[])).next())})},irA=function(G,F){var I={label:0,sent:function(){if(J[0]&1)throw J[1];return J[1]},trys:[],ops:[]},Y,W,J,X=Object.create((typeof Iterator==="function"?Iterator:Object).prototype);return X.next=V(0),X.throw=V(1),X.return=V(2),typeof Symbol==="function"&&(X[Symbol.iterator]=function(){return this}),X;function V(K){return function(H){return C([K,H])}}function C(K){if(Y)throw new TypeError("Generator is already executing.");while(X&&(X=0,K[0]&&(I=0)),I)try{if(Y=1,W&&(J=K[0]&2?W.return:K[0]?W.throw||((J=W.return)&&J.call(W),0):W.next)&&!(J=J.call(W,K[1])).done)return J;if(W=0,J)K=[K[0]&2,J.value];switch(K[0]){case 0:case 1:J=K;break;case 4:return I.label++,{value:K[1],done:!1};case 5:I.label++,W=K[1],K=[0];continue;case 7:K=I.ops.pop(),I.trys.pop();continue;default:if((J=I.trys,!(J=J.length>0&&J[J.length-1]))&&(K[0]===6||K[0]===2)){I=0;continue}if(K[0]===3&&(!J||K[1]>J[0]&&K[1]<J[3])){I.label=K[1];break}if(K[0]===6&&I.label<J[1]){I.label=J[1],J=K;break}if(J&&I.label<J[2]){I.label=J[2],I.ops.push(K);break}if(J[2])I.ops.pop();I.trys.pop();continue}K=F.call(G,I)}catch(H){K=[6,H],W=0}finally{Y=J=0}if(K[0]&5)throw K[1];return{value:K[0]?K[1]:void 0,done:!0}}},nrA=function(G,F){for(var I in G)if(I!=="default"&&!Object.prototype.hasOwnProperty.call(F,I))Yq1(F,G,I)},Yq1=Object.create?function(G,F,I,Y){if(Y===void 0)Y=I;var W=Object.getOwnPropertyDescriptor(F,I);if(!W||("get"in W?!F.__esModule:W.writable||W.configurable))W={enumerable:!0,get:function(){return F[I]}};Object.defineProperty(G,Y,W)}:function(G,F,I,Y){if(Y===void 0)Y=I;G[Y]=F[I]},Iq1=function(G){var F=typeof Symbol==="function"&&Symbol.iterator,I=F&&G[F],Y=0;if(I)return I.call(G);if(G&&typeof G.length==="number")return{next:function(){if(G&&Y>=G.length)G=void 0;return{value:G&&G[Y++],done:!G}}};throw new TypeError(F?"Object is not iterable.":"Symbol.iterator is not defined.")},CQ0=function(G,F){var I=typeof Symbol==="function"&&G[Symbol.iterator];if(!I)return G;var Y=I.call(G),W,J=[],X;try{while((F===void 0||F-- >0)&&!(W=Y.next()).done)J.push(W.value)}catch(V){X={error:V}}finally{try{if(W&&!W.done&&(I=Y.return))I.call(Y)}finally{if(X)throw X.error}}return J},arA=function(){for(var G=[],F=0;F<arguments.length;F++)G=G.concat(CQ0(arguments[F]));return G},srA=function(){for(var G=0,F=0,I=arguments.length;F<I;F++)G+=arguments[F].length;for(var Y=Array(G),W=0,F=0;F<I;F++)for(var J=arguments[F],X=0,V=J.length;X<V;X++,W++)Y[W]=J[X];return Y},rrA=function(G,F,I){if(I||arguments.length===2){for(var Y=0,W=F.length,J;Y<W;Y++)if(J||!(Y in F)){if(!J)J=Array.prototype.slice.call(F,0,Y);J[Y]=F[Y]}}return G.concat(J||Array.prototype.slice.call(F))},ra=function(G){return this instanceof ra?(this.v=G,this):new ra(G)},orA=function(G,F,I){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Y=I.apply(G,F||[]),W,J=[];return W=Object.create((typeof AsyncIterator==="function"?AsyncIterator:Object).prototype),V("next"),V("throw"),V("return",X),W[Symbol.asyncIterator]=function(){return this},W;function X(L){return function(N){return Promise.resolve(N).then(L,z)}}function V(L,N){if(Y[L]){if(W[L]=function(O){return new Promise(function(R,T){J.push([L,O,R,T])>1||C(L,O)})},N)W[L]=N(W[L])}}function C(L,N){try{K(Y[L](N))}catch(O){$(J[0][3],O)}}function K(L){L.value instanceof ra?Promise.resolve(L.value.v).then(H,z):$(J[0][2],L)}function H(L){C("next",L)}function z(L){C("throw",L)}function $(L,N){if(L(N),J.shift(),J.length)C(J[0][0],J[0][1])}},trA=function(G){var F,I;return F={},Y("next"),Y("throw",function(W){throw W}),Y("return"),F[Symbol.iterator]=function(){return this},F;function Y(W,J){F[W]=G[W]?function(X){return(I=!I)?{value:ra(G[W](X)),done:!1}:J?J(X):X}:J}},erA=function(G){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var F=G[Symbol.asyncIterator],I;return F?F.call(G):(G=typeof Iq1==="function"?Iq1(G):G[Symbol.iterator](),I={},Y("next"),Y("throw"),Y("return"),I[Symbol.asyncIterator]=function(){return this},I);function Y(J){I[J]=G[J]&&function(X){return new Promise(function(V,C){X=G[J](X),W(V,C,X.done,X.value)})}}function W(J,X,V,C){Promise.resolve(C).then(function(K){J({value:K,done:V})},X)}},AoA=function(G,F){if(Object.defineProperty)Object.defineProperty(G,"raw",{value:F});else G.raw=F;return G};var Q=Object.create?function(G,F){Object.defineProperty(G,"default",{enumerable:!0,value:F})}:function(G,F){G.default=F},D=function(G){return D=Object.getOwnPropertyNames||function(F){var I=[];for(var Y in F)if(Object.prototype.hasOwnProperty.call(F,Y))I[I.length]=Y;return I},D(G)};BoA=function(G){if(G&&G.__esModule)return G;var F={};if(G!=null){for(var I=D(G),Y=0;Y<I.length;Y++)if(I[Y]!=="default")Yq1(F,G,I[Y])}return Q(F,G),F},QoA=function(G){return G&&G.__esModule?G:{default:G}},DoA=function(G,F,I,Y){if(I==="a"&&!Y)throw new TypeError("Private accessor was defined without a getter");if(typeof F==="function"?G!==F||!Y:!F.has(G))throw new TypeError("Cannot read private member from an object whose class did not declare it");return I==="m"?Y:I==="a"?Y.call(G):Y?Y.value:F.get(G)},ZoA=function(G,F,I,Y,W){if(Y==="m")throw new TypeError("Private method is not writable");if(Y==="a"&&!W)throw new TypeError("Private accessor was defined without a setter");if(typeof F==="function"?G!==F||!W:!F.has(G))throw new TypeError("Cannot write private member to an object whose class did not declare it");return Y==="a"?W.call(G,I):W?W.value=I:F.set(G,I),I},GoA=function(G,F){if(F===null||typeof F!=="object"&&typeof F!=="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof G==="function"?F===G:G.has(F)},FoA=function(G,F,I){if(F!==null&&F!==void 0){if(typeof F!=="object"&&typeof F!=="function")throw new TypeError("Object expected.");var Y,W;if(I){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");Y=F[Symbol.asyncDispose]}if(Y===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");if(Y=F[Symbol.dispose],I)W=Y}if(typeof Y!=="function")throw new TypeError("Object not disposable.");if(W)Y=function(){try{W.call(this)}catch(J){return Promise.reject(J)}};G.stack.push({value:F,dispose:Y,async:I})}else if(I)G.stack.push({async:!0});return F};var Z=typeof SuppressedError==="function"?SuppressedError:function(G,F,I){var Y=new Error(I);return Y.name="SuppressedError",Y.error=G,Y.suppressed=F,Y};IoA=function(G){function F(J){G.error=G.hasError?new Z(J,G.error,"An error was suppressed during disposal."):J,G.hasError=!0}var I,Y=0;function W(){while(I=G.stack.pop())try{if(!I.async&&Y===1)return Y=0,G.stack.push(I),Promise.resolve().then(W);if(I.dispose){var J=I.dispose.call(I.value);if(I.async)return Y|=2,Promise.resolve(J).then(W,function(X){return F(X),W()})}else Y|=1}catch(X){F(X)}if(Y===1)return G.hasError?Promise.reject(G.error):Promise.resolve();if(G.hasError)throw G.error}return W()},YoA=function(G,F){if(typeof G==="string"&&/^\.\.?\//.test(G))return G.replace(/\.(tsx)$|((?:\.d)?)((?:\.[^./]+?)?)\.([cm]?)ts$/i,function(I,Y,W,J,X){return Y?F?".jsx":".js":W&&(!J||!X)?I:W+J+"."+X.toLowerCase()+"js"});return G},A("__extends",vrA),A("__assign",brA),A("__rest",frA),A("__decorate",hrA),A("__param",grA),A("__esDecorate",urA),A("__runInitializers",mrA),A("__propKey",drA),A("__setFunctionName",crA),A("__metadata",lrA),A("__awaiter",prA),A("__generator",irA),A("__exportStar",nrA),A("__createBinding",Yq1),A("__values",Iq1),A("__read",CQ0),A("__spread",arA),A("__spreadArrays",srA),A("__spreadArray",rrA),A("__await",ra),A("__asyncGenerator",orA),A("__asyncDelegator",trA),A("__asyncValues",erA),A("__makeTemplateObject",AoA),A("__importStar",BoA),A("__importDefault",QoA),A("__classPrivateFieldGet",DoA),A("__classPrivateFieldSet",ZoA),A("__classPrivateFieldIn",GoA),A("__addDisposableResource",FoA),A("__disposeResources",IoA),A("__rewriteRelativeImportExtension",YoA)})});
var J6=E((QX5,fQ0)=>{var{defineProperty:Lq1,getOwnPropertyDescriptor:qQ4,getOwnPropertyNames:NQ4}=Object,LQ4=Object.prototype.hasOwnProperty,z8=(A,B)=>Lq1(A,"name",{value:B,configurable:!0}),MQ4=(A,B)=>{for(var Q in B)Lq1(A,Q,{get:B[Q],enumerable:!0})},yQ0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of NQ4(B))if(!LQ4.call(A,Z)&&Z!==Q)Lq1(A,Z,{get:()=>B[Z],enumerable:!(D=qQ4(B,Z))||D.enumerable})}return A},RQ4=(A,B,Q)=>(yQ0(A,B,"default"),Q&&yQ0(Q,B,"default")),OQ4=(A)=>yQ0(Lq1({},"__esModule",{value:!0}),A),vQ0={};MQ4(vQ0,{Client:()=>TQ4,Command:()=>UtA,NoOpLogger:()=>lQ4,SENSITIVE_STRING:()=>SQ4,ServiceException:()=>kQ4,_json:()=>xQ0,collectBody:()=>kQ0.collectBody,convertMap:()=>pQ4,createAggregatedClient:()=>jQ4,decorateServiceException:()=>wtA,emitWarningIfUnsupportedVersion:()=>vQ4,extendedEncodeURIComponent:()=>kQ0.extendedEncodeURIComponent,getArrayIfSingleItem:()=>dQ4,getDefaultClientConfiguration:()=>uQ4,getDefaultExtensionConfiguration:()=>qtA,getValueFromTextNode:()=>NtA,isSerializableHeaderValue:()=>cQ4,loadConfigsForDefaultMode:()=>xQ4,map:()=>bQ0,resolveDefaultRuntimeConfig:()=>mQ4,resolvedPath:()=>kQ0.resolvedPath,serializeDateTime:()=>oQ4,serializeFloat:()=>rQ4,take:()=>iQ4,throwDefaultError:()=>$tA,withBaseException:()=>yQ4});fQ0.exports=OQ4(vQ0);var EtA=Uw(),TQ4=class{constructor(A){this.config=A,this.middlewareStack=EtA.constructStack()}static{z8(this,"Client")}send(A,B,Q){let D=typeof B!=="function"?B:void 0,Z=typeof B==="function"?B:Q,G=D===void 0&&this.config.cacheMiddleware===!0,F;if(G){if(!this.handlers)this.handlers=new WeakMap;let I=this.handlers;if(I.has(A.constructor))F=I.get(A.constructor);else F=A.resolveMiddleware(this.middlewareStack,this.config,D),I.set(A.constructor,F)}else delete this.handlers,F=A.resolveMiddleware(this.middlewareStack,this.config,D);if(Z)F(A).then((I)=>Z(null,I.output),(I)=>Z(I)).catch(()=>{});else return F(A).then((I)=>I.output)}destroy(){this.config?.requestHandler?.destroy?.(),delete this.handlers}},kQ0=$6(),_Q0=JQ0(),UtA=class{constructor(){this.middlewareStack=EtA.constructStack()}static{z8(this,"Command")}static classBuilder(){return new PQ4}resolveMiddlewareWithContext(A,B,Q,{middlewareFn:D,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,smithyContext:Y,additionalContext:W,CommandCtor:J}){for(let H of D.bind(this)(J,A,B,Q))this.middlewareStack.use(H);let X=A.concat(this.middlewareStack),{logger:V}=B,C={logger:V,clientName:Z,commandName:G,inputFilterSensitiveLog:F,outputFilterSensitiveLog:I,[_Q0.SMITHY_CONTEXT_KEY]:{commandInstance:this,...Y},...W},{requestHandler:K}=B;return X.resolve((H)=>K.handle(H.request,Q||{}),C)}},PQ4=class{constructor(){this._init=()=>{},this._ep={},this._middlewareFn=()=>[],this._commandName="",this._clientName="",this._additionalContext={},this._smithyContext={},this._inputFilterSensitiveLog=(A)=>A,this._outputFilterSensitiveLog=(A)=>A,this._serializer=null,this._deserializer=null}static{z8(this,"ClassBuilder")}init(A){this._init=A}ep(A){return this._ep=A,this}m(A){return this._middlewareFn=A,this}s(A,B,Q={}){return this._smithyContext={service:A,operation:B,...Q},this}c(A={}){return this._additionalContext=A,this}n(A,B){return this._clientName=A,this._commandName=B,this}f(A=(Q)=>Q,B=(Q)=>Q){return this._inputFilterSensitiveLog=A,this._outputFilterSensitiveLog=B,this}ser(A){return this._serializer=A,this}de(A){return this._deserializer=A,this}sc(A){return this._operationSchema=A,this._smithyContext.operationSchema=A,this}build(){let A=this,B;return B=class extends UtA{constructor(...[Q]){super();this.serialize=A._serializer,this.deserialize=A._deserializer,this.input=Q??{},A._init(this),this.schema=A._operationSchema}static{z8(this,"CommandRef")}static getEndpointParameterInstructions(){return A._ep}resolveMiddleware(Q,D,Z){return this.resolveMiddlewareWithContext(Q,D,Z,{CommandCtor:B,middlewareFn:A._middlewareFn,clientName:A._clientName,commandName:A._commandName,inputFilterSensitiveLog:A._inputFilterSensitiveLog,outputFilterSensitiveLog:A._outputFilterSensitiveLog,smithyContext:A._smithyContext,additionalContext:A._additionalContext})}}}},SQ4="***SensitiveInformation***",jQ4=z8((A,B)=>{for(let Q of Object.keys(A)){let D=A[Q],Z=z8(async function(F,I,Y){let W=new D(F);if(typeof I==="function")this.send(W,I);else if(typeof Y==="function"){if(typeof I!=="object")throw new Error(`Expected http options but got ${typeof I}`);this.send(W,I||{},Y)}else return this.send(W,I)},"methodImpl"),G=(Q[0].toLowerCase()+Q.slice(1)).replace(/Command$/,"");B.prototype[G]=Z}},"createAggregatedClient"),kQ4=class A extends Error{static{z8(this,"ServiceException")}constructor(B){super(B.message);Object.setPrototypeOf(this,Object.getPrototypeOf(this).constructor.prototype),this.name=B.name,this.$fault=B.$fault,this.$metadata=B.$metadata}static isInstance(B){if(!B)return!1;let Q=B;return A.prototype.isPrototypeOf(Q)||Boolean(Q.$fault)&&Boolean(Q.$metadata)&&(Q.$fault==="client"||Q.$fault==="server")}static[Symbol.hasInstance](B){if(!B)return!1;let Q=B;if(this===A)return A.isInstance(B);if(A.isInstance(B)){if(Q.name&&this.name)return this.prototype.isPrototypeOf(B)||Q.name===this.name;return this.prototype.isPrototypeOf(B)}return!1}},wtA=z8((A,B={})=>{Object.entries(B).filter(([,D])=>D!==void 0).forEach(([D,Z])=>{if(A[D]==null||A[D]==="")A[D]=Z});let Q=A.message||A.Message||"UnknownError";return A.message=Q,delete A.Message,A},"decorateServiceException"),$tA=z8(({output:A,parsedBody:B,exceptionCtor:Q,errorCode:D})=>{let Z=_Q4(A),G=Z.httpStatusCode?Z.httpStatusCode+"":void 0,F=new Q({name:B?.code||B?.Code||D||G||"UnknownError",$fault:"client",$metadata:Z});throw wtA(F,B)},"throwDefaultError"),yQ4=z8((A)=>{return({output:B,parsedBody:Q,errorCode:D})=>{$tA({output:B,parsedBody:Q,exceptionCtor:A,errorCode:D})}},"withBaseException"),_Q4=z8((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),xQ4=z8((A)=>{switch(A){case"standard":return{retryMode:"standard",connectionTimeout:3100};case"in-region":return{retryMode:"standard",connectionTimeout:1100};case"cross-region":return{retryMode:"standard",connectionTimeout:3100};case"mobile":return{retryMode:"standard",connectionTimeout:30000};default:return{}}},"loadConfigsForDefaultMode"),ztA=!1,vQ4=z8((A)=>{if(A&&!ztA&&parseInt(A.substring(1,A.indexOf(".")))<16)ztA=!0},"emitWarningIfUnsupportedVersion"),bQ4=z8((A)=>{let B=[];for(let Q in _Q0.AlgorithmId){let D=_Q0.AlgorithmId[Q];if(A[D]===void 0)continue;B.push({algorithmId:()=>D,checksumConstructor:()=>A[D]})}return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),fQ4=z8((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),hQ4=z8((A)=>{return{setRetryStrategy(B){A.retryStrategy=B},retryStrategy(){return A.retryStrategy}}},"getRetryConfiguration"),gQ4=z8((A)=>{let B={};return B.retryStrategy=A.retryStrategy(),B},"resolveRetryRuntimeConfig"),qtA=z8((A)=>{return Object.assign(bQ4(A),hQ4(A))},"getDefaultExtensionConfiguration"),uQ4=qtA,mQ4=z8((A)=>{return Object.assign(fQ4(A),gQ4(A))},"resolveDefaultRuntimeConfig"),dQ4=z8((A)=>Array.isArray(A)?A:[A],"getArrayIfSingleItem"),NtA=z8((A)=>{for(let Q in A)if(A.hasOwnProperty(Q)&&A[Q]["#text"]!==void 0)A[Q]=A[Q]["#text"];else if(typeof A[Q]==="object"&&A[Q]!==null)A[Q]=NtA(A[Q]);return A},"getValueFromTextNode"),cQ4=z8((A)=>{return A!=null},"isSerializableHeaderValue"),lQ4=class{static{z8(this,"NoOpLogger")}trace(){}debug(){}info(){}warn(){}error(){}};function bQ0(A,B,Q){let D,Z,G;if(typeof B==="undefined"&&typeof Q==="undefined")D={},G=A;else if(D=A,typeof B==="function")return Z=B,G=Q,nQ4(D,Z,G);else G=B;for(let F of Object.keys(G)){if(!Array.isArray(G[F])){D[F]=G[F];continue}LtA(D,null,G,F)}return D}z8(bQ0,"map");var pQ4=z8((A)=>{let B={};for(let[Q,D]of Object.entries(A||{}))B[Q]=[,D];return B},"convertMap"),iQ4=z8((A,B)=>{let Q={};for(let D in B)LtA(Q,A,B,D);return Q},"take"),nQ4=z8((A,B,Q)=>{return bQ0(A,Object.entries(Q).reduce((D,[Z,G])=>{if(Array.isArray(G))D[Z]=G;else if(typeof G==="function")D[Z]=[B,G()];else D[Z]=[B,G];return D},{}))},"mapWithFilter"),LtA=z8((A,B,Q,D)=>{if(B!==null){let F=Q[D];if(typeof F==="function")F=[,F];let[I=aQ4,Y=sQ4,W=D]=F;if(typeof I==="function"&&I(B[W])||typeof I!=="function"&&!!I)A[D]=Y(B[W]);return}let[Z,G]=Q[D];if(typeof G==="function"){let F,I=Z===void 0&&(F=G())!=null,Y=typeof Z==="function"&&!!Z(void 0)||typeof Z!=="function"&&!!Z;if(I)A[D]=F;else if(Y)A[D]=G()}else{let F=Z===void 0&&G!=null,I=typeof Z==="function"&&!!Z(G)||typeof Z!=="function"&&!!Z;if(F||I)A[D]=G}},"applyInstruction"),aQ4=z8((A)=>A!=null,"nonNullish"),sQ4=z8((A)=>A,"pass"),rQ4=z8((A)=>{if(A!==A)return"NaN";switch(A){case 1/0:return"Infinity";case-1/0:return"-Infinity";default:return A}},"serializeFloat"),oQ4=z8((A)=>A.toISOString().replace(".000Z","Z"),"serializeDateTime"),xQ0=z8((A)=>{if(A==null)return{};if(Array.isArray(A))return A.filter((B)=>B!=null).map(xQ0);if(typeof A==="object"){let B={};for(let Q of Object.keys(A)){if(A[Q]==null)continue;B[Q]=xQ0(A[Q])}return B}return A},"_json");RQ4(vQ0,Y6(),fQ0.exports)});
var J61=E((uJ5,MrA)=>{var{defineProperty:Gq1,getOwnPropertyDescriptor:bB4,getOwnPropertyNames:fB4}=Object,hB4=Object.prototype.hasOwnProperty,Zq1=(A,B)=>Gq1(A,"name",{value:B,configurable:!0}),gB4=(A,B)=>{for(var Q in B)Gq1(A,Q,{get:B[Q],enumerable:!0})},uB4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of fB4(B))if(!hB4.call(A,Z)&&Z!==Q)Gq1(A,Z,{get:()=>B[Z],enumerable:!(D=bB4(B,Z))||D.enumerable})}return A},mB4=(A)=>uB4(Gq1({},"__esModule",{value:!0}),A),qrA={};gB4(qrA,{addRecursionDetectionMiddlewareOptions:()=>LrA,getRecursionDetectionPlugin:()=>pB4,recursionDetectionMiddleware:()=>NrA});MrA.exports=mB4(qrA);var dB4=CV(),VQ0="X-Amzn-Trace-Id",cB4="AWS_LAMBDA_FUNCTION_NAME",lB4="_X_AMZN_TRACE_ID",NrA=Zq1((A)=>(B)=>async(Q)=>{let{request:D}=Q;if(!dB4.HttpRequest.isInstance(D)||A.runtime!=="node")return B(Q);let Z=Object.keys(D.headers??{}).find((Y)=>Y.toLowerCase()===VQ0.toLowerCase())??VQ0;if(D.headers.hasOwnProperty(Z))return B(Q);let G=process.env[cB4],F=process.env[lB4],I=Zq1((Y)=>typeof Y==="string"&&Y.length>0,"nonEmptyString");if(I(G)&&I(F))D.headers[VQ0]=F;return B({...Q,request:D})},"recursionDetectionMiddleware"),LrA={step:"build",tags:["RECURSION_DETECTION"],name:"recursionDetectionMiddleware",override:!0,priority:"low"},pB4=Zq1((A)=>({applyToStack:Zq1((B)=>{B.add(NrA(A),LrA)},"applyToStack")}),"getRecursionDetectionPlugin")});
var JQ0=E((_J5,FrA)=>{var{defineProperty:t$1,getOwnPropertyDescriptor:t24,getOwnPropertyNames:e24}=Object,AB4=Object.prototype.hasOwnProperty,e$1=(A,B)=>t$1(A,"name",{value:B,configurable:!0}),BB4=(A,B)=>{for(var Q in B)t$1(A,Q,{get:B[Q],enumerable:!0})},QB4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of e24(B))if(!AB4.call(A,Z)&&Z!==Q)t$1(A,Z,{get:()=>B[Z],enumerable:!(D=t24(B,Z))||D.enumerable})}return A},DB4=(A)=>QB4(t$1({},"__esModule",{value:!0}),A),tsA={};BB4(tsA,{AlgorithmId:()=>QrA,EndpointURLScheme:()=>BrA,FieldPosition:()=>DrA,HttpApiKeyAuthLocation:()=>ArA,HttpAuthLocation:()=>esA,IniSectionType:()=>ZrA,RequestHandlerProtocol:()=>GrA,SMITHY_CONTEXT_KEY:()=>YB4,getDefaultClientConfiguration:()=>FB4,resolveDefaultRuntimeConfig:()=>IB4});FrA.exports=DB4(tsA);var esA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(esA||{}),ArA=((A)=>{return A.HEADER="header",A.QUERY="query",A})(ArA||{}),BrA=((A)=>{return A.HTTP="http",A.HTTPS="https",A})(BrA||{}),QrA=((A)=>{return A.MD5="md5",A.CRC32="crc32",A.CRC32C="crc32c",A.SHA1="sha1",A.SHA256="sha256",A})(QrA||{}),ZB4=e$1((A)=>{let B=[];if(A.sha256!==void 0)B.push({algorithmId:()=>"sha256",checksumConstructor:()=>A.sha256});if(A.md5!=null)B.push({algorithmId:()=>"md5",checksumConstructor:()=>A.md5});return{addChecksumAlgorithm(Q){B.push(Q)},checksumAlgorithms(){return B}}},"getChecksumConfiguration"),GB4=e$1((A)=>{let B={};return A.checksumAlgorithms().forEach((Q)=>{B[Q.algorithmId()]=Q.checksumConstructor()}),B},"resolveChecksumRuntimeConfig"),FB4=e$1((A)=>{return ZB4(A)},"getDefaultClientConfiguration"),IB4=e$1((A)=>{return GB4(A)},"resolveDefaultRuntimeConfig"),DrA=((A)=>{return A[A.HEADER=0]="HEADER",A[A.TRAILER=1]="TRAILER",A})(DrA||{}),YB4="__smithy_context",ZrA=((A)=>{return A.PROFILE="profile",A.SSO_SESSION="sso-session",A.SERVICES="services",A})(ZrA||{}),GrA=((A)=>{return A.HTTP_0_9="http/0.9",A.HTTP_1_0="http/1.0",A.TDS_8_0="tds/8.0",A})(GrA||{})});
var JtA=E((YtA)=>{Object.defineProperty(YtA,"__esModule",{value:!0});YtA.fromBase64=void 0;var XQ4=AD(),VQ4=/^[A-Za-z0-9+/]*={0,2}$/,CQ4=(A)=>{if(A.length*3%4!==0)throw new TypeError("Incorrect padding on base64 string.");if(!VQ4.exec(A))throw new TypeError("Invalid base64 string.");let B=XQ4.fromString(A,"base64");return new Uint8Array(B.buffer,B.byteOffset,B.byteLength)};YtA.fromBase64=CQ4});
var L02=E((q02)=>{Object.defineProperty(q02,"__esModule",{value:!0});q02.getRuntimeConfig=void 0;var c54=Hg(),l54=c54.__importDefault(C40()),E02=YI(),U02=z61(),mq1=V4(),p54=jG(),w02=v4(),$g=QD(),$02=S3(),i54=kG(),n54=hZ(),a54=z02(),s54=J6(),r54=yG(),o54=J6(),t54=(A)=>{o54.emitWarningIfUnsupportedVersion(process.version);let B=r54.resolveDefaultsModeConfig(A),Q=()=>B().then(s54.loadConfigsForDefaultMode),D=a54.getRuntimeConfig(A);E02.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??$g.loadConfig(E02.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??i54.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??U02.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:l54.default.version}),maxAttempts:A?.maxAttempts??$g.loadConfig(w02.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??$g.loadConfig(mq1.NODE_REGION_CONFIG_OPTIONS,{...mq1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:$02.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??$g.loadConfig({...w02.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||n54.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??p54.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??$02.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??$g.loadConfig(mq1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??$g.loadConfig(mq1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??$g.loadConfig(U02.NODE_APP_ID_CONFIG_OPTIONS,Z)}};q02.getRuntimeConfig=t54});
var L40=E((IV5,VA2)=>{var{defineProperty:lq1,getOwnPropertyDescriptor:B74,getOwnPropertyNames:FA2}=Object,Q74=Object.prototype.hasOwnProperty,pq1=(A,B)=>lq1(A,"name",{value:B,configurable:!0}),D74=(A,B)=>function Q(){return A&&(B=A[FA2(A)[0]](A=0)),B},IA2=(A,B)=>{for(var Q in B)lq1(A,Q,{get:B[Q],enumerable:!0})},Z74=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of FA2(B))if(!Q74.call(A,Z)&&Z!==Q)lq1(A,Z,{get:()=>B[Z],enumerable:!(D=B74(B,Z))||D.enumerable})}return A},G74=(A)=>Z74(lq1({},"__esModule",{value:!0}),A),YA2={};IA2(YA2,{GetRoleCredentialsCommand:()=>N40.GetRoleCredentialsCommand,SSOClient:()=>N40.SSOClient});var N40,F74=D74({"src/loadSso.ts"(){N40=c12()}}),WA2={};IA2(WA2,{fromSSO:()=>Y74,isSsoProfile:()=>JA2,validateSsoProfile:()=>XA2});VA2.exports=G74(WA2);var JA2=pq1((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),ZA2=Tw(),I74=q40(),Pw=eB(),cq1=e5(),M61=!1,GA2=pq1(async({ssoStartUrl:A,ssoSession:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoClient:G,clientConfig:F,parentClientConfig:I,profile:Y,logger:W})=>{let J,X="To refresh this SSO session run aws sso login with the corresponding profile.";if(B)try{let f=await I74.fromSso({profile:Y})();J={accessToken:f.token,expiresAt:new Date(f.expiration).toISOString()}}catch(f){throw new Pw.CredentialsProviderError(f.message,{tryNextLink:M61,logger:W})}else try{J=await cq1.getSSOTokenFromFile(A)}catch(f){throw new Pw.CredentialsProviderError("The SSO session associated with this profile is invalid. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:M61,logger:W})}if(new Date(J.expiresAt).getTime()-Date.now()<=0)throw new Pw.CredentialsProviderError("The SSO session associated with this profile has expired. To refresh this SSO session run aws sso login with the corresponding profile.",{tryNextLink:M61,logger:W});let{accessToken:V}=J,{SSOClient:C,GetRoleCredentialsCommand:K}=await Promise.resolve().then(()=>(F74(),YA2)),H=G||new C(Object.assign({},F??{},{logger:F?.logger??I?.logger,region:F?.region??D})),z;try{z=await H.send(new K({accountId:Q,roleName:Z,accessToken:V}))}catch(f){throw new Pw.CredentialsProviderError(f,{tryNextLink:M61,logger:W})}let{roleCredentials:{accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:O,credentialScope:R,accountId:T}={}}=z;if(!$||!L||!N||!O)throw new Pw.CredentialsProviderError("SSO returns an invalid temporary credential.",{tryNextLink:M61,logger:W});let j={accessKeyId:$,secretAccessKey:L,sessionToken:N,expiration:new Date(O),...R&&{credentialScope:R},...T&&{accountId:T}};if(B)ZA2.setCredentialFeature(j,"CREDENTIALS_SSO","s");else ZA2.setCredentialFeature(j,"CREDENTIALS_SSO_LEGACY","u");return j},"resolveSSOCredentials"),XA2=pq1((A,B)=>{let{sso_start_url:Q,sso_account_id:D,sso_region:Z,sso_role_name:G}=A;if(!Q||!D||!Z||!G)throw new Pw.CredentialsProviderError(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(A).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:B});return A},"validateSsoProfile"),Y74=pq1((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:Q,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoSession:F}=A,{ssoClient:I}=A,Y=cq1.getProfileName({profile:A.profile??B?.profile});if(!Q&&!D&&!Z&&!G&&!F){let J=(await cq1.parseKnownFiles(A))[Y];if(!J)throw new Pw.CredentialsProviderError(`Profile ${Y} was not found.`,{logger:A.logger});if(!JA2(J))throw new Pw.CredentialsProviderError(`Profile ${Y} is not configured with SSO credentials.`,{logger:A.logger});if(J?.sso_session){let $=(await cq1.loadSsoSessionData(A))[J.sso_session],L=` configurations in profile ${Y} and sso-session ${J.sso_session}`;if(Z&&Z!==$.sso_region)throw new Pw.CredentialsProviderError("Conflicting SSO region"+L,{tryNextLink:!1,logger:A.logger});if(Q&&Q!==$.sso_start_url)throw new Pw.CredentialsProviderError("Conflicting SSO start_url"+L,{tryNextLink:!1,logger:A.logger});J.sso_region=$.sso_region,J.sso_start_url=$.sso_start_url}let{sso_start_url:X,sso_account_id:V,sso_region:C,sso_role_name:K,sso_session:H}=XA2(J,A.logger);return GA2({ssoStartUrl:X,ssoSession:H,ssoAccountId:V,ssoRegion:C,ssoRoleName:K,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})}else if(!Q||!D||!Z||!G)throw new Pw.CredentialsProviderError('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:A.logger});else return GA2({ssoStartUrl:Q,ssoSession:F,ssoAccountId:D,ssoRegion:Z,ssoRoleName:G,ssoClient:I,clientConfig:A.clientConfig,parentClientConfig:A.parentClientConfig,profile:Y})},"fromSSO")});
var MeA=E((bX5,t64)=>{t64.exports={name:"@aws-sdk/client-sso",description:"AWS SDK for JavaScript Sso Client for Node.js, Browser and React Native",version:"3.840.0",scripts:{build:"concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'","build:cjs":"node ../../scripts/compilation/inline client-sso","build:es":"tsc -p tsconfig.es.json","build:include:deps":"lerna run --scope $npm_package_name --include-dependencies build","build:types":"tsc -p tsconfig.types.json","build:types:downlevel":"downlevel-dts dist-types dist-types/ts3.4",clean:"rimraf ./dist-* && rimraf *.tsbuildinfo","extract:docs":"api-extractor run --local","generate:client":"node ../../scripts/generate-clients/single-service --solo sso"},main:"./dist-cjs/index.js",types:"./dist-types/index.d.ts",module:"./dist-es/index.js",sideEffects:!1,dependencies:{"@aws-crypto/sha256-browser":"5.2.0","@aws-crypto/sha256-js":"5.2.0","@aws-sdk/core":"3.840.0","@aws-sdk/middleware-host-header":"3.840.0","@aws-sdk/middleware-logger":"3.840.0","@aws-sdk/middleware-recursion-detection":"3.840.0","@aws-sdk/middleware-user-agent":"3.840.0","@aws-sdk/region-config-resolver":"3.840.0","@aws-sdk/types":"3.840.0","@aws-sdk/util-endpoints":"3.840.0","@aws-sdk/util-user-agent-browser":"3.840.0","@aws-sdk/util-user-agent-node":"3.840.0","@smithy/config-resolver":"^4.1.4","@smithy/core":"^3.6.0","@smithy/fetch-http-handler":"^5.0.4","@smithy/hash-node":"^4.0.4","@smithy/invalid-dependency":"^4.0.4","@smithy/middleware-content-length":"^4.0.4","@smithy/middleware-endpoint":"^4.1.13","@smithy/middleware-retry":"^4.1.14","@smithy/middleware-serde":"^4.0.8","@smithy/middleware-stack":"^4.0.4","@smithy/node-config-provider":"^4.1.3","@smithy/node-http-handler":"^4.0.6","@smithy/protocol-http":"^5.1.2","@smithy/smithy-client":"^4.4.5","@smithy/types":"^4.3.1","@smithy/url-parser":"^4.0.4","@smithy/util-base64":"^4.0.0","@smithy/util-body-length-browser":"^4.0.0","@smithy/util-body-length-node":"^4.0.0","@smithy/util-defaults-mode-browser":"^4.0.21","@smithy/util-defaults-mode-node":"^4.0.21","@smithy/util-endpoints":"^3.0.6","@smithy/util-middleware":"^4.0.4","@smithy/util-retry":"^4.0.6","@smithy/util-utf8":"^4.0.0",tslib:"^2.6.2"},devDependencies:{"@tsconfig/node18":"18.2.4","@types/node":"^18.19.69",concurrently:"7.0.0","downlevel-dts":"0.10.1",rimraf:"3.0.2",typescript:"~5.8.3"},engines:{node:">=18.0.0"},typesVersions:{"<4.0":{"dist-types/*":["dist-types/ts3.4/*"]}},files:["dist-*/**"],author:{name:"AWS SDK for JavaScript Team",url:"https://aws.amazon.com/javascript/"},license:"Apache-2.0",browser:{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.browser"},"react-native":{"./dist-es/runtimeConfig":"./dist-es/runtimeConfig.native"},homepage:"https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-sso",repository:{type:"git",url:"https://github.com/aws/aws-sdk-js-v3.git",directory:"clients/client-sso"}}});
var O61=E((zA2)=>{Object.defineProperty(zA2,"__esModule",{value:!0});zA2.commonParams=zA2.resolveClientEndpointParameters=void 0;var w74=(A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,useGlobalEndpoint:A.useGlobalEndpoint??!1,defaultSigningName:"sts"})};zA2.resolveClientEndpointParameters=w74;zA2.commonParams={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}}});
var PQ0=E((rJ5,ItA)=>{var{defineProperty:qq1,getOwnPropertyDescriptor:n94,getOwnPropertyNames:a94}=Object,s94=Object.prototype.hasOwnProperty,pZ=(A,B)=>qq1(A,"name",{value:B,configurable:!0}),r94=(A,B)=>{for(var Q in B)qq1(A,Q,{get:B[Q],enumerable:!0})},o94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of a94(B))if(!s94.call(A,Z)&&Z!==Q)qq1(A,Z,{get:()=>B[Z],enumerable:!(D=n94(B,Z))||D.enumerable})}return A},t94=(A)=>o94(qq1({},"__esModule",{value:!0}),A),QtA={};r94(QtA,{AWSSDKSigV4Signer:()=>QQ4,AwsSdkSigV4ASigner:()=>ZQ4,AwsSdkSigV4Signer:()=>TQ0,NODE_AUTH_SCHEME_PREFERENCE_OPTIONS:()=>GQ4,NODE_SIGV4A_CONFIG_OPTIONS:()=>YQ4,getBearerTokenEnvKey:()=>DtA,resolveAWSSDKSigV4Config:()=>JQ4,resolveAwsSdkSigV4AConfig:()=>IQ4,resolveAwsSdkSigV4Config:()=>ZtA,validateSigningProperties:()=>OQ0});ItA.exports=t94(QtA);var e94=CV(),AQ4=CV(),soA=pZ((A)=>AQ4.HttpResponse.isInstance(A)?A.headers?.date??A.headers?.Date:void 0,"getDateHeader"),RQ0=pZ((A)=>new Date(Date.now()+A),"getSkewCorrectedDate"),BQ4=pZ((A,B)=>Math.abs(RQ0(B).getTime()-A)>=300000,"isClockSkewed"),roA=pZ((A,B)=>{let Q=Date.parse(A);if(BQ4(Q,B))return Q-Date.now();return B},"getUpdatedSystemClockOffset"),X61=pZ((A,B)=>{if(!B)throw new Error(`Property \`${A}\` is not resolved for AWS SDK SigV4Auth`);return B},"throwSigningPropertyError"),OQ0=pZ(async(A)=>{let B=X61("context",A.context),Q=X61("config",A.config),D=B.endpointV2?.properties?.authSchemes?.[0],G=await X61("signer",Q.signer)(D),F=A?.signingRegion,I=A?.signingRegionSet,Y=A?.signingName;return{config:Q,signer:G,signingRegion:F,signingRegionSet:I,signingName:Y}},"validateSigningProperties"),TQ0=class{static{pZ(this,"AwsSdkSigV4Signer")}async sign(A,B,Q){if(!e94.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let D=await OQ0(Q),{config:Z,signer:G}=D,{signingRegion:F,signingName:I}=D,Y=Q.context;if(Y?.authSchemes?.length??0>1){let[J,X]=Y.authSchemes;if(J?.name==="sigv4a"&&X?.name==="sigv4")F=X?.signingRegion??F,I=X?.signingName??I}return await G.sign(A,{signingDate:RQ0(Z.systemClockOffset),signingRegion:F,signingService:I})}errorHandler(A){return(B)=>{let Q=B.ServerTime??soA(B.$response);if(Q){let D=X61("config",A.config),Z=D.systemClockOffset;if(D.systemClockOffset=roA(Q,D.systemClockOffset),D.systemClockOffset!==Z&&B.$metadata)B.$metadata.clockSkewCorrected=!0}throw B}}successHandler(A,B){let Q=soA(A);if(Q){let D=X61("config",B.config);D.systemClockOffset=roA(Q,D.systemClockOffset)}}},QQ4=TQ0,DQ4=CV(),ZQ4=class extends TQ0{static{pZ(this,"AwsSdkSigV4ASigner")}async sign(A,B,Q){if(!DQ4.HttpRequest.isInstance(A))throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");let{config:D,signer:Z,signingRegion:G,signingRegionSet:F,signingName:I}=await OQ0(Q),W=(await D.sigv4aSigningRegionSet?.()??F??[G]).join(",");return await Z.sign(A,{signingDate:RQ0(D.systemClockOffset),signingRegion:W,signingService:I})}},ooA=pZ((A)=>typeof A==="string"&&A.length>0?A.split(",").map((B)=>B.trim()):[],"getArrayForCommaSeparatedString"),DtA=pZ((A)=>`AWS_BEARER_TOKEN_${A.replace(/[\s-]/g,"_").toUpperCase()}`,"getBearerTokenEnvKey"),toA="AWS_AUTH_SCHEME_PREFERENCE",eoA="auth_scheme_preference",GQ4={environmentVariableSelector:pZ((A,B)=>{if(B?.signingName){if(DtA(B.signingName)in A)return["httpBearerAuth"]}if(!(toA in A))return;return ooA(A[toA])},"environmentVariableSelector"),configFileSelector:pZ((A)=>{if(!(eoA in A))return;return ooA(A[eoA])},"configFileSelector"),default:[]},FQ4=VB(),AtA=eB(),IQ4=pZ((A)=>{return A.sigv4aSigningRegionSet=FQ4.normalizeProvider(A.sigv4aSigningRegionSet),A},"resolveAwsSdkSigV4AConfig"),YQ4={environmentVariableSelector(A){if(A.AWS_SIGV4A_SIGNING_REGION_SET)return A.AWS_SIGV4A_SIGNING_REGION_SET.split(",").map((B)=>B.trim());throw new AtA.ProviderError("AWS_SIGV4A_SIGNING_REGION_SET not set in env.",{tryNextLink:!0})},configFileSelector(A){if(A.sigv4a_signing_region_set)return(A.sigv4a_signing_region_set??"").split(",").map((B)=>B.trim());throw new AtA.ProviderError("sigv4a_signing_region_set not set in profile.",{tryNextLink:!0})},default:void 0},WQ4=Tw(),zg=VB(),BtA=aoA(),ZtA=pZ((A)=>{let B=A.credentials,Q=!!A.credentials,D=void 0;Object.defineProperty(A,"credentials",{set(W){if(W&&W!==B&&W!==D)Q=!0;B=W;let J=GtA(A,{credentials:B,credentialDefaultProvider:A.credentialDefaultProvider}),X=FtA(A,J);if(Q&&!X.attributed)D=pZ(async(V)=>X(V).then((C)=>WQ4.setCredentialFeature(C,"CREDENTIALS_CODE","e")),"resolvedCredentials"),D.memoized=X.memoized,D.configBound=X.configBound,D.attributed=!0;else D=X},get(){return D},enumerable:!0,configurable:!0}),A.credentials=B;let{signingEscapePath:Z=!0,systemClockOffset:G=A.systemClockOffset||0,sha256:F}=A,I;if(A.signer)I=zg.normalizeProvider(A.signer);else if(A.regionInfoProvider)I=pZ(()=>zg.normalizeProvider(A.region)().then(async(W)=>[await A.regionInfoProvider(W,{useFipsEndpoint:await A.useFipsEndpoint(),useDualstackEndpoint:await A.useDualstackEndpoint()})||{},W]).then(([W,J])=>{let{signingRegion:X,signingService:V}=W;A.signingRegion=A.signingRegion||X||J,A.signingName=A.signingName||V||A.serviceId;let C={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||BtA.SignatureV4)(C)}),"signer");else I=pZ(async(W)=>{W=Object.assign({},{name:"sigv4",signingName:A.signingName||A.defaultSigningName,signingRegion:await zg.normalizeProvider(A.region)(),properties:{}},W);let{signingRegion:J,signingName:X}=W;A.signingRegion=A.signingRegion||J,A.signingName=A.signingName||X||A.serviceId;let V={...A,credentials:A.credentials,region:A.signingRegion,service:A.signingName,sha256:F,uriEscapePath:Z};return new(A.signerConstructor||BtA.SignatureV4)(V)},"signer");return Object.assign(A,{systemClockOffset:G,signingEscapePath:Z,signer:I})},"resolveAwsSdkSigV4Config"),JQ4=ZtA;function GtA(A,{credentials:B,credentialDefaultProvider:Q}){let D;if(B)if(!B?.memoized)D=zg.memoizeIdentityProvider(B,zg.isIdentityExpired,zg.doesIdentityRequireRefresh);else D=B;else if(Q)D=zg.normalizeProvider(Q(Object.assign({},A,{parentClientConfig:A})));else D=pZ(async()=>{throw new Error("@aws-sdk/core::resolveAwsSdkSigV4Config - `credentials` not provided and no credentialDefaultProvider was configured.")},"credentialsProvider");return D.memoized=!0,D}pZ(GtA,"normalizeCredentialProvider");function FtA(A,B){if(B.configBound)return B;let Q=pZ(async(D)=>B({...D,callerClientConfig:A}),"fn");return Q.memoized=B.memoized,Q.configBound=!0,Q}pZ(FtA,"bindCallerConfig")});
var Q12=E((A12)=>{Object.defineProperty(A12,"__esModule",{value:!0});A12.getRuntimeConfig=void 0;var H84=YI(),z84=VB(),E84=J6(),U84=BZ(),teA=dy(),eeA=cB(),w84=Z40(),$84=oeA(),q84=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??teA.fromBase64,base64Encoder:A?.base64Encoder??teA.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??$84.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??w84.defaultSSOHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new H84.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new z84.NoAuthSigner}],logger:A?.logger??new E84.NoOpLogger,serviceId:A?.serviceId??"SSO",urlParser:A?.urlParser??U84.parseUrl,utf8Decoder:A?.utf8Decoder??eeA.fromUtf8,utf8Encoder:A?.utf8Encoder??eeA.toUtf8}};A12.getRuntimeConfig=q84});
var Q40=E((B40)=>{Object.defineProperty(B40,"__esModule",{value:!0});B40.fromHttp=void 0;var c64=qeA();Object.defineProperty(B40,"fromHttp",{enumerable:!0,get:function(){return c64.fromHttp}})});
var R40=E((CA2)=>{Object.defineProperty(CA2,"__esModule",{value:!0});CA2.resolveHttpAuthSchemeConfig=CA2.resolveStsAuthConfig=CA2.defaultSTSHttpAuthSchemeProvider=CA2.defaultSTSHttpAuthSchemeParametersProvider=void 0;var W74=YI(),M40=I5(),J74=R61(),X74=async(A,B,Q)=>{return{operation:M40.getSmithyContext(B).operation,region:await M40.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};CA2.defaultSTSHttpAuthSchemeParametersProvider=X74;function V74(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function C74(A){return{schemeId:"smithy.api#noAuth"}}var K74=(A)=>{let B=[];switch(A.operation){case"AssumeRoleWithWebIdentity":{B.push(C74(A));break}default:B.push(V74(A))}return B};CA2.defaultSTSHttpAuthSchemeProvider=K74;var H74=(A)=>Object.assign(A,{stsClientCtor:J74.STSClient});CA2.resolveStsAuthConfig=H74;var z74=(A)=>{let B=CA2.resolveStsAuthConfig(A),Q=W74.resolveAwsSdkSigV4Config(B);return Object.assign(Q,{authSchemePreference:M40.normalizeProvider(A.authSchemePreference??[])})};CA2.resolveHttpAuthSchemeConfig=z74});
var R61=E((y40)=>{Object.defineProperty(y40,"__esModule",{value:!0});y40.STSClient=y40.__Client=void 0;var V22=Y61(),a74=W61(),s74=J61(),C22=Bs(),r74=V4(),k40=VB(),o74=TG(),t74=q6(),K22=v4(),z22=J6();Object.defineProperty(y40,"__Client",{enumerable:!0,get:function(){return z22.Client}});var H22=R40(),e74=O61(),AD4=B22(),BD4=X22();class E22 extends z22.Client{config;constructor(...[A]){let B=AD4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=e74.resolveClientEndpointParameters(B),D=C22.resolveUserAgentConfig(Q),Z=K22.resolveRetryConfig(D),G=r74.resolveRegionConfig(Z),F=V22.resolveHostHeaderConfig(G),I=t74.resolveEndpointConfig(F),Y=H22.resolveHttpAuthSchemeConfig(I),W=BD4.resolveRuntimeExtensions(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(C22.getUserAgentPlugin(this.config)),this.middlewareStack.use(K22.getRetryPlugin(this.config)),this.middlewareStack.use(o74.getContentLengthPlugin(this.config)),this.middlewareStack.use(V22.getHostHeaderPlugin(this.config)),this.middlewareStack.use(a74.getLoggerPlugin(this.config)),this.middlewareStack.use(s74.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(k40.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:H22.defaultSTSHttpAuthSchemeParametersProvider,identityProviderConfigProvider:async(J)=>new k40.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials})})),this.middlewareStack.use(k40.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}}y40.STSClient=E22});
var Tw=E((cJ5,CoA)=>{var{defineProperty:Jq1,getOwnPropertyDescriptor:Q94,getOwnPropertyNames:D94}=Object,Z94=Object.prototype.hasOwnProperty,Xq1=(A,B)=>Jq1(A,"name",{value:B,configurable:!0}),G94=(A,B)=>{for(var Q in B)Jq1(A,Q,{get:B[Q],enumerable:!0})},F94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of D94(B))if(!Z94.call(A,Z)&&Z!==Q)Jq1(A,Z,{get:()=>B[Z],enumerable:!(D=Q94(B,Z))||D.enumerable})}return A},I94=(A)=>F94(Jq1({},"__esModule",{value:!0}),A),WoA={};G94(WoA,{emitWarningIfUnsupportedVersion:()=>Y94,setCredentialFeature:()=>JoA,setFeature:()=>XoA,setTokenFeature:()=>VoA,state:()=>KQ0});CoA.exports=I94(WoA);var KQ0={warningEmitted:!1},Y94=Xq1((A)=>{if(A&&!KQ0.warningEmitted&&parseInt(A.substring(1,A.indexOf(".")))<18)KQ0.warningEmitted=!0,process.emitWarning(`NodeDeprecationWarning: The AWS SDK for JavaScript (v3) will
no longer support Node.js 16.x on January 6, 2025.

To continue receiving updates to AWS services, bug fixes, and security
updates please upgrade to a supported Node.js LTS version.

More information can be found at: https://a.co/74kJMmI`)},"emitWarningIfUnsupportedVersion");function JoA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}Xq1(JoA,"setCredentialFeature");function XoA(A,B,Q){if(!A.__aws_sdk_context)A.__aws_sdk_context={features:{}};else if(!A.__aws_sdk_context.features)A.__aws_sdk_context.features={};A.__aws_sdk_context.features[B]=Q}Xq1(XoA,"setFeature");function VoA(A,B,Q){if(!A.$source)A.$source={};return A.$source[B]=Q,A}Xq1(VoA,"setTokenFeature")});
var U61=E((dX5,H12)=>{var{defineProperty:_q1,getOwnPropertyDescriptor:y84,getOwnPropertyNames:_84}=Object,x84=Object.prototype.hasOwnProperty,WL=(A,B)=>_q1(A,"name",{value:B,configurable:!0}),v84=(A,B)=>{for(var Q in B)_q1(A,Q,{get:B[Q],enumerable:!0})},b84=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of _84(B))if(!x84.call(A,Z)&&Z!==Q)_q1(A,Z,{get:()=>B[Z],enumerable:!(D=y84(B,Z))||D.enumerable})}return A},f84=(A)=>b84(_q1({},"__esModule",{value:!0}),A),X12={};v84(X12,{NODE_REGION_CONFIG_FILE_OPTIONS:()=>m84,NODE_REGION_CONFIG_OPTIONS:()=>u84,REGION_ENV_NAME:()=>V12,REGION_INI_NAME:()=>C12,getAwsRegionExtensionConfiguration:()=>h84,resolveAwsRegionExtensionConfiguration:()=>g84,resolveRegionConfig:()=>d84});H12.exports=f84(X12);var h84=WL((A)=>{return{setRegion(B){A.region=B},region(){return A.region}}},"getAwsRegionExtensionConfiguration"),g84=WL((A)=>{return{region:A.region()}},"resolveAwsRegionExtensionConfiguration"),V12="AWS_REGION",C12="region",u84={environmentVariableSelector:WL((A)=>A[V12],"environmentVariableSelector"),configFileSelector:WL((A)=>A[C12],"configFileSelector"),default:WL(()=>{throw new Error("Region is missing")},"default")},m84={preferredFile:"credentials"},K12=WL((A)=>typeof A==="string"&&(A.startsWith("fips-")||A.endsWith("-fips")),"isFipsRegion"),J12=WL((A)=>K12(A)?["fips-aws-global","aws-fips"].includes(A)?"us-east-1":A.replace(/fips-(dkr-|prod-)?|-fips/,""):A,"getRealRegion"),d84=WL((A)=>{let{region:B,useFipsEndpoint:Q}=A;if(!B)throw new Error("Region is missing");return Object.assign(A,{region:WL(async()=>{if(typeof B==="string")return J12(B);let D=await B();return J12(D)},"region"),useFipsEndpoint:WL(async()=>{let D=typeof B==="string"?B:await B();if(K12(D))return!0;return typeof Q!=="function"?Promise.resolve(!!Q):Q()},"useFipsEndpoint")})},"resolveRegionConfig")});
var V40=E((l12)=>{Object.defineProperty(l12,"__esModule",{value:!0});l12.resolveHttpAuthSchemeConfig=l12.defaultSSOOIDCHttpAuthSchemeProvider=l12.defaultSSOOIDCHttpAuthSchemeParametersProvider=void 0;var N54=YI(),X40=I5(),L54=async(A,B,Q)=>{return{operation:X40.getSmithyContext(B).operation,region:await X40.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};l12.defaultSSOOIDCHttpAuthSchemeParametersProvider=L54;function M54(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function R54(A){return{schemeId:"smithy.api#noAuth"}}var O54=(A)=>{let B=[];switch(A.operation){case"CreateToken":{B.push(R54(A));break}default:B.push(M54(A))}return B};l12.defaultSSOOIDCHttpAuthSchemeProvider=O54;var T54=(A)=>{let B=N54.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:X40.normalizeProvider(A.authSchemePreference??[])})};l12.resolveHttpAuthSchemeConfig=T54});
var V60=E((NV5,o22)=>{var{defineProperty:aq1,getOwnPropertyDescriptor:DZ4,getOwnPropertyNames:ZZ4}=Object,GZ4=Object.prototype.hasOwnProperty,X60=(A,B)=>aq1(A,"name",{value:B,configurable:!0}),FZ4=(A,B)=>{for(var Q in B)aq1(A,Q,{get:B[Q],enumerable:!0})},IZ4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of ZZ4(B))if(!GZ4.call(A,Z)&&Z!==Q)aq1(A,Z,{get:()=>B[Z],enumerable:!(D=DZ4(B,Z))||D.enumerable})}return A},YZ4=(A)=>IZ4(aq1({},"__esModule",{value:!0}),A),r22={};FZ4(r22,{fromProcess:()=>KZ4});o22.exports=YZ4(r22);var s22=e5(),J60=eB(),WZ4=J1("child_process"),JZ4=J1("util"),XZ4=Tw(),VZ4=X60((A,B,Q)=>{if(B.Version!==1)throw Error(`Profile ${A} credential_process did not return Version 1.`);if(B.AccessKeyId===void 0||B.SecretAccessKey===void 0)throw Error(`Profile ${A} credential_process returned invalid credentials.`);if(B.Expiration){let G=new Date;if(new Date(B.Expiration)<G)throw Error(`Profile ${A} credential_process returned expired credentials.`)}let D=B.AccountId;if(!D&&Q?.[A]?.aws_account_id)D=Q[A].aws_account_id;let Z={accessKeyId:B.AccessKeyId,secretAccessKey:B.SecretAccessKey,...B.SessionToken&&{sessionToken:B.SessionToken},...B.Expiration&&{expiration:new Date(B.Expiration)},...B.CredentialScope&&{credentialScope:B.CredentialScope},...D&&{accountId:D}};return XZ4.setCredentialFeature(Z,"CREDENTIALS_PROCESS","w"),Z},"getValidatedProcessCredentials"),CZ4=X60(async(A,B,Q)=>{let D=B[A];if(B[A]){let Z=D.credential_process;if(Z!==void 0){let G=JZ4.promisify(WZ4.exec);try{let{stdout:F}=await G(Z),I;try{I=JSON.parse(F.trim())}catch{throw Error(`Profile ${A} credential_process returned invalid JSON.`)}return VZ4(A,I,B)}catch(F){throw new J60.CredentialsProviderError(F.message,{logger:Q})}}else throw new J60.CredentialsProviderError(`Profile ${A} did not contain credential_process.`,{logger:Q})}else throw new J60.CredentialsProviderError(`Profile ${A} could not be found in shared credentials file.`,{logger:Q})},"resolveProcessCredentials"),KZ4=X60((A={})=>async({callerClientConfig:B}={})=>{A.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let Q=await s22.parseKnownFiles(A);return CZ4(s22.getProfileName({profile:A.profile??B?.profile}),Q,A.logger)},"fromProcess")});
var W12=E((I12)=>{Object.defineProperty(I12,"__esModule",{value:!0});I12.getRuntimeConfig=void 0;var N84=Hg(),L84=N84.__importDefault(MeA()),D12=YI(),Z12=z61(),yq1=V4(),M84=jG(),G12=v4(),wg=QD(),F12=S3(),R84=kG(),O84=hZ(),T84=Q12(),P84=J6(),S84=yG(),j84=J6(),k84=(A)=>{j84.emitWarningIfUnsupportedVersion(process.version);let B=S84.resolveDefaultsModeConfig(A),Q=()=>B().then(P84.loadConfigsForDefaultMode),D=T84.getRuntimeConfig(A);D12.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??wg.loadConfig(D12.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??R84.calculateBodyLength,defaultUserAgentProvider:A?.defaultUserAgentProvider??Z12.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:L84.default.version}),maxAttempts:A?.maxAttempts??wg.loadConfig(G12.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??wg.loadConfig(yq1.NODE_REGION_CONFIG_OPTIONS,{...yq1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:F12.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??wg.loadConfig({...G12.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||O84.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??M84.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??F12.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??wg.loadConfig(yq1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??wg.loadConfig(yq1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??wg.loadConfig(Z12.NODE_APP_ID_CONFIG_OPTIONS,Z)}};I12.getRuntimeConfig=k84});
var W60=E((UV5,Y60)=>{var{defineProperty:nq1,getOwnPropertyDescriptor:QD4,getOwnPropertyNames:DD4}=Object,ZD4=Object.prototype.hasOwnProperty,D9=(A,B)=>nq1(A,"name",{value:B,configurable:!0}),GD4=(A,B)=>{for(var Q in B)nq1(A,Q,{get:B[Q],enumerable:!0})},B60=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of DD4(B))if(!ZD4.call(A,Z)&&Z!==Q)nq1(A,Z,{get:()=>B[Z],enumerable:!(D=QD4(B,Z))||D.enumerable})}return A},FD4=(A,B,Q)=>(B60(A,B,"default"),Q&&B60(Q,B,"default")),ID4=(A)=>B60(nq1({},"__esModule",{value:!0}),A),D60={};GD4(D60,{AssumeRoleCommand:()=>F60,AssumeRoleResponseFilterSensitiveLog:()=>q22,AssumeRoleWithWebIdentityCommand:()=>I60,AssumeRoleWithWebIdentityRequestFilterSensitiveLog:()=>P22,AssumeRoleWithWebIdentityResponseFilterSensitiveLog:()=>S22,ClientInputEndpointParameters:()=>eD4.ClientInputEndpointParameters,CredentialsFilterSensitiveLog:()=>Z60,ExpiredTokenException:()=>N22,IDPCommunicationErrorException:()=>j22,IDPRejectedClaimException:()=>O22,InvalidIdentityTokenException:()=>T22,MalformedPolicyDocumentException:()=>L22,PackedPolicyTooLargeException:()=>M22,RegionDisabledException:()=>R22,STS:()=>m22,STSServiceException:()=>PT,decorateDefaultCredentialProvider:()=>QZ4,getDefaultRoleAssumer:()=>n22,getDefaultRoleAssumerWithWebIdentity:()=>a22});Y60.exports=ID4(D60);FD4(D60,R61(),Y60.exports);var YD4=J6(),WD4=q6(),JD4=T3(),XD4=J6(),VD4=O61(),$22=J6(),CD4=J6(),PT=class A extends CD4.ServiceException{static{D9(this,"STSServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},Z60=D9((A)=>({...A,...A.SecretAccessKey&&{SecretAccessKey:$22.SENSITIVE_STRING}}),"CredentialsFilterSensitiveLog"),q22=D9((A)=>({...A,...A.Credentials&&{Credentials:Z60(A.Credentials)}}),"AssumeRoleResponseFilterSensitiveLog"),N22=class A extends PT{static{D9(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},L22=class A extends PT{static{D9(this,"MalformedPolicyDocumentException")}name="MalformedPolicyDocumentException";$fault="client";constructor(B){super({name:"MalformedPolicyDocumentException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},M22=class A extends PT{static{D9(this,"PackedPolicyTooLargeException")}name="PackedPolicyTooLargeException";$fault="client";constructor(B){super({name:"PackedPolicyTooLargeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},R22=class A extends PT{static{D9(this,"RegionDisabledException")}name="RegionDisabledException";$fault="client";constructor(B){super({name:"RegionDisabledException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},O22=class A extends PT{static{D9(this,"IDPRejectedClaimException")}name="IDPRejectedClaimException";$fault="client";constructor(B){super({name:"IDPRejectedClaimException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},T22=class A extends PT{static{D9(this,"InvalidIdentityTokenException")}name="InvalidIdentityTokenException";$fault="client";constructor(B){super({name:"InvalidIdentityTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},P22=D9((A)=>({...A,...A.WebIdentityToken&&{WebIdentityToken:$22.SENSITIVE_STRING}}),"AssumeRoleWithWebIdentityRequestFilterSensitiveLog"),S22=D9((A)=>({...A,...A.Credentials&&{Credentials:Z60(A.Credentials)}}),"AssumeRoleWithWebIdentityResponseFilterSensitiveLog"),j22=class A extends PT{static{D9(this,"IDPCommunicationErrorException")}name="IDPCommunicationErrorException";$fault="client";constructor(B){super({name:"IDPCommunicationErrorException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},G60=YI(),KD4=CV(),X5=J6(),HD4=D9(async(A,B)=>{let Q=b22,D;return D=u22({...OD4(A,B),[h22]:lD4,[g22]:f22}),v22(B,Q,"/",void 0,D)},"se_AssumeRoleCommand"),zD4=D9(async(A,B)=>{let Q=b22,D;return D=u22({...TD4(A,B),[h22]:pD4,[g22]:f22}),v22(B,Q,"/",void 0,D)},"se_AssumeRoleWithWebIdentityCommand"),ED4=D9(async(A,B)=>{if(A.statusCode>=300)return k22(A,B);let Q=await G60.parseXmlBody(A.body,B),D={};return D=xD4(Q.AssumeRoleResult,B),{$metadata:ST(A),...D}},"de_AssumeRoleCommand"),UD4=D9(async(A,B)=>{if(A.statusCode>=300)return k22(A,B);let Q=await G60.parseXmlBody(A.body,B),D={};return D=vD4(Q.AssumeRoleWithWebIdentityResult,B),{$metadata:ST(A),...D}},"de_AssumeRoleWithWebIdentityCommand"),k22=D9(async(A,B)=>{let Q={...A,body:await G60.parseXmlErrorBody(A.body,B)},D=iD4(A,Q.body);switch(D){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await wD4(Q,B);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await LD4(Q,B);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await MD4(Q,B);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await RD4(Q,B);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await $D4(Q,B);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await qD4(Q,B);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await ND4(Q,B);default:let Z=Q.body;return cD4({output:A,parsedBody:Z.Error,errorCode:D})}},"de_CommandError"),wD4=D9(async(A,B)=>{let Q=A.body,D=bD4(Q.Error,B),Z=new N22({$metadata:ST(A),...D});return X5.decorateServiceException(Z,Q)},"de_ExpiredTokenExceptionRes"),$D4=D9(async(A,B)=>{let Q=A.body,D=fD4(Q.Error,B),Z=new j22({$metadata:ST(A),...D});return X5.decorateServiceException(Z,Q)},"de_IDPCommunicationErrorExceptionRes"),qD4=D9(async(A,B)=>{let Q=A.body,D=hD4(Q.Error,B),Z=new O22({$metadata:ST(A),...D});return X5.decorateServiceException(Z,Q)},"de_IDPRejectedClaimExceptionRes"),ND4=D9(async(A,B)=>{let Q=A.body,D=gD4(Q.Error,B),Z=new T22({$metadata:ST(A),...D});return X5.decorateServiceException(Z,Q)},"de_InvalidIdentityTokenExceptionRes"),LD4=D9(async(A,B)=>{let Q=A.body,D=uD4(Q.Error,B),Z=new L22({$metadata:ST(A),...D});return X5.decorateServiceException(Z,Q)},"de_MalformedPolicyDocumentExceptionRes"),MD4=D9(async(A,B)=>{let Q=A.body,D=mD4(Q.Error,B),Z=new M22({$metadata:ST(A),...D});return X5.decorateServiceException(Z,Q)},"de_PackedPolicyTooLargeExceptionRes"),RD4=D9(async(A,B)=>{let Q=A.body,D=dD4(Q.Error,B),Z=new R22({$metadata:ST(A),...D});return X5.decorateServiceException(Z,Q)},"de_RegionDisabledExceptionRes"),OD4=D9((A,B)=>{let Q={};if(A[Es]!=null)Q[Es]=A[Es];if(A[Us]!=null)Q[Us]=A[Us];if(A[Hs]!=null){let D=y22(A[Hs],B);if(A[Hs]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Ks]!=null)Q[Ks]=A[Ks];if(A[Cs]!=null)Q[Cs]=A[Cs];if(A[s40]!=null){let D=_D4(A[s40],B);if(A[s40]?.length===0)Q.Tags=[];Object.entries(D).forEach(([Z,G])=>{let F=`Tags.${Z}`;Q[F]=G})}if(A[o40]!=null){let D=yD4(A[o40],B);if(A[o40]?.length===0)Q.TransitiveTagKeys=[];Object.entries(D).forEach(([Z,G])=>{let F=`TransitiveTagKeys.${Z}`;Q[F]=G})}if(A[g40]!=null)Q[g40]=A[g40];if(A[n40]!=null)Q[n40]=A[n40];if(A[r40]!=null)Q[r40]=A[r40];if(A[TT]!=null)Q[TT]=A[TT];if(A[d40]!=null){let D=jD4(A[d40],B);if(A[d40]?.length===0)Q.ProvidedContexts=[];Object.entries(D).forEach(([Z,G])=>{let F=`ProvidedContexts.${Z}`;Q[F]=G})}return Q},"se_AssumeRoleRequest"),TD4=D9((A,B)=>{let Q={};if(A[Es]!=null)Q[Es]=A[Es];if(A[Us]!=null)Q[Us]=A[Us];if(A[e40]!=null)Q[e40]=A[e40];if(A[c40]!=null)Q[c40]=A[c40];if(A[Hs]!=null){let D=y22(A[Hs],B);if(A[Hs]?.length===0)Q.PolicyArns=[];Object.entries(D).forEach(([Z,G])=>{let F=`PolicyArns.${Z}`;Q[F]=G})}if(A[Ks]!=null)Q[Ks]=A[Ks];if(A[Cs]!=null)Q[Cs]=A[Cs];return Q},"se_AssumeRoleWithWebIdentityRequest"),y22=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=PD4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_policyDescriptorListType"),PD4=D9((A,B)=>{let Q={};if(A[A60]!=null)Q[A60]=A[A60];return Q},"se_PolicyDescriptorType"),SD4=D9((A,B)=>{let Q={};if(A[m40]!=null)Q[m40]=A[m40];if(A[f40]!=null)Q[f40]=A[f40];return Q},"se_ProvidedContext"),jD4=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=SD4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_ProvidedContextsListType"),kD4=D9((A,B)=>{let Q={};if(A[u40]!=null)Q[u40]=A[u40];if(A[t40]!=null)Q[t40]=A[t40];return Q},"se_Tag"),yD4=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;Q[`member.${D}`]=Z,D++}return Q},"se_tagKeyListType"),_D4=D9((A,B)=>{let Q={},D=1;for(let Z of A){if(Z===null)continue;let G=kD4(Z,B);Object.entries(G).forEach(([F,I])=>{Q[`member.${D}.${F}`]=I}),D++}return Q},"se_tagListType"),_22=D9((A,B)=>{let Q={};if(A[x40]!=null)Q[x40]=X5.expectString(A[x40]);if(A[v40]!=null)Q[v40]=X5.expectString(A[v40]);return Q},"de_AssumedRoleUser"),xD4=D9((A,B)=>{let Q={};if(A[Vs]!=null)Q[Vs]=x22(A[Vs],B);if(A[Xs]!=null)Q[Xs]=_22(A[Xs],B);if(A[zs]!=null)Q[zs]=X5.strictParseInt32(A[zs]);if(A[TT]!=null)Q[TT]=X5.expectString(A[TT]);return Q},"de_AssumeRoleResponse"),vD4=D9((A,B)=>{let Q={};if(A[Vs]!=null)Q[Vs]=x22(A[Vs],B);if(A[i40]!=null)Q[i40]=X5.expectString(A[i40]);if(A[Xs]!=null)Q[Xs]=_22(A[Xs],B);if(A[zs]!=null)Q[zs]=X5.strictParseInt32(A[zs]);if(A[l40]!=null)Q[l40]=X5.expectString(A[l40]);if(A[b40]!=null)Q[b40]=X5.expectString(A[b40]);if(A[TT]!=null)Q[TT]=X5.expectString(A[TT]);return Q},"de_AssumeRoleWithWebIdentityResponse"),x22=D9((A,B)=>{let Q={};if(A[_40]!=null)Q[_40]=X5.expectString(A[_40]);if(A[p40]!=null)Q[p40]=X5.expectString(A[p40]);if(A[a40]!=null)Q[a40]=X5.expectString(A[a40]);if(A[h40]!=null)Q[h40]=X5.expectNonNull(X5.parseRfc3339DateTimeWithOffset(A[h40]));return Q},"de_Credentials"),bD4=D9((A,B)=>{let Q={};if(A[iZ]!=null)Q[iZ]=X5.expectString(A[iZ]);return Q},"de_ExpiredTokenException"),fD4=D9((A,B)=>{let Q={};if(A[iZ]!=null)Q[iZ]=X5.expectString(A[iZ]);return Q},"de_IDPCommunicationErrorException"),hD4=D9((A,B)=>{let Q={};if(A[iZ]!=null)Q[iZ]=X5.expectString(A[iZ]);return Q},"de_IDPRejectedClaimException"),gD4=D9((A,B)=>{let Q={};if(A[iZ]!=null)Q[iZ]=X5.expectString(A[iZ]);return Q},"de_InvalidIdentityTokenException"),uD4=D9((A,B)=>{let Q={};if(A[iZ]!=null)Q[iZ]=X5.expectString(A[iZ]);return Q},"de_MalformedPolicyDocumentException"),mD4=D9((A,B)=>{let Q={};if(A[iZ]!=null)Q[iZ]=X5.expectString(A[iZ]);return Q},"de_PackedPolicyTooLargeException"),dD4=D9((A,B)=>{let Q={};if(A[iZ]!=null)Q[iZ]=X5.expectString(A[iZ]);return Q},"de_RegionDisabledException"),ST=D9((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),cD4=X5.withBaseException(PT),v22=D9(async(A,B,Q,D,Z)=>{let{hostname:G,protocol:F="https",port:I,path:Y}=await A.endpoint(),W={protocol:F,hostname:G,port:I,method:"POST",path:Y.endsWith("/")?Y.slice(0,-1)+Q:Y+Q,headers:B};if(D!==void 0)W.hostname=D;if(Z!==void 0)W.body=Z;return new KD4.HttpRequest(W)},"buildHttpRpcRequest"),b22={"content-type":"application/x-www-form-urlencoded"},f22="2011-06-15",h22="Action",_40="AccessKeyId",lD4="AssumeRole",x40="AssumedRoleId",Xs="AssumedRoleUser",pD4="AssumeRoleWithWebIdentity",v40="Arn",b40="Audience",Vs="Credentials",f40="ContextAssertion",Cs="DurationSeconds",h40="Expiration",g40="ExternalId",u40="Key",Ks="Policy",Hs="PolicyArns",m40="ProviderArn",d40="ProvidedContexts",c40="ProviderId",zs="PackedPolicySize",l40="Provider",Es="RoleArn",Us="RoleSessionName",p40="SecretAccessKey",i40="SubjectFromWebIdentityToken",TT="SourceIdentity",n40="SerialNumber",a40="SessionToken",s40="Tags",r40="TokenCode",o40="TransitiveTagKeys",g22="Version",t40="Value",e40="WebIdentityToken",A60="arn",iZ="message",u22=D9((A)=>Object.entries(A).map(([B,Q])=>X5.extendedEncodeURIComponent(B)+"="+X5.extendedEncodeURIComponent(Q)).join("&"),"buildFormUrlencodedString"),iD4=D9((A,B)=>{if(B.Error?.Code!==void 0)return B.Error.Code;if(A.statusCode==404)return"NotFound"},"loadQueryErrorCode"),F60=class extends XD4.Command.classBuilder().ep(VD4.commonParams).m(function(A,B,Q,D){return[JD4.getSerdePlugin(Q,this.serialize,this.deserialize),WD4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,q22).ser(HD4).de(ED4).build(){static{D9(this,"AssumeRoleCommand")}},nD4=q6(),aD4=T3(),sD4=J6(),rD4=O61(),I60=class extends sD4.Command.classBuilder().ep(rD4.commonParams).m(function(A,B,Q,D){return[aD4.getSerdePlugin(Q,this.serialize,this.deserialize),nD4.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(P22,S22).ser(zD4).de(UD4).build(){static{D9(this,"AssumeRoleWithWebIdentityCommand")}},oD4=R61(),tD4={AssumeRoleCommand:F60,AssumeRoleWithWebIdentityCommand:I60},m22=class extends oD4.STSClient{static{D9(this,"STS")}};YD4.createAggregatedClient(tD4,m22);var eD4=O61(),Q60=Tw(),w22="us-east-1",d22=D9((A)=>{if(typeof A?.Arn==="string"){let B=A.Arn.split(":");if(B.length>4&&B[4]!=="")return B[4]}return},"getAccountIdFromAssumedRoleUser"),c22=D9(async(A,B,Q)=>{let D=typeof A==="function"?await A():A,Z=typeof B==="function"?await B():B;return Q?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${D} (provider)`,`${Z} (parent client)`,`${w22} (STS default)`),D??Z??w22},"resolveRegion"),AZ4=D9((A,B)=>{let Q,D;return async(Z,G)=>{if(D=Z,!Q){let{logger:J=A?.parentClientConfig?.logger,region:X,requestHandler:V=A?.parentClientConfig?.requestHandler,credentialProviderLogger:C}=A,K=await c22(X,A?.parentClientConfig?.region,C),H=!l22(V);Q=new B({profile:A?.parentClientConfig?.profile,credentialDefaultProvider:D9(()=>async()=>D,"credentialDefaultProvider"),region:K,requestHandler:H?V:void 0,logger:J})}let{Credentials:F,AssumedRoleUser:I}=await Q.send(new F60(G));if(!F||!F.AccessKeyId||!F.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRole call with role ${G.RoleArn}`);let Y=d22(I),W={accessKeyId:F.AccessKeyId,secretAccessKey:F.SecretAccessKey,sessionToken:F.SessionToken,expiration:F.Expiration,...F.CredentialScope&&{credentialScope:F.CredentialScope},...Y&&{accountId:Y}};return Q60.setCredentialFeature(W,"CREDENTIALS_STS_ASSUME_ROLE","i"),W}},"getDefaultRoleAssumer"),BZ4=D9((A,B)=>{let Q;return async(D)=>{if(!Q){let{logger:Y=A?.parentClientConfig?.logger,region:W,requestHandler:J=A?.parentClientConfig?.requestHandler,credentialProviderLogger:X}=A,V=await c22(W,A?.parentClientConfig?.region,X),C=!l22(J);Q=new B({profile:A?.parentClientConfig?.profile,region:V,requestHandler:C?J:void 0,logger:Y})}let{Credentials:Z,AssumedRoleUser:G}=await Q.send(new I60(D));if(!Z||!Z.AccessKeyId||!Z.SecretAccessKey)throw new Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${D.RoleArn}`);let F=d22(G),I={accessKeyId:Z.AccessKeyId,secretAccessKey:Z.SecretAccessKey,sessionToken:Z.SessionToken,expiration:Z.Expiration,...Z.CredentialScope&&{credentialScope:Z.CredentialScope},...F&&{accountId:F}};if(F)Q60.setCredentialFeature(I,"RESOLVED_ACCOUNT_ID","T");return Q60.setCredentialFeature(I,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),I}},"getDefaultRoleAssumerWithWebIdentity"),l22=D9((A)=>{return A?.metadata?.handlerProtocol==="h2"},"isH2"),p22=R61(),i22=D9((A,B)=>{if(!B)return A;else return class Q extends A{static{D9(this,"CustomizableSTSClient")}constructor(D){super(D);for(let Z of B)this.middlewareStack.use(Z)}}},"getCustomizableStsClientCtor"),n22=D9((A={},B)=>AZ4(A,i22(p22.STSClient,B)),"getDefaultRoleAssumer"),a22=D9((A={},B)=>BZ4(A,i22(p22.STSClient,B)),"getDefaultRoleAssumerWithWebIdentity"),QZ4=D9((A)=>(B)=>A({roleAssumer:n22(B),roleAssumerWithWebIdentity:a22(B),...B}),"decorateDefaultCredentialProvider")});
var W61=E((gJ5,$rA)=>{var{defineProperty:Dq1,getOwnPropertyDescriptor:SB4,getOwnPropertyNames:jB4}=Object,kB4=Object.prototype.hasOwnProperty,XQ0=(A,B)=>Dq1(A,"name",{value:B,configurable:!0}),yB4=(A,B)=>{for(var Q in B)Dq1(A,Q,{get:B[Q],enumerable:!0})},_B4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jB4(B))if(!kB4.call(A,Z)&&Z!==Q)Dq1(A,Z,{get:()=>B[Z],enumerable:!(D=SB4(B,Z))||D.enumerable})}return A},xB4=(A)=>_B4(Dq1({},"__esModule",{value:!0}),A),ErA={};yB4(ErA,{getLoggerPlugin:()=>vB4,loggerMiddleware:()=>UrA,loggerMiddlewareOptions:()=>wrA});$rA.exports=xB4(ErA);var UrA=XQ0(()=>(A,B)=>async(Q)=>{try{let D=await A(Q),{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y,overrideOutputFilterSensitiveLog:W}=I,J=Y??B.inputFilterSensitiveLog,X=W??B.outputFilterSensitiveLog,{$metadata:V,...C}=D.output;return F?.info?.({clientName:Z,commandName:G,input:J(Q.input),output:X(C),metadata:V}),D}catch(D){let{clientName:Z,commandName:G,logger:F,dynamoDbDocumentClientOptions:I={}}=B,{overrideInputFilterSensitiveLog:Y}=I,W=Y??B.inputFilterSensitiveLog;throw F?.error?.({clientName:Z,commandName:G,input:W(Q.input),error:D,metadata:D.$metadata}),D}},"loggerMiddleware"),wrA={name:"loggerMiddleware",tags:["LOGGER"],step:"initialize",override:!0},vB4=XQ0((A)=>({applyToStack:XQ0((B)=>{B.add(UrA(),wrA)},"applyToStack")}),"getLoggerPlugin")});
var W62=E((yV5,Y62)=>{var{defineProperty:FN1,getOwnPropertyDescriptor:SG4,getOwnPropertyNames:jG4}=Object,kG4=Object.prototype.hasOwnProperty,p=(A,B)=>FN1(A,"name",{value:B,configurable:!0}),yG4=(A,B)=>{for(var Q in B)FN1(A,Q,{get:B[Q],enumerable:!0})},_G4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of jG4(B))if(!kG4.call(A,Z)&&Z!==Q)FN1(A,Z,{get:()=>B[Z],enumerable:!(D=SG4(B,Z))||D.enumerable})}return A},xG4=(A)=>_G4(FN1({},"__esModule",{value:!0}),A),q92={};yG4(q92,{AccessDeniedException:()=>N92,AgreementStatus:()=>lG4,ApplicationType:()=>oG4,AttributeType:()=>QF4,AuthorizationStatus:()=>gF4,AutomatedEvaluationConfigFilterSensitiveLog:()=>m92,AutomatedEvaluationCustomMetricConfigFilterSensitiveLog:()=>g92,AutomatedEvaluationCustomMetricSource:()=>eq1,AutomatedEvaluationCustomMetricSourceFilterSensitiveLog:()=>h92,BatchDeleteEvaluationJobCommand:()=>I42,BatchDeleteEvaluationJobErrorFilterSensitiveLog:()=>v92,BatchDeleteEvaluationJobItemFilterSensitiveLog:()=>b92,BatchDeleteEvaluationJobRequestFilterSensitiveLog:()=>x92,BatchDeleteEvaluationJobResponseFilterSensitiveLog:()=>f92,Bedrock:()=>I62,BedrockClient:()=>EV,BedrockServiceException:()=>kw,ByteContentDocFilterSensitiveLog:()=>a92,CommitmentDuration:()=>bF4,ConflictException:()=>T92,CreateCustomModelCommand:()=>Y42,CreateEvaluationJobCommand:()=>W42,CreateEvaluationJobRequestFilterSensitiveLog:()=>aQ2,CreateFoundationModelAgreementCommand:()=>J42,CreateGuardrailCommand:()=>X42,CreateGuardrailRequestFilterSensitiveLog:()=>VQ2,CreateGuardrailVersionCommand:()=>V42,CreateGuardrailVersionRequestFilterSensitiveLog:()=>CQ2,CreateInferenceProfileCommand:()=>C42,CreateInferenceProfileRequestFilterSensitiveLog:()=>SQ2,CreateMarketplaceModelEndpointCommand:()=>K42,CreateModelCopyJobCommand:()=>H42,CreateModelCustomizationJobCommand:()=>z42,CreateModelCustomizationJobRequestFilterSensitiveLog:()=>uQ2,CreateModelImportJobCommand:()=>E42,CreateModelInvocationJobCommand:()=>U42,CreatePromptRouterCommand:()=>w42,CreatePromptRouterRequestFilterSensitiveLog:()=>bQ2,CreateProvisionedModelThroughputCommand:()=>$42,CustomMetricDefinitionFilterSensitiveLog:()=>uF4,CustomizationConfig:()=>R60,CustomizationType:()=>iG4,DeleteCustomModelCommand:()=>q42,DeleteFoundationModelAgreementCommand:()=>N42,DeleteGuardrailCommand:()=>L42,DeleteImportedModelCommand:()=>M42,DeleteInferenceProfileCommand:()=>R42,DeleteMarketplaceModelEndpointCommand:()=>O42,DeleteModelInvocationLoggingConfigurationCommand:()=>T42,DeletePromptRouterCommand:()=>P42,DeleteProvisionedModelThroughputCommand:()=>S42,DeregisterMarketplaceModelEndpointCommand:()=>j42,EndpointConfig:()=>L60,EntitlementAvailability:()=>dF4,EvaluationBedrockModelFilterSensitiveLog:()=>p92,EvaluationConfig:()=>AN1,EvaluationConfigFilterSensitiveLog:()=>g60,EvaluationDatasetFilterSensitiveLog:()=>u92,EvaluationDatasetLocation:()=>P60,EvaluationDatasetMetricConfigFilterSensitiveLog:()=>h60,EvaluationInferenceConfig:()=>ZN1,EvaluationInferenceConfigFilterSensitiveLog:()=>i60,EvaluationJobStatus:()=>rG4,EvaluationJobType:()=>IF4,EvaluationModelConfig:()=>j60,EvaluationModelConfigFilterSensitiveLog:()=>i92,EvaluationPrecomputedRagSourceConfig:()=>y60,EvaluationTaskType:()=>tG4,EvaluatorModelConfig:()=>S60,ExternalSourceFilterSensitiveLog:()=>s92,ExternalSourceType:()=>AF4,ExternalSourcesGenerationConfigurationFilterSensitiveLog:()=>n92,ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog:()=>r92,FineTuningJobStatus:()=>nF4,FoundationModelLifecycleStatus:()=>_F4,GenerationConfigurationFilterSensitiveLog:()=>o92,GetCustomModelCommand:()=>k42,GetCustomModelResponseFilterSensitiveLog:()=>_92,GetEvaluationJobCommand:()=>y42,GetEvaluationJobRequestFilterSensitiveLog:()=>DQ2,GetEvaluationJobResponseFilterSensitiveLog:()=>sQ2,GetFoundationModelAvailabilityCommand:()=>_42,GetFoundationModelCommand:()=>x42,GetGuardrailCommand:()=>v42,GetGuardrailResponseFilterSensitiveLog:()=>RQ2,GetImportedModelCommand:()=>b42,GetInferenceProfileCommand:()=>f42,GetInferenceProfileResponseFilterSensitiveLog:()=>jQ2,GetMarketplaceModelEndpointCommand:()=>h42,GetModelCopyJobCommand:()=>g42,GetModelCustomizationJobCommand:()=>u42,GetModelCustomizationJobResponseFilterSensitiveLog:()=>mQ2,GetModelImportJobCommand:()=>m42,GetModelInvocationJobCommand:()=>d42,GetModelInvocationJobResponseFilterSensitiveLog:()=>_Q2,GetModelInvocationLoggingConfigurationCommand:()=>c42,GetPromptRouterCommand:()=>l42,GetPromptRouterResponseFilterSensitiveLog:()=>fQ2,GetProvisionedModelThroughputCommand:()=>p42,GetUseCaseForModelAccessCommand:()=>i42,GuardrailContentFilterAction:()=>WF4,GuardrailContentFilterConfigFilterSensitiveLog:()=>GQ2,GuardrailContentFilterFilterSensitiveLog:()=>KQ2,GuardrailContentFilterType:()=>VF4,GuardrailContentFiltersTierConfigFilterSensitiveLog:()=>FQ2,GuardrailContentFiltersTierFilterSensitiveLog:()=>HQ2,GuardrailContentFiltersTierName:()=>CF4,GuardrailContentPolicyConfigFilterSensitiveLog:()=>m60,GuardrailContentPolicyFilterSensitiveLog:()=>zQ2,GuardrailContextualGroundingAction:()=>KF4,GuardrailContextualGroundingFilterConfigFilterSensitiveLog:()=>IQ2,GuardrailContextualGroundingFilterFilterSensitiveLog:()=>EQ2,GuardrailContextualGroundingFilterType:()=>HF4,GuardrailContextualGroundingPolicyConfigFilterSensitiveLog:()=>d60,GuardrailContextualGroundingPolicyFilterSensitiveLog:()=>UQ2,GuardrailFilterStrength:()=>XF4,GuardrailManagedWordsConfigFilterSensitiveLog:()=>JQ2,GuardrailManagedWordsFilterSensitiveLog:()=>NQ2,GuardrailManagedWordsType:()=>NF4,GuardrailModality:()=>JF4,GuardrailPiiEntityType:()=>EF4,GuardrailSensitiveInformationAction:()=>zF4,GuardrailStatus:()=>LF4,GuardrailSummaryFilterSensitiveLog:()=>OQ2,GuardrailTopicAction:()=>wF4,GuardrailTopicConfigFilterSensitiveLog:()=>WQ2,GuardrailTopicFilterSensitiveLog:()=>$Q2,GuardrailTopicPolicyConfigFilterSensitiveLog:()=>c60,GuardrailTopicPolicyFilterSensitiveLog:()=>qQ2,GuardrailTopicType:()=>$F4,GuardrailTopicsTierConfigFilterSensitiveLog:()=>YQ2,GuardrailTopicsTierFilterSensitiveLog:()=>wQ2,GuardrailTopicsTierName:()=>UF4,GuardrailWordAction:()=>qF4,GuardrailWordConfigFilterSensitiveLog:()=>XQ2,GuardrailWordFilterSensitiveLog:()=>LQ2,GuardrailWordPolicyConfigFilterSensitiveLog:()=>l60,GuardrailWordPolicyFilterSensitiveLog:()=>MQ2,HumanEvaluationConfigFilterSensitiveLog:()=>l92,HumanEvaluationCustomMetricFilterSensitiveLog:()=>d92,HumanWorkflowConfigFilterSensitiveLog:()=>c92,ImplicitFilterConfigurationFilterSensitiveLog:()=>t92,InferenceProfileModelSource:()=>_60,InferenceProfileStatus:()=>MF4,InferenceProfileSummaryFilterSensitiveLog:()=>kQ2,InferenceProfileType:()=>RF4,InferenceType:()=>kF4,InternalServerException:()=>L92,InvocationLogSource:()=>O60,InvocationLogsConfigFilterSensitiveLog:()=>y92,JobStatusDetails:()=>iF4,KnowledgeBaseConfig:()=>QN1,KnowledgeBaseConfigFilterSensitiveLog:()=>iQ2,KnowledgeBaseRetrievalConfigurationFilterSensitiveLog:()=>p60,KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog:()=>cQ2,KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog:()=>dQ2,ListCustomModelsCommand:()=>s60,ListEvaluationJobsCommand:()=>r60,ListFoundationModelAgreementOffersCommand:()=>n42,ListFoundationModelsCommand:()=>a42,ListGuardrailsCommand:()=>o60,ListGuardrailsResponseFilterSensitiveLog:()=>TQ2,ListImportedModelsCommand:()=>t60,ListInferenceProfilesCommand:()=>e60,ListInferenceProfilesResponseFilterSensitiveLog:()=>yQ2,ListMarketplaceModelEndpointsCommand:()=>A80,ListModelCopyJobsCommand:()=>B80,ListModelCustomizationJobsCommand:()=>Q80,ListModelImportJobsCommand:()=>D80,ListModelInvocationJobsCommand:()=>Z80,ListModelInvocationJobsResponseFilterSensitiveLog:()=>vQ2,ListPromptRoutersCommand:()=>G80,ListPromptRoutersResponseFilterSensitiveLog:()=>gQ2,ListProvisionedModelThroughputsCommand:()=>F80,ListTagsForResourceCommand:()=>s42,MetadataAttributeSchemaFilterSensitiveLog:()=>mF4,MetadataConfigurationForRerankingFilterSensitiveLog:()=>AQ2,ModelCopyJobStatus:()=>OF4,ModelCustomization:()=>jF4,ModelCustomizationJobStatus:()=>pF4,ModelDataSource:()=>M60,ModelImportJobStatus:()=>TF4,ModelInvocationJobInputDataConfig:()=>x60,ModelInvocationJobOutputDataConfig:()=>v60,ModelInvocationJobStatus:()=>SF4,ModelInvocationJobSummaryFilterSensitiveLog:()=>xQ2,ModelModality:()=>yF4,ModelStatus:()=>nG4,OfferType:()=>lF4,PerformanceConfigLatency:()=>eG4,PromptRouterStatus:()=>xF4,PromptRouterSummaryFilterSensitiveLog:()=>hQ2,PromptRouterType:()=>vF4,PromptTemplateFilterSensitiveLog:()=>u60,ProvisionedModelStatus:()=>fF4,PutModelInvocationLoggingConfigurationCommand:()=>r42,PutUseCaseForModelAccessCommand:()=>o42,QueryTransformationType:()=>BF4,RAGConfig:()=>DN1,RAGConfigFilterSensitiveLog:()=>nQ2,RatingScaleItemValue:()=>tq1,RegionAvailability:()=>cF4,RegisterMarketplaceModelEndpointCommand:()=>t42,RequestMetadataBaseFiltersFilterSensitiveLog:()=>b60,RequestMetadataFilters:()=>T60,RequestMetadataFiltersFilterSensitiveLog:()=>k92,RerankingMetadataSelectionMode:()=>ZF4,RerankingMetadataSelectiveModeConfiguration:()=>k60,RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog:()=>e92,ResourceNotFoundException:()=>M92,RetrievalFilter:()=>BN1,RetrievalFilterFilterSensitiveLog:()=>aF4,RetrieveAndGenerateConfigurationFilterSensitiveLog:()=>pQ2,RetrieveAndGenerateType:()=>FF4,RetrieveConfigFilterSensitiveLog:()=>lQ2,S3InputFormat:()=>PF4,SearchType:()=>DF4,ServiceQuotaExceededException:()=>P92,ServiceUnavailableException:()=>S92,SortByProvisionedModels:()=>hF4,SortJobsBy:()=>YF4,SortModelsBy:()=>aG4,SortOrder:()=>sG4,Status:()=>pG4,StopEvaluationJobCommand:()=>e42,StopEvaluationJobRequestFilterSensitiveLog:()=>ZQ2,StopModelCustomizationJobCommand:()=>A62,StopModelInvocationJobCommand:()=>B62,TagResourceCommand:()=>Q62,ThrottlingException:()=>R92,TooManyTagsException:()=>j92,TrainingDataConfigFilterSensitiveLog:()=>IN1,UntagResourceCommand:()=>D62,UpdateGuardrailCommand:()=>Z62,UpdateGuardrailRequestFilterSensitiveLog:()=>PQ2,UpdateMarketplaceModelEndpointCommand:()=>G62,UpdateProvisionedModelThroughputCommand:()=>F62,ValidationException:()=>O92,VectorSearchBedrockRerankingConfigurationFilterSensitiveLog:()=>BQ2,VectorSearchRerankingConfigurationFilterSensitiveLog:()=>QQ2,VectorSearchRerankingConfigurationType:()=>GF4,__Client:()=>_.Client,paginateListCustomModels:()=>wX4,paginateListEvaluationJobs:()=>$X4,paginateListGuardrails:()=>qX4,paginateListImportedModels:()=>NX4,paginateListInferenceProfiles:()=>LX4,paginateListMarketplaceModelEndpoints:()=>MX4,paginateListModelCopyJobs:()=>RX4,paginateListModelCustomizationJobs:()=>OX4,paginateListModelImportJobs:()=>TX4,paginateListModelInvocationJobs:()=>PX4,paginateListPromptRouters:()=>SX4,paginateListProvisionedModelThroughputs:()=>jX4});Y62.exports=xG4(q92);var oB2=Y61(),vG4=W61(),bG4=J61(),tB2=Bs(),fG4=V4(),$2=VB(),hG4=TG(),$B=q6(),eB2=v4(),A92=tQ0(),gG4=p((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"bedrock"})},"resolveClientEndpointParameters"),PB={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},uG4=rB2(),B92=U61(),Q92=CV(),_=J6(),mG4=p((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D,token:Z}=A;return{setHttpAuthScheme(G){let F=B.findIndex((I)=>I.schemeId===G.schemeId);if(F===-1)B.push(G);else B.splice(F,1,G)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(G){Q=G},httpAuthSchemeProvider(){return Q},setCredentials(G){D=G},credentials(){return D},setToken(G){Z=G},token(){return Z}}},"getHttpAuthExtensionConfiguration"),dG4=p((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials(),token:A.token()}},"resolveHttpAuthRuntimeConfig"),cG4=p((A,B)=>{let Q=Object.assign(B92.getAwsRegionExtensionConfiguration(A),_.getDefaultExtensionConfiguration(A),Q92.getHttpHandlerExtensionConfiguration(A),mG4(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,B92.resolveAwsRegionExtensionConfiguration(Q),_.resolveDefaultRuntimeConfig(Q),Q92.resolveHttpHandlerRuntimeConfig(Q),dG4(Q))},"resolveRuntimeExtensions"),EV=class extends _.Client{static{p(this,"BedrockClient")}config;constructor(...[A]){let B=uG4.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=gG4(B),D=tB2.resolveUserAgentConfig(Q),Z=eB2.resolveRetryConfig(D),G=fG4.resolveRegionConfig(Z),F=oB2.resolveHostHeaderConfig(G),I=$B.resolveEndpointConfig(F),Y=A92.resolveHttpAuthSchemeConfig(I),W=cG4(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(tB2.getUserAgentPlugin(this.config)),this.middlewareStack.use(eB2.getRetryPlugin(this.config)),this.middlewareStack.use(hG4.getContentLengthPlugin(this.config)),this.middlewareStack.use(oB2.getHostHeaderPlugin(this.config)),this.middlewareStack.use(vG4.getLoggerPlugin(this.config)),this.middlewareStack.use(bG4.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use($2.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:A92.defaultBedrockHttpAuthSchemeParametersProvider,identityProviderConfigProvider:p(async(J)=>new $2.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials,"smithy.api#httpBearerAuth":J.token}),"identityProviderConfigProvider")})),this.middlewareStack.use($2.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},SB=T3(),kw=class A extends _.ServiceException{static{p(this,"BedrockServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},N92=class A extends kw{static{p(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},lG4={AVAILABLE:"AVAILABLE",ERROR:"ERROR",NOT_AVAILABLE:"NOT_AVAILABLE",PENDING:"PENDING"},L92=class A extends kw{static{p(this,"InternalServerException")}name="InternalServerException";$fault="server";constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},M92=class A extends kw{static{p(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},R92=class A extends kw{static{p(this,"ThrottlingException")}name="ThrottlingException";$fault="client";constructor(B){super({name:"ThrottlingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},O92=class A extends kw{static{p(this,"ValidationException")}name="ValidationException";$fault="client";constructor(B){super({name:"ValidationException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},T92=class A extends kw{static{p(this,"ConflictException")}name="ConflictException";$fault="client";constructor(B){super({name:"ConflictException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},L60;((A)=>{A.visit=p((B,Q)=>{if(B.sageMaker!==void 0)return Q.sageMaker(B.sageMaker);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(L60||(L60={}));var pG4={INCOMPATIBLE_ENDPOINT:"INCOMPATIBLE_ENDPOINT",REGISTERED:"REGISTERED"},P92=class A extends kw{static{p(this,"ServiceQuotaExceededException")}name="ServiceQuotaExceededException";$fault="client";constructor(B){super({name:"ServiceQuotaExceededException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},S92=class A extends kw{static{p(this,"ServiceUnavailableException")}name="ServiceUnavailableException";$fault="server";constructor(B){super({name:"ServiceUnavailableException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype)}},M60;((A)=>{A.visit=p((B,Q)=>{if(B.s3DataSource!==void 0)return Q.s3DataSource(B.s3DataSource);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(M60||(M60={}));var j92=class A extends kw{static{p(this,"TooManyTagsException")}name="TooManyTagsException";$fault="client";resourceName;constructor(B){super({name:"TooManyTagsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.resourceName=B.resourceName}},R60;((A)=>{A.visit=p((B,Q)=>{if(B.distillationConfig!==void 0)return Q.distillationConfig(B.distillationConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(R60||(R60={}));var iG4={CONTINUED_PRE_TRAINING:"CONTINUED_PRE_TRAINING",DISTILLATION:"DISTILLATION",FINE_TUNING:"FINE_TUNING",IMPORTED:"IMPORTED"},nG4={ACTIVE:"Active",CREATING:"Creating",FAILED:"Failed"},O60;((A)=>{A.visit=p((B,Q)=>{if(B.s3Uri!==void 0)return Q.s3Uri(B.s3Uri);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(O60||(O60={}));var T60;((A)=>{A.visit=p((B,Q)=>{if(B.equals!==void 0)return Q.equals(B.equals);if(B.notEquals!==void 0)return Q.notEquals(B.notEquals);if(B.andAll!==void 0)return Q.andAll(B.andAll);if(B.orAll!==void 0)return Q.orAll(B.orAll);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(T60||(T60={}));var aG4={CREATION_TIME:"CreationTime"},sG4={ASCENDING:"Ascending",DESCENDING:"Descending"},rG4={COMPLETED:"Completed",DELETING:"Deleting",FAILED:"Failed",IN_PROGRESS:"InProgress",STOPPED:"Stopped",STOPPING:"Stopping"},oG4={MODEL_EVALUATION:"ModelEvaluation",RAG_EVALUATION:"RagEvaluation"},tq1;((A)=>{A.visit=p((B,Q)=>{if(B.stringValue!==void 0)return Q.stringValue(B.stringValue);if(B.floatValue!==void 0)return Q.floatValue(B.floatValue);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(tq1||(tq1={}));var eq1;((A)=>{A.visit=p((B,Q)=>{if(B.customMetricDefinition!==void 0)return Q.customMetricDefinition(B.customMetricDefinition);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(eq1||(eq1={}));var P60;((A)=>{A.visit=p((B,Q)=>{if(B.s3Uri!==void 0)return Q.s3Uri(B.s3Uri);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(P60||(P60={}));var tG4={CLASSIFICATION:"Classification",CUSTOM:"Custom",GENERATION:"Generation",QUESTION_AND_ANSWER:"QuestionAndAnswer",SUMMARIZATION:"Summarization"},S60;((A)=>{A.visit=p((B,Q)=>{if(B.bedrockEvaluatorModels!==void 0)return Q.bedrockEvaluatorModels(B.bedrockEvaluatorModels);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(S60||(S60={}));var AN1;((A)=>{A.visit=p((B,Q)=>{if(B.automated!==void 0)return Q.automated(B.automated);if(B.human!==void 0)return Q.human(B.human);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(AN1||(AN1={}));var eG4={OPTIMIZED:"optimized",STANDARD:"standard"},j60;((A)=>{A.visit=p((B,Q)=>{if(B.bedrockModel!==void 0)return Q.bedrockModel(B.bedrockModel);if(B.precomputedInferenceSource!==void 0)return Q.precomputedInferenceSource(B.precomputedInferenceSource);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(j60||(j60={}));var AF4={BYTE_CONTENT:"BYTE_CONTENT",S3:"S3"},BF4={QUERY_DECOMPOSITION:"QUERY_DECOMPOSITION"},QF4={BOOLEAN:"BOOLEAN",NUMBER:"NUMBER",STRING:"STRING",STRING_LIST:"STRING_LIST"},DF4={HYBRID:"HYBRID",SEMANTIC:"SEMANTIC"},ZF4={ALL:"ALL",SELECTIVE:"SELECTIVE"},k60;((A)=>{A.visit=p((B,Q)=>{if(B.fieldsToInclude!==void 0)return Q.fieldsToInclude(B.fieldsToInclude);if(B.fieldsToExclude!==void 0)return Q.fieldsToExclude(B.fieldsToExclude);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(k60||(k60={}));var GF4={BEDROCK_RERANKING_MODEL:"BEDROCK_RERANKING_MODEL"},FF4={EXTERNAL_SOURCES:"EXTERNAL_SOURCES",KNOWLEDGE_BASE:"KNOWLEDGE_BASE"},y60;((A)=>{A.visit=p((B,Q)=>{if(B.retrieveSourceConfig!==void 0)return Q.retrieveSourceConfig(B.retrieveSourceConfig);if(B.retrieveAndGenerateSourceConfig!==void 0)return Q.retrieveAndGenerateSourceConfig(B.retrieveAndGenerateSourceConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(y60||(y60={}));var IF4={AUTOMATED:"Automated",HUMAN:"Human"},YF4={CREATION_TIME:"CreationTime"},WF4={BLOCK:"BLOCK",NONE:"NONE"},JF4={IMAGE:"IMAGE",TEXT:"TEXT"},XF4={HIGH:"HIGH",LOW:"LOW",MEDIUM:"MEDIUM",NONE:"NONE"},VF4={HATE:"HATE",INSULTS:"INSULTS",MISCONDUCT:"MISCONDUCT",PROMPT_ATTACK:"PROMPT_ATTACK",SEXUAL:"SEXUAL",VIOLENCE:"VIOLENCE"},CF4={CLASSIC:"CLASSIC",STANDARD:"STANDARD"},KF4={BLOCK:"BLOCK",NONE:"NONE"},HF4={GROUNDING:"GROUNDING",RELEVANCE:"RELEVANCE"},zF4={ANONYMIZE:"ANONYMIZE",BLOCK:"BLOCK",NONE:"NONE"},EF4={ADDRESS:"ADDRESS",AGE:"AGE",AWS_ACCESS_KEY:"AWS_ACCESS_KEY",AWS_SECRET_KEY:"AWS_SECRET_KEY",CA_HEALTH_NUMBER:"CA_HEALTH_NUMBER",CA_SOCIAL_INSURANCE_NUMBER:"CA_SOCIAL_INSURANCE_NUMBER",CREDIT_DEBIT_CARD_CVV:"CREDIT_DEBIT_CARD_CVV",CREDIT_DEBIT_CARD_EXPIRY:"CREDIT_DEBIT_CARD_EXPIRY",CREDIT_DEBIT_CARD_NUMBER:"CREDIT_DEBIT_CARD_NUMBER",DRIVER_ID:"DRIVER_ID",EMAIL:"EMAIL",INTERNATIONAL_BANK_ACCOUNT_NUMBER:"INTERNATIONAL_BANK_ACCOUNT_NUMBER",IP_ADDRESS:"IP_ADDRESS",LICENSE_PLATE:"LICENSE_PLATE",MAC_ADDRESS:"MAC_ADDRESS",NAME:"NAME",PASSWORD:"PASSWORD",PHONE:"PHONE",PIN:"PIN",SWIFT_CODE:"SWIFT_CODE",UK_NATIONAL_HEALTH_SERVICE_NUMBER:"UK_NATIONAL_HEALTH_SERVICE_NUMBER",UK_NATIONAL_INSURANCE_NUMBER:"UK_NATIONAL_INSURANCE_NUMBER",UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER:"UK_UNIQUE_TAXPAYER_REFERENCE_NUMBER",URL:"URL",USERNAME:"USERNAME",US_BANK_ACCOUNT_NUMBER:"US_BANK_ACCOUNT_NUMBER",US_BANK_ROUTING_NUMBER:"US_BANK_ROUTING_NUMBER",US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER:"US_INDIVIDUAL_TAX_IDENTIFICATION_NUMBER",US_PASSPORT_NUMBER:"US_PASSPORT_NUMBER",US_SOCIAL_SECURITY_NUMBER:"US_SOCIAL_SECURITY_NUMBER",VEHICLE_IDENTIFICATION_NUMBER:"VEHICLE_IDENTIFICATION_NUMBER"},UF4={CLASSIC:"CLASSIC",STANDARD:"STANDARD"},wF4={BLOCK:"BLOCK",NONE:"NONE"},$F4={DENY:"DENY"},qF4={BLOCK:"BLOCK",NONE:"NONE"},NF4={PROFANITY:"PROFANITY"},LF4={CREATING:"CREATING",DELETING:"DELETING",FAILED:"FAILED",READY:"READY",UPDATING:"UPDATING",VERSIONING:"VERSIONING"},_60;((A)=>{A.visit=p((B,Q)=>{if(B.copyFrom!==void 0)return Q.copyFrom(B.copyFrom);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(_60||(_60={}));var MF4={ACTIVE:"ACTIVE"},RF4={APPLICATION:"APPLICATION",SYSTEM_DEFINED:"SYSTEM_DEFINED"},OF4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress"},TF4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress"},PF4={JSONL:"JSONL"},x60;((A)=>{A.visit=p((B,Q)=>{if(B.s3InputDataConfig!==void 0)return Q.s3InputDataConfig(B.s3InputDataConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(x60||(x60={}));var v60;((A)=>{A.visit=p((B,Q)=>{if(B.s3OutputDataConfig!==void 0)return Q.s3OutputDataConfig(B.s3OutputDataConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(v60||(v60={}));var SF4={COMPLETED:"Completed",EXPIRED:"Expired",FAILED:"Failed",IN_PROGRESS:"InProgress",PARTIALLY_COMPLETED:"PartiallyCompleted",SCHEDULED:"Scheduled",STOPPED:"Stopped",STOPPING:"Stopping",SUBMITTED:"Submitted",VALIDATING:"Validating"},jF4={CONTINUED_PRE_TRAINING:"CONTINUED_PRE_TRAINING",DISTILLATION:"DISTILLATION",FINE_TUNING:"FINE_TUNING"},kF4={ON_DEMAND:"ON_DEMAND",PROVISIONED:"PROVISIONED"},yF4={EMBEDDING:"EMBEDDING",IMAGE:"IMAGE",TEXT:"TEXT"},_F4={ACTIVE:"ACTIVE",LEGACY:"LEGACY"},xF4={AVAILABLE:"AVAILABLE"},vF4={CUSTOM:"custom",DEFAULT:"default"},bF4={ONE_MONTH:"OneMonth",SIX_MONTHS:"SixMonths"},fF4={CREATING:"Creating",FAILED:"Failed",IN_SERVICE:"InService",UPDATING:"Updating"},hF4={CREATION_TIME:"CreationTime"},gF4={AUTHORIZED:"AUTHORIZED",NOT_AUTHORIZED:"NOT_AUTHORIZED"},b60=p((A)=>({...A,...A.equals&&{equals:_.SENSITIVE_STRING},...A.notEquals&&{notEquals:_.SENSITIVE_STRING}}),"RequestMetadataBaseFiltersFilterSensitiveLog"),k92=p((A)=>{if(A.equals!==void 0)return{equals:_.SENSITIVE_STRING};if(A.notEquals!==void 0)return{notEquals:_.SENSITIVE_STRING};if(A.andAll!==void 0)return{andAll:A.andAll.map((B)=>b60(B))};if(A.orAll!==void 0)return{orAll:A.orAll.map((B)=>b60(B))};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RequestMetadataFiltersFilterSensitiveLog"),y92=p((A)=>({...A,...A.invocationLogSource&&{invocationLogSource:A.invocationLogSource},...A.requestMetadataFilters&&{requestMetadataFilters:k92(A.requestMetadataFilters)}}),"InvocationLogsConfigFilterSensitiveLog"),IN1=p((A)=>({...A,...A.invocationLogsConfig&&{invocationLogsConfig:y92(A.invocationLogsConfig)}}),"TrainingDataConfigFilterSensitiveLog"),_92=p((A)=>({...A,...A.trainingDataConfig&&{trainingDataConfig:IN1(A.trainingDataConfig)},...A.customizationConfig&&{customizationConfig:A.customizationConfig}}),"GetCustomModelResponseFilterSensitiveLog"),x92=p((A)=>({...A,...A.jobIdentifiers&&{jobIdentifiers:_.SENSITIVE_STRING}}),"BatchDeleteEvaluationJobRequestFilterSensitiveLog"),v92=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"BatchDeleteEvaluationJobErrorFilterSensitiveLog"),b92=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"BatchDeleteEvaluationJobItemFilterSensitiveLog"),f92=p((A)=>({...A,...A.errors&&{errors:A.errors.map((B)=>v92(B))},...A.evaluationJobs&&{evaluationJobs:A.evaluationJobs.map((B)=>b92(B))}}),"BatchDeleteEvaluationJobResponseFilterSensitiveLog"),uF4=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.ratingScale&&{ratingScale:A.ratingScale.map((B)=>B)}}),"CustomMetricDefinitionFilterSensitiveLog"),h92=p((A)=>{if(A.customMetricDefinition!==void 0)return{customMetricDefinition:_.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"AutomatedEvaluationCustomMetricSourceFilterSensitiveLog"),g92=p((A)=>({...A,...A.customMetrics&&{customMetrics:A.customMetrics.map((B)=>h92(B))}}),"AutomatedEvaluationCustomMetricConfigFilterSensitiveLog"),u92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.datasetLocation&&{datasetLocation:A.datasetLocation}}),"EvaluationDatasetFilterSensitiveLog"),h60=p((A)=>({...A,...A.dataset&&{dataset:u92(A.dataset)},...A.metricNames&&{metricNames:_.SENSITIVE_STRING}}),"EvaluationDatasetMetricConfigFilterSensitiveLog"),m92=p((A)=>({...A,...A.datasetMetricConfigs&&{datasetMetricConfigs:A.datasetMetricConfigs.map((B)=>h60(B))},...A.evaluatorModelConfig&&{evaluatorModelConfig:A.evaluatorModelConfig},...A.customMetricConfig&&{customMetricConfig:g92(A.customMetricConfig)}}),"AutomatedEvaluationConfigFilterSensitiveLog"),d92=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING}}),"HumanEvaluationCustomMetricFilterSensitiveLog"),c92=p((A)=>({...A,...A.instructions&&{instructions:_.SENSITIVE_STRING}}),"HumanWorkflowConfigFilterSensitiveLog"),l92=p((A)=>({...A,...A.humanWorkflowConfig&&{humanWorkflowConfig:c92(A.humanWorkflowConfig)},...A.customMetrics&&{customMetrics:A.customMetrics.map((B)=>d92(B))},...A.datasetMetricConfigs&&{datasetMetricConfigs:A.datasetMetricConfigs.map((B)=>h60(B))}}),"HumanEvaluationConfigFilterSensitiveLog"),g60=p((A)=>{if(A.automated!==void 0)return{automated:m92(A.automated)};if(A.human!==void 0)return{human:l92(A.human)};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"EvaluationConfigFilterSensitiveLog"),p92=p((A)=>({...A,...A.inferenceParams&&{inferenceParams:_.SENSITIVE_STRING}}),"EvaluationBedrockModelFilterSensitiveLog"),i92=p((A)=>{if(A.bedrockModel!==void 0)return{bedrockModel:p92(A.bedrockModel)};if(A.precomputedInferenceSource!==void 0)return{precomputedInferenceSource:A.precomputedInferenceSource};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"EvaluationModelConfigFilterSensitiveLog"),u60=p((A)=>({...A,...A.textPromptTemplate&&{textPromptTemplate:_.SENSITIVE_STRING}}),"PromptTemplateFilterSensitiveLog"),n92=p((A)=>({...A,...A.promptTemplate&&{promptTemplate:u60(A.promptTemplate)}}),"ExternalSourcesGenerationConfigurationFilterSensitiveLog"),a92=p((A)=>({...A,...A.identifier&&{identifier:_.SENSITIVE_STRING},...A.data&&{data:_.SENSITIVE_STRING}}),"ByteContentDocFilterSensitiveLog"),s92=p((A)=>({...A,...A.byteContent&&{byteContent:a92(A.byteContent)}}),"ExternalSourceFilterSensitiveLog"),r92=p((A)=>({...A,...A.sources&&{sources:A.sources.map((B)=>s92(B))},...A.generationConfiguration&&{generationConfiguration:n92(A.generationConfiguration)}}),"ExternalSourcesRetrieveAndGenerateConfigurationFilterSensitiveLog"),o92=p((A)=>({...A,...A.promptTemplate&&{promptTemplate:u60(A.promptTemplate)}}),"GenerationConfigurationFilterSensitiveLog"),mF4=p((A)=>({...A}),"MetadataAttributeSchemaFilterSensitiveLog"),t92=p((A)=>({...A,...A.metadataAttributes&&{metadataAttributes:_.SENSITIVE_STRING}}),"ImplicitFilterConfigurationFilterSensitiveLog"),e92=p((A)=>{if(A.fieldsToInclude!==void 0)return{fieldsToInclude:_.SENSITIVE_STRING};if(A.fieldsToExclude!==void 0)return{fieldsToExclude:_.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RerankingMetadataSelectiveModeConfigurationFilterSensitiveLog"),AQ2=p((A)=>({...A,...A.selectiveModeConfiguration&&{selectiveModeConfiguration:e92(A.selectiveModeConfiguration)}}),"MetadataConfigurationForRerankingFilterSensitiveLog"),BQ2=p((A)=>({...A,...A.metadataConfiguration&&{metadataConfiguration:AQ2(A.metadataConfiguration)}}),"VectorSearchBedrockRerankingConfigurationFilterSensitiveLog"),QQ2=p((A)=>({...A,...A.bedrockRerankingConfiguration&&{bedrockRerankingConfiguration:BQ2(A.bedrockRerankingConfiguration)}}),"VectorSearchRerankingConfigurationFilterSensitiveLog"),DQ2=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"GetEvaluationJobRequestFilterSensitiveLog"),ZQ2=p((A)=>({...A,...A.jobIdentifier&&{jobIdentifier:_.SENSITIVE_STRING}}),"StopEvaluationJobRequestFilterSensitiveLog"),GQ2=p((A)=>({...A,...A.inputModalities&&{inputModalities:_.SENSITIVE_STRING},...A.outputModalities&&{outputModalities:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailContentFilterConfigFilterSensitiveLog"),FQ2=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailContentFiltersTierConfigFilterSensitiveLog"),m60=p((A)=>({...A,...A.filtersConfig&&{filtersConfig:A.filtersConfig.map((B)=>GQ2(B))},...A.tierConfig&&{tierConfig:FQ2(A.tierConfig)}}),"GuardrailContentPolicyConfigFilterSensitiveLog"),IQ2=p((A)=>({...A,...A.action&&{action:_.SENSITIVE_STRING}}),"GuardrailContextualGroundingFilterConfigFilterSensitiveLog"),d60=p((A)=>({...A,...A.filtersConfig&&{filtersConfig:A.filtersConfig.map((B)=>IQ2(B))}}),"GuardrailContextualGroundingPolicyConfigFilterSensitiveLog"),YQ2=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailTopicsTierConfigFilterSensitiveLog"),WQ2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.definition&&{definition:_.SENSITIVE_STRING},...A.examples&&{examples:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailTopicConfigFilterSensitiveLog"),c60=p((A)=>({...A,...A.topicsConfig&&{topicsConfig:A.topicsConfig.map((B)=>WQ2(B))},...A.tierConfig&&{tierConfig:YQ2(A.tierConfig)}}),"GuardrailTopicPolicyConfigFilterSensitiveLog"),JQ2=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailManagedWordsConfigFilterSensitiveLog"),XQ2=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailWordConfigFilterSensitiveLog"),l60=p((A)=>({...A,...A.wordsConfig&&{wordsConfig:A.wordsConfig.map((B)=>XQ2(B))},...A.managedWordListsConfig&&{managedWordListsConfig:A.managedWordListsConfig.map((B)=>JQ2(B))}}),"GuardrailWordPolicyConfigFilterSensitiveLog"),VQ2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING},...A.topicPolicyConfig&&{topicPolicyConfig:c60(A.topicPolicyConfig)},...A.contentPolicyConfig&&{contentPolicyConfig:m60(A.contentPolicyConfig)},...A.wordPolicyConfig&&{wordPolicyConfig:l60(A.wordPolicyConfig)},...A.contextualGroundingPolicyConfig&&{contextualGroundingPolicyConfig:d60(A.contextualGroundingPolicyConfig)},...A.blockedInputMessaging&&{blockedInputMessaging:_.SENSITIVE_STRING},...A.blockedOutputsMessaging&&{blockedOutputsMessaging:_.SENSITIVE_STRING}}),"CreateGuardrailRequestFilterSensitiveLog"),CQ2=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"CreateGuardrailVersionRequestFilterSensitiveLog"),KQ2=p((A)=>({...A,...A.inputModalities&&{inputModalities:_.SENSITIVE_STRING},...A.outputModalities&&{outputModalities:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailContentFilterFilterSensitiveLog"),HQ2=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailContentFiltersTierFilterSensitiveLog"),zQ2=p((A)=>({...A,...A.filters&&{filters:A.filters.map((B)=>KQ2(B))},...A.tier&&{tier:HQ2(A.tier)}}),"GuardrailContentPolicyFilterSensitiveLog"),EQ2=p((A)=>({...A,...A.action&&{action:_.SENSITIVE_STRING}}),"GuardrailContextualGroundingFilterFilterSensitiveLog"),UQ2=p((A)=>({...A,...A.filters&&{filters:A.filters.map((B)=>EQ2(B))}}),"GuardrailContextualGroundingPolicyFilterSensitiveLog"),wQ2=p((A)=>({...A,...A.tierName&&{tierName:_.SENSITIVE_STRING}}),"GuardrailTopicsTierFilterSensitiveLog"),$Q2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.definition&&{definition:_.SENSITIVE_STRING},...A.examples&&{examples:_.SENSITIVE_STRING},...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailTopicFilterSensitiveLog"),qQ2=p((A)=>({...A,...A.topics&&{topics:A.topics.map((B)=>$Q2(B))},...A.tier&&{tier:wQ2(A.tier)}}),"GuardrailTopicPolicyFilterSensitiveLog"),NQ2=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailManagedWordsFilterSensitiveLog"),LQ2=p((A)=>({...A,...A.inputAction&&{inputAction:_.SENSITIVE_STRING},...A.outputAction&&{outputAction:_.SENSITIVE_STRING}}),"GuardrailWordFilterSensitiveLog"),MQ2=p((A)=>({...A,...A.words&&{words:A.words.map((B)=>LQ2(B))},...A.managedWordLists&&{managedWordLists:A.managedWordLists.map((B)=>NQ2(B))}}),"GuardrailWordPolicyFilterSensitiveLog"),RQ2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING},...A.topicPolicy&&{topicPolicy:qQ2(A.topicPolicy)},...A.contentPolicy&&{contentPolicy:zQ2(A.contentPolicy)},...A.wordPolicy&&{wordPolicy:MQ2(A.wordPolicy)},...A.contextualGroundingPolicy&&{contextualGroundingPolicy:UQ2(A.contextualGroundingPolicy)},...A.statusReasons&&{statusReasons:_.SENSITIVE_STRING},...A.failureRecommendations&&{failureRecommendations:_.SENSITIVE_STRING},...A.blockedInputMessaging&&{blockedInputMessaging:_.SENSITIVE_STRING},...A.blockedOutputsMessaging&&{blockedOutputsMessaging:_.SENSITIVE_STRING}}),"GetGuardrailResponseFilterSensitiveLog"),OQ2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING}}),"GuardrailSummaryFilterSensitiveLog"),TQ2=p((A)=>({...A,...A.guardrails&&{guardrails:A.guardrails.map((B)=>OQ2(B))}}),"ListGuardrailsResponseFilterSensitiveLog"),PQ2=p((A)=>({...A,...A.name&&{name:_.SENSITIVE_STRING},...A.description&&{description:_.SENSITIVE_STRING},...A.topicPolicyConfig&&{topicPolicyConfig:c60(A.topicPolicyConfig)},...A.contentPolicyConfig&&{contentPolicyConfig:m60(A.contentPolicyConfig)},...A.wordPolicyConfig&&{wordPolicyConfig:l60(A.wordPolicyConfig)},...A.contextualGroundingPolicyConfig&&{contextualGroundingPolicyConfig:d60(A.contextualGroundingPolicyConfig)},...A.blockedInputMessaging&&{blockedInputMessaging:_.SENSITIVE_STRING},...A.blockedOutputsMessaging&&{blockedOutputsMessaging:_.SENSITIVE_STRING}}),"UpdateGuardrailRequestFilterSensitiveLog"),SQ2=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING},...A.modelSource&&{modelSource:A.modelSource}}),"CreateInferenceProfileRequestFilterSensitiveLog"),jQ2=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"GetInferenceProfileResponseFilterSensitiveLog"),kQ2=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"InferenceProfileSummaryFilterSensitiveLog"),yQ2=p((A)=>({...A,...A.inferenceProfileSummaries&&{inferenceProfileSummaries:A.inferenceProfileSummaries.map((B)=>kQ2(B))}}),"ListInferenceProfilesResponseFilterSensitiveLog"),_Q2=p((A)=>({...A,...A.message&&{message:_.SENSITIVE_STRING},...A.inputDataConfig&&{inputDataConfig:A.inputDataConfig},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"GetModelInvocationJobResponseFilterSensitiveLog"),xQ2=p((A)=>({...A,...A.message&&{message:_.SENSITIVE_STRING},...A.inputDataConfig&&{inputDataConfig:A.inputDataConfig},...A.outputDataConfig&&{outputDataConfig:A.outputDataConfig}}),"ModelInvocationJobSummaryFilterSensitiveLog"),vQ2=p((A)=>({...A,...A.invocationJobSummaries&&{invocationJobSummaries:A.invocationJobSummaries.map((B)=>xQ2(B))}}),"ListModelInvocationJobsResponseFilterSensitiveLog"),bQ2=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"CreatePromptRouterRequestFilterSensitiveLog"),fQ2=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"GetPromptRouterResponseFilterSensitiveLog"),hQ2=p((A)=>({...A,...A.description&&{description:_.SENSITIVE_STRING}}),"PromptRouterSummaryFilterSensitiveLog"),gQ2=p((A)=>({...A,...A.promptRouterSummaries&&{promptRouterSummaries:A.promptRouterSummaries.map((B)=>hQ2(B))}}),"ListPromptRoutersResponseFilterSensitiveLog"),XB=YI(),mz=sQ1(),dF4={AVAILABLE:"AVAILABLE",NOT_AVAILABLE:"NOT_AVAILABLE"},cF4={AVAILABLE:"AVAILABLE",NOT_AVAILABLE:"NOT_AVAILABLE"},lF4={ALL:"ALL",PUBLIC:"PUBLIC"},pF4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress",STOPPED:"Stopped",STOPPING:"Stopping"},iF4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress",NOT_STARTED:"NotStarted",STOPPED:"Stopped",STOPPING:"Stopping"},nF4={COMPLETED:"Completed",FAILED:"Failed",IN_PROGRESS:"InProgress",STOPPED:"Stopped",STOPPING:"Stopping"},BN1;((A)=>{A.visit=p((B,Q)=>{if(B.equals!==void 0)return Q.equals(B.equals);if(B.notEquals!==void 0)return Q.notEquals(B.notEquals);if(B.greaterThan!==void 0)return Q.greaterThan(B.greaterThan);if(B.greaterThanOrEquals!==void 0)return Q.greaterThanOrEquals(B.greaterThanOrEquals);if(B.lessThan!==void 0)return Q.lessThan(B.lessThan);if(B.lessThanOrEquals!==void 0)return Q.lessThanOrEquals(B.lessThanOrEquals);if(B.in!==void 0)return Q.in(B.in);if(B.notIn!==void 0)return Q.notIn(B.notIn);if(B.startsWith!==void 0)return Q.startsWith(B.startsWith);if(B.listContains!==void 0)return Q.listContains(B.listContains);if(B.stringContains!==void 0)return Q.stringContains(B.stringContains);if(B.andAll!==void 0)return Q.andAll(B.andAll);if(B.orAll!==void 0)return Q.orAll(B.orAll);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(BN1||(BN1={}));var QN1;((A)=>{A.visit=p((B,Q)=>{if(B.retrieveConfig!==void 0)return Q.retrieveConfig(B.retrieveConfig);if(B.retrieveAndGenerateConfig!==void 0)return Q.retrieveAndGenerateConfig(B.retrieveAndGenerateConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(QN1||(QN1={}));var DN1;((A)=>{A.visit=p((B,Q)=>{if(B.knowledgeBaseConfig!==void 0)return Q.knowledgeBaseConfig(B.knowledgeBaseConfig);if(B.precomputedRagSourceConfig!==void 0)return Q.precomputedRagSourceConfig(B.precomputedRagSourceConfig);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(DN1||(DN1={}));var ZN1;((A)=>{A.visit=p((B,Q)=>{if(B.models!==void 0)return Q.models(B.models);if(B.ragConfigs!==void 0)return Q.ragConfigs(B.ragConfigs);return Q._(B.$unknown[0],B.$unknown[1])},"visit")})(ZN1||(ZN1={}));var uQ2=p((A)=>({...A,...A.trainingDataConfig&&{trainingDataConfig:IN1(A.trainingDataConfig)},...A.customizationConfig&&{customizationConfig:A.customizationConfig}}),"CreateModelCustomizationJobRequestFilterSensitiveLog"),mQ2=p((A)=>({...A,...A.trainingDataConfig&&{trainingDataConfig:IN1(A.trainingDataConfig)},...A.customizationConfig&&{customizationConfig:A.customizationConfig}}),"GetModelCustomizationJobResponseFilterSensitiveLog"),aF4=p((A)=>{if(A.equals!==void 0)return{equals:A.equals};if(A.notEquals!==void 0)return{notEquals:A.notEquals};if(A.greaterThan!==void 0)return{greaterThan:A.greaterThan};if(A.greaterThanOrEquals!==void 0)return{greaterThanOrEquals:A.greaterThanOrEquals};if(A.lessThan!==void 0)return{lessThan:A.lessThan};if(A.lessThanOrEquals!==void 0)return{lessThanOrEquals:A.lessThanOrEquals};if(A.in!==void 0)return{in:A.in};if(A.notIn!==void 0)return{notIn:A.notIn};if(A.startsWith!==void 0)return{startsWith:A.startsWith};if(A.listContains!==void 0)return{listContains:A.listContains};if(A.stringContains!==void 0)return{stringContains:A.stringContains};if(A.andAll!==void 0)return{andAll:_.SENSITIVE_STRING};if(A.orAll!==void 0)return{orAll:_.SENSITIVE_STRING};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RetrievalFilterFilterSensitiveLog"),dQ2=p((A)=>({...A,...A.filter&&{filter:_.SENSITIVE_STRING},...A.implicitFilterConfiguration&&{implicitFilterConfiguration:t92(A.implicitFilterConfiguration)},...A.rerankingConfiguration&&{rerankingConfiguration:QQ2(A.rerankingConfiguration)}}),"KnowledgeBaseVectorSearchConfigurationFilterSensitiveLog"),p60=p((A)=>({...A,...A.vectorSearchConfiguration&&{vectorSearchConfiguration:dQ2(A.vectorSearchConfiguration)}}),"KnowledgeBaseRetrievalConfigurationFilterSensitiveLog"),cQ2=p((A)=>({...A,...A.retrievalConfiguration&&{retrievalConfiguration:p60(A.retrievalConfiguration)},...A.generationConfiguration&&{generationConfiguration:o92(A.generationConfiguration)}}),"KnowledgeBaseRetrieveAndGenerateConfigurationFilterSensitiveLog"),lQ2=p((A)=>({...A,...A.knowledgeBaseRetrievalConfiguration&&{knowledgeBaseRetrievalConfiguration:p60(A.knowledgeBaseRetrievalConfiguration)}}),"RetrieveConfigFilterSensitiveLog"),pQ2=p((A)=>({...A,...A.knowledgeBaseConfiguration&&{knowledgeBaseConfiguration:cQ2(A.knowledgeBaseConfiguration)},...A.externalSourcesConfiguration&&{externalSourcesConfiguration:r92(A.externalSourcesConfiguration)}}),"RetrieveAndGenerateConfigurationFilterSensitiveLog"),iQ2=p((A)=>{if(A.retrieveConfig!==void 0)return{retrieveConfig:lQ2(A.retrieveConfig)};if(A.retrieveAndGenerateConfig!==void 0)return{retrieveAndGenerateConfig:pQ2(A.retrieveAndGenerateConfig)};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"KnowledgeBaseConfigFilterSensitiveLog"),nQ2=p((A)=>{if(A.knowledgeBaseConfig!==void 0)return{knowledgeBaseConfig:iQ2(A.knowledgeBaseConfig)};if(A.precomputedRagSourceConfig!==void 0)return{precomputedRagSourceConfig:A.precomputedRagSourceConfig};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"RAGConfigFilterSensitiveLog"),i60=p((A)=>{if(A.models!==void 0)return{models:A.models.map((B)=>i92(B))};if(A.ragConfigs!==void 0)return{ragConfigs:A.ragConfigs.map((B)=>nQ2(B))};if(A.$unknown!==void 0)return{[A.$unknown[0]]:"UNKNOWN"}},"EvaluationInferenceConfigFilterSensitiveLog"),aQ2=p((A)=>({...A,...A.jobDescription&&{jobDescription:_.SENSITIVE_STRING},...A.evaluationConfig&&{evaluationConfig:g60(A.evaluationConfig)},...A.inferenceConfig&&{inferenceConfig:i60(A.inferenceConfig)}}),"CreateEvaluationJobRequestFilterSensitiveLog"),sQ2=p((A)=>({...A,...A.jobDescription&&{jobDescription:_.SENSITIVE_STRING},...A.evaluationConfig&&{evaluationConfig:g60(A.evaluationConfig)},...A.inferenceConfig&&{inferenceConfig:i60(A.inferenceConfig)}}),"GetEvaluationJobResponseFilterSensitiveLog"),sF4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/evaluation-jobs/batch-delete");let Z;return Z=JSON.stringify(_.take(A,{jobIdentifiers:p((G)=>_._json(G),"jobIdentifiers")})),Q.m("POST").h(D).b(Z),Q.build()},"se_BatchDeleteEvaluationJobCommand"),rF4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/custom-models/create-custom-model");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],modelKmsKeyArn:[],modelName:[],modelSourceConfig:p((G)=>_._json(G),"modelSourceConfig"),modelTags:p((G)=>_._json(G),"modelTags"),roleArn:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateCustomModelCommand"),oF4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/evaluation-jobs");let Z;return Z=JSON.stringify(_.take(A,{applicationType:[],clientRequestToken:[!0,(G)=>G??mz.v4()],customerEncryptionKeyId:[],evaluationConfig:p((G)=>bW4(G,B),"evaluationConfig"),inferenceConfig:p((G)=>fW4(G,B),"inferenceConfig"),jobDescription:[],jobName:[],jobTags:p((G)=>_._json(G),"jobTags"),outputDataConfig:p((G)=>_._json(G),"outputDataConfig"),roleArn:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateEvaluationJobCommand"),tF4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/create-foundation-model-agreement");let Z;return Z=JSON.stringify(_.take(A,{modelId:[],offerToken:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateFoundationModelAgreementCommand"),eF4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrails");let Z;return Z=JSON.stringify(_.take(A,{blockedInputMessaging:[],blockedOutputsMessaging:[],clientRequestToken:[!0,(G)=>G??mz.v4()],contentPolicyConfig:p((G)=>_._json(G),"contentPolicyConfig"),contextualGroundingPolicyConfig:p((G)=>rQ2(G,B),"contextualGroundingPolicyConfig"),crossRegionConfig:p((G)=>_._json(G),"crossRegionConfig"),description:[],kmsKeyId:[],name:[],sensitiveInformationPolicyConfig:p((G)=>_._json(G),"sensitiveInformationPolicyConfig"),tags:p((G)=>_._json(G),"tags"),topicPolicyConfig:p((G)=>_._json(G),"topicPolicyConfig"),wordPolicyConfig:p((G)=>_._json(G),"wordPolicyConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateGuardrailCommand"),AI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],description:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateGuardrailVersionCommand"),BI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/inference-profiles");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],description:[],inferenceProfileName:[],modelSource:p((G)=>_._json(G),"modelSource"),tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateInferenceProfileCommand"),QI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/marketplace-model/endpoints");let Z;return Z=JSON.stringify(_.take(A,{acceptEula:[],clientRequestToken:[!0,(G)=>G??mz.v4()],endpointConfig:p((G)=>_._json(G),"endpointConfig"),endpointName:[],modelSourceIdentifier:[],tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateMarketplaceModelEndpointCommand"),DI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-copy-jobs");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],modelKmsKeyId:[],sourceModelArn:[],targetModelName:[],targetModelTags:p((G)=>_._json(G),"targetModelTags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelCopyJobCommand"),ZI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-customization-jobs");let Z;return Z=JSON.stringify(_.take(A,{baseModelIdentifier:[],clientRequestToken:[!0,(G)=>G??mz.v4()],customModelKmsKeyId:[],customModelName:[],customModelTags:p((G)=>_._json(G),"customModelTags"),customizationConfig:p((G)=>_._json(G),"customizationConfig"),customizationType:[],hyperParameters:p((G)=>_._json(G),"hyperParameters"),jobName:[],jobTags:p((G)=>_._json(G),"jobTags"),outputDataConfig:p((G)=>_._json(G),"outputDataConfig"),roleArn:[],trainingDataConfig:p((G)=>_._json(G),"trainingDataConfig"),validationDataConfig:p((G)=>_._json(G),"validationDataConfig"),vpcConfig:p((G)=>_._json(G),"vpcConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelCustomizationJobCommand"),GI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-import-jobs");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[],importedModelKmsKeyId:[],importedModelName:[],importedModelTags:p((G)=>_._json(G),"importedModelTags"),jobName:[],jobTags:p((G)=>_._json(G),"jobTags"),modelDataSource:p((G)=>_._json(G),"modelDataSource"),roleArn:[],vpcConfig:p((G)=>_._json(G),"vpcConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelImportJobCommand"),FI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/model-invocation-job");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],inputDataConfig:p((G)=>_._json(G),"inputDataConfig"),jobName:[],modelId:[],outputDataConfig:p((G)=>_._json(G),"outputDataConfig"),roleArn:[],tags:p((G)=>_._json(G),"tags"),timeoutDurationInHours:[],vpcConfig:p((G)=>_._json(G),"vpcConfig")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateModelInvocationJobCommand"),II4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/prompt-routers");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],description:[],fallbackModel:p((G)=>_._json(G),"fallbackModel"),models:p((G)=>_._json(G),"models"),promptRouterName:[],routingCriteria:p((G)=>QJ4(G,B),"routingCriteria"),tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreatePromptRouterCommand"),YI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/provisioned-model-throughput");let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],commitmentDuration:[],modelId:[],modelUnits:[],provisionedModelName:[],tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateProvisionedModelThroughputCommand"),WI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/custom-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteCustomModelCommand"),JI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/delete-foundation-model-agreement");let Z;return Z=JSON.stringify(_.take(A,{modelId:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_DeleteFoundationModelAgreementCommand"),XI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z=_.map({[GN1]:[,A[GN1]]}),G;return Q.m("DELETE").h(D).q(Z).b(G),Q.build()},"se_DeleteGuardrailCommand"),VI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/imported-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteImportedModelCommand"),CI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/inference-profiles/{inferenceProfileIdentifier}"),Q.p("inferenceProfileIdentifier",()=>A.inferenceProfileIdentifier,"{inferenceProfileIdentifier}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteInferenceProfileCommand"),KI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints/{endpointArn}"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteMarketplaceModelEndpointCommand"),HI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/logging/modelinvocations");let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteModelInvocationLoggingConfigurationCommand"),zI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/prompt-routers/{promptRouterArn}"),Q.p("promptRouterArn",()=>A.promptRouterArn,"{promptRouterArn}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeletePromptRouterCommand"),EI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/provisioned-model-throughput/{provisionedModelId}"),Q.p("provisionedModelId",()=>A.provisionedModelId,"{provisionedModelId}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeleteProvisionedModelThroughputCommand"),UI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints/{endpointArn}/registration"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Q.m("DELETE").h(D).b(Z),Q.build()},"se_DeregisterMarketplaceModelEndpointCommand"),wI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/custom-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetCustomModelCommand"),$I4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/evaluation-jobs/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetEvaluationJobCommand"),qI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/foundation-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetFoundationModelCommand"),NI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/foundation-model-availability/{modelId}"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetFoundationModelAvailabilityCommand"),LI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z=_.map({[GN1]:[,A[GN1]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetGuardrailCommand"),MI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/imported-models/{modelIdentifier}"),Q.p("modelIdentifier",()=>A.modelIdentifier,"{modelIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetImportedModelCommand"),RI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/inference-profiles/{inferenceProfileIdentifier}"),Q.p("inferenceProfileIdentifier",()=>A.inferenceProfileIdentifier,"{inferenceProfileIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetInferenceProfileCommand"),OI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints/{endpointArn}"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetMarketplaceModelEndpointCommand"),TI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-copy-jobs/{jobArn}"),Q.p("jobArn",()=>A.jobArn,"{jobArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelCopyJobCommand"),PI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-customization-jobs/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelCustomizationJobCommand"),SI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-import-jobs/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelImportJobCommand"),jI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-invocation-job/{jobIdentifier}"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelInvocationJobCommand"),kI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/logging/modelinvocations");let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetModelInvocationLoggingConfigurationCommand"),yI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/prompt-routers/{promptRouterArn}"),Q.p("promptRouterArn",()=>A.promptRouterArn,"{promptRouterArn}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetPromptRouterCommand"),_I4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/provisioned-model-throughput/{provisionedModelId}"),Q.p("provisionedModelId",()=>A.provisionedModelId,"{provisionedModelId}",!1);let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetProvisionedModelThroughputCommand"),xI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/use-case-for-model-access");let Z;return Q.m("GET").h(D).b(Z),Q.build()},"se_GetUseCaseForModelAccessCommand"),vI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/custom-models");let Z=_.map({[HV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[HV]).toString()],[KV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[KV]).toString()],[zV]:[,A[zV]],[Y92]:[,A[Y92]],[X92]:[,A[X92]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]],[C92]:[()=>A.isOwned!==void 0,()=>A[C92].toString()],[H92]:[,A[H92]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListCustomModelsCommand"),bI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/evaluation-jobs");let Z=_.map({[KV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[KV]).toString()],[HV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[HV]).toString()],[uz]:[,A[uz]],[G92]:[,A[G92]],[zV]:[,A[zV]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListEvaluationJobsCommand"),fI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/list-foundation-model-agreement-offers/{modelId}"),Q.p("modelId",()=>A.modelId,"{modelId}",!1);let Z=_.map({[z92]:[,A[z92]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListFoundationModelAgreementOffersCommand"),hI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/foundation-models");let Z=_.map({[J92]:[,A[J92]],[F92]:[,A[F92]],[W92]:[,A[W92]],[I92]:[,A[I92]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListFoundationModelsCommand"),gI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/guardrails");let Z=_.map({[V92]:[,A[V92]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListGuardrailsCommand"),uI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/imported-models");let Z=_.map({[HV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[HV]).toString()],[KV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[KV]).toString()],[zV]:[,A[zV]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListImportedModelsCommand"),mI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/inference-profiles");let Z=_.map({[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[f60]:[,A[zX4]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListInferenceProfilesCommand"),dI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/marketplace-model/endpoints");let Z=_.map({[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[KX4]:[,A[CX4]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListMarketplaceModelEndpointsCommand"),cI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-copy-jobs");let Z=_.map({[KV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[KV]).toString()],[HV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[HV]).toString()],[uz]:[,A[uz]],[E92]:[,A[E92]],[U92]:[,A[U92]],[HX4]:[,A[EX4]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelCopyJobsCommand"),lI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-customization-jobs");let Z=_.map({[KV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[KV]).toString()],[HV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[HV]).toString()],[uz]:[,A[uz]],[zV]:[,A[zV]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelCustomizationJobsCommand"),pI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-import-jobs");let Z=_.map({[KV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[KV]).toString()],[HV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[HV]).toString()],[uz]:[,A[uz]],[zV]:[,A[zV]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelImportJobsCommand"),iI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-invocation-jobs");let Z=_.map({[w92]:[()=>A.submitTimeAfter!==void 0,()=>_.serializeDateTime(A[w92]).toString()],[$92]:[()=>A.submitTimeBefore!==void 0,()=>_.serializeDateTime(A[$92]).toString()],[uz]:[,A[uz]],[zV]:[,A[zV]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListModelInvocationJobsCommand"),nI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/prompt-routers");let Z=_.map({[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[f60]:[,A[f60]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListPromptRoutersCommand"),aI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/provisioned-model-throughputs");let Z=_.map({[KV]:[()=>A.creationTimeAfter!==void 0,()=>_.serializeDateTime(A[KV]).toString()],[HV]:[()=>A.creationTimeBefore!==void 0,()=>_.serializeDateTime(A[HV]).toString()],[uz]:[,A[uz]],[K92]:[,A[K92]],[zV]:[,A[zV]],[XD]:[()=>A.maxResults!==void 0,()=>A[XD].toString()],[VD]:[,A[VD]],[dW]:[,A[dW]],[cW]:[,A[cW]]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListProvisionedModelThroughputsCommand"),sI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/listTagsForResource");let Z;return Z=JSON.stringify(_.take(A,{resourceARN:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_ListTagsForResourceCommand"),rI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/logging/modelinvocations");let Z;return Z=JSON.stringify(_.take(A,{loggingConfig:p((G)=>_._json(G),"loggingConfig")})),Q.m("PUT").h(D).b(Z),Q.build()},"se_PutModelInvocationLoggingConfigurationCommand"),oI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/use-case-for-model-access");let Z;return Z=JSON.stringify(_.take(A,{formData:p((G)=>B.base64Encoder(G),"formData")})),Q.m("POST").h(D).b(Z),Q.build()},"se_PutUseCaseForModelAccessCommand"),tI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/marketplace-model/endpoints/{endpointIdentifier}/registration"),Q.p("endpointIdentifier",()=>A.endpointIdentifier,"{endpointIdentifier}",!1);let Z;return Z=JSON.stringify(_.take(A,{modelSourceIdentifier:[]})),Q.m("POST").h(D).b(Z),Q.build()},"se_RegisterMarketplaceModelEndpointCommand"),eI4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/evaluation-job/{jobIdentifier}/stop"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_StopEvaluationJobCommand"),AY4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-customization-jobs/{jobIdentifier}/stop"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_StopModelCustomizationJobCommand"),BY4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={};Q.bp("/model-invocation-job/{jobIdentifier}/stop"),Q.p("jobIdentifier",()=>A.jobIdentifier,"{jobIdentifier}",!1);let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_StopModelInvocationJobCommand"),QY4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/tagResource");let Z;return Z=JSON.stringify(_.take(A,{resourceARN:[],tags:p((G)=>_._json(G),"tags")})),Q.m("POST").h(D).b(Z),Q.build()},"se_TagResourceCommand"),DY4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/untagResource");let Z;return Z=JSON.stringify(_.take(A,{resourceARN:[],tagKeys:p((G)=>_._json(G),"tagKeys")})),Q.m("POST").h(D).b(Z),Q.build()},"se_UntagResourceCommand"),ZY4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/guardrails/{guardrailIdentifier}"),Q.p("guardrailIdentifier",()=>A.guardrailIdentifier,"{guardrailIdentifier}",!1);let Z;return Z=JSON.stringify(_.take(A,{blockedInputMessaging:[],blockedOutputsMessaging:[],contentPolicyConfig:p((G)=>_._json(G),"contentPolicyConfig"),contextualGroundingPolicyConfig:p((G)=>rQ2(G,B),"contextualGroundingPolicyConfig"),crossRegionConfig:p((G)=>_._json(G),"crossRegionConfig"),description:[],kmsKeyId:[],name:[],sensitiveInformationPolicyConfig:p((G)=>_._json(G),"sensitiveInformationPolicyConfig"),topicPolicyConfig:p((G)=>_._json(G),"topicPolicyConfig"),wordPolicyConfig:p((G)=>_._json(G),"wordPolicyConfig")})),Q.m("PUT").h(D).b(Z),Q.build()},"se_UpdateGuardrailCommand"),GY4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/marketplace-model/endpoints/{endpointArn}"),Q.p("endpointArn",()=>A.endpointArn,"{endpointArn}",!1);let Z;return Z=JSON.stringify(_.take(A,{clientRequestToken:[!0,(G)=>G??mz.v4()],endpointConfig:p((G)=>_._json(G),"endpointConfig")})),Q.m("PATCH").h(D).b(Z),Q.build()},"se_UpdateMarketplaceModelEndpointCommand"),FY4=p(async(A,B)=>{let Q=$2.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/provisioned-model-throughput/{provisionedModelId}"),Q.p("provisionedModelId",()=>A.provisionedModelId,"{provisionedModelId}",!1);let Z;return Z=JSON.stringify(_.take(A,{desiredModelId:[],desiredProvisionedModelName:[]})),Q.m("PATCH").h(D).b(Z),Q.build()},"se_UpdateProvisionedModelThroughputCommand"),IY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{errors:_._json,evaluationJobs:_._json});return Object.assign(Q,Z),Q},"de_BatchDeleteEvaluationJobCommand"),YY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateCustomModelCommand"),WY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateEvaluationJobCommand"),JY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelId:_.expectString});return Object.assign(Q,Z),Q},"de_CreateFoundationModelAgreementCommand"),XY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),guardrailArn:_.expectString,guardrailId:_.expectString,version:_.expectString});return Object.assign(Q,Z),Q},"de_CreateGuardrailCommand"),VY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{guardrailId:_.expectString,version:_.expectString});return Object.assign(Q,Z),Q},"de_CreateGuardrailVersionCommand"),CY4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{inferenceProfileArn:_.expectString,status:_.expectString});return Object.assign(Q,Z),Q},"de_CreateInferenceProfileCommand"),KY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>YN1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_CreateMarketplaceModelEndpointCommand"),HY4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelCopyJobCommand"),zY4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelCustomizationJobCommand"),EY4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelImportJobCommand"),UY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateModelInvocationJobCommand"),wY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{promptRouterArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreatePromptRouterCommand"),$Y4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{provisionedModelArn:_.expectString});return Object.assign(Q,Z),Q},"de_CreateProvisionedModelThroughputCommand"),qY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteCustomModelCommand"),NY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteFoundationModelAgreementCommand"),LY4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteGuardrailCommand"),MY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteImportedModelCommand"),RY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteInferenceProfileCommand"),OY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteMarketplaceModelEndpointCommand"),TY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteModelInvocationLoggingConfigurationCommand"),PY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeletePromptRouterCommand"),SY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeleteProvisionedModelThroughputCommand"),jY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_DeregisterMarketplaceModelEndpointCommand"),kY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{baseModelArn:_.expectString,creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customizationConfig:p((G)=>_._json(XB.awsExpectUnion(G)),"customizationConfig"),customizationType:_.expectString,failureMessage:_.expectString,hyperParameters:_._json,jobArn:_.expectString,jobName:_.expectString,modelArn:_.expectString,modelKmsKeyArn:_.expectString,modelName:_.expectString,modelStatus:_.expectString,outputDataConfig:_._json,trainingDataConfig:_._json,trainingMetrics:p((G)=>G42(G,B),"trainingMetrics"),validationDataConfig:_._json,validationMetrics:p((G)=>F42(G,B),"validationMetrics")});return Object.assign(Q,Z),Q},"de_GetCustomModelCommand"),yY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{applicationType:_.expectString,creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customerEncryptionKeyId:_.expectString,evaluationConfig:p((G)=>EJ4(XB.awsExpectUnion(G),B),"evaluationConfig"),failureMessages:_._json,inferenceConfig:p((G)=>UJ4(XB.awsExpectUnion(G),B),"inferenceConfig"),jobArn:_.expectString,jobDescription:_.expectString,jobName:_.expectString,jobType:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),outputDataConfig:_._json,roleArn:_.expectString,status:_.expectString});return Object.assign(Q,Z),Q},"de_GetEvaluationJobCommand"),_Y4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelDetails:_._json});return Object.assign(Q,Z),Q},"de_GetFoundationModelCommand"),xY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{agreementAvailability:_._json,authorizationStatus:_.expectString,entitlementAvailability:_.expectString,modelId:_.expectString,regionAvailability:_.expectString});return Object.assign(Q,Z),Q},"de_GetFoundationModelAvailabilityCommand"),vY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{blockedInputMessaging:_.expectString,blockedOutputsMessaging:_.expectString,contentPolicy:_._json,contextualGroundingPolicy:p((G)=>SJ4(G,B),"contextualGroundingPolicy"),createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),crossRegionDetails:_._json,description:_.expectString,failureRecommendations:_._json,guardrailArn:_.expectString,guardrailId:_.expectString,kmsKeyArn:_.expectString,name:_.expectString,sensitiveInformationPolicy:_._json,status:_.expectString,statusReasons:_._json,topicPolicy:_._json,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt"),version:_.expectString,wordPolicy:_._json});return Object.assign(Q,Z),Q},"de_GetGuardrailCommand"),bY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customModelUnits:_._json,instructSupported:_.expectBoolean,jobArn:_.expectString,jobName:_.expectString,modelArchitecture:_.expectString,modelArn:_.expectString,modelDataSource:p((G)=>_._json(XB.awsExpectUnion(G)),"modelDataSource"),modelKmsKeyArn:_.expectString,modelName:_.expectString});return Object.assign(Q,Z),Q},"de_GetImportedModelCommand"),fY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),description:_.expectString,inferenceProfileArn:_.expectString,inferenceProfileId:_.expectString,inferenceProfileName:_.expectString,models:_._json,status:_.expectString,type:_.expectString,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt")});return Object.assign(Q,Z),Q},"de_GetInferenceProfileCommand"),hY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>YN1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_GetMarketplaceModelEndpointCommand"),gY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),failureMessage:_.expectString,jobArn:_.expectString,sourceAccountId:_.expectString,sourceModelArn:_.expectString,sourceModelName:_.expectString,status:_.expectString,targetModelArn:_.expectString,targetModelKmsKeyArn:_.expectString,targetModelName:_.expectString,targetModelTags:_._json});return Object.assign(Q,Z),Q},"de_GetModelCopyJobCommand"),uY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{baseModelArn:_.expectString,clientRequestToken:_.expectString,creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),customizationConfig:p((G)=>_._json(XB.awsExpectUnion(G)),"customizationConfig"),customizationType:_.expectString,endTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"endTime"),failureMessage:_.expectString,hyperParameters:_._json,jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),outputDataConfig:_._json,outputModelArn:_.expectString,outputModelKmsKeyArn:_.expectString,outputModelName:_.expectString,roleArn:_.expectString,status:_.expectString,statusDetails:p((G)=>Z42(G,B),"statusDetails"),trainingDataConfig:_._json,trainingMetrics:p((G)=>G42(G,B),"trainingMetrics"),validationDataConfig:_._json,validationMetrics:p((G)=>F42(G,B),"validationMetrics"),vpcConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelCustomizationJobCommand"),mY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),endTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"endTime"),failureMessage:_.expectString,importedModelArn:_.expectString,importedModelKmsKeyArn:_.expectString,importedModelName:_.expectString,jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),modelDataSource:p((G)=>_._json(XB.awsExpectUnion(G)),"modelDataSource"),roleArn:_.expectString,status:_.expectString,vpcConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelImportJobCommand"),dY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{clientRequestToken:_.expectString,endTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"endTime"),inputDataConfig:p((G)=>_._json(XB.awsExpectUnion(G)),"inputDataConfig"),jobArn:_.expectString,jobExpirationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"jobExpirationTime"),jobName:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),message:_.expectString,modelId:_.expectString,outputDataConfig:p((G)=>_._json(XB.awsExpectUnion(G)),"outputDataConfig"),roleArn:_.expectString,status:_.expectString,submitTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"submitTime"),timeoutDurationInHours:_.expectInt32,vpcConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelInvocationJobCommand"),cY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{loggingConfig:_._json});return Object.assign(Q,Z),Q},"de_GetModelInvocationLoggingConfigurationCommand"),lY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{createdAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"createdAt"),description:_.expectString,fallbackModel:_._json,models:_._json,promptRouterArn:_.expectString,promptRouterName:_.expectString,routingCriteria:p((G)=>D42(G,B),"routingCriteria"),status:_.expectString,type:_.expectString,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt")});return Object.assign(Q,Z),Q},"de_GetPromptRouterCommand"),pY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{commitmentDuration:_.expectString,commitmentExpirationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"commitmentExpirationTime"),creationTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"creationTime"),desiredModelArn:_.expectString,desiredModelUnits:_.expectInt32,failureMessage:_.expectString,foundationModelArn:_.expectString,lastModifiedTime:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"lastModifiedTime"),modelArn:_.expectString,modelUnits:_.expectInt32,provisionedModelArn:_.expectString,provisionedModelName:_.expectString,status:_.expectString});return Object.assign(Q,Z),Q},"de_GetProvisionedModelThroughputCommand"),iY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{formData:B.base64Decoder});return Object.assign(Q,Z),Q},"de_GetUseCaseForModelAccessCommand"),nY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelSummaries:p((G)=>HJ4(G,B),"modelSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListCustomModelsCommand"),aY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{jobSummaries:p((G)=>wJ4(G,B),"jobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListEvaluationJobsCommand"),sY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelId:_.expectString,offers:_._json});return Object.assign(Q,Z),Q},"de_ListFoundationModelAgreementOffersCommand"),rY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelSummaries:_._json});return Object.assign(Q,Z),Q},"de_ListFoundationModelsCommand"),oY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{guardrails:p((G)=>jJ4(G,B),"guardrails"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListGuardrailsCommand"),tY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelSummaries:p((G)=>_J4(G,B),"modelSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListImportedModelsCommand"),eY4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{inferenceProfileSummaries:p((G)=>xJ4(G,B),"inferenceProfileSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListInferenceProfilesCommand"),AW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoints:p((G)=>gJ4(G,B),"marketplaceModelEndpoints"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListMarketplaceModelEndpointsCommand"),BW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelCopyJobSummaries:p((G)=>mJ4(G,B),"modelCopyJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelCopyJobsCommand"),QW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelCustomizationJobSummaries:p((G)=>cJ4(G,B),"modelCustomizationJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelCustomizationJobsCommand"),DW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{modelImportJobSummaries:p((G)=>pJ4(G,B),"modelImportJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelImportJobsCommand"),ZW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{invocationJobSummaries:p((G)=>nJ4(G,B),"invocationJobSummaries"),nextToken:_.expectString});return Object.assign(Q,Z),Q},"de_ListModelInvocationJobsCommand"),GW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{nextToken:_.expectString,promptRouterSummaries:p((G)=>sJ4(G,B),"promptRouterSummaries")});return Object.assign(Q,Z),Q},"de_ListPromptRoutersCommand"),FW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{nextToken:_.expectString,provisionedModelSummaries:p((G)=>oJ4(G,B),"provisionedModelSummaries")});return Object.assign(Q,Z),Q},"de_ListProvisionedModelThroughputsCommand"),IW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{tags:_._json});return Object.assign(Q,Z),Q},"de_ListTagsForResourceCommand"),YW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_PutModelInvocationLoggingConfigurationCommand"),WW4=p(async(A,B)=>{if(A.statusCode!==201&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_PutUseCaseForModelAccessCommand"),JW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>YN1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_RegisterMarketplaceModelEndpointCommand"),XW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_StopEvaluationJobCommand"),VW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_StopModelCustomizationJobCommand"),CW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_StopModelInvocationJobCommand"),KW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_TagResourceCommand"),HW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_UntagResourceCommand"),zW4=p(async(A,B)=>{if(A.statusCode!==202&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{guardrailArn:_.expectString,guardrailId:_.expectString,updatedAt:p((G)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(G)),"updatedAt"),version:_.expectString});return Object.assign(Q,Z),Q},"de_UpdateGuardrailCommand"),EW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)}),D=_.expectNonNull(_.expectObject(await XB.parseJsonBody(A.body,B)),"body"),Z=_.take(D,{marketplaceModelEndpoint:p((G)=>YN1(G,B),"marketplaceModelEndpoint")});return Object.assign(Q,Z),Q},"de_UpdateMarketplaceModelEndpointCommand"),UW4=p(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return jB(A,B);let Q=_.map({$metadata:i2(A)});return await _.collectBody(A.body,B),Q},"de_UpdateProvisionedModelThroughputCommand"),jB=p(async(A,B)=>{let Q={...A,body:await XB.parseJsonErrorBody(A.body,B)},D=XB.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.bedrock#AccessDeniedException":throw await $W4(Q,B);case"ConflictException":case"com.amazonaws.bedrock#ConflictException":throw await qW4(Q,B);case"InternalServerException":case"com.amazonaws.bedrock#InternalServerException":throw await NW4(Q,B);case"ResourceNotFoundException":case"com.amazonaws.bedrock#ResourceNotFoundException":throw await LW4(Q,B);case"ThrottlingException":case"com.amazonaws.bedrock#ThrottlingException":throw await OW4(Q,B);case"ValidationException":case"com.amazonaws.bedrock#ValidationException":throw await PW4(Q,B);case"ServiceQuotaExceededException":case"com.amazonaws.bedrock#ServiceQuotaExceededException":throw await MW4(Q,B);case"TooManyTagsException":case"com.amazonaws.bedrock#TooManyTagsException":throw await TW4(Q,B);case"ServiceUnavailableException":case"com.amazonaws.bedrock#ServiceUnavailableException":throw await RW4(Q,B);default:let Z=Q.body;return wW4({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),wW4=_.withBaseException(kw),$W4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new N92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),qW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new T92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ConflictExceptionRes"),NW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new L92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),LW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new M92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),MW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new P92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ServiceQuotaExceededExceptionRes"),RW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new S92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ServiceUnavailableExceptionRes"),OW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new R92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ThrottlingExceptionRes"),TW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString,resourceName:_.expectString});Object.assign(Q,Z);let G=new j92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_TooManyTagsExceptionRes"),PW4=p(async(A,B)=>{let Q=_.map({}),D=A.body,Z=_.take(D,{message:_.expectString});Object.assign(Q,Z);let G=new O92({$metadata:i2(A),...Q});return _.decorateServiceException(G,A.body)},"de_ValidationExceptionRes"),n60=p((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=SW4(Z,B),Q},{})},"se_AdditionalModelRequestFields"),SW4=p((A,B)=>{return A},"se_AdditionalModelRequestFieldsValue"),jW4=p((A,B)=>{return _.take(A,{customMetricConfig:p((Q)=>kW4(Q,B),"customMetricConfig"),datasetMetricConfigs:_._json,evaluatorModelConfig:_._json})},"se_AutomatedEvaluationConfig"),kW4=p((A,B)=>{return _.take(A,{customMetrics:p((Q)=>yW4(Q,B),"customMetrics"),evaluatorModelConfig:_._json})},"se_AutomatedEvaluationCustomMetricConfig"),yW4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return _W4(Q,B)})},"se_AutomatedEvaluationCustomMetrics"),_W4=p((A,B)=>{return eq1.visit(A,{customMetricDefinition:p((Q)=>({customMetricDefinition:vW4(Q,B)}),"customMetricDefinition"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_AutomatedEvaluationCustomMetricSource"),xW4=p((A,B)=>{return _.take(A,{contentType:[],data:B.base64Encoder,identifier:[]})},"se_ByteContentDoc"),vW4=p((A,B)=>{return _.take(A,{instructions:[],name:[],ratingScale:p((Q)=>oW4(Q,B),"ratingScale")})},"se_CustomMetricDefinition"),bW4=p((A,B)=>{return AN1.visit(A,{automated:p((Q)=>({automated:jW4(Q,B)}),"automated"),human:p((Q)=>({human:_._json(Q)}),"human"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_EvaluationConfig"),fW4=p((A,B)=>{return ZN1.visit(A,{models:p((Q)=>({models:_._json(Q)}),"models"),ragConfigs:p((Q)=>({ragConfigs:rW4(Q,B)}),"ragConfigs"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_EvaluationInferenceConfig"),hW4=p((A,B)=>{return _.take(A,{byteContent:p((Q)=>xW4(Q,B),"byteContent"),s3Location:_._json,sourceType:[]})},"se_ExternalSource"),gW4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return hW4(Q,B)})},"se_ExternalSources"),uW4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>n60(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>oQ2(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"se_ExternalSourcesGenerationConfiguration"),mW4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>uW4(Q,B),"generationConfiguration"),modelArn:[],sources:p((Q)=>gW4(Q,B),"sources")})},"se_ExternalSourcesRetrieveAndGenerateConfiguration"),Sw=p((A,B)=>{return _.take(A,{key:[],value:p((Q)=>dW4(Q,B),"value")})},"se_FilterAttribute"),dW4=p((A,B)=>{return A},"se_FilterValue"),cW4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>n60(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>oQ2(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"se_GenerationConfiguration"),lW4=p((A,B)=>{return _.take(A,{action:[],enabled:[],threshold:_.serializeFloat,type:[]})},"se_GuardrailContextualGroundingFilterConfig"),pW4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return lW4(Q,B)})},"se_GuardrailContextualGroundingFiltersConfig"),rQ2=p((A,B)=>{return _.take(A,{filtersConfig:p((Q)=>pW4(Q,B),"filtersConfig")})},"se_GuardrailContextualGroundingPolicyConfig"),oQ2=p((A,B)=>{return _.take(A,{textInferenceConfig:p((Q)=>DJ4(Q,B),"textInferenceConfig")})},"se_KbInferenceConfig"),iW4=p((A,B)=>{return QN1.visit(A,{retrieveAndGenerateConfig:p((Q)=>({retrieveAndGenerateConfig:AJ4(Q,B)}),"retrieveAndGenerateConfig"),retrieveConfig:p((Q)=>({retrieveConfig:BJ4(Q,B)}),"retrieveConfig"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_KnowledgeBaseConfig"),tQ2=p((A,B)=>{return _.take(A,{vectorSearchConfiguration:p((Q)=>aW4(Q,B),"vectorSearchConfiguration")})},"se_KnowledgeBaseRetrievalConfiguration"),nW4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>cW4(Q,B),"generationConfiguration"),knowledgeBaseId:[],modelArn:[],orchestrationConfiguration:_._json,retrievalConfiguration:p((Q)=>tQ2(Q,B),"retrievalConfiguration")})},"se_KnowledgeBaseRetrieveAndGenerateConfiguration"),aW4=p((A,B)=>{return _.take(A,{filter:p((Q)=>eQ2(Q,B),"filter"),implicitFilterConfiguration:_._json,numberOfResults:[],overrideSearchType:[],rerankingConfiguration:p((Q)=>FJ4(Q,B),"rerankingConfiguration")})},"se_KnowledgeBaseVectorSearchConfiguration"),sW4=p((A,B)=>{return DN1.visit(A,{knowledgeBaseConfig:p((Q)=>({knowledgeBaseConfig:iW4(Q,B)}),"knowledgeBaseConfig"),precomputedRagSourceConfig:p((Q)=>({precomputedRagSourceConfig:_._json(Q)}),"precomputedRagSourceConfig"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_RAGConfig"),rW4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return sW4(Q,B)})},"se_RagConfigs"),oW4=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return tW4(Q,B)})},"se_RatingScale"),tW4=p((A,B)=>{return _.take(A,{definition:[],value:p((Q)=>eW4(Q,B),"value")})},"se_RatingScaleItem"),eW4=p((A,B)=>{return tq1.visit(A,{floatValue:p((Q)=>({floatValue:_.serializeFloat(Q)}),"floatValue"),stringValue:p((Q)=>({stringValue:Q}),"stringValue"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_RatingScaleItemValue"),eQ2=p((A,B)=>{return BN1.visit(A,{andAll:p((Q)=>({andAll:D92(Q,B)}),"andAll"),equals:p((Q)=>({equals:Sw(Q,B)}),"equals"),greaterThan:p((Q)=>({greaterThan:Sw(Q,B)}),"greaterThan"),greaterThanOrEquals:p((Q)=>({greaterThanOrEquals:Sw(Q,B)}),"greaterThanOrEquals"),in:p((Q)=>({in:Sw(Q,B)}),"in"),lessThan:p((Q)=>({lessThan:Sw(Q,B)}),"lessThan"),lessThanOrEquals:p((Q)=>({lessThanOrEquals:Sw(Q,B)}),"lessThanOrEquals"),listContains:p((Q)=>({listContains:Sw(Q,B)}),"listContains"),notEquals:p((Q)=>({notEquals:Sw(Q,B)}),"notEquals"),notIn:p((Q)=>({notIn:Sw(Q,B)}),"notIn"),orAll:p((Q)=>({orAll:D92(Q,B)}),"orAll"),startsWith:p((Q)=>({startsWith:Sw(Q,B)}),"startsWith"),stringContains:p((Q)=>({stringContains:Sw(Q,B)}),"stringContains"),_:p((Q,D)=>({[Q]:D}),"_")})},"se_RetrievalFilter"),D92=p((A,B)=>{return A.filter((Q)=>Q!=null).map((Q)=>{return eQ2(Q,B)})},"se_RetrievalFilterList"),AJ4=p((A,B)=>{return _.take(A,{externalSourcesConfiguration:p((Q)=>mW4(Q,B),"externalSourcesConfiguration"),knowledgeBaseConfiguration:p((Q)=>nW4(Q,B),"knowledgeBaseConfiguration"),type:[]})},"se_RetrieveAndGenerateConfiguration"),BJ4=p((A,B)=>{return _.take(A,{knowledgeBaseId:[],knowledgeBaseRetrievalConfiguration:p((Q)=>tQ2(Q,B),"knowledgeBaseRetrievalConfiguration")})},"se_RetrieveConfig"),QJ4=p((A,B)=>{return _.take(A,{responseQualityDifference:_.serializeFloat})},"se_RoutingCriteria"),DJ4=p((A,B)=>{return _.take(A,{maxTokens:[],stopSequences:_._json,temperature:_.serializeFloat,topP:_.serializeFloat})},"se_TextInferenceConfig"),ZJ4=p((A,B)=>{return _.take(A,{metadataConfiguration:_._json,modelConfiguration:p((Q)=>GJ4(Q,B),"modelConfiguration"),numberOfRerankedResults:[]})},"se_VectorSearchBedrockRerankingConfiguration"),GJ4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>n60(Q,B),"additionalModelRequestFields"),modelArn:[]})},"se_VectorSearchBedrockRerankingModelConfiguration"),FJ4=p((A,B)=>{return _.take(A,{bedrockRerankingConfiguration:p((Q)=>ZJ4(Q,B),"bedrockRerankingConfiguration"),type:[]})},"se_VectorSearchRerankingConfiguration"),a60=p((A,B)=>{return Object.entries(A).reduce((Q,[D,Z])=>{if(Z===null)return Q;return Q[D]=IJ4(Z,B),Q},{})},"de_AdditionalModelRequestFields"),IJ4=p((A,B)=>{return A},"de_AdditionalModelRequestFieldsValue"),YJ4=p((A,B)=>{return _.take(A,{customMetricConfig:p((Q)=>WJ4(Q,B),"customMetricConfig"),datasetMetricConfigs:_._json,evaluatorModelConfig:p((Q)=>_._json(XB.awsExpectUnion(Q)),"evaluatorModelConfig")})},"de_AutomatedEvaluationConfig"),WJ4=p((A,B)=>{return _.take(A,{customMetrics:p((Q)=>JJ4(Q,B),"customMetrics"),evaluatorModelConfig:_._json})},"de_AutomatedEvaluationCustomMetricConfig"),JJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return XJ4(XB.awsExpectUnion(D),B)})},"de_AutomatedEvaluationCustomMetrics"),XJ4=p((A,B)=>{if(A.customMetricDefinition!=null)return{customMetricDefinition:CJ4(A.customMetricDefinition,B)};return{$unknown:Object.entries(A)[0]}},"de_AutomatedEvaluationCustomMetricSource"),VJ4=p((A,B)=>{return _.take(A,{contentType:_.expectString,data:B.base64Decoder,identifier:_.expectString})},"de_ByteContentDoc"),CJ4=p((A,B)=>{return _.take(A,{instructions:_.expectString,name:_.expectString,ratingScale:p((Q)=>BX4(Q,B),"ratingScale")})},"de_CustomMetricDefinition"),KJ4=p((A,B)=>{return _.take(A,{baseModelArn:_.expectString,baseModelName:_.expectString,creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),customizationType:_.expectString,modelArn:_.expectString,modelName:_.expectString,modelStatus:_.expectString,ownerAccountId:_.expectString})},"de_CustomModelSummary"),HJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return KJ4(D,B)})},"de_CustomModelSummaryList"),zJ4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_DataProcessingDetails"),EJ4=p((A,B)=>{if(A.automated!=null)return{automated:YJ4(A.automated,B)};if(A.human!=null)return{human:_._json(A.human)};return{$unknown:Object.entries(A)[0]}},"de_EvaluationConfig"),UJ4=p((A,B)=>{if(A.models!=null)return{models:_._json(A.models)};if(A.ragConfigs!=null)return{ragConfigs:AX4(A.ragConfigs,B)};return{$unknown:Object.entries(A)[0]}},"de_EvaluationInferenceConfig"),wJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return $J4(D,B)})},"de_EvaluationSummaries"),$J4=p((A,B)=>{return _.take(A,{applicationType:_.expectString,creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),customMetricsEvaluatorModelIdentifiers:_._json,evaluationTaskTypes:_._json,evaluatorModelIdentifiers:_._json,inferenceConfigSummary:_._json,jobArn:_.expectString,jobName:_.expectString,jobType:_.expectString,modelIdentifiers:_._json,ragIdentifiers:_._json,status:_.expectString})},"de_EvaluationSummary"),qJ4=p((A,B)=>{return _.take(A,{byteContent:p((Q)=>VJ4(Q,B),"byteContent"),s3Location:_._json,sourceType:_.expectString})},"de_ExternalSource"),NJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return qJ4(D,B)})},"de_ExternalSources"),LJ4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>a60(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>A42(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"de_ExternalSourcesGenerationConfiguration"),MJ4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>LJ4(Q,B),"generationConfiguration"),modelArn:_.expectString,sources:p((Q)=>NJ4(Q,B),"sources")})},"de_ExternalSourcesRetrieveAndGenerateConfiguration"),jw=p((A,B)=>{return _.take(A,{key:_.expectString,value:p((Q)=>RJ4(Q,B),"value")})},"de_FilterAttribute"),RJ4=p((A,B)=>{return A},"de_FilterValue"),OJ4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>a60(Q,B),"additionalModelRequestFields"),guardrailConfiguration:_._json,kbInferenceConfig:p((Q)=>A42(Q,B),"kbInferenceConfig"),promptTemplate:_._json})},"de_GenerationConfiguration"),TJ4=p((A,B)=>{return _.take(A,{action:_.expectString,enabled:_.expectBoolean,threshold:_.limitedParseDouble,type:_.expectString})},"de_GuardrailContextualGroundingFilter"),PJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return TJ4(D,B)})},"de_GuardrailContextualGroundingFilters"),SJ4=p((A,B)=>{return _.take(A,{filters:p((Q)=>PJ4(Q,B),"filters")})},"de_GuardrailContextualGroundingPolicy"),jJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return kJ4(D,B)})},"de_GuardrailSummaries"),kJ4=p((A,B)=>{return _.take(A,{arn:_.expectString,createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),crossRegionDetails:_._json,description:_.expectString,id:_.expectString,name:_.expectString,status:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt"),version:_.expectString})},"de_GuardrailSummary"),yJ4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),instructSupported:_.expectBoolean,modelArchitecture:_.expectString,modelArn:_.expectString,modelName:_.expectString})},"de_ImportedModelSummary"),_J4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return yJ4(D,B)})},"de_ImportedModelSummaryList"),xJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return vJ4(D,B)})},"de_InferenceProfileSummaries"),vJ4=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),description:_.expectString,inferenceProfileArn:_.expectString,inferenceProfileId:_.expectString,inferenceProfileName:_.expectString,models:_._json,status:_.expectString,type:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_InferenceProfileSummary"),A42=p((A,B)=>{return _.take(A,{textInferenceConfig:p((Q)=>FX4(Q,B),"textInferenceConfig")})},"de_KbInferenceConfig"),bJ4=p((A,B)=>{if(A.retrieveAndGenerateConfig!=null)return{retrieveAndGenerateConfig:ZX4(A.retrieveAndGenerateConfig,B)};if(A.retrieveConfig!=null)return{retrieveConfig:GX4(A.retrieveConfig,B)};return{$unknown:Object.entries(A)[0]}},"de_KnowledgeBaseConfig"),B42=p((A,B)=>{return _.take(A,{vectorSearchConfiguration:p((Q)=>hJ4(Q,B),"vectorSearchConfiguration")})},"de_KnowledgeBaseRetrievalConfiguration"),fJ4=p((A,B)=>{return _.take(A,{generationConfiguration:p((Q)=>OJ4(Q,B),"generationConfiguration"),knowledgeBaseId:_.expectString,modelArn:_.expectString,orchestrationConfiguration:_._json,retrievalConfiguration:p((Q)=>B42(Q,B),"retrievalConfiguration")})},"de_KnowledgeBaseRetrieveAndGenerateConfiguration"),hJ4=p((A,B)=>{return _.take(A,{filter:p((Q)=>Q42(XB.awsExpectUnion(Q),B),"filter"),implicitFilterConfiguration:_._json,numberOfResults:_.expectInt32,overrideSearchType:_.expectString,rerankingConfiguration:p((Q)=>VX4(Q,B),"rerankingConfiguration")})},"de_KnowledgeBaseVectorSearchConfiguration"),YN1=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),endpointArn:_.expectString,endpointConfig:p((Q)=>_._json(XB.awsExpectUnion(Q)),"endpointConfig"),endpointStatus:_.expectString,endpointStatusMessage:_.expectString,modelSourceIdentifier:_.expectString,status:_.expectString,statusMessage:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_MarketplaceModelEndpoint"),gJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return uJ4(D,B)})},"de_MarketplaceModelEndpointSummaries"),uJ4=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),endpointArn:_.expectString,modelSourceIdentifier:_.expectString,status:_.expectString,statusMessage:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_MarketplaceModelEndpointSummary"),mJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return dJ4(D,B)})},"de_ModelCopyJobSummaries"),dJ4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),failureMessage:_.expectString,jobArn:_.expectString,sourceAccountId:_.expectString,sourceModelArn:_.expectString,sourceModelName:_.expectString,status:_.expectString,targetModelArn:_.expectString,targetModelKmsKeyArn:_.expectString,targetModelName:_.expectString,targetModelTags:_._json})},"de_ModelCopyJobSummary"),cJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return lJ4(D,B)})},"de_ModelCustomizationJobSummaries"),lJ4=p((A,B)=>{return _.take(A,{baseModelArn:_.expectString,creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),customModelArn:_.expectString,customModelName:_.expectString,customizationType:_.expectString,endTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"endTime"),jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString,statusDetails:p((Q)=>Z42(Q,B),"statusDetails")})},"de_ModelCustomizationJobSummary"),pJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return iJ4(D,B)})},"de_ModelImportJobSummaries"),iJ4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),endTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"endTime"),importedModelArn:_.expectString,importedModelName:_.expectString,jobArn:_.expectString,jobName:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_ModelImportJobSummary"),nJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return aJ4(D,B)})},"de_ModelInvocationJobSummaries"),aJ4=p((A,B)=>{return _.take(A,{clientRequestToken:_.expectString,endTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"endTime"),inputDataConfig:p((Q)=>_._json(XB.awsExpectUnion(Q)),"inputDataConfig"),jobArn:_.expectString,jobExpirationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"jobExpirationTime"),jobName:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),message:_.expectString,modelId:_.expectString,outputDataConfig:p((Q)=>_._json(XB.awsExpectUnion(Q)),"outputDataConfig"),roleArn:_.expectString,status:_.expectString,submitTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"submitTime"),timeoutDurationInHours:_.expectInt32,vpcConfig:_._json})},"de_ModelInvocationJobSummary"),sJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return rJ4(D,B)})},"de_PromptRouterSummaries"),rJ4=p((A,B)=>{return _.take(A,{createdAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"createdAt"),description:_.expectString,fallbackModel:_._json,models:_._json,promptRouterArn:_.expectString,promptRouterName:_.expectString,routingCriteria:p((Q)=>D42(Q,B),"routingCriteria"),status:_.expectString,type:_.expectString,updatedAt:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"updatedAt")})},"de_PromptRouterSummary"),oJ4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return tJ4(D,B)})},"de_ProvisionedModelSummaries"),tJ4=p((A,B)=>{return _.take(A,{commitmentDuration:_.expectString,commitmentExpirationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"commitmentExpirationTime"),creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),desiredModelArn:_.expectString,desiredModelUnits:_.expectInt32,foundationModelArn:_.expectString,lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),modelArn:_.expectString,modelUnits:_.expectInt32,provisionedModelArn:_.expectString,provisionedModelName:_.expectString,status:_.expectString})},"de_ProvisionedModelSummary"),eJ4=p((A,B)=>{if(A.knowledgeBaseConfig!=null)return{knowledgeBaseConfig:bJ4(XB.awsExpectUnion(A.knowledgeBaseConfig),B)};if(A.precomputedRagSourceConfig!=null)return{precomputedRagSourceConfig:_._json(XB.awsExpectUnion(A.precomputedRagSourceConfig))};return{$unknown:Object.entries(A)[0]}},"de_RAGConfig"),AX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return eJ4(XB.awsExpectUnion(D),B)})},"de_RagConfigs"),BX4=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return QX4(D,B)})},"de_RatingScale"),QX4=p((A,B)=>{return _.take(A,{definition:_.expectString,value:p((Q)=>DX4(XB.awsExpectUnion(Q),B),"value")})},"de_RatingScaleItem"),DX4=p((A,B)=>{if(_.limitedParseFloat32(A.floatValue)!==void 0)return{floatValue:_.limitedParseFloat32(A.floatValue)};if(_.expectString(A.stringValue)!==void 0)return{stringValue:_.expectString(A.stringValue)};return{$unknown:Object.entries(A)[0]}},"de_RatingScaleItemValue"),Q42=p((A,B)=>{if(A.andAll!=null)return{andAll:Z92(A.andAll,B)};if(A.equals!=null)return{equals:jw(A.equals,B)};if(A.greaterThan!=null)return{greaterThan:jw(A.greaterThan,B)};if(A.greaterThanOrEquals!=null)return{greaterThanOrEquals:jw(A.greaterThanOrEquals,B)};if(A.in!=null)return{in:jw(A.in,B)};if(A.lessThan!=null)return{lessThan:jw(A.lessThan,B)};if(A.lessThanOrEquals!=null)return{lessThanOrEquals:jw(A.lessThanOrEquals,B)};if(A.listContains!=null)return{listContains:jw(A.listContains,B)};if(A.notEquals!=null)return{notEquals:jw(A.notEquals,B)};if(A.notIn!=null)return{notIn:jw(A.notIn,B)};if(A.orAll!=null)return{orAll:Z92(A.orAll,B)};if(A.startsWith!=null)return{startsWith:jw(A.startsWith,B)};if(A.stringContains!=null)return{stringContains:jw(A.stringContains,B)};return{$unknown:Object.entries(A)[0]}},"de_RetrievalFilter"),Z92=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return Q42(XB.awsExpectUnion(D),B)})},"de_RetrievalFilterList"),ZX4=p((A,B)=>{return _.take(A,{externalSourcesConfiguration:p((Q)=>MJ4(Q,B),"externalSourcesConfiguration"),knowledgeBaseConfiguration:p((Q)=>fJ4(Q,B),"knowledgeBaseConfiguration"),type:_.expectString})},"de_RetrieveAndGenerateConfiguration"),GX4=p((A,B)=>{return _.take(A,{knowledgeBaseId:_.expectString,knowledgeBaseRetrievalConfiguration:p((Q)=>B42(Q,B),"knowledgeBaseRetrievalConfiguration")})},"de_RetrieveConfig"),D42=p((A,B)=>{return _.take(A,{responseQualityDifference:_.limitedParseDouble})},"de_RoutingCriteria"),Z42=p((A,B)=>{return _.take(A,{dataProcessingDetails:p((Q)=>zJ4(Q,B),"dataProcessingDetails"),trainingDetails:p((Q)=>IX4(Q,B),"trainingDetails"),validationDetails:p((Q)=>YX4(Q,B),"validationDetails")})},"de_StatusDetails"),FX4=p((A,B)=>{return _.take(A,{maxTokens:_.expectInt32,stopSequences:_._json,temperature:_.limitedParseFloat32,topP:_.limitedParseFloat32})},"de_TextInferenceConfig"),IX4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_TrainingDetails"),G42=p((A,B)=>{return _.take(A,{trainingLoss:_.limitedParseFloat32})},"de_TrainingMetrics"),YX4=p((A,B)=>{return _.take(A,{creationTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"creationTime"),lastModifiedTime:p((Q)=>_.expectNonNull(_.parseRfc3339DateTimeWithOffset(Q)),"lastModifiedTime"),status:_.expectString})},"de_ValidationDetails"),F42=p((A,B)=>{return(A||[]).filter((D)=>D!=null).map((D)=>{return WX4(D,B)})},"de_ValidationMetrics"),WX4=p((A,B)=>{return _.take(A,{validationLoss:_.limitedParseFloat32})},"de_ValidatorMetric"),JX4=p((A,B)=>{return _.take(A,{metadataConfiguration:_._json,modelConfiguration:p((Q)=>XX4(Q,B),"modelConfiguration"),numberOfRerankedResults:_.expectInt32})},"de_VectorSearchBedrockRerankingConfiguration"),XX4=p((A,B)=>{return _.take(A,{additionalModelRequestFields:p((Q)=>a60(Q,B),"additionalModelRequestFields"),modelArn:_.expectString})},"de_VectorSearchBedrockRerankingModelConfiguration"),VX4=p((A,B)=>{return _.take(A,{bedrockRerankingConfiguration:p((Q)=>JX4(Q,B),"bedrockRerankingConfiguration"),type:_.expectString})},"de_VectorSearchRerankingConfiguration"),i2=p((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),G92="applicationTypeEquals",F92="byCustomizationType",I92="byInferenceType",Y92="baseModelArnEquals",W92="byOutputModality",J92="byProvider",KV="creationTimeAfter",HV="creationTimeBefore",X92="foundationModelArnEquals",V92="guardrailIdentifier",GN1="guardrailVersion",C92="isOwned",K92="modelArnEquals",XD="maxResults",H92="modelStatus",CX4="modelSourceEquals",KX4="modelSourceIdentifier",zV="nameContains",VD="nextToken",HX4="outputModelNameContains",z92="offerType",E92="sourceAccountEquals",dW="sortBy",uz="statusEquals",U92="sourceModelArnEquals",cW="sortOrder",w92="submitTimeAfter",$92="submitTimeBefore",f60="type",zX4="typeEquals",EX4="targetModelNameContains",I42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","BatchDeleteEvaluationJob",{}).n("BedrockClient","BatchDeleteEvaluationJobCommand").f(x92,f92).ser(sF4).de(IY4).build(){static{p(this,"BatchDeleteEvaluationJobCommand")}},Y42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateCustomModel",{}).n("BedrockClient","CreateCustomModelCommand").f(void 0,void 0).ser(rF4).de(YY4).build(){static{p(this,"CreateCustomModelCommand")}},W42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateEvaluationJob",{}).n("BedrockClient","CreateEvaluationJobCommand").f(aQ2,void 0).ser(oF4).de(WY4).build(){static{p(this,"CreateEvaluationJobCommand")}},J42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateFoundationModelAgreement",{}).n("BedrockClient","CreateFoundationModelAgreementCommand").f(void 0,void 0).ser(tF4).de(JY4).build(){static{p(this,"CreateFoundationModelAgreementCommand")}},X42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateGuardrail",{}).n("BedrockClient","CreateGuardrailCommand").f(VQ2,void 0).ser(eF4).de(XY4).build(){static{p(this,"CreateGuardrailCommand")}},V42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateGuardrailVersion",{}).n("BedrockClient","CreateGuardrailVersionCommand").f(CQ2,void 0).ser(AI4).de(VY4).build(){static{p(this,"CreateGuardrailVersionCommand")}},C42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateInferenceProfile",{}).n("BedrockClient","CreateInferenceProfileCommand").f(SQ2,void 0).ser(BI4).de(CY4).build(){static{p(this,"CreateInferenceProfileCommand")}},K42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateMarketplaceModelEndpoint",{}).n("BedrockClient","CreateMarketplaceModelEndpointCommand").f(void 0,void 0).ser(QI4).de(KY4).build(){static{p(this,"CreateMarketplaceModelEndpointCommand")}},H42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelCopyJob",{}).n("BedrockClient","CreateModelCopyJobCommand").f(void 0,void 0).ser(DI4).de(HY4).build(){static{p(this,"CreateModelCopyJobCommand")}},z42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelCustomizationJob",{}).n("BedrockClient","CreateModelCustomizationJobCommand").f(uQ2,void 0).ser(ZI4).de(zY4).build(){static{p(this,"CreateModelCustomizationJobCommand")}},E42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelImportJob",{}).n("BedrockClient","CreateModelImportJobCommand").f(void 0,void 0).ser(GI4).de(EY4).build(){static{p(this,"CreateModelImportJobCommand")}},U42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateModelInvocationJob",{}).n("BedrockClient","CreateModelInvocationJobCommand").f(void 0,void 0).ser(FI4).de(UY4).build(){static{p(this,"CreateModelInvocationJobCommand")}},w42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreatePromptRouter",{}).n("BedrockClient","CreatePromptRouterCommand").f(bQ2,void 0).ser(II4).de(wY4).build(){static{p(this,"CreatePromptRouterCommand")}},$42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","CreateProvisionedModelThroughput",{}).n("BedrockClient","CreateProvisionedModelThroughputCommand").f(void 0,void 0).ser(YI4).de($Y4).build(){static{p(this,"CreateProvisionedModelThroughputCommand")}},q42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteCustomModel",{}).n("BedrockClient","DeleteCustomModelCommand").f(void 0,void 0).ser(WI4).de(qY4).build(){static{p(this,"DeleteCustomModelCommand")}},N42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteFoundationModelAgreement",{}).n("BedrockClient","DeleteFoundationModelAgreementCommand").f(void 0,void 0).ser(JI4).de(NY4).build(){static{p(this,"DeleteFoundationModelAgreementCommand")}},L42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteGuardrail",{}).n("BedrockClient","DeleteGuardrailCommand").f(void 0,void 0).ser(XI4).de(LY4).build(){static{p(this,"DeleteGuardrailCommand")}},M42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteImportedModel",{}).n("BedrockClient","DeleteImportedModelCommand").f(void 0,void 0).ser(VI4).de(MY4).build(){static{p(this,"DeleteImportedModelCommand")}},R42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteInferenceProfile",{}).n("BedrockClient","DeleteInferenceProfileCommand").f(void 0,void 0).ser(CI4).de(RY4).build(){static{p(this,"DeleteInferenceProfileCommand")}},O42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteMarketplaceModelEndpoint",{}).n("BedrockClient","DeleteMarketplaceModelEndpointCommand").f(void 0,void 0).ser(KI4).de(OY4).build(){static{p(this,"DeleteMarketplaceModelEndpointCommand")}},T42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteModelInvocationLoggingConfiguration",{}).n("BedrockClient","DeleteModelInvocationLoggingConfigurationCommand").f(void 0,void 0).ser(HI4).de(TY4).build(){static{p(this,"DeleteModelInvocationLoggingConfigurationCommand")}},P42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeletePromptRouter",{}).n("BedrockClient","DeletePromptRouterCommand").f(void 0,void 0).ser(zI4).de(PY4).build(){static{p(this,"DeletePromptRouterCommand")}},S42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeleteProvisionedModelThroughput",{}).n("BedrockClient","DeleteProvisionedModelThroughputCommand").f(void 0,void 0).ser(EI4).de(SY4).build(){static{p(this,"DeleteProvisionedModelThroughputCommand")}},j42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","DeregisterMarketplaceModelEndpoint",{}).n("BedrockClient","DeregisterMarketplaceModelEndpointCommand").f(void 0,void 0).ser(UI4).de(jY4).build(){static{p(this,"DeregisterMarketplaceModelEndpointCommand")}},k42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetCustomModel",{}).n("BedrockClient","GetCustomModelCommand").f(void 0,_92).ser(wI4).de(kY4).build(){static{p(this,"GetCustomModelCommand")}},y42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetEvaluationJob",{}).n("BedrockClient","GetEvaluationJobCommand").f(DQ2,sQ2).ser($I4).de(yY4).build(){static{p(this,"GetEvaluationJobCommand")}},_42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetFoundationModelAvailability",{}).n("BedrockClient","GetFoundationModelAvailabilityCommand").f(void 0,void 0).ser(NI4).de(xY4).build(){static{p(this,"GetFoundationModelAvailabilityCommand")}},x42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetFoundationModel",{}).n("BedrockClient","GetFoundationModelCommand").f(void 0,void 0).ser(qI4).de(_Y4).build(){static{p(this,"GetFoundationModelCommand")}},v42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetGuardrail",{}).n("BedrockClient","GetGuardrailCommand").f(void 0,RQ2).ser(LI4).de(vY4).build(){static{p(this,"GetGuardrailCommand")}},b42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetImportedModel",{}).n("BedrockClient","GetImportedModelCommand").f(void 0,void 0).ser(MI4).de(bY4).build(){static{p(this,"GetImportedModelCommand")}},f42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetInferenceProfile",{}).n("BedrockClient","GetInferenceProfileCommand").f(void 0,jQ2).ser(RI4).de(fY4).build(){static{p(this,"GetInferenceProfileCommand")}},h42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetMarketplaceModelEndpoint",{}).n("BedrockClient","GetMarketplaceModelEndpointCommand").f(void 0,void 0).ser(OI4).de(hY4).build(){static{p(this,"GetMarketplaceModelEndpointCommand")}},g42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelCopyJob",{}).n("BedrockClient","GetModelCopyJobCommand").f(void 0,void 0).ser(TI4).de(gY4).build(){static{p(this,"GetModelCopyJobCommand")}},u42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelCustomizationJob",{}).n("BedrockClient","GetModelCustomizationJobCommand").f(void 0,mQ2).ser(PI4).de(uY4).build(){static{p(this,"GetModelCustomizationJobCommand")}},m42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelImportJob",{}).n("BedrockClient","GetModelImportJobCommand").f(void 0,void 0).ser(SI4).de(mY4).build(){static{p(this,"GetModelImportJobCommand")}},d42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelInvocationJob",{}).n("BedrockClient","GetModelInvocationJobCommand").f(void 0,_Q2).ser(jI4).de(dY4).build(){static{p(this,"GetModelInvocationJobCommand")}},c42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetModelInvocationLoggingConfiguration",{}).n("BedrockClient","GetModelInvocationLoggingConfigurationCommand").f(void 0,void 0).ser(kI4).de(cY4).build(){static{p(this,"GetModelInvocationLoggingConfigurationCommand")}},l42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetPromptRouter",{}).n("BedrockClient","GetPromptRouterCommand").f(void 0,fQ2).ser(yI4).de(lY4).build(){static{p(this,"GetPromptRouterCommand")}},p42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetProvisionedModelThroughput",{}).n("BedrockClient","GetProvisionedModelThroughputCommand").f(void 0,void 0).ser(_I4).de(pY4).build(){static{p(this,"GetProvisionedModelThroughputCommand")}},i42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","GetUseCaseForModelAccess",{}).n("BedrockClient","GetUseCaseForModelAccessCommand").f(void 0,void 0).ser(xI4).de(iY4).build(){static{p(this,"GetUseCaseForModelAccessCommand")}},s60=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListCustomModels",{}).n("BedrockClient","ListCustomModelsCommand").f(void 0,void 0).ser(vI4).de(nY4).build(){static{p(this,"ListCustomModelsCommand")}},r60=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListEvaluationJobs",{}).n("BedrockClient","ListEvaluationJobsCommand").f(void 0,void 0).ser(bI4).de(aY4).build(){static{p(this,"ListEvaluationJobsCommand")}},n42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListFoundationModelAgreementOffers",{}).n("BedrockClient","ListFoundationModelAgreementOffersCommand").f(void 0,void 0).ser(fI4).de(sY4).build(){static{p(this,"ListFoundationModelAgreementOffersCommand")}},a42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListFoundationModels",{}).n("BedrockClient","ListFoundationModelsCommand").f(void 0,void 0).ser(hI4).de(rY4).build(){static{p(this,"ListFoundationModelsCommand")}},o60=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListGuardrails",{}).n("BedrockClient","ListGuardrailsCommand").f(void 0,TQ2).ser(gI4).de(oY4).build(){static{p(this,"ListGuardrailsCommand")}},t60=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListImportedModels",{}).n("BedrockClient","ListImportedModelsCommand").f(void 0,void 0).ser(uI4).de(tY4).build(){static{p(this,"ListImportedModelsCommand")}},e60=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListInferenceProfiles",{}).n("BedrockClient","ListInferenceProfilesCommand").f(void 0,yQ2).ser(mI4).de(eY4).build(){static{p(this,"ListInferenceProfilesCommand")}},A80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListMarketplaceModelEndpoints",{}).n("BedrockClient","ListMarketplaceModelEndpointsCommand").f(void 0,void 0).ser(dI4).de(AW4).build(){static{p(this,"ListMarketplaceModelEndpointsCommand")}},B80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelCopyJobs",{}).n("BedrockClient","ListModelCopyJobsCommand").f(void 0,void 0).ser(cI4).de(BW4).build(){static{p(this,"ListModelCopyJobsCommand")}},Q80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelCustomizationJobs",{}).n("BedrockClient","ListModelCustomizationJobsCommand").f(void 0,void 0).ser(lI4).de(QW4).build(){static{p(this,"ListModelCustomizationJobsCommand")}},D80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelImportJobs",{}).n("BedrockClient","ListModelImportJobsCommand").f(void 0,void 0).ser(pI4).de(DW4).build(){static{p(this,"ListModelImportJobsCommand")}},Z80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListModelInvocationJobs",{}).n("BedrockClient","ListModelInvocationJobsCommand").f(void 0,vQ2).ser(iI4).de(ZW4).build(){static{p(this,"ListModelInvocationJobsCommand")}},G80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListPromptRouters",{}).n("BedrockClient","ListPromptRoutersCommand").f(void 0,gQ2).ser(nI4).de(GW4).build(){static{p(this,"ListPromptRoutersCommand")}},F80=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListProvisionedModelThroughputs",{}).n("BedrockClient","ListProvisionedModelThroughputsCommand").f(void 0,void 0).ser(aI4).de(FW4).build(){static{p(this,"ListProvisionedModelThroughputsCommand")}},s42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","ListTagsForResource",{}).n("BedrockClient","ListTagsForResourceCommand").f(void 0,void 0).ser(sI4).de(IW4).build(){static{p(this,"ListTagsForResourceCommand")}},r42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","PutModelInvocationLoggingConfiguration",{}).n("BedrockClient","PutModelInvocationLoggingConfigurationCommand").f(void 0,void 0).ser(rI4).de(YW4).build(){static{p(this,"PutModelInvocationLoggingConfigurationCommand")}},o42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","PutUseCaseForModelAccess",{}).n("BedrockClient","PutUseCaseForModelAccessCommand").f(void 0,void 0).ser(oI4).de(WW4).build(){static{p(this,"PutUseCaseForModelAccessCommand")}},t42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","RegisterMarketplaceModelEndpoint",{}).n("BedrockClient","RegisterMarketplaceModelEndpointCommand").f(void 0,void 0).ser(tI4).de(JW4).build(){static{p(this,"RegisterMarketplaceModelEndpointCommand")}},e42=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","StopEvaluationJob",{}).n("BedrockClient","StopEvaluationJobCommand").f(ZQ2,void 0).ser(eI4).de(XW4).build(){static{p(this,"StopEvaluationJobCommand")}},A62=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","StopModelCustomizationJob",{}).n("BedrockClient","StopModelCustomizationJobCommand").f(void 0,void 0).ser(AY4).de(VW4).build(){static{p(this,"StopModelCustomizationJobCommand")}},B62=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","StopModelInvocationJob",{}).n("BedrockClient","StopModelInvocationJobCommand").f(void 0,void 0).ser(BY4).de(CW4).build(){static{p(this,"StopModelInvocationJobCommand")}},Q62=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","TagResource",{}).n("BedrockClient","TagResourceCommand").f(void 0,void 0).ser(QY4).de(KW4).build(){static{p(this,"TagResourceCommand")}},D62=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UntagResource",{}).n("BedrockClient","UntagResourceCommand").f(void 0,void 0).ser(DY4).de(HW4).build(){static{p(this,"UntagResourceCommand")}},Z62=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UpdateGuardrail",{}).n("BedrockClient","UpdateGuardrailCommand").f(PQ2,void 0).ser(ZY4).de(zW4).build(){static{p(this,"UpdateGuardrailCommand")}},G62=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UpdateMarketplaceModelEndpoint",{}).n("BedrockClient","UpdateMarketplaceModelEndpointCommand").f(void 0,void 0).ser(GY4).de(EW4).build(){static{p(this,"UpdateMarketplaceModelEndpointCommand")}},F62=class extends _.Command.classBuilder().ep(PB).m(function(A,B,Q,D){return[SB.getSerdePlugin(Q,this.serialize,this.deserialize),$B.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AmazonBedrockControlPlaneService","UpdateProvisionedModelThroughput",{}).n("BedrockClient","UpdateProvisionedModelThroughputCommand").f(void 0,void 0).ser(FY4).de(UW4).build(){static{p(this,"UpdateProvisionedModelThroughputCommand")}},UX4={BatchDeleteEvaluationJobCommand:I42,CreateCustomModelCommand:Y42,CreateEvaluationJobCommand:W42,CreateFoundationModelAgreementCommand:J42,CreateGuardrailCommand:X42,CreateGuardrailVersionCommand:V42,CreateInferenceProfileCommand:C42,CreateMarketplaceModelEndpointCommand:K42,CreateModelCopyJobCommand:H42,CreateModelCustomizationJobCommand:z42,CreateModelImportJobCommand:E42,CreateModelInvocationJobCommand:U42,CreatePromptRouterCommand:w42,CreateProvisionedModelThroughputCommand:$42,DeleteCustomModelCommand:q42,DeleteFoundationModelAgreementCommand:N42,DeleteGuardrailCommand:L42,DeleteImportedModelCommand:M42,DeleteInferenceProfileCommand:R42,DeleteMarketplaceModelEndpointCommand:O42,DeleteModelInvocationLoggingConfigurationCommand:T42,DeletePromptRouterCommand:P42,DeleteProvisionedModelThroughputCommand:S42,DeregisterMarketplaceModelEndpointCommand:j42,GetCustomModelCommand:k42,GetEvaluationJobCommand:y42,GetFoundationModelCommand:x42,GetFoundationModelAvailabilityCommand:_42,GetGuardrailCommand:v42,GetImportedModelCommand:b42,GetInferenceProfileCommand:f42,GetMarketplaceModelEndpointCommand:h42,GetModelCopyJobCommand:g42,GetModelCustomizationJobCommand:u42,GetModelImportJobCommand:m42,GetModelInvocationJobCommand:d42,GetModelInvocationLoggingConfigurationCommand:c42,GetPromptRouterCommand:l42,GetProvisionedModelThroughputCommand:p42,GetUseCaseForModelAccessCommand:i42,ListCustomModelsCommand:s60,ListEvaluationJobsCommand:r60,ListFoundationModelAgreementOffersCommand:n42,ListFoundationModelsCommand:a42,ListGuardrailsCommand:o60,ListImportedModelsCommand:t60,ListInferenceProfilesCommand:e60,ListMarketplaceModelEndpointsCommand:A80,ListModelCopyJobsCommand:B80,ListModelCustomizationJobsCommand:Q80,ListModelImportJobsCommand:D80,ListModelInvocationJobsCommand:Z80,ListPromptRoutersCommand:G80,ListProvisionedModelThroughputsCommand:F80,ListTagsForResourceCommand:s42,PutModelInvocationLoggingConfigurationCommand:r42,PutUseCaseForModelAccessCommand:o42,RegisterMarketplaceModelEndpointCommand:t42,StopEvaluationJobCommand:e42,StopModelCustomizationJobCommand:A62,StopModelInvocationJobCommand:B62,TagResourceCommand:Q62,UntagResourceCommand:D62,UpdateGuardrailCommand:Z62,UpdateMarketplaceModelEndpointCommand:G62,UpdateProvisionedModelThroughputCommand:F62},I62=class extends EV{static{p(this,"Bedrock")}};_.createAggregatedClient(UX4,I62);var wX4=$2.createPaginator(EV,s60,"nextToken","nextToken","maxResults"),$X4=$2.createPaginator(EV,r60,"nextToken","nextToken","maxResults"),qX4=$2.createPaginator(EV,o60,"nextToken","nextToken","maxResults"),NX4=$2.createPaginator(EV,t60,"nextToken","nextToken","maxResults"),LX4=$2.createPaginator(EV,e60,"nextToken","nextToken","maxResults"),MX4=$2.createPaginator(EV,A80,"nextToken","nextToken","maxResults"),RX4=$2.createPaginator(EV,B80,"nextToken","nextToken","maxResults"),OX4=$2.createPaginator(EV,Q80,"nextToken","nextToken","maxResults"),TX4=$2.createPaginator(EV,D80,"nextToken","nextToken","maxResults"),PX4=$2.createPaginator(EV,Z80,"nextToken","nextToken","maxResults"),SX4=$2.createPaginator(EV,G80,"nextToken","nextToken","maxResults"),jX4=$2.createPaginator(EV,F80,"nextToken","nextToken","maxResults")});
var X02=E((W02)=>{Object.defineProperty(W02,"__esModule",{value:!0});W02.defaultEndpointResolver=void 0;var y54=sa(),z40=$7(),_54=Y02(),x54=new z40.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),v54=(A,B={})=>{return x54.get(A,()=>z40.resolveEndpoint(_54.ruleSet,{endpointParams:A,logger:B.logger}))};W02.defaultEndpointResolver=v54;z40.customEndpointFunctions.aws=y54.awsEndpointFunctions});
var X22=E((W22)=>{Object.defineProperty(W22,"__esModule",{value:!0});W22.resolveRuntimeExtensions=void 0;var G22=U61(),F22=CV(),I22=J6(),Y22=Z22(),n74=(A,B)=>{let Q=Object.assign(G22.getAwsRegionExtensionConfiguration(A),I22.getDefaultExtensionConfiguration(A),F22.getHttpHandlerExtensionConfiguration(A),Y22.getHttpAuthExtensionConfiguration(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,G22.resolveAwsRegionExtensionConfiguration(Q),I22.resolveDefaultRuntimeConfig(Q),F22.resolveHttpHandlerRuntimeConfig(Q),Y22.resolveHttpAuthRuntimeConfig(Q))};W22.resolveRuntimeExtensions=n74});
var XB2=E((OV5,JB2)=>{var{create:jZ4,defineProperty:P61,getOwnPropertyDescriptor:kZ4,getOwnPropertyNames:yZ4,getPrototypeOf:_Z4}=Object,xZ4=Object.prototype.hasOwnProperty,fG=(A,B)=>P61(A,"name",{value:B,configurable:!0}),vZ4=(A,B)=>{for(var Q in B)P61(A,Q,{get:B[Q],enumerable:!0})},IB2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of yZ4(B))if(!xZ4.call(A,Z)&&Z!==Q)P61(A,Z,{get:()=>B[Z],enumerable:!(D=kZ4(B,Z))||D.enumerable})}return A},ny=(A,B,Q)=>(Q=A!=null?jZ4(_Z4(A)):{},IB2(B||!A||!A.__esModule?P61(Q,"default",{value:A,enumerable:!0}):Q,A)),bZ4=(A)=>IB2(P61({},"__esModule",{value:!0}),A),YB2={};vZ4(YB2,{fromIni:()=>aZ4});JB2.exports=bZ4(YB2);var U60=e5(),ay=Tw(),T61=eB(),fZ4=fG((A,B,Q)=>{let D={EcsContainer:fG(async(Z)=>{let{fromHttp:G}=await Promise.resolve().then(()=>ny(Q40())),{fromContainerMetadata:F}=await Promise.resolve().then(()=>ny($F()));return Q?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>T61.chain(G(Z??{}),F(Z))().then(E60)},"EcsContainer"),Ec2InstanceMetadata:fG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:G}=await Promise.resolve().then(()=>ny($F()));return async()=>G(Z)().then(E60)},"Ec2InstanceMetadata"),Environment:fG(async(Z)=>{Q?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:G}=await Promise.resolve().then(()=>ny(eQ0()));return async()=>G(Z)().then(E60)},"Environment")};if(A in D)return D[A];else throw new T61.CredentialsProviderError(`Unsupported credential source in profile ${B}. Got ${A}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:Q})},"resolveCredentialSource"),E60=fG((A)=>ay.setCredentialFeature(A,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),"setNamedProvider"),hZ4=fG((A,{profile:B="default",logger:Q}={})=>{return Boolean(A)&&typeof A==="object"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1&&["undefined","string"].indexOf(typeof A.external_id)>-1&&["undefined","string"].indexOf(typeof A.mfa_serial)>-1&&(gZ4(A,{profile:B,logger:Q})||uZ4(A,{profile:B,logger:Q}))},"isAssumeRoleProfile"),gZ4=fG((A,{profile:B,logger:Q})=>{let D=typeof A.source_profile==="string"&&typeof A.credential_source==="undefined";if(D)Q?.debug?.(`    ${B} isAssumeRoleWithSourceProfile source_profile=${A.source_profile}`);return D},"isAssumeRoleWithSourceProfile"),uZ4=fG((A,{profile:B,logger:Q})=>{let D=typeof A.credential_source==="string"&&typeof A.source_profile==="undefined";if(D)Q?.debug?.(`    ${B} isCredentialSourceProfile credential_source=${A.credential_source}`);return D},"isCredentialSourceProfile"),mZ4=fG(async(A,B,Q,D={})=>{Q.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let Z=B[A],{source_profile:G,region:F}=Z;if(!Q.roleAssumer){let{getDefaultRoleAssumer:Y}=await Promise.resolve().then(()=>ny(W60()));Q.roleAssumer=Y({...Q.clientConfig,credentialProviderLogger:Q.logger,parentClientConfig:{...Q?.parentClientConfig,region:F??Q?.parentClientConfig?.region}},Q.clientPlugins)}if(G&&G in D)throw new T61.CredentialsProviderError(`Detected a cycle attempting to resolve credentials for profile ${U60.getProfileName(Q)}. Profiles visited: `+Object.keys(D).join(", "),{logger:Q.logger});Q.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${G?`source_profile=[${G}]`:`profile=[${A}]`}`);let I=G?WB2(G,B,Q,{...D,[G]:!0},ZB2(B[G]??{})):(await fZ4(Z.credential_source,A,Q.logger)(Q))();if(ZB2(Z))return I.then((Y)=>ay.setCredentialFeature(Y,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));else{let Y={RoleArn:Z.role_arn,RoleSessionName:Z.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:Z.external_id,DurationSeconds:parseInt(Z.duration_seconds||"3600",10)},{mfa_serial:W}=Z;if(W){if(!Q.mfaCodeProvider)throw new T61.CredentialsProviderError(`Profile ${A} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:Q.logger,tryNextLink:!1});Y.SerialNumber=W,Y.TokenCode=await Q.mfaCodeProvider(W)}let J=await I;return Q.roleAssumer(J,Y).then((X)=>ay.setCredentialFeature(X,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},"resolveAssumeRoleCredentials"),ZB2=fG((A)=>{return!A.role_arn&&!!A.credential_source},"isCredentialSourceWithoutRoleArn"),dZ4=fG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.credential_process==="string","isProcessProfile"),cZ4=fG(async(A,B)=>Promise.resolve().then(()=>ny(V60())).then(({fromProcess:Q})=>Q({...A,profile:B})().then((D)=>ay.setCredentialFeature(D,"CREDENTIALS_PROFILE_PROCESS","v"))),"resolveProcessCredentials"),lZ4=fG(async(A,B,Q={})=>{let{fromSSO:D}=await Promise.resolve().then(()=>ny(L40()));return D({profile:A,logger:Q.logger,parentClientConfig:Q.parentClientConfig,clientConfig:Q.clientConfig})().then((Z)=>{if(B.sso_session)return ay.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO","r");else return ay.setCredentialFeature(Z,"CREDENTIALS_PROFILE_SSO_LEGACY","t")})},"resolveSsoCredentials"),pZ4=fG((A)=>A&&(typeof A.sso_start_url==="string"||typeof A.sso_account_id==="string"||typeof A.sso_session==="string"||typeof A.sso_region==="string"||typeof A.sso_role_name==="string"),"isSsoProfile"),GB2=fG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.aws_access_key_id==="string"&&typeof A.aws_secret_access_key==="string"&&["undefined","string"].indexOf(typeof A.aws_session_token)>-1&&["undefined","string"].indexOf(typeof A.aws_account_id)>-1,"isStaticCredsProfile"),FB2=fG(async(A,B)=>{B?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let Q={accessKeyId:A.aws_access_key_id,secretAccessKey:A.aws_secret_access_key,sessionToken:A.aws_session_token,...A.aws_credential_scope&&{credentialScope:A.aws_credential_scope},...A.aws_account_id&&{accountId:A.aws_account_id}};return ay.setCredentialFeature(Q,"CREDENTIALS_PROFILE","n")},"resolveStaticCredentials"),iZ4=fG((A)=>Boolean(A)&&typeof A==="object"&&typeof A.web_identity_token_file==="string"&&typeof A.role_arn==="string"&&["undefined","string"].indexOf(typeof A.role_session_name)>-1,"isWebIdentityProfile"),nZ4=fG(async(A,B)=>Promise.resolve().then(()=>ny(z60())).then(({fromTokenFile:Q})=>Q({webIdentityTokenFile:A.web_identity_token_file,roleArn:A.role_arn,roleSessionName:A.role_session_name,roleAssumerWithWebIdentity:B.roleAssumerWithWebIdentity,logger:B.logger,parentClientConfig:B.parentClientConfig})().then((D)=>ay.setCredentialFeature(D,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),"resolveWebIdentityCredentials"),WB2=fG(async(A,B,Q,D={},Z=!1)=>{let G=B[A];if(Object.keys(D).length>0&&GB2(G))return FB2(G,Q);if(Z||hZ4(G,{profile:A,logger:Q.logger}))return mZ4(A,B,Q,D);if(GB2(G))return FB2(G,Q);if(iZ4(G))return nZ4(G,Q);if(dZ4(G))return cZ4(Q,A);if(pZ4(G))return await lZ4(A,G,Q);throw new T61.CredentialsProviderError(`Could not resolve credentials using profile: [${A}] in configuration/credentials file(s).`,{logger:Q.logger})},"resolveProfileData"),aZ4=fG((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let D=await U60.parseKnownFiles(Q);return WB2(U60.getProfileName({profile:A.profile??B?.profile}),D,Q)},"fromIni")});
var XeA=E((WeA)=>{Object.defineProperty(WeA,"__esModule",{value:!0});WeA.checkUrl=void 0;var U64=eB(),w64="*************",$64="**************",q64="[fd00:ec2::23]",N64=(A,B)=>{if(A.protocol==="https:")return;if(A.hostname===w64||A.hostname===$64||A.hostname===q64)return;if(A.hostname.includes("[")){if(A.hostname==="[::1]"||A.hostname==="[0000:0000:0000:0000:0000:0000:0000:0001]")return}else{if(A.hostname==="localhost")return;let Q=A.hostname.split("."),D=(Z)=>{let G=parseInt(Z,10);return 0<=G&&G<=255};if(Q[0]==="127"&&D(Q[1])&&D(Q[2])&&D(Q[3])&&Q.length===4)return}throw new U64.CredentialsProviderError(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host ************** or [fd00:ec2::23]`,{logger:B})};WeA.checkUrl=N64});
var Y02=E((F02)=>{Object.defineProperty(F02,"__esModule",{value:!0});F02.ruleSet=void 0;var Q02="required",fz="fn",hz="argv",Ys="ref",i12=!0,n12="isSet",q61="booleanEquals",Fs="error",Is="endpoint",RT="tree",K40="PartitionResult",H40="getAttr",a12={[Q02]:!1,type:"String"},s12={[Q02]:!0,default:!1,type:"Boolean"},r12={[Ys]:"Endpoint"},D02={[fz]:q61,[hz]:[{[Ys]:"UseFIPS"},!0]},Z02={[fz]:q61,[hz]:[{[Ys]:"UseDualStack"},!0]},bz={},o12={[fz]:H40,[hz]:[{[Ys]:K40},"supportsFIPS"]},G02={[Ys]:K40},t12={[fz]:q61,[hz]:[!0,{[fz]:H40,[hz]:[G02,"supportsDualStack"]}]},e12=[D02],A02=[Z02],B02=[{[Ys]:"Region"}],k54={version:"1.0",parameters:{Region:a12,UseDualStack:s12,UseFIPS:s12,Endpoint:a12},rules:[{conditions:[{[fz]:n12,[hz]:[r12]}],rules:[{conditions:e12,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Fs},{conditions:A02,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Fs},{endpoint:{url:r12,properties:bz,headers:bz},type:Is}],type:RT},{conditions:[{[fz]:n12,[hz]:B02}],rules:[{conditions:[{[fz]:"aws.partition",[hz]:B02,assign:K40}],rules:[{conditions:[D02,Z02],rules:[{conditions:[{[fz]:q61,[hz]:[i12,o12]},t12],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:bz,headers:bz},type:Is}],type:RT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Fs}],type:RT},{conditions:e12,rules:[{conditions:[{[fz]:q61,[hz]:[o12,i12]}],rules:[{conditions:[{[fz]:"stringEquals",[hz]:[{[fz]:H40,[hz]:[G02,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:bz,headers:bz},type:Is},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:bz,headers:bz},type:Is}],type:RT},{error:"FIPS is enabled but this partition does not support FIPS",type:Fs}],type:RT},{conditions:A02,rules:[{conditions:[t12],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:bz,headers:bz},type:Is}],type:RT},{error:"DualStack is enabled but this partition does not support DualStack",type:Fs}],type:RT},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:bz,headers:bz},type:Is}],type:RT}],type:RT},{error:"Invalid Configuration: Missing Region",type:Fs}]};F02.ruleSet=k54});
var Y61=E((hJ5,zrA)=>{var{defineProperty:Qq1,getOwnPropertyDescriptor:qB4,getOwnPropertyNames:NB4}=Object,LB4=Object.prototype.hasOwnProperty,Bq1=(A,B)=>Qq1(A,"name",{value:B,configurable:!0}),MB4=(A,B)=>{for(var Q in B)Qq1(A,Q,{get:B[Q],enumerable:!0})},RB4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of NB4(B))if(!LB4.call(A,Z)&&Z!==Q)Qq1(A,Z,{get:()=>B[Z],enumerable:!(D=qB4(B,Z))||D.enumerable})}return A},OB4=(A)=>RB4(Qq1({},"__esModule",{value:!0}),A),VrA={};MB4(VrA,{getHostHeaderPlugin:()=>PB4,hostHeaderMiddleware:()=>KrA,hostHeaderMiddlewareOptions:()=>HrA,resolveHostHeaderConfig:()=>CrA});zrA.exports=OB4(VrA);var TB4=CV();function CrA(A){return A}Bq1(CrA,"resolveHostHeaderConfig");var KrA=Bq1((A)=>(B)=>async(Q)=>{if(!TB4.HttpRequest.isInstance(Q.request))return B(Q);let{request:D}=Q,{handlerProtocol:Z=""}=A.requestHandler.metadata||{};if(Z.indexOf("h2")>=0&&!D.headers[":authority"])delete D.headers.host,D.headers[":authority"]=D.hostname+(D.port?":"+D.port:"");else if(!D.headers.host){let G=D.hostname;if(D.port!=null)G+=`:${D.port}`;D.headers.host=G}return B(Q)},"hostHeaderMiddleware"),HrA={name:"hostHeaderMiddleware",step:"build",priority:"low",tags:["HOST"],override:!0},PB4=Bq1((A)=>({applyToStack:Bq1((B)=>{B.add(KrA(A),HrA)},"applyToStack")}),"getHostHeaderPlugin")});
var YI=E((H61)=>{Object.defineProperty(H61,"__esModule",{value:!0});var nQ0=Hg();nQ0.__exportStar(Tw(),H61);nQ0.__exportStar(PQ0(),H61);nQ0.__exportStar(gtA(),H61)});
var Z22=E((Q22)=>{Object.defineProperty(Q22,"__esModule",{value:!0});Q22.resolveHttpAuthRuntimeConfig=Q22.getHttpAuthExtensionConfiguration=void 0;var l74=(A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}};Q22.getHttpAuthExtensionConfiguration=l74;var p74=(A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}};Q22.resolveHttpAuthRuntimeConfig=p74});
var Z40=E((NeA)=>{Object.defineProperty(NeA,"__esModule",{value:!0});NeA.resolveHttpAuthSchemeConfig=NeA.defaultSSOHttpAuthSchemeProvider=NeA.defaultSSOHttpAuthSchemeParametersProvider=void 0;var p64=YI(),D40=I5(),i64=async(A,B,Q)=>{return{operation:D40.getSmithyContext(B).operation,region:await D40.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};NeA.defaultSSOHttpAuthSchemeParametersProvider=i64;function n64(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function Sq1(A){return{schemeId:"smithy.api#noAuth"}}var a64=(A)=>{let B=[];switch(A.operation){case"GetRoleCredentials":{B.push(Sq1(A));break}case"ListAccountRoles":{B.push(Sq1(A));break}case"ListAccounts":{B.push(Sq1(A));break}case"Logout":{B.push(Sq1(A));break}default:B.push(n64(A))}return B};NeA.defaultSSOHttpAuthSchemeProvider=a64;var s64=(A)=>{let B=p64.resolveAwsSdkSigV4Config(A);return Object.assign(B,{authSchemePreference:D40.normalizeProvider(A.authSchemePreference??[])})};NeA.resolveHttpAuthSchemeConfig=s64});
var aeA=E((ieA)=>{Object.defineProperty(ieA,"__esModule",{value:!0});ieA.ruleSet=void 0;var deA="required",xz="fn",vz="argv",Zs="ref",yeA=!0,_eA="isSet",E61="booleanEquals",Qs="error",Ds="endpoint",LT="tree",F40="PartitionResult",I40="getAttr",xeA={[deA]:!1,type:"String"},veA={[deA]:!0,default:!1,type:"Boolean"},beA={[Zs]:"Endpoint"},ceA={[xz]:E61,[vz]:[{[Zs]:"UseFIPS"},!0]},leA={[xz]:E61,[vz]:[{[Zs]:"UseDualStack"},!0]},_z={},feA={[xz]:I40,[vz]:[{[Zs]:F40},"supportsFIPS"]},peA={[Zs]:F40},heA={[xz]:E61,[vz]:[!0,{[xz]:I40,[vz]:[peA,"supportsDualStack"]}]},geA=[ceA],ueA=[leA],meA=[{[Zs]:"Region"}],J84={version:"1.0",parameters:{Region:xeA,UseDualStack:veA,UseFIPS:veA,Endpoint:xeA},rules:[{conditions:[{[xz]:_eA,[vz]:[beA]}],rules:[{conditions:geA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:Qs},{conditions:ueA,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:Qs},{endpoint:{url:beA,properties:_z,headers:_z},type:Ds}],type:LT},{conditions:[{[xz]:_eA,[vz]:meA}],rules:[{conditions:[{[xz]:"aws.partition",[vz]:meA,assign:F40}],rules:[{conditions:[ceA,leA],rules:[{conditions:[{[xz]:E61,[vz]:[yeA,feA]},heA],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:_z,headers:_z},type:Ds}],type:LT},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:Qs}],type:LT},{conditions:geA,rules:[{conditions:[{[xz]:E61,[vz]:[feA,yeA]}],rules:[{conditions:[{[xz]:"stringEquals",[vz]:[{[xz]:I40,[vz]:[peA,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:_z,headers:_z},type:Ds},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:_z,headers:_z},type:Ds}],type:LT},{error:"FIPS is enabled but this partition does not support FIPS",type:Qs}],type:LT},{conditions:ueA,rules:[{conditions:[heA],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:_z,headers:_z},type:Ds}],type:LT},{error:"DualStack is enabled but this partition does not support DualStack",type:Qs}],type:LT},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:_z,headers:_z},type:Ds}],type:LT}],type:LT},{error:"Invalid Configuration: Missing Region",type:Qs}]};ieA.ruleSet=J84});
var aoA=E((iJ5,noA)=>{var{defineProperty:$q1,getOwnPropertyDescriptor:R94,getOwnPropertyNames:O94}=Object,T94=Object.prototype.hasOwnProperty,_Y=(A,B)=>$q1(A,"name",{value:B,configurable:!0}),P94=(A,B)=>{for(var Q in B)$q1(A,Q,{get:B[Q],enumerable:!0})},S94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of O94(B))if(!T94.call(A,Z)&&Z!==Q)$q1(A,Z,{get:()=>B[Z],enumerable:!(D=R94(B,Z))||D.enumerable})}return A},j94=(A)=>S94($q1({},"__esModule",{value:!0}),A),RoA={};P94(RoA,{ALGORITHM_IDENTIFIER:()=>Kq1,ALGORITHM_IDENTIFIER_V4A:()=>x94,ALGORITHM_QUERY_PARAM:()=>OoA,ALWAYS_UNSIGNABLE_HEADERS:()=>xoA,AMZ_DATE_HEADER:()=>LQ0,AMZ_DATE_QUERY_PARAM:()=>wQ0,AUTH_HEADER:()=>NQ0,CREDENTIAL_QUERY_PARAM:()=>ToA,DATE_HEADER:()=>joA,EVENT_ALGORITHM_IDENTIFIER:()=>foA,EXPIRES_QUERY_PARAM:()=>SoA,GENERATED_HEADERS:()=>koA,HOST_HEADER:()=>y94,KEY_TYPE_IDENTIFIER:()=>MQ0,MAX_CACHE_SIZE:()=>goA,MAX_PRESIGNED_TTL:()=>uoA,PROXY_HEADER_PATTERN:()=>voA,REGION_SET_PARAM:()=>k94,SEC_HEADER_PATTERN:()=>boA,SHA256_HEADER:()=>wq1,SIGNATURE_HEADER:()=>yoA,SIGNATURE_QUERY_PARAM:()=>$Q0,SIGNED_HEADERS_QUERY_PARAM:()=>PoA,SignatureV4:()=>p94,SignatureV4Base:()=>ioA,TOKEN_HEADER:()=>_oA,TOKEN_QUERY_PARAM:()=>qQ0,UNSIGNABLE_PATTERNS:()=>_94,UNSIGNED_PAYLOAD:()=>hoA,clearCredentialCache:()=>b94,createScope:()=>zq1,getCanonicalHeaders:()=>zQ0,getCanonicalQuery:()=>poA,getPayloadHash:()=>Eq1,getSigningKey:()=>moA,hasHeader:()=>doA,moveHeadersToQuery:()=>loA,prepareRequest:()=>UQ0,signatureV4aContainer:()=>i94});noA.exports=j94(RoA);var qoA=cB(),OoA="X-Amz-Algorithm",ToA="X-Amz-Credential",wQ0="X-Amz-Date",PoA="X-Amz-SignedHeaders",SoA="X-Amz-Expires",$Q0="X-Amz-Signature",qQ0="X-Amz-Security-Token",k94="X-Amz-Region-Set",NQ0="authorization",LQ0=wQ0.toLowerCase(),joA="date",koA=[NQ0,LQ0,joA],yoA=$Q0.toLowerCase(),wq1="x-amz-content-sha256",_oA=qQ0.toLowerCase(),y94="host",xoA={authorization:!0,"cache-control":!0,connection:!0,expect:!0,from:!0,"keep-alive":!0,"max-forwards":!0,pragma:!0,referer:!0,te:!0,trailer:!0,"transfer-encoding":!0,upgrade:!0,"user-agent":!0,"x-amzn-trace-id":!0},voA=/^proxy-/,boA=/^sec-/,_94=[/^proxy-/i,/^sec-/i],Kq1="AWS4-HMAC-SHA256",x94="AWS4-ECDSA-P256-SHA256",foA="AWS4-HMAC-SHA256-PAYLOAD",hoA="UNSIGNED-PAYLOAD",goA=50,MQ0="aws4_request",uoA=604800,my=Zy(),v94=cB(),oa={},Hq1=[],zq1=_Y((A,B,Q)=>`${A}/${B}/${Q}/${MQ0}`,"createScope"),moA=_Y(async(A,B,Q,D,Z)=>{let G=await NoA(A,B.secretAccessKey,B.accessKeyId),F=`${Q}:${D}:${Z}:${my.toHex(G)}:${B.sessionToken}`;if(F in oa)return oa[F];Hq1.push(F);while(Hq1.length>goA)delete oa[Hq1.shift()];let I=`AWS4${B.secretAccessKey}`;for(let Y of[Q,D,Z,MQ0])I=await NoA(A,I,Y);return oa[F]=I},"getSigningKey"),b94=_Y(()=>{Hq1.length=0,Object.keys(oa).forEach((A)=>{delete oa[A]})},"clearCredentialCache"),NoA=_Y((A,B,Q)=>{let D=new A(B);return D.update(v94.toUint8Array(Q)),D.digest()},"hmac"),zQ0=_Y(({headers:A},B,Q)=>{let D={};for(let Z of Object.keys(A).sort()){if(A[Z]==null)continue;let G=Z.toLowerCase();if(G in xoA||B?.has(G)||voA.test(G)||boA.test(G)){if(!Q||Q&&!Q.has(G))continue}D[G]=A[Z].trim().replace(/\s+/g," ")}return D},"getCanonicalHeaders"),f94=zoA(),h94=cB(),Eq1=_Y(async({headers:A,body:B},Q)=>{for(let D of Object.keys(A))if(D.toLowerCase()===wq1)return A[D];if(B==null)return"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";else if(typeof B==="string"||ArrayBuffer.isView(B)||f94.isArrayBuffer(B)){let D=new Q;return D.update(h94.toUint8Array(B)),my.toHex(await D.digest())}return hoA},"getPayloadHash"),LoA=cB(),g94=class{static{_Y(this,"HeaderFormatter")}format(A){let B=[];for(let Z of Object.keys(A)){let G=LoA.fromUtf8(Z);B.push(Uint8Array.from([G.byteLength]),G,this.formatHeaderValue(A[Z]))}let Q=new Uint8Array(B.reduce((Z,G)=>Z+G.byteLength,0)),D=0;for(let Z of B)Q.set(Z,D),D+=Z.byteLength;return Q}formatHeaderValue(A){switch(A.type){case"boolean":return Uint8Array.from([A.value?0:1]);case"byte":return Uint8Array.from([2,A.value]);case"short":let B=new DataView(new ArrayBuffer(3));return B.setUint8(0,3),B.setInt16(1,A.value,!1),new Uint8Array(B.buffer);case"integer":let Q=new DataView(new ArrayBuffer(5));return Q.setUint8(0,4),Q.setInt32(1,A.value,!1),new Uint8Array(Q.buffer);case"long":let D=new Uint8Array(9);return D[0]=5,D.set(A.value.bytes,1),D;case"binary":let Z=new DataView(new ArrayBuffer(3+A.value.byteLength));Z.setUint8(0,6),Z.setUint16(1,A.value.byteLength,!1);let G=new Uint8Array(Z.buffer);return G.set(A.value,3),G;case"string":let F=LoA.fromUtf8(A.value),I=new DataView(new ArrayBuffer(3+F.byteLength));I.setUint8(0,7),I.setUint16(1,F.byteLength,!1);let Y=new Uint8Array(I.buffer);return Y.set(F,3),Y;case"timestamp":let W=new Uint8Array(9);return W[0]=8,W.set(m94.fromNumber(A.value.valueOf()).bytes,1),W;case"uuid":if(!u94.test(A.value))throw new Error(`Invalid UUID received: ${A.value}`);let J=new Uint8Array(17);return J[0]=9,J.set(my.fromHex(A.value.replace(/\-/g,"")),1),J}}},u94=/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/,m94=class A{constructor(B){if(this.bytes=B,B.byteLength!==8)throw new Error("Int64 buffers must be exactly 8 bytes")}static{_Y(this,"Int64")}static fromNumber(B){if(B>9223372036854776000||B<-9223372036854776000)throw new Error(`${B} is too large (or, if negative, too small) to represent as an Int64`);let Q=new Uint8Array(8);for(let D=7,Z=Math.abs(Math.round(B));D>-1&&Z>0;D--,Z/=256)Q[D]=Z;if(B<0)EQ0(Q);return new A(Q)}valueOf(){let B=this.bytes.slice(0),Q=B[0]&128;if(Q)EQ0(B);return parseInt(my.toHex(B),16)*(Q?-1:1)}toString(){return String(this.valueOf())}};function EQ0(A){for(let B=0;B<8;B++)A[B]^=255;for(let B=7;B>-1;B--)if(A[B]++,A[B]!==0)break}_Y(EQ0,"negate");var doA=_Y((A,B)=>{A=A.toLowerCase();for(let Q of Object.keys(B))if(A===Q.toLowerCase())return!0;return!1},"hasHeader"),coA=CV(),loA=_Y((A,B={})=>{let{headers:Q,query:D={}}=coA.HttpRequest.clone(A);for(let Z of Object.keys(Q)){let G=Z.toLowerCase();if(G.slice(0,6)==="x-amz-"&&!B.unhoistableHeaders?.has(G)||B.hoistableHeaders?.has(G))D[Z]=Q[Z],delete Q[Z]}return{...A,headers:Q,query:D}},"moveHeadersToQuery"),UQ0=_Y((A)=>{A=coA.HttpRequest.clone(A);for(let B of Object.keys(A.headers))if(koA.indexOf(B.toLowerCase())>-1)delete A.headers[B];return A},"prepareRequest"),MoA=I5(),d94=cB(),Uq1=$oA(),poA=_Y(({query:A={}})=>{let B=[],Q={};for(let D of Object.keys(A)){if(D.toLowerCase()===yoA)continue;let Z=Uq1.escapeUri(D);B.push(Z);let G=A[D];if(typeof G==="string")Q[Z]=`${Z}=${Uq1.escapeUri(G)}`;else if(Array.isArray(G))Q[Z]=G.slice(0).reduce((F,I)=>F.concat([`${Z}=${Uq1.escapeUri(I)}`]),[]).sort().join("&")}return B.sort().map((D)=>Q[D]).filter((D)=>D).join("&")},"getCanonicalQuery"),c94=_Y((A)=>l94(A).toISOString().replace(/\.\d{3}Z$/,"Z"),"iso8601"),l94=_Y((A)=>{if(typeof A==="number")return new Date(A*1000);if(typeof A==="string"){if(Number(A))return new Date(Number(A)*1000);return new Date(A)}return A},"toDate"),ioA=class{static{_Y(this,"SignatureV4Base")}constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){this.service=D,this.sha256=Z,this.uriEscapePath=G,this.applyChecksum=typeof A==="boolean"?A:!0,this.regionProvider=MoA.normalizeProvider(Q),this.credentialProvider=MoA.normalizeProvider(B)}createCanonicalRequest(A,B,Q){let D=Object.keys(B).sort();return`${A.method}
${this.getCanonicalPath(A)}
${poA(A)}
${D.map((Z)=>`${Z}:${B[Z]}`).join(`
`)}

${D.join(";")}
${Q}`}async createStringToSign(A,B,Q,D){let Z=new this.sha256;Z.update(d94.toUint8Array(Q));let G=await Z.digest();return`${D}
${A}
${B}
${my.toHex(G)}`}getCanonicalPath({path:A}){if(this.uriEscapePath){let B=[];for(let Z of A.split("/")){if(Z?.length===0)continue;if(Z===".")continue;if(Z==="..")B.pop();else B.push(Z)}let Q=`${A?.startsWith("/")?"/":""}${B.join("/")}${B.length>0&&A?.endsWith("/")?"/":""}`;return Uq1.escapeUri(Q).replace(/%2F/g,"/")}return A}validateResolvedCredentials(A){if(typeof A!=="object"||typeof A.accessKeyId!=="string"||typeof A.secretAccessKey!=="string")throw new Error("Resolved credential object is not valid")}formatDate(A){let B=c94(A).replace(/[\-:]/g,"");return{longDate:B,shortDate:B.slice(0,8)}}getCanonicalHeaderList(A){return Object.keys(A).sort().join(";")}},p94=class extends ioA{constructor({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G=!0}){super({applyChecksum:A,credentials:B,region:Q,service:D,sha256:Z,uriEscapePath:G});this.headerFormatter=new g94}static{_Y(this,"SignatureV4")}async presign(A,B={}){let{signingDate:Q=new Date,expiresIn:D=3600,unsignableHeaders:Z,unhoistableHeaders:G,signableHeaders:F,hoistableHeaders:I,signingRegion:Y,signingService:W}=B,J=await this.credentialProvider();this.validateResolvedCredentials(J);let X=Y??await this.regionProvider(),{longDate:V,shortDate:C}=this.formatDate(Q);if(D>uoA)return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");let K=zq1(C,X,W??this.service),H=loA(UQ0(A),{unhoistableHeaders:G,hoistableHeaders:I});if(J.sessionToken)H.query[qQ0]=J.sessionToken;H.query[OoA]=Kq1,H.query[ToA]=`${J.accessKeyId}/${K}`,H.query[wQ0]=V,H.query[SoA]=D.toString(10);let z=zQ0(H,Z,F);return H.query[PoA]=this.getCanonicalHeaderList(z),H.query[$Q0]=await this.getSignature(V,K,this.getSigningKey(J,X,C,W),this.createCanonicalRequest(H,z,await Eq1(A,this.sha256))),H}async sign(A,B){if(typeof A==="string")return this.signString(A,B);else if(A.headers&&A.payload)return this.signEvent(A,B);else if(A.message)return this.signMessage(A,B);else return this.signRequest(A,B)}async signEvent({headers:A,payload:B},{signingDate:Q=new Date,priorSignature:D,signingRegion:Z,signingService:G}){let F=Z??await this.regionProvider(),{shortDate:I,longDate:Y}=this.formatDate(Q),W=zq1(I,F,G??this.service),J=await Eq1({headers:{},body:B},this.sha256),X=new this.sha256;X.update(A);let V=my.toHex(await X.digest()),C=[foA,Y,W,D,V,J].join(`
`);return this.signString(C,{signingDate:Q,signingRegion:F,signingService:G})}async signMessage(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}){return this.signEvent({headers:this.headerFormatter.format(A.message.headers),payload:A.message.body},{signingDate:B,signingRegion:Q,signingService:D,priorSignature:A.priorSignature}).then((G)=>{return{message:A.message,signature:G}})}async signString(A,{signingDate:B=new Date,signingRegion:Q,signingService:D}={}){let Z=await this.credentialProvider();this.validateResolvedCredentials(Z);let G=Q??await this.regionProvider(),{shortDate:F}=this.formatDate(B),I=new this.sha256(await this.getSigningKey(Z,G,F,D));return I.update(qoA.toUint8Array(A)),my.toHex(await I.digest())}async signRequest(A,{signingDate:B=new Date,signableHeaders:Q,unsignableHeaders:D,signingRegion:Z,signingService:G}={}){let F=await this.credentialProvider();this.validateResolvedCredentials(F);let I=Z??await this.regionProvider(),Y=UQ0(A),{longDate:W,shortDate:J}=this.formatDate(B),X=zq1(J,I,G??this.service);if(Y.headers[LQ0]=W,F.sessionToken)Y.headers[_oA]=F.sessionToken;let V=await Eq1(Y,this.sha256);if(!doA(wq1,Y.headers)&&this.applyChecksum)Y.headers[wq1]=V;let C=zQ0(Y,D,Q),K=await this.getSignature(W,X,this.getSigningKey(F,I,J,G),this.createCanonicalRequest(Y,C,V));return Y.headers[NQ0]=`${Kq1} Credential=${F.accessKeyId}/${X}, SignedHeaders=${this.getCanonicalHeaderList(C)}, Signature=${K}`,Y}async getSignature(A,B,Q,D){let Z=await this.createStringToSign(A,B,D,Kq1),G=new this.sha256(await Q);return G.update(qoA.toUint8Array(Z)),my.toHex(await G.digest())}getSigningKey(A,B,Q,D){return moA(this.sha256,A,Q,B,D||this.service)}},i94={SignatureV4a:null}});
var c12=E((cX5,d12)=>{var{defineProperty:xq1,getOwnPropertyDescriptor:c84,getOwnPropertyNames:l84}=Object,p84=Object.prototype.hasOwnProperty,R6=(A,B)=>xq1(A,"name",{value:B,configurable:!0}),i84=(A,B)=>{for(var Q in B)xq1(A,Q,{get:B[Q],enumerable:!0})},n84=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of l84(B))if(!p84.call(A,Z)&&Z!==Q)xq1(A,Z,{get:()=>B[Z],enumerable:!(D=c84(B,Z))||D.enumerable})}return A},a84=(A)=>n84(xq1({},"__esModule",{value:!0}),A),N12={};i84(N12,{GetRoleCredentialsCommand:()=>g12,GetRoleCredentialsRequestFilterSensitiveLog:()=>T12,GetRoleCredentialsResponseFilterSensitiveLog:()=>S12,InvalidRequestException:()=>L12,ListAccountRolesCommand:()=>W40,ListAccountRolesRequestFilterSensitiveLog:()=>j12,ListAccountsCommand:()=>J40,ListAccountsRequestFilterSensitiveLog:()=>k12,LogoutCommand:()=>u12,LogoutRequestFilterSensitiveLog:()=>y12,ResourceNotFoundException:()=>M12,RoleCredentialsFilterSensitiveLog:()=>P12,SSO:()=>m12,SSOClient:()=>bq1,SSOServiceException:()=>Gs,TooManyRequestsException:()=>R12,UnauthorizedException:()=>O12,__Client:()=>TB.Client,paginateListAccountRoles:()=>$54,paginateListAccounts:()=>q54});d12.exports=a84(N12);var z12=Y61(),s84=W61(),r84=J61(),E12=Bs(),o84=V4(),MT=VB(),t84=TG(),$61=q6(),U12=v4(),w12=Z40(),e84=R6((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"})},"resolveClientEndpointParameters"),vq1={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},A54=W12(),$12=U61(),q12=CV(),TB=J6(),B54=R6((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),Q54=R6((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),D54=R6((A,B)=>{let Q=Object.assign($12.getAwsRegionExtensionConfiguration(A),TB.getDefaultExtensionConfiguration(A),q12.getHttpHandlerExtensionConfiguration(A),B54(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,$12.resolveAwsRegionExtensionConfiguration(Q),TB.resolveDefaultRuntimeConfig(Q),q12.resolveHttpHandlerRuntimeConfig(Q),Q54(Q))},"resolveRuntimeExtensions"),bq1=class extends TB.Client{static{R6(this,"SSOClient")}config;constructor(...[A]){let B=A54.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=e84(B),D=E12.resolveUserAgentConfig(Q),Z=U12.resolveRetryConfig(D),G=o84.resolveRegionConfig(Z),F=z12.resolveHostHeaderConfig(G),I=$61.resolveEndpointConfig(F),Y=w12.resolveHttpAuthSchemeConfig(I),W=D54(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(E12.getUserAgentPlugin(this.config)),this.middlewareStack.use(U12.getRetryPlugin(this.config)),this.middlewareStack.use(t84.getContentLengthPlugin(this.config)),this.middlewareStack.use(z12.getHostHeaderPlugin(this.config)),this.middlewareStack.use(s84.getLoggerPlugin(this.config)),this.middlewareStack.use(r84.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(MT.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:w12.defaultSSOHttpAuthSchemeParametersProvider,identityProviderConfigProvider:R6(async(J)=>new MT.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(MT.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},fq1=T3(),Gs=class A extends TB.ServiceException{static{R6(this,"SSOServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},L12=class A extends Gs{static{R6(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},M12=class A extends Gs{static{R6(this,"ResourceNotFoundException")}name="ResourceNotFoundException";$fault="client";constructor(B){super({name:"ResourceNotFoundException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},R12=class A extends Gs{static{R6(this,"TooManyRequestsException")}name="TooManyRequestsException";$fault="client";constructor(B){super({name:"TooManyRequestsException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},O12=class A extends Gs{static{R6(this,"UnauthorizedException")}name="UnauthorizedException";$fault="client";constructor(B){super({name:"UnauthorizedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype)}},T12=R6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"GetRoleCredentialsRequestFilterSensitiveLog"),P12=R6((A)=>({...A,...A.secretAccessKey&&{secretAccessKey:TB.SENSITIVE_STRING},...A.sessionToken&&{sessionToken:TB.SENSITIVE_STRING}}),"RoleCredentialsFilterSensitiveLog"),S12=R6((A)=>({...A,...A.roleCredentials&&{roleCredentials:P12(A.roleCredentials)}}),"GetRoleCredentialsResponseFilterSensitiveLog"),j12=R6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"ListAccountRolesRequestFilterSensitiveLog"),k12=R6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"ListAccountsRequestFilterSensitiveLog"),y12=R6((A)=>({...A,...A.accessToken&&{accessToken:TB.SENSITIVE_STRING}}),"LogoutRequestFilterSensitiveLog"),w61=YI(),Z54=R6(async(A,B)=>{let Q=MT.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[uq1]:A[gq1]});Q.bp("/federation/credentials");let Z=TB.map({[U54]:[,TB.expectNonNull(A[E54],"roleName")],[x12]:[,TB.expectNonNull(A[_12],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_GetRoleCredentialsCommand"),G54=R6(async(A,B)=>{let Q=MT.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[uq1]:A[gq1]});Q.bp("/assignment/roles");let Z=TB.map({[h12]:[,A[f12]],[b12]:[()=>A.maxResults!==void 0,()=>A[v12].toString()],[x12]:[,TB.expectNonNull(A[_12],"accountId")]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountRolesCommand"),F54=R6(async(A,B)=>{let Q=MT.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[uq1]:A[gq1]});Q.bp("/assignment/accounts");let Z=TB.map({[h12]:[,A[f12]],[b12]:[()=>A.maxResults!==void 0,()=>A[v12].toString()]}),G;return Q.m("GET").h(D).q(Z).b(G),Q.build()},"se_ListAccountsCommand"),I54=R6(async(A,B)=>{let Q=MT.requestBuilder(A,B),D=TB.map({},TB.isSerializableHeaderValue,{[uq1]:A[gq1]});Q.bp("/logout");let Z;return Q.m("POST").h(D).b(Z),Q.build()},"se_LogoutCommand"),Y54=R6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return hq1(A,B);let Q=TB.map({$metadata:ly(A)}),D=TB.expectNonNull(TB.expectObject(await w61.parseJsonBody(A.body,B)),"body"),Z=TB.take(D,{roleCredentials:TB._json});return Object.assign(Q,Z),Q},"de_GetRoleCredentialsCommand"),W54=R6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return hq1(A,B);let Q=TB.map({$metadata:ly(A)}),D=TB.expectNonNull(TB.expectObject(await w61.parseJsonBody(A.body,B)),"body"),Z=TB.take(D,{nextToken:TB.expectString,roleList:TB._json});return Object.assign(Q,Z),Q},"de_ListAccountRolesCommand"),J54=R6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return hq1(A,B);let Q=TB.map({$metadata:ly(A)}),D=TB.expectNonNull(TB.expectObject(await w61.parseJsonBody(A.body,B)),"body"),Z=TB.take(D,{accountList:TB._json,nextToken:TB.expectString});return Object.assign(Q,Z),Q},"de_ListAccountsCommand"),X54=R6(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return hq1(A,B);let Q=TB.map({$metadata:ly(A)});return await TB.collectBody(A.body,B),Q},"de_LogoutCommand"),hq1=R6(async(A,B)=>{let Q={...A,body:await w61.parseJsonErrorBody(A.body,B)},D=w61.loadRestJsonErrorCode(A,Q.body);switch(D){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await C54(Q,B);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await K54(Q,B);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await H54(Q,B);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await z54(Q,B);default:let Z=Q.body;return V54({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),V54=TB.withBaseException(Gs),C54=R6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new L12({$metadata:ly(A),...Q});return TB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),K54=R6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new M12({$metadata:ly(A),...Q});return TB.decorateServiceException(G,A.body)},"de_ResourceNotFoundExceptionRes"),H54=R6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new R12({$metadata:ly(A),...Q});return TB.decorateServiceException(G,A.body)},"de_TooManyRequestsExceptionRes"),z54=R6(async(A,B)=>{let Q=TB.map({}),D=A.body,Z=TB.take(D,{message:TB.expectString});Object.assign(Q,Z);let G=new O12({$metadata:ly(A),...Q});return TB.decorateServiceException(G,A.body)},"de_UnauthorizedExceptionRes"),ly=R6((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),_12="accountId",gq1="accessToken",x12="account_id",v12="maxResults",b12="max_result",f12="nextToken",h12="next_token",E54="roleName",U54="role_name",uq1="x-amz-sso_bearer_token",g12=class extends TB.Command.classBuilder().ep(vq1).m(function(A,B,Q,D){return[fq1.getSerdePlugin(Q,this.serialize,this.deserialize),$61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(T12,S12).ser(Z54).de(Y54).build(){static{R6(this,"GetRoleCredentialsCommand")}},W40=class extends TB.Command.classBuilder().ep(vq1).m(function(A,B,Q,D){return[fq1.getSerdePlugin(Q,this.serialize,this.deserialize),$61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccountRoles",{}).n("SSOClient","ListAccountRolesCommand").f(j12,void 0).ser(G54).de(W54).build(){static{R6(this,"ListAccountRolesCommand")}},J40=class extends TB.Command.classBuilder().ep(vq1).m(function(A,B,Q,D){return[fq1.getSerdePlugin(Q,this.serialize,this.deserialize),$61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","ListAccounts",{}).n("SSOClient","ListAccountsCommand").f(k12,void 0).ser(F54).de(J54).build(){static{R6(this,"ListAccountsCommand")}},u12=class extends TB.Command.classBuilder().ep(vq1).m(function(A,B,Q,D){return[fq1.getSerdePlugin(Q,this.serialize,this.deserialize),$61.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("SWBPortalService","Logout",{}).n("SSOClient","LogoutCommand").f(y12,void 0).ser(I54).de(X54).build(){static{R6(this,"LogoutCommand")}},w54={GetRoleCredentialsCommand:g12,ListAccountRolesCommand:W40,ListAccountsCommand:J40,LogoutCommand:u12},m12=class extends bq1{static{R6(this,"SSO")}};TB.createAggregatedClient(w54,m12);var $54=MT.createPaginator(bq1,W40,"nextToken","nextToken","maxResults"),q54=MT.createPaginator(bq1,J40,"nextToken","nextToken","maxResults")});
var cB2=E((mB2)=>{Object.defineProperty(mB2,"__esModule",{value:!0});mB2.getRuntimeConfig=void 0;var JG4=YI(),XG4=VB(),VG4=J6(),CG4=BZ(),gB2=dy(),uB2=cB(),KG4=tQ0(),HG4=hB2(),zG4=(A)=>{return{apiVersion:"2023-04-20",base64Decoder:A?.base64Decoder??gB2.fromBase64,base64Encoder:A?.base64Encoder??gB2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??HG4.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??KG4.defaultBedrockHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new JG4.AwsSdkSigV4Signer},{schemeId:"smithy.api#httpBearerAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#httpBearerAuth"),signer:new XG4.HttpBearerAuthSigner}],logger:A?.logger??new VG4.NoOpLogger,serviceId:A?.serviceId??"Bedrock",urlParser:A?.urlParser??CG4.parseUrl,utf8Decoder:A?.utf8Decoder??uB2.fromUtf8,utf8Encoder:A?.utf8Encoder??uB2.toUtf8}};mB2.getRuntimeConfig=zG4});
var dy=E((BX5,Nq1)=>{var{defineProperty:KtA,getOwnPropertyDescriptor:EQ4,getOwnPropertyNames:UQ4}=Object,wQ4=Object.prototype.hasOwnProperty,SQ0=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of UQ4(B))if(!wQ4.call(A,Z)&&Z!==Q)KtA(A,Z,{get:()=>B[Z],enumerable:!(D=EQ4(B,Z))||D.enumerable})}return A},HtA=(A,B,Q)=>(SQ0(A,B,"default"),Q&&SQ0(Q,B,"default")),$Q4=(A)=>SQ0(KtA({},"__esModule",{value:!0}),A),jQ0={};Nq1.exports=$Q4(jQ0);HtA(jQ0,JtA(),Nq1.exports);HtA(jQ0,CtA(),Nq1.exports)});
var eQ0=E((SX5,YeA)=>{var{defineProperty:Pq1,getOwnPropertyDescriptor:Y64,getOwnPropertyNames:W64}=Object,J64=Object.prototype.hasOwnProperty,X64=(A,B)=>Pq1(A,"name",{value:B,configurable:!0}),V64=(A,B)=>{for(var Q in B)Pq1(A,Q,{get:B[Q],enumerable:!0})},C64=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of W64(B))if(!J64.call(A,Z)&&Z!==Q)Pq1(A,Z,{get:()=>B[Z],enumerable:!(D=Y64(B,Z))||D.enumerable})}return A},K64=(A)=>C64(Pq1({},"__esModule",{value:!0}),A),BeA={};V64(BeA,{ENV_ACCOUNT_ID:()=>IeA,ENV_CREDENTIAL_SCOPE:()=>FeA,ENV_EXPIRATION:()=>GeA,ENV_KEY:()=>QeA,ENV_SECRET:()=>DeA,ENV_SESSION:()=>ZeA,fromEnv:()=>E64});YeA.exports=K64(BeA);var H64=Tw(),z64=eB(),QeA="AWS_ACCESS_KEY_ID",DeA="AWS_SECRET_ACCESS_KEY",ZeA="AWS_SESSION_TOKEN",GeA="AWS_CREDENTIAL_EXPIRATION",FeA="AWS_CREDENTIAL_SCOPE",IeA="AWS_ACCOUNT_ID",E64=X64((A)=>async()=>{A?.logger?.debug("@aws-sdk/credential-provider-env - fromEnv");let B=process.env[QeA],Q=process.env[DeA],D=process.env[ZeA],Z=process.env[GeA],G=process.env[FeA],F=process.env[IeA];if(B&&Q){let I={accessKeyId:B,secretAccessKey:Q,...D&&{sessionToken:D},...Z&&{expiration:new Date(Z)},...G&&{credentialScope:G},...F&&{accountId:F}};return H64.setCredentialFeature(I,"CREDENTIALS_ENV_VARS","g"),I}throw new z64.CredentialsProviderError("Unable to find environment variable credentials.",{logger:A?.logger})},"fromEnv")});
var gtA=E((YX5,htA)=>{var{defineProperty:Rq1,getOwnPropertyDescriptor:tQ4,getOwnPropertyNames:eQ4}=Object,A44=Object.prototype.hasOwnProperty,e6=(A,B)=>Rq1(A,"name",{value:B,configurable:!0}),B44=(A,B)=>{for(var Q in B)Rq1(A,Q,{get:B[Q],enumerable:!0})},Q44=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of eQ4(B))if(!A44.call(A,Z)&&Z!==Q)Rq1(A,Z,{get:()=>B[Z],enumerable:!(D=tQ4(B,Z))||D.enumerable})}return A},D44=(A)=>Q44(Rq1({},"__esModule",{value:!0}),A),PtA={};B44(PtA,{AwsEc2QueryProtocol:()=>y44,AwsJson1_0Protocol:()=>E44,AwsJson1_1Protocol:()=>U44,AwsJsonRpcProtocol:()=>pQ0,AwsQueryProtocol:()=>_tA,AwsRestJsonProtocol:()=>$44,AwsRestXmlProtocol:()=>h44,JsonCodec:()=>lQ0,JsonShapeDeserializer:()=>ktA,JsonShapeSerializer:()=>ytA,XmlCodec:()=>ftA,XmlShapeDeserializer:()=>iQ0,XmlShapeSerializer:()=>btA,_toBool:()=>G44,_toNum:()=>F44,_toStr:()=>Z44,awsExpectUnion:()=>N44,loadRestJsonErrorCode:()=>cQ0,loadRestXmlErrorCode:()=>vtA,parseJsonBody:()=>dQ0,parseJsonErrorBody:()=>V44,parseXmlBody:()=>xtA,parseXmlErrorBody:()=>b44});htA.exports=D44(PtA);var Z44=e6((A)=>{if(A==null)return A;if(typeof A==="number"||typeof A==="bigint"){let B=new Error(`Received number ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}if(typeof A==="boolean"){let B=new Error(`Received boolean ${A} where a string was expected.`);return B.name="Warning",console.warn(B),String(A)}return A},"_toStr"),G44=e6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=A.toLowerCase();if(A!==""&&B!=="false"&&B!=="true"){let Q=new Error(`Received string "${A}" where a boolean was expected.`);Q.name="Warning",console.warn(Q)}return A!==""&&B!=="false"}return A},"_toBool"),F44=e6((A)=>{if(A==null)return A;if(typeof A==="string"){let B=Number(A);if(B.toString()!==A){let Q=new Error(`Received string "${A}" where a number was expected.`);return Q.name="Warning",console.warn(Q),A}return B}return A},"_toNum"),I44=$6(),ta=jQ(),Y44=wY(),Ug=class{static{e6(this,"SerdeContextConfig")}serdeContext;setSerdeContext(A){this.serdeContext=A}},V61=jQ(),ea=Y6(),W44=dy(),J44=Y6();function StA(A,B,Q){if(Q?.source){let D=Q.source;if(typeof B==="number"){if(B>Number.MAX_SAFE_INTEGER||B<Number.MIN_SAFE_INTEGER||D!==String(B))if(D.includes("."))return new J44.NumericValue(D,"bigDecimal");else return BigInt(D)}}return B}e6(StA,"jsonReviver");var X44=J6(),jtA=e6((A,B)=>X44.collectBody(A,B).then((Q)=>B.utf8Encoder(Q)),"collectBodyString"),dQ0=e6((A,B)=>jtA(A,B).then((Q)=>{if(Q.length)try{return JSON.parse(Q)}catch(D){if(D?.name==="SyntaxError")Object.defineProperty(D,"$responseBodyText",{value:Q});throw D}return{}}),"parseJsonBody"),V44=e6(async(A,B)=>{let Q=await dQ0(A,B);return Q.message=Q.message??Q.Message,Q},"parseJsonErrorBody"),cQ0=e6((A,B)=>{let Q=e6((G,F)=>Object.keys(G).find((I)=>I.toLowerCase()===F.toLowerCase()),"findKey"),D=e6((G)=>{let F=G;if(typeof F==="number")F=F.toString();if(F.indexOf(",")>=0)F=F.split(",")[0];if(F.indexOf(":")>=0)F=F.split(":")[0];if(F.indexOf("#")>=0)F=F.split("#")[1];return F},"sanitizeErrorCode"),Z=Q(A.headers,"x-amzn-errortype");if(Z!==void 0)return D(A.headers[Z]);if(B&&typeof B==="object"){let G=Q(B,"code");if(G&&B[G]!==void 0)return D(B[G]);if(B.__type!==void 0)return D(B.__type)}},"loadRestJsonErrorCode"),ktA=class extends Ug{constructor(A){super();this.settings=A}static{e6(this,"JsonShapeDeserializer")}async read(A,B){return this._read(A,typeof B==="string"?JSON.parse(B,StA):await dQ0(B,this.serdeContext))}readObject(A,B){return this._read(A,B)}_read(A,B){let Q=B!==null&&typeof B==="object",D=V61.NormalizedSchema.of(A);if(D.isListSchema()&&Array.isArray(B)){let G=D.getValueSchema(),F=[],I=!!D.getMergedTraits().sparse;for(let Y of B)if(I||Y!=null)F.push(this._read(G,Y));return F}else if(D.isMapSchema()&&Q){let G=D.getValueSchema(),F={},I=!!D.getMergedTraits().sparse;for(let[Y,W]of Object.entries(B))if(I||W!=null)F[Y]=this._read(G,W);return F}else if(D.isStructSchema()&&Q){let G={};for(let[F,I]of D.structIterator()){let Y=this.settings.jsonName?I.getMergedTraits().jsonName??F:F,W=this._read(I,B[Y]);if(W!=null)G[F]=W}return G}if(D.isBlobSchema()&&typeof B==="string")return W44.fromBase64(B);let Z=D.getMergedTraits().mediaType;if(D.isStringSchema()&&typeof B==="string"&&Z){if(Z==="application/json"||Z.endsWith("+json"))return ea.LazyJsonString.from(B)}if(D.isTimestampSchema()){let G=this.settings.timestampFormat;switch(G.useTrait?D.getSchema()===V61.SCHEMA.TIMESTAMP_DEFAULT?G.default:D.getSchema()??G.default:G.default){case V61.SCHEMA.TIMESTAMP_DATE_TIME:return ea.parseRfc3339DateTimeWithOffset(B);case V61.SCHEMA.TIMESTAMP_HTTP_DATE:return ea.parseRfc7231DateTime(B);case V61.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return ea.parseEpochTimestamp(B);default:return console.warn("Missing timestamp format, parsing value with Date constructor:",B),new Date(B)}}if(D.isBigIntegerSchema()&&(typeof B==="number"||typeof B==="string"))return BigInt(B);if(D.isBigDecimalSchema()&&B!=null){if(B instanceof ea.NumericValue)return B;return new ea.NumericValue(String(B),"bigDecimal")}if(D.isNumericSchema()&&typeof B==="string")switch(B){case"Infinity":return 1/0;case"-Infinity":return-1/0;case"NaN":return NaN}return B}},As=jQ(),C44=Y6(),K44=Y6(),H44=Y6(),MtA=String.fromCharCode(925),z44=class{static{e6(this,"JsonReplacer")}values=new Map;counter=0;stage=0;createReplacer(){if(this.stage===1)throw new Error("@aws-sdk/core/protocols - JsonReplacer already created.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");return this.stage=1,(A,B)=>{if(B instanceof H44.NumericValue){let Q=`${MtA+NaN+this.counter++}_`+B.string;return this.values.set(`"${Q}"`,B.string),Q}if(typeof B==="bigint"){let Q=B.toString(),D=`${MtA+"b"+this.counter++}_`+Q;return this.values.set(`"${D}"`,Q),D}return B}}replaceInJson(A){if(this.stage===0)throw new Error("@aws-sdk/core/protocols - JsonReplacer not created yet.");if(this.stage===2)throw new Error("@aws-sdk/core/protocols - JsonReplacer exhausted.");if(this.stage=2,this.counter===0)return A;for(let[B,Q]of this.values)A=A.replace(B,Q);return A}},ytA=class extends Ug{constructor(A){super();this.settings=A}static{e6(this,"JsonShapeSerializer")}buffer;rootSchema;write(A,B){this.rootSchema=As.NormalizedSchema.of(A),this.buffer=this._write(this.rootSchema,B)}flush(){if(this.rootSchema?.isStructSchema()||this.rootSchema?.isDocumentSchema()){let A=new z44;return A.replaceInJson(JSON.stringify(this.buffer,A.createReplacer(),0))}return this.buffer}_write(A,B,Q){let D=B!==null&&typeof B==="object",Z=As.NormalizedSchema.of(A);if(Z.isListSchema()&&Array.isArray(B)){let F=Z.getValueSchema(),I=[],Y=!!Z.getMergedTraits().sparse;for(let W of B)if(Y||W!=null)I.push(this._write(F,W));return I}else if(Z.isMapSchema()&&D){let F=Z.getValueSchema(),I={},Y=!!Z.getMergedTraits().sparse;for(let[W,J]of Object.entries(B))if(Y||J!=null)I[W]=this._write(F,J);return I}else if(Z.isStructSchema()&&D){let F={};for(let[I,Y]of Z.structIterator()){let W=this.settings.jsonName?Y.getMergedTraits().jsonName??I:I,J=this._write(Y,B[I],Z);if(J!==void 0)F[W]=J}return F}if(B===null&&Q?.isStructSchema())return;if(Z.isBlobSchema()&&(B instanceof Uint8Array||typeof B==="string")){if(Z===this.rootSchema)return B;if(!this.serdeContext?.base64Encoder)throw new Error("Missing base64Encoder in serdeContext");return this.serdeContext?.base64Encoder(B)}if(Z.isTimestampSchema()&&B instanceof Date){let F=this.settings.timestampFormat;switch(F.useTrait?Z.getSchema()===As.SCHEMA.TIMESTAMP_DEFAULT?F.default:Z.getSchema()??F.default:F.default){case As.SCHEMA.TIMESTAMP_DATE_TIME:return B.toISOString().replace(".000Z","Z");case As.SCHEMA.TIMESTAMP_HTTP_DATE:return C44.dateToUtcString(B);case As.SCHEMA.TIMESTAMP_EPOCH_SECONDS:return B.getTime()/1000;default:return console.warn("Missing timestamp format, using epoch seconds",B),B.getTime()/1000}}if(Z.isNumericSchema()&&typeof B==="number"){if(Math.abs(B)===1/0||isNaN(B))return String(B)}let G=Z.getMergedTraits().mediaType;if(Z.isStringSchema()&&typeof B==="string"&&G){if(G==="application/json"||G.endsWith("+json"))return K44.LazyJsonString.from(B)}return B}},lQ0=class extends Ug{constructor(A){super();this.settings=A}static{e6(this,"JsonCodec")}createSerializer(){let A=new ytA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new ktA(this.settings);return A.setSerdeContext(this.serdeContext),A}},pQ0=class extends I44.RpcProtocol{static{e6(this,"AwsJsonRpcProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});this.codec=new lQ0({timestampFormat:{useTrait:!0,default:ta.SCHEMA.TIMESTAMP_EPOCH_SECONDS},jsonName:!1}),this.serializer=this.codec.createSerializer(),this.deserializer=this.codec.createDeserializer()}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":`application/x-amz-json-${this.getJsonRpcVersion()}`,"x-amz-target":(this.getJsonRpcVersion()==="1.0"?"JsonRpc10.":"JsonProtocol.")+ta.NormalizedSchema.of(A).getName()}),ta.deref(A.input)==="unit"||!D.body)D.body="{}";try{D.headers["content-length"]=String(Y44.calculateBodyLength(D.body))}catch(Z){}return D}getPayloadCodec(){return this.codec}async handleError(A,B,Q,D,Z){let G=cQ0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=ta.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=ta.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=ta.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},E44=class extends pQ0{static{e6(this,"AwsJson1_0Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_0"}getJsonRpcVersion(){return"1.0"}},U44=class extends pQ0{static{e6(this,"AwsJson1_1Protocol")}constructor({defaultNamespace:A}){super({defaultNamespace:A})}getShapeId(){return"aws.protocols#awsJson1_1"}getJsonRpcVersion(){return"1.1"}},hQ0=$6(),C61=jQ(),w44=wY(),$44=class extends hQ0.HttpBindingProtocol{static{e6(this,"AwsRestJsonProtocol")}serializer;deserializer;codec;constructor({defaultNamespace:A}){super({defaultNamespace:A});let B={timestampFormat:{useTrait:!0,default:C61.SCHEMA.TIMESTAMP_EPOCH_SECONDS},httpBindings:!0,jsonName:!0};this.codec=new lQ0(B),this.serializer=new hQ0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new hQ0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getShapeId(){return"aws.protocols#restJson1"}getPayloadCodec(){return this.codec}setSerdeContext(A){this.codec.setSerdeContext(A),super.setSerdeContext(A)}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=C61.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/json"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/json"}}if(D.headers["content-type"]&&!D.body)D.body="{}";if(D.body)try{D.headers["content-length"]=String(w44.calculateBodyLength(D.body))}catch(F){}return D}async handleError(A,B,Q,D,Z){let G=cQ0(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=C61.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=C61.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=C61.NormalizedSchema.of(W),X=D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().jsonName??K;C[K]=this.codec.createDeserializer().readObject(H,D[z])}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}},q44=J6(),N44=e6((A)=>{if(A==null)return;if(typeof A==="object"&&"__type"in A)delete A.__type;return q44.expectUnion(A)},"awsExpectUnion"),gQ0=$6(),cy=jQ(),L44=wY(),M44=$6(),RtA=jQ(),R44=J6(),O44=cB(),T44=gN(),iQ0=class extends Ug{constructor(A){super();this.settings=A,this.stringDeserializer=new M44.FromStringShapeDeserializer(A)}static{e6(this,"XmlShapeDeserializer")}stringDeserializer;setSerdeContext(A){this.serdeContext=A,this.stringDeserializer.setSerdeContext(A)}read(A,B,Q){let D=RtA.NormalizedSchema.of(A),Z=D.getMemberSchemas();if(D.isStructSchema()&&D.isMemberSchema()&&!!Object.values(Z).find((Y)=>{return!!Y.getMemberTraits().eventPayload})){let Y={},W=Object.keys(Z)[0];if(Z[W].isBlobSchema())Y[W]=B;else Y[W]=this.read(Z[W],B);return Y}let F=(this.serdeContext?.utf8Encoder??O44.toUtf8)(B),I=this.parseXml(F);return this.readSchema(A,Q?I[Q]:I)}readSchema(A,B){let Q=RtA.NormalizedSchema.of(A),D=Q.getMergedTraits(),Z=Q.getSchema();if(Q.isListSchema()&&!Array.isArray(B))return this.readSchema(Z,[B]);if(B==null)return B;if(typeof B==="object"){let G=!!D.sparse,F=!!D.xmlFlattened;if(Q.isListSchema()){let Y=Q.getValueSchema(),W=[],J=Y.getMergedTraits().xmlName??"member",X=F?B:(B[0]??B)[J],V=Array.isArray(X)?X:[X];for(let C of V)if(C!=null||G)W.push(this.readSchema(Y,C));return W}let I={};if(Q.isMapSchema()){let Y=Q.getKeySchema(),W=Q.getValueSchema(),J;if(F)J=Array.isArray(B)?B:[B];else J=Array.isArray(B.entry)?B.entry:[B.entry];let X=Y.getMergedTraits().xmlName??"key",V=W.getMergedTraits().xmlName??"value";for(let C of J){let K=C[X],H=C[V];if(H!=null||G)I[K]=this.readSchema(W,H)}return I}if(Q.isStructSchema()){for(let[Y,W]of Q.structIterator()){let J=W.getMergedTraits(),X=!J.httpPayload?W.getMemberTraits().xmlName??Y:J.xmlName??W.getName();if(B[X]!=null)I[Y]=this.readSchema(W,B[X])}return I}if(Q.isDocumentSchema())return B;throw new Error(`@aws-sdk/core/protocols - xml deserializer unhandled schema type for ${Q.getName(!0)}`)}else{if(Q.isListSchema())return[];else if(Q.isMapSchema()||Q.isStructSchema())return{};return this.stringDeserializer.read(Q,B)}}parseXml(A){if(A.length){let B=new T44.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:e6((F,I)=>I.trim()===""&&I.includes(`
`)?"":void 0,"tagValueProcessor")});B.addEntity("#xD","\r"),B.addEntity("#10",`
`);let Q;try{Q=B.parse(A,!0)}catch(F){if(F&&typeof F==="object")Object.defineProperty(F,"$responseBodyText",{value:A});throw F}let D="#text",Z=Object.keys(Q)[0],G=Q[Z];if(G[D])G[Z]=G[D],delete G[D];return R44.getValueFromTextNode(G)}return{}}},uQ0=$6(),Mq1=jQ(),P44=Y6(),S44=J6(),j44=dy(),k44=class extends Ug{constructor(A){super();this.settings=A}static{e6(this,"QueryShapeSerializer")}buffer;write(A,B,Q=""){if(this.buffer===void 0)this.buffer="";let D=Mq1.NormalizedSchema.of(A);if(Q&&!Q.endsWith("."))Q+=".";if(D.isBlobSchema()){if(typeof B==="string"||B instanceof Uint8Array)this.writeKey(Q),this.writeValue((this.serdeContext?.base64Encoder??j44.toBase64)(B))}else if(D.isBooleanSchema()||D.isNumericSchema()||D.isStringSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigIntegerSchema()){if(B!=null)this.writeKey(Q),this.writeValue(String(B))}else if(D.isBigDecimalSchema()){if(B!=null)this.writeKey(Q),this.writeValue(B instanceof P44.NumericValue?B.string:String(B))}else if(D.isTimestampSchema()){if(B instanceof Date)switch(this.writeKey(Q),uQ0.determineTimestampFormat(D,this.settings)){case Mq1.SCHEMA.TIMESTAMP_DATE_TIME:this.writeValue(B.toISOString().replace(".000Z","Z"));break;case Mq1.SCHEMA.TIMESTAMP_HTTP_DATE:this.writeValue(S44.dateToUtcString(B));break;case Mq1.SCHEMA.TIMESTAMP_EPOCH_SECONDS:this.writeValue(String(B.getTime()/1000));break}}else if(D.isDocumentSchema())throw new Error(`@aws-sdk/core/protocols - QuerySerializer unsupported document type ${D.getName(!0)}`);else if(D.isListSchema()){if(Array.isArray(B))if(B.length===0){if(this.settings.serializeEmptyLists)this.writeKey(Q),this.writeValue("")}else{let Z=D.getValueSchema(),G=this.settings.flattenLists||D.getMergedTraits().xmlFlattened,F=1;for(let I of B){if(I==null)continue;let Y=this.getKey("member",Z.getMergedTraits().xmlName),W=G?`${Q}${F}`:`${Q}${Y}.${F}`;this.write(Z,I,W),++F}}}else if(D.isMapSchema()){if(B&&typeof B==="object"){let Z=D.getKeySchema(),G=D.getValueSchema(),F=D.getMergedTraits().xmlFlattened,I=1;for(let[Y,W]of Object.entries(B)){if(W==null)continue;let J=this.getKey("key",Z.getMergedTraits().xmlName),X=F?`${Q}${I}.${J}`:`${Q}entry.${I}.${J}`,V=this.getKey("value",G.getMergedTraits().xmlName),C=F?`${Q}${I}.${V}`:`${Q}entry.${I}.${V}`;this.write(Z,Y,X),this.write(G,W,C),++I}}}else if(D.isStructSchema()){if(B&&typeof B==="object")for(let[Z,G]of D.structIterator()){if(B[Z]==null)continue;let F=this.getKey(Z,G.getMergedTraits().xmlName),I=`${Q}${F}`;this.write(G,B[Z],I)}}else if(D.isUnitSchema());else throw new Error(`@aws-sdk/core/protocols - QuerySerializer unrecognized schema type ${D.getName(!0)}`)}flush(){if(this.buffer===void 0)throw new Error("@aws-sdk/core/protocols - QuerySerializer cannot flush with nothing written to buffer.");let A=this.buffer;return delete this.buffer,A}getKey(A,B){let Q=B??A;if(this.settings.capitalizeKeys)return Q[0].toUpperCase()+Q.slice(1);return Q}writeKey(A){if(A.endsWith("."))A=A.slice(0,A.length-1);this.buffer+=`&${uQ0.extendedEncodeURIComponent(A)}=`}writeValue(A){this.buffer+=uQ0.extendedEncodeURIComponent(A)}},_tA=class extends gQ0.RpcProtocol{constructor(A){super({defaultNamespace:A.defaultNamespace});this.options=A;let B={timestampFormat:{useTrait:!0,default:cy.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!1,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace,serializeEmptyLists:!0};this.serializer=new k44(B),this.deserializer=new iQ0(B)}static{e6(this,"AwsQueryProtocol")}serializer;deserializer;getShapeId(){return"aws.protocols#awsQuery"}setSerdeContext(A){this.serializer.setSerdeContext(A),this.deserializer.setSerdeContext(A)}getPayloadCodec(){throw new Error("AWSQuery protocol has no payload codec.")}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q);if(!D.path.endsWith("/"))D.path+="/";if(Object.assign(D.headers,{"content-type":"application/x-www-form-urlencoded"}),cy.deref(A.input)==="unit"||!D.body)D.body="";if(D.body=`Action=${A.name.split("#")[1]}&Version=${this.options.version}`+D.body,D.body.endsWith("&"))D.body=D.body.slice(-1);try{D.headers["content-length"]=String(L44.calculateBodyLength(D.body))}catch(Z){}return D}async deserializeResponse(A,B,Q){let D=this.deserializer,Z=cy.NormalizedSchema.of(A.output),G={};if(Q.statusCode>=300){let W=await gQ0.collectBody(Q.body,B);if(W.byteLength>0)Object.assign(G,await D.read(cy.SCHEMA.DOCUMENT,W));await this.handleError(A,B,Q,G,this.deserializeMetadata(Q))}for(let W in Q.headers){let J=Q.headers[W];delete Q.headers[W],Q.headers[W.toLowerCase()]=J}let F=Z.isStructSchema()&&this.useNestedResult()?A.name.split("#")[1]+"Result":void 0,I=await gQ0.collectBody(Q.body,B);if(I.byteLength>0)Object.assign(G,await D.read(Z,I,F));return{$metadata:this.deserializeMetadata(Q),...G}}useNestedResult(){return!0}async handleError(A,B,Q,D,Z){let G=this.loadQueryErrorCode(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=this.loadQueryError(D),W=cy.TypeRegistry.for(F),J;try{if(J=W.find((H)=>cy.NormalizedSchema.of(H).getMergedTraits().awsQueryError?.[0]===I),!J)J=W.getSchema(G)}catch(H){let z=cy.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(z){let $=z.ctor;throw Object.assign(new $(I),Y)}throw new Error(I)}let X=cy.NormalizedSchema.of(J),V=this.loadQueryErrorMessage(D),C=new J.ctor(V),K={};for(let[H,z]of X.structIterator()){let $=z.getMergedTraits().xmlName??H,L=Y[$]??D[$];K[H]=this.deserializer.readSchema(z,L)}throw Object.assign(C,{$metadata:Z,$response:Q,$fault:X.getMergedTraits().error,message:V,...K}),C}loadQueryErrorCode(A,B){let Q=(B.Errors?.[0]?.Error??B.Errors?.Error??B.Error)?.Code;if(Q!==void 0)return Q;if(A.statusCode==404)return"NotFound"}loadQueryError(A){return A.Errors?.[0]?.Error??A.Errors?.Error??A.Error}loadQueryErrorMessage(A){let B=this.loadQueryError(A);return B?.message??B?.Message??A.message??A.Message??"Unknown"}},y44=class extends _tA{constructor(A){super(A);this.options=A;let B={capitalizeKeys:!0,flattenLists:!0,serializeEmptyLists:!1};Object.assign(this.serializer.settings,B)}static{e6(this,"AwsEc2QueryProtocol")}useNestedResult(){return!1}},mQ0=$6(),K61=jQ(),_44=wY(),x44=J6(),v44=gN(),xtA=e6((A,B)=>jtA(A,B).then((Q)=>{if(Q.length){let D=new v44.XMLParser({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:e6((Y,W)=>W.trim()===""&&W.includes(`
`)?"":void 0,"tagValueProcessor")});D.addEntity("#xD","\r"),D.addEntity("#10",`
`);let Z;try{Z=D.parse(Q,!0)}catch(Y){if(Y&&typeof Y==="object")Object.defineProperty(Y,"$responseBodyText",{value:Q});throw Y}let G="#text",F=Object.keys(Z)[0],I=Z[F];if(I[G])I[F]=I[G],delete I[G];return x44.getValueFromTextNode(I)}return{}}),"parseXmlBody"),b44=e6(async(A,B)=>{let Q=await xtA(A,B);if(Q.Error)Q.Error.message=Q.Error.message??Q.Error.Message;return Q},"parseXmlErrorBody"),vtA=e6((A,B)=>{if(B?.Error?.Code!==void 0)return B.Error.Code;if(B?.Code!==void 0)return B.Code;if(A.statusCode==404)return"NotFound"},"loadRestXmlErrorCode"),IL=gQ1(),Eg=jQ(),f44=Y6(),OtA=J6(),TtA=dy(),btA=class extends Ug{constructor(A){super();this.settings=A}static{e6(this,"XmlShapeSerializer")}stringBuffer;byteBuffer;buffer;write(A,B){let Q=Eg.NormalizedSchema.of(A);if(Q.isStringSchema()&&typeof B==="string")this.stringBuffer=B;else if(Q.isBlobSchema())this.byteBuffer="byteLength"in B?B:(this.serdeContext?.base64Decoder??TtA.fromBase64)(B);else{this.buffer=this.writeStruct(Q,B,void 0);let D=Q.getMergedTraits();if(D.httpPayload&&!D.xmlName)this.buffer.withName(Q.getName())}}flush(){if(this.byteBuffer!==void 0){let B=this.byteBuffer;return delete this.byteBuffer,B}if(this.stringBuffer!==void 0){let B=this.stringBuffer;return delete this.stringBuffer,B}let A=this.buffer;if(this.settings.xmlNamespace){if(!A?.attributes?.xmlns)A.addAttribute("xmlns",this.settings.xmlNamespace)}return delete this.buffer,A.toString()}writeStruct(A,B,Q){let D=A.getMergedTraits(),Z=A.isMemberSchema()&&!D.httpPayload?A.getMemberTraits().xmlName??A.getMemberName():D.xmlName??A.getName();if(!Z||!A.isStructSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write struct with empty name or non-struct, schema=${A.getName(!0)}.`);let G=IL.XmlNode.of(Z),[F,I]=this.getXmlnsAttribute(A,Q);if(I)G.addAttribute(F,I);for(let[Y,W]of A.structIterator()){let J=B[Y];if(J!=null){if(W.getMergedTraits().xmlAttribute){G.addAttribute(W.getMergedTraits().xmlName??Y,this.writeSimple(W,J));continue}if(W.isListSchema())this.writeList(W,J,G,I);else if(W.isMapSchema())this.writeMap(W,J,G,I);else if(W.isStructSchema())G.addChildNode(this.writeStruct(W,J,I));else{let X=IL.XmlNode.of(W.getMergedTraits().xmlName??W.getMemberName());this.writeSimpleInto(W,J,X,I),G.addChildNode(X)}}}return G}writeList(A,B,Q,D){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member list: ${A.getName(!0)}`);let Z=A.getMergedTraits(),G=A.getValueSchema(),F=G.getMergedTraits(),I=!!F.sparse,Y=!!Z.xmlFlattened,[W,J]=this.getXmlnsAttribute(A,D),X=e6((V,C)=>{if(G.isListSchema())this.writeList(G,Array.isArray(C)?C:[C],V,J);else if(G.isMapSchema())this.writeMap(G,C,V,J);else if(G.isStructSchema()){let K=this.writeStruct(G,C,J);V.addChildNode(K.withName(Y?Z.xmlName??A.getMemberName():F.xmlName??"member"))}else{let K=IL.XmlNode.of(Y?Z.xmlName??A.getMemberName():F.xmlName??"member");this.writeSimpleInto(G,C,K,J),V.addChildNode(K)}},"writeItem");if(Y){for(let V of B)if(I||V!=null)X(Q,V)}else{let V=IL.XmlNode.of(Z.xmlName??A.getMemberName());if(J)V.addAttribute(W,J);for(let C of B)if(I||C!=null)X(V,C);Q.addChildNode(V)}}writeMap(A,B,Q,D,Z=!1){if(!A.isMemberSchema())throw new Error(`@aws-sdk/core/protocols - xml serializer, cannot write non-member map: ${A.getName(!0)}`);let G=A.getMergedTraits(),F=A.getKeySchema(),Y=F.getMergedTraits().xmlName??"key",W=A.getValueSchema(),J=W.getMergedTraits(),X=J.xmlName??"value",V=!!J.sparse,C=!!G.xmlFlattened,[K,H]=this.getXmlnsAttribute(A,D),z=e6(($,L,N)=>{let O=IL.XmlNode.of(Y,L),[R,T]=this.getXmlnsAttribute(F,H);if(T)O.addAttribute(R,T);$.addChildNode(O);let j=IL.XmlNode.of(X);if(W.isListSchema())this.writeList(W,N,j,H);else if(W.isMapSchema())this.writeMap(W,N,j,H,!0);else if(W.isStructSchema())j=this.writeStruct(W,N,H);else this.writeSimpleInto(W,N,j,H);$.addChildNode(j)},"addKeyValue");if(C){for(let[$,L]of Object.entries(B))if(V||L!=null){let N=IL.XmlNode.of(G.xmlName??A.getMemberName());z(N,$,L),Q.addChildNode(N)}}else{let $;if(!Z){if($=IL.XmlNode.of(G.xmlName??A.getMemberName()),H)$.addAttribute(K,H);Q.addChildNode($)}for(let[L,N]of Object.entries(B))if(V||N!=null){let O=IL.XmlNode.of("entry");z(O,L,N),(Z?Q:$).addChildNode(O)}}}writeSimple(A,B){if(B===null)throw new Error("@aws-sdk/core/protocols - (XML serializer) cannot write null value.");let Q=Eg.NormalizedSchema.of(A),D=null;if(B&&typeof B==="object")if(Q.isBlobSchema())D=(this.serdeContext?.base64Encoder??TtA.toBase64)(B);else if(Q.isTimestampSchema()&&B instanceof Date){let Z=this.settings.timestampFormat;switch(Z.useTrait?Q.getSchema()===Eg.SCHEMA.TIMESTAMP_DEFAULT?Z.default:Q.getSchema()??Z.default:Z.default){case Eg.SCHEMA.TIMESTAMP_DATE_TIME:D=B.toISOString().replace(".000Z","Z");break;case Eg.SCHEMA.TIMESTAMP_HTTP_DATE:D=OtA.dateToUtcString(B);break;case Eg.SCHEMA.TIMESTAMP_EPOCH_SECONDS:D=String(B.getTime()/1000);break;default:console.warn("Missing timestamp format, using http date",B),D=OtA.dateToUtcString(B);break}}else if(Q.isBigDecimalSchema()&&B){if(B instanceof f44.NumericValue)return B.string;return String(B)}else if(Q.isMapSchema()||Q.isListSchema())throw new Error("@aws-sdk/core/protocols - xml serializer, cannot call _write() on List/Map schema, call writeList or writeMap() instead.");else throw new Error(`@aws-sdk/core/protocols - xml serializer, unhandled schema type for object value and schema: ${Q.getName(!0)}`);if(Q.isStringSchema()||Q.isBooleanSchema()||Q.isNumericSchema()||Q.isBigIntegerSchema()||Q.isBigDecimalSchema())D=String(B);if(D===null)throw new Error(`Unhandled schema-value pair ${Q.getName(!0)}=${B}`);return D}writeSimpleInto(A,B,Q,D){let Z=this.writeSimple(A,B),G=Eg.NormalizedSchema.of(A),F=new IL.XmlText(Z),[I,Y]=this.getXmlnsAttribute(G,D);if(Y)Q.addAttribute(I,Y);Q.addChildNode(F)}getXmlnsAttribute(A,B){let Q=A.getMergedTraits(),[D,Z]=Q.xmlNamespace??[];if(Z&&Z!==B)return[D?`xmlns:${D}`:"xmlns",Z];return[void 0,void 0]}},ftA=class extends Ug{constructor(A){super();this.settings=A}static{e6(this,"XmlCodec")}createSerializer(){let A=new btA(this.settings);return A.setSerdeContext(this.serdeContext),A}createDeserializer(){let A=new iQ0(this.settings);return A.setSerdeContext(this.serdeContext),A}},h44=class extends mQ0.HttpBindingProtocol{static{e6(this,"AwsRestXmlProtocol")}codec;serializer;deserializer;constructor(A){super(A);let B={timestampFormat:{useTrait:!0,default:K61.SCHEMA.TIMESTAMP_DATE_TIME},httpBindings:!0,xmlNamespace:A.xmlNamespace,serviceNamespace:A.defaultNamespace};this.codec=new ftA(B),this.serializer=new mQ0.HttpInterceptingShapeSerializer(this.codec.createSerializer(),B),this.deserializer=new mQ0.HttpInterceptingShapeDeserializer(this.codec.createDeserializer(),B)}getPayloadCodec(){return this.codec}getShapeId(){return"aws.protocols#restXml"}async serializeRequest(A,B,Q){let D=await super.serializeRequest(A,B,Q),Z=K61.NormalizedSchema.of(A.input),G=Z.getMemberSchemas();if(D.path=String(D.path).split("/").filter((F)=>{return F!=="{Bucket}"}).join("/")||"/",!D.headers["content-type"]){let F=Object.values(G).find((I)=>{return!!I.getMergedTraits().httpPayload});if(F){let I=F.getMergedTraits().mediaType;if(I)D.headers["content-type"]=I;else if(F.isStringSchema())D.headers["content-type"]="text/plain";else if(F.isBlobSchema())D.headers["content-type"]="application/octet-stream";else D.headers["content-type"]="application/xml"}else if(!Z.isUnitSchema()){if(Object.values(G).find((Y)=>{let{httpQuery:W,httpQueryParams:J,httpHeader:X,httpLabel:V,httpPrefixHeaders:C}=Y.getMergedTraits();return!W&&!J&&!X&&!V&&C===void 0}))D.headers["content-type"]="application/xml"}}if(D.headers["content-type"]==="application/xml"){if(typeof D.body==="string")D.body='<?xml version="1.0" encoding="UTF-8"?>'+D.body}if(D.body)try{D.headers["content-length"]=String(_44.calculateBodyLength(D.body))}catch(F){}return D}async deserializeResponse(A,B,Q){return super.deserializeResponse(A,B,Q)}async handleError(A,B,Q,D,Z){let G=vtA(Q,D)??"Unknown",F=this.options.defaultNamespace,I=G;if(G.includes("#"))[F,I]=G.split("#");let Y=K61.TypeRegistry.for(F),W;try{W=Y.getSchema(G)}catch(K){let H=K61.TypeRegistry.for("smithy.ts.sdk.synthetic."+F).getBaseException();if(H){let z=H.ctor;throw Object.assign(new z(I),D)}throw new Error(I)}let J=K61.NormalizedSchema.of(W),X=D.Error?.message??D.Error?.Message??D.message??D.Message??"Unknown",V=new W.ctor(X);await this.deserializeHttpMessage(W,B,Q,D);let C={};for(let[K,H]of J.structIterator()){let z=H.getMergedTraits().xmlName??K,$=D.Error?.[z]??D[z];C[K]=this.codec.createDeserializer().readSchema(H,$)}throw Object.assign(V,{$metadata:Z,$response:Q,$fault:J.getMergedTraits().error,message:X,...C}),V}}});
var hB2=E((bB2)=>{Object.defineProperty(bB2,"__esModule",{value:!0});bB2.defaultEndpointResolver=void 0;var FG4=sa(),q60=$7(),IG4=vB2(),YG4=new q60.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),WG4=(A,B={})=>{return YG4.get(A,()=>q60.resolveEndpoint(IG4.ruleSet,{endpointParams:A,logger:B.logger}))};bB2.defaultEndpointResolver=WG4;q60.customEndpointFunctions.aws=FG4.awsEndpointFunctions});
var lA2=E((dA2)=>{Object.defineProperty(dA2,"__esModule",{value:!0});dA2.defaultEndpointResolver=void 0;var N74=sa(),S40=$7(),L74=mA2(),M74=new S40.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),R74=(A,B={})=>{return M74.get(A,()=>S40.resolveEndpoint(L74.ruleSet,{endpointParams:A,logger:B.logger}))};dA2.defaultEndpointResolver=R74;S40.customEndpointFunctions.aws=N74.awsEndpointFunctions});
var mA2=E((gA2)=>{Object.defineProperty(gA2,"__esModule",{value:!0});gA2.ruleSet=void 0;var SA2="required",E4="type",m8="fn",d8="argv",iy="ref",UA2=!1,O40=!0,py="booleanEquals",xY="stringEquals",jA2="sigv4",kA2="sts",yA2="us-east-1",JD="endpoint",wA2="https://sts.{Region}.{PartitionResult#dnsSuffix}",JL="tree",Js="error",P40="getAttr",$A2={[SA2]:!1,[E4]:"String"},T40={[SA2]:!0,default:!1,[E4]:"Boolean"},_A2={[iy]:"Endpoint"},qA2={[m8]:"isSet",[d8]:[{[iy]:"Region"}]},vY={[iy]:"Region"},NA2={[m8]:"aws.partition",[d8]:[vY],assign:"PartitionResult"},xA2={[iy]:"UseFIPS"},vA2={[iy]:"UseDualStack"},mW={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:jA2,signingName:kA2,signingRegion:yA2}]},headers:{}},XK={},LA2={conditions:[{[m8]:xY,[d8]:[vY,"aws-global"]}],[JD]:mW,[E4]:JD},bA2={[m8]:py,[d8]:[xA2,!0]},fA2={[m8]:py,[d8]:[vA2,!0]},MA2={[m8]:P40,[d8]:[{[iy]:"PartitionResult"},"supportsFIPS"]},hA2={[iy]:"PartitionResult"},RA2={[m8]:py,[d8]:[!0,{[m8]:P40,[d8]:[hA2,"supportsDualStack"]}]},OA2=[{[m8]:"isSet",[d8]:[_A2]}],TA2=[bA2],PA2=[fA2],q74={version:"1.0",parameters:{Region:$A2,UseDualStack:T40,UseFIPS:T40,Endpoint:$A2,UseGlobalEndpoint:T40},rules:[{conditions:[{[m8]:py,[d8]:[{[iy]:"UseGlobalEndpoint"},O40]},{[m8]:"not",[d8]:OA2},qA2,NA2,{[m8]:py,[d8]:[xA2,UA2]},{[m8]:py,[d8]:[vA2,UA2]}],rules:[{conditions:[{[m8]:xY,[d8]:[vY,"ap-northeast-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"ap-south-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"ap-southeast-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"ap-southeast-2"]}],endpoint:mW,[E4]:JD},LA2,{conditions:[{[m8]:xY,[d8]:[vY,"ca-central-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"eu-central-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"eu-north-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"eu-west-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"eu-west-2"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"eu-west-3"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"sa-east-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,yA2]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"us-east-2"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"us-west-1"]}],endpoint:mW,[E4]:JD},{conditions:[{[m8]:xY,[d8]:[vY,"us-west-2"]}],endpoint:mW,[E4]:JD},{endpoint:{url:wA2,properties:{authSchemes:[{name:jA2,signingName:kA2,signingRegion:"{Region}"}]},headers:XK},[E4]:JD}],[E4]:JL},{conditions:OA2,rules:[{conditions:TA2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[E4]:Js},{conditions:PA2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[E4]:Js},{endpoint:{url:_A2,properties:XK,headers:XK},[E4]:JD}],[E4]:JL},{conditions:[qA2],rules:[{conditions:[NA2],rules:[{conditions:[bA2,fA2],rules:[{conditions:[{[m8]:py,[d8]:[O40,MA2]},RA2],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:XK,headers:XK},[E4]:JD}],[E4]:JL},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[E4]:Js}],[E4]:JL},{conditions:TA2,rules:[{conditions:[{[m8]:py,[d8]:[MA2,O40]}],rules:[{conditions:[{[m8]:xY,[d8]:[{[m8]:P40,[d8]:[hA2,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:XK,headers:XK},[E4]:JD},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:XK,headers:XK},[E4]:JD}],[E4]:JL},{error:"FIPS is enabled but this partition does not support FIPS",[E4]:Js}],[E4]:JL},{conditions:PA2,rules:[{conditions:[RA2],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:XK,headers:XK},[E4]:JD}],[E4]:JL},{error:"DualStack is enabled but this partition does not support DualStack",[E4]:Js}],[E4]:JL},LA2,{endpoint:{url:wA2,properties:XK,headers:XK},[E4]:JD}],[E4]:JL}],[E4]:JL},{error:"Invalid Configuration: Missing Region",[E4]:Js}]};gA2.ruleSet=q74});
var oeA=E((seA)=>{Object.defineProperty(seA,"__esModule",{value:!0});seA.defaultEndpointResolver=void 0;var X84=sa(),Y40=$7(),V84=aeA(),C84=new Y40.EndpointCache({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),K84=(A,B={})=>{return C84.get(A,()=>Y40.resolveEndpoint(V84.ruleSet,{endpointParams:A,logger:B.logger}))};seA.defaultEndpointResolver=K84;Y40.customEndpointFunctions.aws=X84.awsEndpointFunctions});
var q40=E((FV5,DA2)=>{var{create:f34,defineProperty:L61,getOwnPropertyDescriptor:h34,getOwnPropertyNames:g34,getPrototypeOf:u34}=Object,m34=Object.prototype.hasOwnProperty,OT=(A,B)=>L61(A,"name",{value:B,configurable:!0}),d34=(A,B)=>{for(var Q in B)L61(A,Q,{get:B[Q],enumerable:!0})},e02=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of g34(B))if(!m34.call(A,Z)&&Z!==Q)L61(A,Z,{get:()=>B[Z],enumerable:!(D=h34(B,Z))||D.enumerable})}return A},AA2=(A,B,Q)=>(Q=A!=null?f34(u34(A)):{},e02(B||!A||!A.__esModule?L61(Q,"default",{value:A,enumerable:!0}):Q,A)),c34=(A)=>e02(L61({},"__esModule",{value:!0}),A),BA2={};d34(BA2,{fromEnvSigningName:()=>i34,fromSso:()=>QA2,fromStatic:()=>e34,nodeProvider:()=>A74});DA2.exports=c34(BA2);var l34=Tw(),p34=PQ0(),JK=eB(),i34=OT(({logger:A,signingName:B}={})=>async()=>{if(A?.debug?.("@aws-sdk/token-providers - fromEnvSigningName"),!B)throw new JK.TokenProviderError("Please pass 'signingName' to compute environment variable key",{logger:A});let Q=p34.getBearerTokenEnvKey(B);if(!(Q in process.env))throw new JK.TokenProviderError(`Token not present in '${Q}' environment variable`,{logger:A});let D={token:process.env[Q]};return l34.setTokenFeature(D,"BEARER_SERVICE_ENV_VARS","3"),D},"fromEnvSigningName"),n34=300000,$40="To refresh this SSO session run 'aws sso login' with the corresponding profile.",a34=OT(async(A,B={})=>{let{SSOOIDCClient:Q}=await Promise.resolve().then(()=>AA2(w40()));return new Q(Object.assign({},B.clientConfig??{},{region:A??B.clientConfig?.region,logger:B.clientConfig?.logger??B.parentClientConfig?.logger}))},"getSsoOidcClient"),s34=OT(async(A,B,Q={})=>{let{CreateTokenCommand:D}=await Promise.resolve().then(()=>AA2(w40()));return(await a34(B,Q)).send(new D({clientId:A.clientId,clientSecret:A.clientSecret,refreshToken:A.refreshToken,grantType:"refresh_token"}))},"getNewSsoOidcToken"),o02=OT((A)=>{if(A.expiration&&A.expiration.getTime()<Date.now())throw new JK.TokenProviderError(`Token is expired. ${$40}`,!1)},"validateTokenExpiry"),qg=OT((A,B,Q=!1)=>{if(typeof B==="undefined")throw new JK.TokenProviderError(`Value not present for '${A}' in SSO Token${Q?". Cannot refresh":""}. ${$40}`,!1)},"validateTokenKey"),N61=e5(),r34=J1("fs"),{writeFile:o34}=r34.promises,t34=OT((A,B)=>{let Q=N61.getSSOTokenFilepath(A),D=JSON.stringify(B,null,2);return o34(Q,D)},"writeSSOTokenToFile"),t02=new Date(0),QA2=OT((A={})=>async({callerClientConfig:B}={})=>{let Q={...A,parentClientConfig:{...B,...A.parentClientConfig}};Q.logger?.debug("@aws-sdk/token-providers - fromSso");let D=await N61.parseKnownFiles(Q),Z=N61.getProfileName({profile:Q.profile??B?.profile}),G=D[Z];if(!G)throw new JK.TokenProviderError(`Profile '${Z}' could not be found in shared credentials file.`,!1);else if(!G.sso_session)throw new JK.TokenProviderError(`Profile '${Z}' is missing required property 'sso_session'.`);let F=G.sso_session,Y=(await N61.loadSsoSessionData(Q))[F];if(!Y)throw new JK.TokenProviderError(`Sso session '${F}' could not be found in shared credentials file.`,!1);for(let H of["sso_start_url","sso_region"])if(!Y[H])throw new JK.TokenProviderError(`Sso session '${F}' is missing required property '${H}'.`,!1);let{sso_start_url:W,sso_region:J}=Y,X;try{X=await N61.getSSOTokenFromFile(F)}catch(H){throw new JK.TokenProviderError(`The SSO session token associated with profile=${Z} was not found or is invalid. ${$40}`,!1)}qg("accessToken",X.accessToken),qg("expiresAt",X.expiresAt);let{accessToken:V,expiresAt:C}=X,K={token:V,expiration:new Date(C)};if(K.expiration.getTime()-Date.now()>n34)return K;if(Date.now()-t02.getTime()<30000)return o02(K),K;qg("clientId",X.clientId,!0),qg("clientSecret",X.clientSecret,!0),qg("refreshToken",X.refreshToken,!0);try{t02.setTime(Date.now());let H=await s34(X,J,Q);qg("accessToken",H.accessToken),qg("expiresIn",H.expiresIn);let z=new Date(Date.now()+H.expiresIn*1000);try{await t34(F,{...X,accessToken:H.accessToken,expiresAt:z.toISOString(),refreshToken:H.refreshToken})}catch($){}return{token:H.accessToken,expiration:z}}catch(H){return o02(K),K}},"fromSso"),e34=OT(({token:A,logger:B})=>async()=>{if(B?.debug("@aws-sdk/token-providers - fromStatic"),!A||!A.token)throw new JK.TokenProviderError("Please pass a valid token to fromStatic",!1);return A},"fromStatic"),A74=OT((A={})=>JK.memoize(JK.chain(QA2(A),async()=>{throw new JK.TokenProviderError("Could not load token from any providers",!1)}),(B)=>B.expiration!==void 0&&B.expiration.getTime()-Date.now()<300000,(B)=>B.expiration!==void 0),"nodeProvider")});
var qeA=E((weA)=>{Object.defineProperty(weA,"__esModule",{value:!0});weA.fromHttp=void 0;var k64=Hg(),y64=Tw(),_64=S3(),EeA=eB(),x64=k64.__importDefault(J1("fs/promises")),v64=XeA(),UeA=CeA(),b64=zeA(),f64="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",h64="http://*************",g64="AWS_CONTAINER_CREDENTIALS_FULL_URI",u64="AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE",m64="AWS_CONTAINER_AUTHORIZATION_TOKEN",d64=(A={})=>{A.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let B,Q=A.awsContainerCredentialsRelativeUri??process.env[f64],D=A.awsContainerCredentialsFullUri??process.env[g64],Z=A.awsContainerAuthorizationToken??process.env[m64],G=A.awsContainerAuthorizationTokenFile??process.env[u64],F=A.logger?.constructor?.name==="NoOpLogger"||!A.logger?console.warn:A.logger.warn;if(Q&&D)F("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),F("awsContainerCredentialsFullUri will take precedence.");if(Z&&G)F("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),F("awsContainerAuthorizationToken will take precedence.");if(D)B=D;else if(Q)B=`${h64}${Q}`;else throw new EeA.CredentialsProviderError(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:A.logger});let I=new URL(B);v64.checkUrl(I,A.logger);let Y=new _64.NodeHttpHandler({requestTimeout:A.timeout??1000,connectionTimeout:A.timeout??1000});return b64.retryWrapper(async()=>{let W=UeA.createGetRequest(I);if(Z)W.headers.Authorization=Z;else if(G)W.headers.Authorization=(await x64.default.readFile(G)).toString();try{let J=await Y.handle(W);return UeA.getCredentials(J.response).then((X)=>y64.setCredentialFeature(X,"CREDENTIALS_HTTP","z"))}catch(J){throw new EeA.CredentialsProviderError(String(J),{logger:A.logger})}},A.maxRetries??3,A.timeout??1000)};weA.fromHttp=d64});
var rB2=E((aB2)=>{Object.defineProperty(aB2,"__esModule",{value:!0});aB2.getRuntimeConfig=void 0;var EG4=Hg(),UG4=EG4.__importDefault(AeA()),N60=YI(),wG4=wB2(),lB2=q40(),pB2=z61(),oq1=V4(),$G4=VB(),qG4=jG(),iB2=v4(),Mg=QD(),nB2=S3(),NG4=kG(),LG4=hZ(),MG4=cB2(),RG4=J6(),OG4=yG(),TG4=J6(),PG4=(A)=>{TG4.emitWarningIfUnsupportedVersion(process.version);let B=OG4.resolveDefaultsModeConfig(A),Q=()=>B().then(RG4.loadConfigsForDefaultMode),D=MG4.getRuntimeConfig(A);N60.emitWarningIfUnsupportedVersion(process.version);let Z={profile:A?.profile,logger:D.logger,signingName:"bedrock"};return{...D,...A,runtime:"node",defaultsMode:B,authSchemePreference:A?.authSchemePreference??Mg.loadConfig(N60.NODE_AUTH_SCHEME_PREFERENCE_OPTIONS,Z),bodyLengthChecker:A?.bodyLengthChecker??NG4.calculateBodyLength,credentialDefaultProvider:A?.credentialDefaultProvider??wG4.defaultProvider,defaultUserAgentProvider:A?.defaultUserAgentProvider??pB2.createDefaultUserAgentProvider({serviceId:D.serviceId,clientVersion:UG4.default.version}),httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(G)=>G.getIdentityProvider("aws.auth#sigv4"),signer:new N60.AwsSdkSigV4Signer},{schemeId:"smithy.api#httpBearerAuth",identityProvider:(G)=>G.getIdentityProvider("smithy.api#httpBearerAuth")||(async(F)=>{try{return await lB2.fromEnvSigningName({signingName:"bedrock"})()}catch(I){return await lB2.nodeProvider(F)(F)}}),signer:new $G4.HttpBearerAuthSigner}],maxAttempts:A?.maxAttempts??Mg.loadConfig(iB2.NODE_MAX_ATTEMPT_CONFIG_OPTIONS,A),region:A?.region??Mg.loadConfig(oq1.NODE_REGION_CONFIG_OPTIONS,{...oq1.NODE_REGION_CONFIG_FILE_OPTIONS,...Z}),requestHandler:nB2.NodeHttpHandler.create(A?.requestHandler??Q),retryMode:A?.retryMode??Mg.loadConfig({...iB2.NODE_RETRY_MODE_CONFIG_OPTIONS,default:async()=>(await Q()).retryMode||LG4.DEFAULT_RETRY_MODE},A),sha256:A?.sha256??qG4.Hash.bind(null,"sha256"),streamCollector:A?.streamCollector??nB2.streamCollector,useDualstackEndpoint:A?.useDualstackEndpoint??Mg.loadConfig(oq1.NODE_USE_DUALSTACK_ENDPOINT_CONFIG_OPTIONS,Z),useFipsEndpoint:A?.useFipsEndpoint??Mg.loadConfig(oq1.NODE_USE_FIPS_ENDPOINT_CONFIG_OPTIONS,Z),userAgentAppId:A?.userAgentAppId??Mg.loadConfig(pB2.NODE_APP_ID_CONFIG_OPTIONS,Z)}};aB2.getRuntimeConfig=PG4});
var sA2=E((nA2)=>{Object.defineProperty(nA2,"__esModule",{value:!0});nA2.getRuntimeConfig=void 0;var O74=YI(),T74=VB(),P74=J6(),S74=BZ(),pA2=dy(),iA2=cB(),j74=R40(),k74=lA2(),y74=(A)=>{return{apiVersion:"2011-06-15",base64Decoder:A?.base64Decoder??pA2.fromBase64,base64Encoder:A?.base64Encoder??pA2.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??k74.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??j74.defaultSTSHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new O74.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new T74.NoAuthSigner}],logger:A?.logger??new P74.NoOpLogger,serviceId:A?.serviceId??"STS",urlParser:A?.urlParser??S74.parseUrl,utf8Decoder:A?.utf8Decoder??iA2.fromUtf8,utf8Encoder:A?.utf8Encoder??iA2.toUtf8}};nA2.getRuntimeConfig=y74});
var sa=E((mJ5,xrA)=>{var{defineProperty:Fq1,getOwnPropertyDescriptor:iB4,getOwnPropertyNames:nB4}=Object,aB4=Object.prototype.hasOwnProperty,aa=(A,B)=>Fq1(A,"name",{value:B,configurable:!0}),sB4=(A,B)=>{for(var Q in B)Fq1(A,Q,{get:B[Q],enumerable:!0})},rB4=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of nB4(B))if(!aB4.call(A,Z)&&Z!==Q)Fq1(A,Z,{get:()=>B[Z],enumerable:!(D=iB4(B,Z))||D.enumerable})}return A},oB4=(A)=>rB4(Fq1({},"__esModule",{value:!0}),A),OrA={};sB4(OrA,{ConditionObject:()=>A7.ConditionObject,DeprecatedObject:()=>A7.DeprecatedObject,EndpointError:()=>A7.EndpointError,EndpointObject:()=>A7.EndpointObject,EndpointObjectHeaders:()=>A7.EndpointObjectHeaders,EndpointObjectProperties:()=>A7.EndpointObjectProperties,EndpointParams:()=>A7.EndpointParams,EndpointResolverOptions:()=>A7.EndpointResolverOptions,EndpointRuleObject:()=>A7.EndpointRuleObject,ErrorRuleObject:()=>A7.ErrorRuleObject,EvaluateOptions:()=>A7.EvaluateOptions,Expression:()=>A7.Expression,FunctionArgv:()=>A7.FunctionArgv,FunctionObject:()=>A7.FunctionObject,FunctionReturn:()=>A7.FunctionReturn,ParameterObject:()=>A7.ParameterObject,ReferenceObject:()=>A7.ReferenceObject,ReferenceRecord:()=>A7.ReferenceRecord,RuleSetObject:()=>A7.RuleSetObject,RuleSetRules:()=>A7.RuleSetRules,TreeRuleObject:()=>A7.TreeRuleObject,awsEndpointFunctions:()=>_rA,getUserAgentPrefix:()=>B94,isIpAddress:()=>A7.isIpAddress,partition:()=>krA,resolveEndpoint:()=>A7.resolveEndpoint,setPartitionInfo:()=>yrA,useDefaultPartitionInfo:()=>A94});xrA.exports=oB4(OrA);var A7=$7(),TrA=aa((A,B=!1)=>{if(B){for(let Q of A.split("."))if(!TrA(Q))return!1;return!0}if(!A7.isValidHostLabel(A))return!1;if(A.length<3||A.length>63)return!1;if(A!==A.toLowerCase())return!1;if(A7.isIpAddress(A))return!1;return!0},"isVirtualHostableS3Bucket"),RrA=":",tB4="/",eB4=aa((A)=>{let B=A.split(RrA);if(B.length<6)return null;let[Q,D,Z,G,F,...I]=B;if(Q!=="arn"||D===""||Z===""||I.join(RrA)==="")return null;let Y=I.map((W)=>W.split(tB4)).flat();return{partition:D,service:Z,region:G,accountId:F,resourceId:Y}},"parseArn"),PrA={partitions:[{id:"aws",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-east-1",name:"aws",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",regions:{"af-south-1":{description:"Africa (Cape Town)"},"ap-east-1":{description:"Asia Pacific (Hong Kong)"},"ap-east-2":{description:"Asia Pacific (Taipei)"},"ap-northeast-1":{description:"Asia Pacific (Tokyo)"},"ap-northeast-2":{description:"Asia Pacific (Seoul)"},"ap-northeast-3":{description:"Asia Pacific (Osaka)"},"ap-south-1":{description:"Asia Pacific (Mumbai)"},"ap-south-2":{description:"Asia Pacific (Hyderabad)"},"ap-southeast-1":{description:"Asia Pacific (Singapore)"},"ap-southeast-2":{description:"Asia Pacific (Sydney)"},"ap-southeast-3":{description:"Asia Pacific (Jakarta)"},"ap-southeast-4":{description:"Asia Pacific (Melbourne)"},"ap-southeast-5":{description:"Asia Pacific (Malaysia)"},"ap-southeast-7":{description:"Asia Pacific (Thailand)"},"aws-global":{description:"AWS Standard global region"},"ca-central-1":{description:"Canada (Central)"},"ca-west-1":{description:"Canada West (Calgary)"},"eu-central-1":{description:"Europe (Frankfurt)"},"eu-central-2":{description:"Europe (Zurich)"},"eu-north-1":{description:"Europe (Stockholm)"},"eu-south-1":{description:"Europe (Milan)"},"eu-south-2":{description:"Europe (Spain)"},"eu-west-1":{description:"Europe (Ireland)"},"eu-west-2":{description:"Europe (London)"},"eu-west-3":{description:"Europe (Paris)"},"il-central-1":{description:"Israel (Tel Aviv)"},"me-central-1":{description:"Middle East (UAE)"},"me-south-1":{description:"Middle East (Bahrain)"},"mx-central-1":{description:"Mexico (Central)"},"sa-east-1":{description:"South America (Sao Paulo)"},"us-east-1":{description:"US East (N. Virginia)"},"us-east-2":{description:"US East (Ohio)"},"us-west-1":{description:"US West (N. California)"},"us-west-2":{description:"US West (Oregon)"}}},{id:"aws-cn",outputs:{dnsSuffix:"amazonaws.com.cn",dualStackDnsSuffix:"api.amazonwebservices.com.cn",implicitGlobalRegion:"cn-northwest-1",name:"aws-cn",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^cn\\-\\w+\\-\\d+$",regions:{"aws-cn-global":{description:"AWS China global region"},"cn-north-1":{description:"China (Beijing)"},"cn-northwest-1":{description:"China (Ningxia)"}}},{id:"aws-us-gov",outputs:{dnsSuffix:"amazonaws.com",dualStackDnsSuffix:"api.aws",implicitGlobalRegion:"us-gov-west-1",name:"aws-us-gov",supportsDualStack:!0,supportsFIPS:!0},regionRegex:"^us\\-gov\\-\\w+\\-\\d+$",regions:{"aws-us-gov-global":{description:"AWS GovCloud (US) global region"},"us-gov-east-1":{description:"AWS GovCloud (US-East)"},"us-gov-west-1":{description:"AWS GovCloud (US-West)"}}},{id:"aws-iso",outputs:{dnsSuffix:"c2s.ic.gov",dualStackDnsSuffix:"c2s.ic.gov",implicitGlobalRegion:"us-iso-east-1",name:"aws-iso",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-iso\\-\\w+\\-\\d+$",regions:{"aws-iso-global":{description:"AWS ISO (US) global region"},"us-iso-east-1":{description:"US ISO East"},"us-iso-west-1":{description:"US ISO WEST"}}},{id:"aws-iso-b",outputs:{dnsSuffix:"sc2s.sgov.gov",dualStackDnsSuffix:"sc2s.sgov.gov",implicitGlobalRegion:"us-isob-east-1",name:"aws-iso-b",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isob\\-\\w+\\-\\d+$",regions:{"aws-iso-b-global":{description:"AWS ISOB (US) global region"},"us-isob-east-1":{description:"US ISOB East (Ohio)"}}},{id:"aws-iso-e",outputs:{dnsSuffix:"cloud.adc-e.uk",dualStackDnsSuffix:"cloud.adc-e.uk",implicitGlobalRegion:"eu-isoe-west-1",name:"aws-iso-e",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eu\\-isoe\\-\\w+\\-\\d+$",regions:{"aws-iso-e-global":{description:"AWS ISOE (Europe) global region"},"eu-isoe-west-1":{description:"EU ISOE West"}}},{id:"aws-iso-f",outputs:{dnsSuffix:"csp.hci.ic.gov",dualStackDnsSuffix:"csp.hci.ic.gov",implicitGlobalRegion:"us-isof-south-1",name:"aws-iso-f",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^us\\-isof\\-\\w+\\-\\d+$",regions:{"aws-iso-f-global":{description:"AWS ISOF global region"},"us-isof-east-1":{description:"US ISOF EAST"},"us-isof-south-1":{description:"US ISOF SOUTH"}}},{id:"aws-eusc",outputs:{dnsSuffix:"amazonaws.eu",dualStackDnsSuffix:"amazonaws.eu",implicitGlobalRegion:"eusc-de-east-1",name:"aws-eusc",supportsDualStack:!1,supportsFIPS:!0},regionRegex:"^eusc\\-(de)\\-\\w+\\-\\d+$",regions:{"eusc-de-east-1":{description:"EU (Germany)"}}}],version:"1.1"},SrA=PrA,jrA="",krA=aa((A)=>{let{partitions:B}=SrA;for(let D of B){let{regions:Z,outputs:G}=D;for(let[F,I]of Object.entries(Z))if(F===A)return{...G,...I}}for(let D of B){let{regionRegex:Z,outputs:G}=D;if(new RegExp(Z).test(A))return{...G}}let Q=B.find((D)=>D.id==="aws");if(!Q)throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");return{...Q.outputs}},"partition"),yrA=aa((A,B="")=>{SrA=A,jrA=B},"setPartitionInfo"),A94=aa(()=>{yrA(PrA,"")},"useDefaultPartitionInfo"),B94=aa(()=>jrA,"getUserAgentPrefix"),_rA={isVirtualHostableS3Bucket:TrA,parseArn:eB4,partition:krA};A7.customEndpointFunctions.aws=_rA});
var tQ0=E((ttA)=>{Object.defineProperty(ttA,"__esModule",{value:!0});ttA.resolveHttpAuthSchemeConfig=ttA.defaultBedrockHttpAuthSchemeProvider=ttA.defaultBedrockHttpAuthSchemeParametersProvider=void 0;var e44=YI(),rQ0=VB(),oQ0=I5(),A64=async(A,B,Q)=>{return{operation:oQ0.getSmithyContext(B).operation,region:await oQ0.normalizeProvider(A.region)()||(()=>{throw new Error("expected `region` to be configured for `aws.auth#sigv4`")})()}};ttA.defaultBedrockHttpAuthSchemeParametersProvider=A64;function B64(A){return{schemeId:"aws.auth#sigv4",signingProperties:{name:"bedrock",region:A.region},propertiesExtractor:(B,Q)=>({signingProperties:{config:B,context:Q}})}}function Q64(A){return{schemeId:"smithy.api#httpBearerAuth",propertiesExtractor:({profile:B,filepath:Q,configFilepath:D,ignoreCache:Z},G)=>({identityProperties:{profile:B,filepath:Q,configFilepath:D,ignoreCache:Z}})}}var D64=(A)=>{let B=[];switch(A.operation){default:B.push(B64(A)),B.push(Q64(A))}return B};ttA.defaultBedrockHttpAuthSchemeProvider=D64;var Z64=(A)=>{let B=rQ0.memoizeIdentityProvider(A.token,rQ0.isIdentityExpired,rQ0.doesIdentityRequireRefresh),Q=e44.resolveAwsSdkSigV4Config(A);return Object.assign(Q,{authSchemePreference:oQ0.normalizeProvider(A.authSchemePreference??[]),token:B})};ttA.resolveHttpAuthSchemeConfig=Z64});
var vB2=E((_B2)=>{Object.defineProperty(_B2,"__esModule",{value:!0});_B2.ruleSet=void 0;var jB2="required",CL="fn",KL="argv",qs="ref",$B2=!0,qB2="isSet",k61="booleanEquals",$s="error",j61="endpoint",pJ="tree",$60="PartitionResult",NB2={[jB2]:!1,type:"String"},LB2={[jB2]:!0,default:!1,type:"Boolean"},MB2={[qs]:"Endpoint"},kB2={[CL]:k61,[KL]:[{[qs]:"UseFIPS"},!0]},yB2={[CL]:k61,[KL]:[{[qs]:"UseDualStack"},!0]},VL={},RB2={[CL]:"getAttr",[KL]:[{[qs]:$60},"supportsFIPS"]},OB2={[CL]:k61,[KL]:[!0,{[CL]:"getAttr",[KL]:[{[qs]:$60},"supportsDualStack"]}]},TB2=[kB2],PB2=[yB2],SB2=[{[qs]:"Region"}],GG4={version:"1.0",parameters:{Region:NB2,UseDualStack:LB2,UseFIPS:LB2,Endpoint:NB2},rules:[{conditions:[{[CL]:qB2,[KL]:[MB2]}],rules:[{conditions:TB2,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:$s},{rules:[{conditions:PB2,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:$s},{endpoint:{url:MB2,properties:VL,headers:VL},type:j61}],type:pJ}],type:pJ},{rules:[{conditions:[{[CL]:qB2,[KL]:SB2}],rules:[{conditions:[{[CL]:"aws.partition",[KL]:SB2,assign:$60}],rules:[{conditions:[kB2,yB2],rules:[{conditions:[{[CL]:k61,[KL]:[$B2,RB2]},OB2],rules:[{rules:[{endpoint:{url:"https://bedrock-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:VL,headers:VL},type:j61}],type:pJ}],type:pJ},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:$s}],type:pJ},{conditions:TB2,rules:[{conditions:[{[CL]:k61,[KL]:[RB2,$B2]}],rules:[{rules:[{endpoint:{url:"https://bedrock-fips.{Region}.{PartitionResult#dnsSuffix}",properties:VL,headers:VL},type:j61}],type:pJ}],type:pJ},{error:"FIPS is enabled but this partition does not support FIPS",type:$s}],type:pJ},{conditions:PB2,rules:[{conditions:[OB2],rules:[{rules:[{endpoint:{url:"https://bedrock.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:VL,headers:VL},type:j61}],type:pJ}],type:pJ},{error:"DualStack is enabled but this partition does not support DualStack",type:$s}],type:pJ},{rules:[{endpoint:{url:"https://bedrock.{Region}.{PartitionResult#dnsSuffix}",properties:VL,headers:VL},type:j61}],type:pJ}],type:pJ}],type:pJ},{error:"Invalid Configuration: Missing Region",type:$s}],type:pJ}]};_B2.ruleSet=GG4});
var w40=E((QV5,r02)=>{var{defineProperty:dq1,getOwnPropertyDescriptor:e54,getOwnPropertyNames:A34}=Object,B34=Object.prototype.hasOwnProperty,n4=(A,B)=>dq1(A,"name",{value:B,configurable:!0}),Q34=(A,B)=>{for(var Q in B)dq1(A,Q,{get:B[Q],enumerable:!0})},D34=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of A34(B))if(!B34.call(A,Z)&&Z!==Q)dq1(A,Z,{get:()=>B[Z],enumerable:!(D=e54(B,Z))||D.enumerable})}return A},Z34=(A)=>D34(dq1({},"__esModule",{value:!0}),A),k02={};Q34(k02,{$Command:()=>x02.Command,AccessDeniedException:()=>v02,AuthorizationPendingException:()=>b02,CreateTokenCommand:()=>a02,CreateTokenRequestFilterSensitiveLog:()=>f02,CreateTokenResponseFilterSensitiveLog:()=>h02,ExpiredTokenException:()=>g02,InternalServerException:()=>u02,InvalidClientException:()=>m02,InvalidGrantException:()=>d02,InvalidRequestException:()=>c02,InvalidScopeException:()=>l02,SSOOIDC:()=>s02,SSOOIDCClient:()=>_02,SSOOIDCServiceException:()=>WK,SlowDownException:()=>p02,UnauthorizedClientException:()=>i02,UnsupportedGrantTypeException:()=>n02,__Client:()=>y02.Client});r02.exports=Z34(k02);var M02=Y61(),G34=W61(),F34=J61(),R02=Bs(),I34=V4(),E40=VB(),Y34=TG(),W34=q6(),O02=v4(),y02=J6(),T02=V40(),J34=n4((A)=>{return Object.assign(A,{useDualstackEndpoint:A.useDualstackEndpoint??!1,useFipsEndpoint:A.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"})},"resolveClientEndpointParameters"),X34={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}},V34=L02(),P02=U61(),S02=CV(),j02=J6(),C34=n4((A)=>{let{httpAuthSchemes:B,httpAuthSchemeProvider:Q,credentials:D}=A;return{setHttpAuthScheme(Z){let G=B.findIndex((F)=>F.schemeId===Z.schemeId);if(G===-1)B.push(Z);else B.splice(G,1,Z)},httpAuthSchemes(){return B},setHttpAuthSchemeProvider(Z){Q=Z},httpAuthSchemeProvider(){return Q},setCredentials(Z){D=Z},credentials(){return D}}},"getHttpAuthExtensionConfiguration"),K34=n4((A)=>{return{httpAuthSchemes:A.httpAuthSchemes(),httpAuthSchemeProvider:A.httpAuthSchemeProvider(),credentials:A.credentials()}},"resolveHttpAuthRuntimeConfig"),H34=n4((A,B)=>{let Q=Object.assign(P02.getAwsRegionExtensionConfiguration(A),j02.getDefaultExtensionConfiguration(A),S02.getHttpHandlerExtensionConfiguration(A),C34(A));return B.forEach((D)=>D.configure(Q)),Object.assign(A,P02.resolveAwsRegionExtensionConfiguration(Q),j02.resolveDefaultRuntimeConfig(Q),S02.resolveHttpHandlerRuntimeConfig(Q),K34(Q))},"resolveRuntimeExtensions"),_02=class extends y02.Client{static{n4(this,"SSOOIDCClient")}config;constructor(...[A]){let B=V34.getRuntimeConfig(A||{});super(B);this.initConfig=B;let Q=J34(B),D=R02.resolveUserAgentConfig(Q),Z=O02.resolveRetryConfig(D),G=I34.resolveRegionConfig(Z),F=M02.resolveHostHeaderConfig(G),I=W34.resolveEndpointConfig(F),Y=T02.resolveHttpAuthSchemeConfig(I),W=H34(Y,A?.extensions||[]);this.config=W,this.middlewareStack.use(R02.getUserAgentPlugin(this.config)),this.middlewareStack.use(O02.getRetryPlugin(this.config)),this.middlewareStack.use(Y34.getContentLengthPlugin(this.config)),this.middlewareStack.use(M02.getHostHeaderPlugin(this.config)),this.middlewareStack.use(G34.getLoggerPlugin(this.config)),this.middlewareStack.use(F34.getRecursionDetectionPlugin(this.config)),this.middlewareStack.use(E40.getHttpAuthSchemeEndpointRuleSetPlugin(this.config,{httpAuthSchemeParametersProvider:T02.defaultSSOOIDCHttpAuthSchemeParametersProvider,identityProviderConfigProvider:n4(async(J)=>new E40.DefaultIdentityProviderConfig({"aws.auth#sigv4":J.credentials}),"identityProviderConfigProvider")})),this.middlewareStack.use(E40.getHttpSigningPlugin(this.config))}destroy(){super.destroy()}},z34=J6(),E34=q6(),U34=T3(),x02=J6(),Ws=J6(),w34=J6(),WK=class A extends w34.ServiceException{static{n4(this,"SSOOIDCServiceException")}constructor(B){super(B);Object.setPrototypeOf(this,A.prototype)}},v02=class A extends WK{static{n4(this,"AccessDeniedException")}name="AccessDeniedException";$fault="client";error;error_description;constructor(B){super({name:"AccessDeniedException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},b02=class A extends WK{static{n4(this,"AuthorizationPendingException")}name="AuthorizationPendingException";$fault="client";error;error_description;constructor(B){super({name:"AuthorizationPendingException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},f02=n4((A)=>({...A,...A.clientSecret&&{clientSecret:Ws.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Ws.SENSITIVE_STRING},...A.codeVerifier&&{codeVerifier:Ws.SENSITIVE_STRING}}),"CreateTokenRequestFilterSensitiveLog"),h02=n4((A)=>({...A,...A.accessToken&&{accessToken:Ws.SENSITIVE_STRING},...A.refreshToken&&{refreshToken:Ws.SENSITIVE_STRING},...A.idToken&&{idToken:Ws.SENSITIVE_STRING}}),"CreateTokenResponseFilterSensitiveLog"),g02=class A extends WK{static{n4(this,"ExpiredTokenException")}name="ExpiredTokenException";$fault="client";error;error_description;constructor(B){super({name:"ExpiredTokenException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},u02=class A extends WK{static{n4(this,"InternalServerException")}name="InternalServerException";$fault="server";error;error_description;constructor(B){super({name:"InternalServerException",$fault:"server",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},m02=class A extends WK{static{n4(this,"InvalidClientException")}name="InvalidClientException";$fault="client";error;error_description;constructor(B){super({name:"InvalidClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},d02=class A extends WK{static{n4(this,"InvalidGrantException")}name="InvalidGrantException";$fault="client";error;error_description;constructor(B){super({name:"InvalidGrantException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},c02=class A extends WK{static{n4(this,"InvalidRequestException")}name="InvalidRequestException";$fault="client";error;error_description;constructor(B){super({name:"InvalidRequestException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},l02=class A extends WK{static{n4(this,"InvalidScopeException")}name="InvalidScopeException";$fault="client";error;error_description;constructor(B){super({name:"InvalidScopeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},p02=class A extends WK{static{n4(this,"SlowDownException")}name="SlowDownException";$fault="client";error;error_description;constructor(B){super({name:"SlowDownException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},i02=class A extends WK{static{n4(this,"UnauthorizedClientException")}name="UnauthorizedClientException";$fault="client";error;error_description;constructor(B){super({name:"UnauthorizedClientException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},n02=class A extends WK{static{n4(this,"UnsupportedGrantTypeException")}name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(B){super({name:"UnsupportedGrantTypeException",$fault:"client",...B});Object.setPrototypeOf(this,A.prototype),this.error=B.error,this.error_description=B.error_description}},U40=YI(),$34=VB(),wB=J6(),q34=n4(async(A,B)=>{let Q=$34.requestBuilder(A,B),D={"content-type":"application/json"};Q.bp("/token");let Z;return Z=JSON.stringify(wB.take(A,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:n4((G)=>wB._json(G),"scope")})),Q.m("POST").h(D).b(Z),Q.build()},"se_CreateTokenCommand"),N34=n4(async(A,B)=>{if(A.statusCode!==200&&A.statusCode>=300)return L34(A,B);let Q=wB.map({$metadata:gz(A)}),D=wB.expectNonNull(wB.expectObject(await U40.parseJsonBody(A.body,B)),"body"),Z=wB.take(D,{accessToken:wB.expectString,expiresIn:wB.expectInt32,idToken:wB.expectString,refreshToken:wB.expectString,tokenType:wB.expectString});return Object.assign(Q,Z),Q},"de_CreateTokenCommand"),L34=n4(async(A,B)=>{let Q={...A,body:await U40.parseJsonErrorBody(A.body,B)},D=U40.loadRestJsonErrorCode(A,Q.body);switch(D){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await R34(Q,B);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await O34(Q,B);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await T34(Q,B);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await P34(Q,B);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await S34(Q,B);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await j34(Q,B);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await k34(Q,B);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await y34(Q,B);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await _34(Q,B);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await x34(Q,B);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await v34(Q,B);default:let Z=Q.body;return M34({output:A,parsedBody:Z,errorCode:D})}},"de_CommandError"),M34=wB.withBaseException(WK),R34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new v02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_AccessDeniedExceptionRes"),O34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new b02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_AuthorizationPendingExceptionRes"),T34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new g02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_ExpiredTokenExceptionRes"),P34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new u02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InternalServerExceptionRes"),S34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new m02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidClientExceptionRes"),j34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new d02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidGrantExceptionRes"),k34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new c02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidRequestExceptionRes"),y34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new l02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_InvalidScopeExceptionRes"),_34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new p02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_SlowDownExceptionRes"),x34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new i02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_UnauthorizedClientExceptionRes"),v34=n4(async(A,B)=>{let Q=wB.map({}),D=A.body,Z=wB.take(D,{error:wB.expectString,error_description:wB.expectString});Object.assign(Q,Z);let G=new n02({$metadata:gz(A),...Q});return wB.decorateServiceException(G,A.body)},"de_UnsupportedGrantTypeExceptionRes"),gz=n4((A)=>({httpStatusCode:A.statusCode,requestId:A.headers["x-amzn-requestid"]??A.headers["x-amzn-request-id"]??A.headers["x-amz-request-id"],extendedRequestId:A.headers["x-amz-id-2"],cfId:A.headers["x-amz-cf-id"]}),"deserializeMetadata"),a02=class extends x02.Command.classBuilder().ep(X34).m(function(A,B,Q,D){return[U34.getSerdePlugin(Q,this.serialize,this.deserialize),E34.getEndpointPlugin(Q,A.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(f02,h02).ser(q34).de(N34).build(){static{n4(this,"CreateTokenCommand")}},b34={CreateTokenCommand:a02},s02=class extends _02{static{n4(this,"SSOOIDC")}};z34.createAggregatedClient(b34,s02)});
var wB2=E((TV5,UB2)=>{var{create:sZ4,defineProperty:S61,getOwnPropertyDescriptor:rZ4,getOwnPropertyNames:oZ4,getPrototypeOf:tZ4}=Object,eZ4=Object.prototype.hasOwnProperty,rq1=(A,B)=>S61(A,"name",{value:B,configurable:!0}),AG4=(A,B)=>{for(var Q in B)S61(A,Q,{get:B[Q],enumerable:!0})},KB2=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of oZ4(B))if(!eZ4.call(A,Z)&&Z!==Q)S61(A,Z,{get:()=>B[Z],enumerable:!(D=rZ4(B,Z))||D.enumerable})}return A},ws=(A,B,Q)=>(Q=A!=null?sZ4(tZ4(A)):{},KB2(B||!A||!A.__esModule?S61(Q,"default",{value:A,enumerable:!0}):Q,A)),BG4=(A)=>KB2(S61({},"__esModule",{value:!0}),A),HB2={};AG4(HB2,{credentialsTreatedAsExpired:()=>EB2,credentialsWillNeedRefresh:()=>zB2,defaultProvider:()=>ZG4});UB2.exports=BG4(HB2);var w60=eQ0(),QG4=e5(),Lg=eB(),VB2="AWS_EC2_METADATA_DISABLED",DG4=rq1(async(A)=>{let{ENV_CMDS_FULL_URI:B,ENV_CMDS_RELATIVE_URI:Q,fromContainerMetadata:D,fromInstanceMetadata:Z}=await Promise.resolve().then(()=>ws($F()));if(process.env[Q]||process.env[B]){A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromHttp/fromContainerMetadata");let{fromHttp:G}=await Promise.resolve().then(()=>ws(Q40()));return Lg.chain(G(A),D(A))}if(process.env[VB2]&&process.env[VB2]!=="false")return async()=>{throw new Lg.CredentialsProviderError("EC2 Instance Metadata Service access disabled",{logger:A.logger})};return A.logger?.debug("@aws-sdk/credential-provider-node - remoteProvider::fromInstanceMetadata"),Z(A)},"remoteProvider"),CB2=!1,ZG4=rq1((A={})=>Lg.memoize(Lg.chain(async()=>{if(A.profile??process.env[QG4.ENV_PROFILE]){if(process.env[w60.ENV_KEY]&&process.env[w60.ENV_SECRET]){if(!CB2)(A.logger?.warn&&A.logger?.constructor?.name!=="NoOpLogger"?A.logger.warn:console.warn)(`@aws-sdk/credential-provider-node - defaultProvider::fromEnv WARNING:
    Multiple credential sources detected: 
    Both AWS_PROFILE and the pair AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY static credentials are set.
    This SDK will proceed with the AWS_PROFILE value.
    
    However, a future version may change this behavior to prefer the ENV static credentials.
    Please ensure that your environment only sets either the AWS_PROFILE or the
    AWS_ACCESS_KEY_ID/AWS_SECRET_ACCESS_KEY pair.
`),CB2=!0}throw new Lg.CredentialsProviderError("AWS_PROFILE is set, skipping fromEnv provider.",{logger:A.logger,tryNextLink:!0})}return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromEnv"),w60.fromEnv(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromSSO");let{ssoStartUrl:B,ssoAccountId:Q,ssoRegion:D,ssoRoleName:Z,ssoSession:G}=A;if(!B&&!Q&&!D&&!Z&&!G)throw new Lg.CredentialsProviderError("Skipping SSO provider in default chain (inputs do not include SSO fields).",{logger:A.logger});let{fromSSO:F}=await Promise.resolve().then(()=>ws(L40()));return F(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromIni");let{fromIni:B}=await Promise.resolve().then(()=>ws(XB2()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromProcess");let{fromProcess:B}=await Promise.resolve().then(()=>ws(V60()));return B(A)()},async()=>{A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::fromTokenFile");let{fromTokenFile:B}=await Promise.resolve().then(()=>ws(z60()));return B(A)()},async()=>{return A.logger?.debug("@aws-sdk/credential-provider-node - defaultProvider::remoteProvider"),(await DG4(A))()},async()=>{throw new Lg.CredentialsProviderError("Could not load credentials from any providers",{tryNextLink:!1,logger:A.logger})}),EB2,zB2),"defaultProvider"),zB2=rq1((A)=>A?.expiration!==void 0,"credentialsWillNeedRefresh"),EB2=rq1((A)=>A?.expiration!==void 0&&A.expiration.getTime()-Date.now()<300000,"credentialsTreatedAsExpired")});
var z02=E((K02)=>{Object.defineProperty(K02,"__esModule",{value:!0});K02.getRuntimeConfig=void 0;var b54=YI(),f54=VB(),h54=J6(),g54=BZ(),V02=dy(),C02=cB(),u54=V40(),m54=X02(),d54=(A)=>{return{apiVersion:"2019-06-10",base64Decoder:A?.base64Decoder??V02.fromBase64,base64Encoder:A?.base64Encoder??V02.toBase64,disableHostPrefix:A?.disableHostPrefix??!1,endpointProvider:A?.endpointProvider??m54.defaultEndpointResolver,extensions:A?.extensions??[],httpAuthSchemeProvider:A?.httpAuthSchemeProvider??u54.defaultSSOOIDCHttpAuthSchemeProvider,httpAuthSchemes:A?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:(B)=>B.getIdentityProvider("aws.auth#sigv4"),signer:new b54.AwsSdkSigV4Signer},{schemeId:"smithy.api#noAuth",identityProvider:(B)=>B.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new f54.NoAuthSigner}],logger:A?.logger??new h54.NoOpLogger,serviceId:A?.serviceId??"SSO OIDC",urlParser:A?.urlParser??g54.parseUrl,utf8Decoder:A?.utf8Decoder??C02.fromUtf8,utf8Encoder:A?.utf8Encoder??C02.toUtf8}};K02.getRuntimeConfig=d54});
var z60=E((RV5,sq1)=>{var{defineProperty:QB2,getOwnPropertyDescriptor:OZ4,getOwnPropertyNames:TZ4}=Object,PZ4=Object.prototype.hasOwnProperty,K60=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of TZ4(B))if(!PZ4.call(A,Z)&&Z!==Q)QB2(A,Z,{get:()=>B[Z],enumerable:!(D=OZ4(B,Z))||D.enumerable})}return A},DB2=(A,B,Q)=>(K60(A,B,"default"),Q&&K60(Q,B,"default")),SZ4=(A)=>K60(QB2({},"__esModule",{value:!0}),A),H60={};sq1.exports=SZ4(H60);DB2(H60,BB2(),sq1.exports);DB2(H60,C60(),sq1.exports)});
var z61=E((fX5,keA)=>{var{defineProperty:kq1,getOwnPropertyDescriptor:e64,getOwnPropertyNames:A84}=Object,B84=Object.prototype.hasOwnProperty,jq1=(A,B)=>kq1(A,"name",{value:B,configurable:!0}),Q84=(A,B)=>{for(var Q in B)kq1(A,Q,{get:B[Q],enumerable:!0})},D84=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of A84(B))if(!B84.call(A,Z)&&Z!==Q)kq1(A,Z,{get:()=>B[Z],enumerable:!(D=e64(B,Z))||D.enumerable})}return A},Z84=(A)=>D84(kq1({},"__esModule",{value:!0}),A),OeA={};Q84(OeA,{NODE_APP_ID_CONFIG_OPTIONS:()=>W84,UA_APP_ID_ENV_NAME:()=>SeA,UA_APP_ID_INI_NAME:()=>jeA,createDefaultUserAgentProvider:()=>PeA,crtAvailability:()=>TeA,defaultUserAgent:()=>F84});keA.exports=Z84(OeA);var ReA=J1("os"),G40=J1("process"),TeA={isCrtAvailable:!1},G84=jq1(()=>{if(TeA.isCrtAvailable)return["md/crt-avail"];return null},"isCrtAvailable"),PeA=jq1(({serviceId:A,clientVersion:B})=>{return async(Q)=>{let D=[["aws-sdk-js",B],["ua","2.1"],[`os/${ReA.platform()}`,ReA.release()],["lang/js"],["md/nodejs",`${G40.versions.node}`]],Z=G84();if(Z)D.push(Z);if(A)D.push([`api/${A}`,B]);if(G40.env.AWS_EXECUTION_ENV)D.push([`exec-env/${G40.env.AWS_EXECUTION_ENV}`]);let G=await Q?.userAgentAppId?.();return G?[...D,[`app/${G}`]]:[...D]}},"createDefaultUserAgentProvider"),F84=PeA,I84=Bs(),SeA="AWS_SDK_UA_APP_ID",jeA="sdk_ua_app_id",Y84="sdk-ua-app-id",W84={environmentVariableSelector:jq1((A)=>A[SeA],"environmentVariableSelector"),configFileSelector:jq1((A)=>A[jeA]??A[Y84],"configFileSelector"),default:I84.DEFAULT_UA_APP_ID}});
var zeA=E((KeA)=>{Object.defineProperty(KeA,"__esModule",{value:!0});KeA.retryWrapper=void 0;var j64=(A,B,Q)=>{return async()=>{for(let D=0;D<B;++D)try{return await A()}catch(Z){await new Promise((G)=>setTimeout(G,Q))}return await A()}};KeA.retryWrapper=j64});
var zoA=E((lJ5,HoA)=>{var{defineProperty:Vq1,getOwnPropertyDescriptor:W94,getOwnPropertyNames:J94}=Object,X94=Object.prototype.hasOwnProperty,V94=(A,B)=>Vq1(A,"name",{value:B,configurable:!0}),C94=(A,B)=>{for(var Q in B)Vq1(A,Q,{get:B[Q],enumerable:!0})},K94=(A,B,Q,D)=>{if(B&&typeof B==="object"||typeof B==="function"){for(let Z of J94(B))if(!X94.call(A,Z)&&Z!==Q)Vq1(A,Z,{get:()=>B[Z],enumerable:!(D=W94(B,Z))||D.enumerable})}return A},H94=(A)=>K94(Vq1({},"__esModule",{value:!0}),A),KoA={};C94(KoA,{isArrayBuffer:()=>z94});HoA.exports=H94(KoA);var z94=V94((A)=>typeof ArrayBuffer==="function"&&A instanceof ArrayBuffer||Object.prototype.toString.call(A)==="[object ArrayBuffer]","isArrayBuffer")});

module.exports = W62;
