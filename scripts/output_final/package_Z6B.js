// Package extracted with entry point: Z6B

var B6B=E((Vi5,A6B)=>{var HC0=process.platform==="win32";function zC0(A,B){return Object.assign(new Error(`${B} ${A.command} ENOENT`),{code:"ENOENT",errno:"ENOENT",syscall:`${B} ${A.command}`,path:A.command,spawnargs:A.args})}function JT6(A,B){if(!HC0)return;let Q=A.emit;A.emit=function(D,Z){if(D==="exit"){let G=e4B(Z,B);if(G)return Q.call(A,"error",G)}return Q.apply(A,arguments)}}function e4B(A,B){if(HC0&&A===1&&!B.file)return zC0(B.original,"spawn");return null}function XT6(A,B){if(HC0&&A===1&&!B.file)return zC0(B.original,"spawnSync");return null}A6B.exports={hookChildProcess:JT6,verifyENOENT:e4B,verifyENOENTSync:XT6,notFoundError:zC0}});
var O4B=E((Qi5,R4B)=>{R4B.exports=L4B;L4B.sync=uO6;var N4B=J1("fs");function L4B(A,B,Q){N4B.stat(A,function(D,Z){Q(D,D?!1:M4B(Z,B))})}function uO6(A,B){return M4B(N4B.statSync(A),B)}function M4B(A,B){return A.isFile()&&mO6(A,B)}function mO6(A,B){var{mode:Q,uid:D,gid:Z}=A,G=B.uid!==void 0?B.uid:process.getuid&&process.getuid(),F=B.gid!==void 0?B.gid:process.getgid&&process.getgid(),I=parseInt("100",8),Y=parseInt("010",8),W=parseInt("001",8),J=I|Y,X=Q&W||Q&Y&&Z===F||Q&I&&D===G||Q&J&&G===0;return X}});
var P4B=E((Zi5,T4B)=>{var Di5=J1("fs"),jj1;if(process.platform==="win32"||global.TESTING_WINDOWS)jj1=q4B();else jj1=O4B();T4B.exports=JC0;JC0.sync=dO6;function JC0(A,B,Q){if(typeof B==="function")Q=B,B={};if(!Q){if(typeof Promise!=="function")throw new TypeError("callback not provided");return new Promise(function(D,Z){JC0(A,B||{},function(G,F){if(G)Z(G);else D(F)})})}jj1(A,B||{},function(D,Z){if(D){if(D.code==="EACCES"||B&&B.ignoreErrors)D=null,Z=!1}Q(D,Z)})}function dO6(A,B){try{return jj1.sync(A,B||{})}catch(Q){if(B&&B.ignoreErrors||Q.code==="EACCES")return!1;else throw Q}}});
var Z6B=E((Ci5,st)=>{var Q6B=J1("child_process"),EC0=t4B(),UC0=B6B();function D6B(A,B,Q){let D=EC0(A,B,Q),Z=Q6B.spawn(D.command,D.args,D.options);return UC0.hookChildProcess(Z,D),Z}function VT6(A,B,Q){let D=EC0(A,B,Q),Z=Q6B.spawnSync(D.command,D.args,D.options);return Z.error=Z.error||UC0.verifyENOENTSync(Z.status,D),Z}st.exports=D6B;st.exports.spawn=D6B;st.exports.sync=VT6;st.exports._parse=EC0;st.exports._enoent=UC0});
var a4B=E((Ji5,n4B)=>{var KC0=J1("fs"),AT6=i4B();function BT6(A){let Q=Buffer.alloc(150),D;try{D=KC0.openSync(A,"r"),KC0.readSync(D,Q,0,150,0),KC0.closeSync(D)}catch(Z){}return AT6(Q.toString())}n4B.exports=BT6});
var d4B=E((rO6,CC0)=>{var VC0=/([()\][%!^"`<>&|;, *?])/g;function aO6(A){return A=A.replace(VC0,"^$1"),A}function sO6(A,B){if(A=`${A}`,A=A.replace(/(?=(\\+?)?)\1"/g,"$1$1\\\""),A=A.replace(/(?=(\\+?)?)\1$/,"$1$1"),A=`"${A}"`,A=A.replace(VC0,"^$1"),B)A=A.replace(VC0,"^$1");return A}rO6.command=aO6;rO6.argument=sO6});
var f4B=E((Fi5,XC0)=>{var b4B=(A={})=>{let B=A.env||process.env;if((A.platform||process.platform)!=="win32")return"PATH";return Object.keys(B).reverse().find((D)=>D.toUpperCase()==="PATH")||"Path"};XC0.exports=b4B;XC0.exports.default=b4B});
var i4B=E((Wi5,p4B)=>{var eO6=l4B();p4B.exports=(A="")=>{let B=A.match(eO6);if(!B)return null;let[Q,D]=B[0].replace(/#! ?/,"").split(" "),Z=Q.split("/").pop();if(Z==="env")return D;return D?`${Z} ${D}`:Z}});
var l4B=E((Yi5,c4B)=>{c4B.exports=/^#!(.*)/});
var m4B=E((Ii5,u4B)=>{var h4B=J1("path"),pO6=v4B(),iO6=f4B();function g4B(A,B){let Q=A.options.env||process.env,D=process.cwd(),Z=A.options.cwd!=null,G=Z&&process.chdir!==void 0&&!process.chdir.disabled;if(G)try{process.chdir(A.options.cwd)}catch(I){}let F;try{F=pO6.sync(A.command,{path:Q[iO6({env:Q})],pathExt:B?h4B.delimiter:void 0})}catch(I){}finally{if(G)process.chdir(D)}if(F)F=h4B.resolve(Z?A.options.cwd:"",F);return F}function nO6(A){return g4B(A)||g4B(A,!0)}u4B.exports=nO6});
var q4B=E((Bi5,$4B)=>{$4B.exports=w4B;w4B.sync=gO6;var E4B=J1("fs");function hO6(A,B){var Q=B.pathExt!==void 0?B.pathExt:process.env.PATHEXT;if(!Q)return!0;if(Q=Q.split(";"),Q.indexOf("")!==-1)return!0;for(var D=0;D<Q.length;D++){var Z=Q[D].toLowerCase();if(Z&&A.substr(-Z.length).toLowerCase()===Z)return!0}return!1}function U4B(A,B,Q){if(!A.isSymbolicLink()&&!A.isFile())return!1;return hO6(B,Q)}function w4B(A,B,Q){E4B.stat(A,function(D,Z){Q(D,D?!1:U4B(Z,A,B))})}function gO6(A,B){return U4B(E4B.statSync(A),A,B)}});
var t4B=E((Xi5,o4B)=>{var QT6=J1("path"),s4B=m4B(),r4B=d4B(),DT6=a4B(),ZT6=process.platform==="win32",GT6=/\.(?:com|exe)$/i,FT6=/node_modules[\\/].bin[\\/][^\\/]+\.cmd$/i;function IT6(A){A.file=s4B(A);let B=A.file&&DT6(A.file);if(B)return A.args.unshift(A.file),A.command=B,s4B(A);return A.file}function YT6(A){if(!ZT6)return A;let B=IT6(A),Q=!GT6.test(B);if(A.options.forceShell||Q){let D=FT6.test(B);A.command=QT6.normalize(A.command),A.command=r4B.command(A.command),A.args=A.args.map((G)=>r4B.argument(G,D));let Z=[A.command].concat(A.args).join(" ");A.args=["/d","/s","/c",`"${Z}"`],A.command=process.env.comspec||"cmd.exe",A.options.windowsVerbatimArguments=!0}return A}function WT6(A,B,Q){if(B&&!Array.isArray(B))Q=B,B=null;B=B?B.slice(0):[],Q=Object.assign({},Q);let D={command:A,args:B,options:Q,file:void 0,original:{command:A,args:B}};return Q.shell?D:YT6(D)}o4B.exports=WT6});
var v4B=E((Gi5,x4B)=>{var at=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys",S4B=J1("path"),cO6=at?";":":",j4B=P4B(),k4B=(A)=>Object.assign(new Error(`not found: ${A}`),{code:"ENOENT"}),y4B=(A,B)=>{let Q=B.colon||cO6,D=A.match(/\//)||at&&A.match(/\\/)?[""]:[...at?[process.cwd()]:[],...(B.path||process.env.PATH||"").split(Q)],Z=at?B.pathExt||process.env.PATHEXT||".EXE;.CMD;.BAT;.COM":"",G=at?Z.split(Q):[""];if(at){if(A.indexOf(".")!==-1&&G[0]!=="")G.unshift("")}return{pathEnv:D,pathExt:G,pathExtExe:Z}},_4B=(A,B,Q)=>{if(typeof B==="function")Q=B,B={};if(!B)B={};let{pathEnv:D,pathExt:Z,pathExtExe:G}=y4B(A,B),F=[],I=(W)=>new Promise((J,X)=>{if(W===D.length)return B.all&&F.length?J(F):X(k4B(A));let V=D[W],C=/^".*"$/.test(V)?V.slice(1,-1):V,K=S4B.join(C,A),H=!C&&/^\.[\\\/]/.test(A)?A.slice(0,2)+K:K;J(Y(H,W,0))}),Y=(W,J,X)=>new Promise((V,C)=>{if(X===Z.length)return V(I(J+1));let K=Z[X];j4B(W+K,{pathExt:G},(H,z)=>{if(!H&&z)if(B.all)F.push(W+K);else return V(W+K);return V(Y(W,J,X+1))})});return Q?I(0).then((W)=>Q(null,W),Q):I(0)},lO6=(A,B)=>{B=B||{};let{pathEnv:Q,pathExt:D,pathExtExe:Z}=y4B(A,B),G=[];for(let F=0;F<Q.length;F++){let I=Q[F],Y=/^".*"$/.test(I)?I.slice(1,-1):I,W=S4B.join(Y,A),J=!Y&&/^\.[\\\/]/.test(A)?A.slice(0,2)+W:W;for(let X=0;X<D.length;X++){let V=J+D[X];try{if(j4B.sync(V,{pathExt:Z}))if(B.all)G.push(V);else return V}catch(C){}}}if(B.all&&G.length)return G;if(B.nothrow)return null;throw k4B(A)};x4B.exports=_4B;_4B.sync=lO6});

module.exports = Z6B;
