// Package extracted with entry point: ZxB

var EL0=E((_z3,vkB)=>{var xkB=GW(),iJ8={nextElementSibling:{get:function(){if(this.parentNode){for(var A=this.nextSibling;A!==null;A=A.nextSibling)if(A.nodeType===xkB.ELEMENT_NODE)return A}return null}},previousElementSibling:{get:function(){if(this.parentNode){for(var A=this.previousSibling;A!==null;A=A.previousSibling)if(A.nodeType===xkB.ELEMENT_NODE)return A}return null}}};vkB.exports=iJ8});
var EyB=E((pz3,zyB)=>{zyB.exports=HyB;var KX8=GW(),FW=KF1(),XyB=bL0(),KyB=zZ(),fL0={first:"firstChild",last:"lastChild",next:"firstChild",previous:"lastChild"},hL0={first:"nextSibling",last:"previousSibling",next:"nextSibling",previous:"previousSibling"};function VyB(A,B){var Q,D,Z,G,F;D=A._currentNode[fL0[B]];while(D!==null){if(G=A._internalFilter(D),G===FW.FILTER_ACCEPT)return A._currentNode=D,D;if(G===FW.FILTER_SKIP){if(Q=D[fL0[B]],Q!==null){D=Q;continue}}while(D!==null){if(F=D[hL0[B]],F!==null){D=F;break}if(Z=D.parentNode,Z===null||Z===A.root||Z===A._currentNode)return null;else D=Z}}return null}function CyB(A,B){var Q,D,Z;if(Q=A._currentNode,Q===A.root)return null;while(!0){Z=Q[hL0[B]];while(Z!==null){if(Q=Z,D=A._internalFilter(Q),D===FW.FILTER_ACCEPT)return A._currentNode=Q,Q;if(Z=Q[fL0[B]],D===FW.FILTER_REJECT||Z===null)Z=Q[hL0[B]]}if(Q=Q.parentNode,Q===null||Q===A.root)return null;if(A._internalFilter(Q)===FW.FILTER_ACCEPT)return null}}function HyB(A,B,Q){if(!A||!A.nodeType)KyB.NotSupportedError();this._root=A,this._whatToShow=Number(B)||0,this._filter=Q||null,this._active=!1,this._currentNode=A}Object.defineProperties(HyB.prototype,{root:{get:function(){return this._root}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},currentNode:{get:function A(){return this._currentNode},set:function A(B){if(!(B instanceof KX8))throw new TypeError("Not a Node");this._currentNode=B}},_internalFilter:{value:function A(B){var Q,D;if(this._active)KyB.InvalidStateError();if(!(1<<B.nodeType-1&this._whatToShow))return FW.FILTER_SKIP;if(D=this._filter,D===null)Q=FW.FILTER_ACCEPT;else{this._active=!0;try{if(typeof D==="function")Q=D(B);else Q=D.acceptNode(B)}finally{this._active=!1}}return+Q}},parentNode:{value:function A(){var B=this._currentNode;while(B!==this.root){if(B=B.parentNode,B===null)return null;if(this._internalFilter(B)===FW.FILTER_ACCEPT)return this._currentNode=B,B}return null}},firstChild:{value:function A(){return VyB(this,"first")}},lastChild:{value:function A(){return VyB(this,"last")}},previousSibling:{value:function A(){return CyB(this,"previous")}},nextSibling:{value:function A(){return CyB(this,"next")}},previousNode:{value:function A(){var B,Q,D,Z;B=this._currentNode;while(B!==this._root){for(D=B.previousSibling;D;D=B.previousSibling){if(B=D,Q=this._internalFilter(B),Q===FW.FILTER_REJECT)continue;for(Z=B.lastChild;Z;Z=B.lastChild)if(B=Z,Q=this._internalFilter(B),Q===FW.FILTER_REJECT)break;if(Q===FW.FILTER_ACCEPT)return this._currentNode=B,B}if(B===this.root||B.parentNode===null)return null;if(B=B.parentNode,this._internalFilter(B)===FW.FILTER_ACCEPT)return this._currentNode=B,B}return null}},nextNode:{value:function A(){var B,Q,D,Z;B=this._currentNode,Q=FW.FILTER_ACCEPT;A:while(!0){for(D=B.firstChild;D;D=B.firstChild)if(B=D,Q=this._internalFilter(B),Q===FW.FILTER_ACCEPT)return this._currentNode=B,B;else if(Q===FW.FILTER_REJECT)break;for(Z=XyB.nextSkippingChildren(B,this.root);Z;Z=XyB.nextSkippingChildren(B,this.root))if(B=Z,Q=this._internalFilter(B),Q===FW.FILTER_ACCEPT)return this._currentNode=B,B;else if(Q===FW.FILTER_SKIP)continue A;return null}}},toString:{value:function A(){return"[object TreeWalker]"}}})});
var FM0=E((CE3,m_B)=>{var MV8=wF1(),RV8=rN0(),OV8=v_B(),qF1=zZ();m_B.exports=$f1;function $f1(A){this.document=A||new MV8(null).createHTMLDocument(""),this.document._scripting_enabled=!0,this.document.defaultView=this,this.location=new OV8(this,this.document._address||"about:blank")}$f1.prototype=Object.create(RV8.prototype,{console:{value:console},history:{value:{back:qF1.nyi,forward:qF1.nyi,go:qF1.nyi}},navigator:{value:f_B()},window:{get:function(){return this}},self:{get:function(){return this}},frames:{get:function(){return this}},parent:{get:function(){return this}},top:{get:function(){return this}},length:{value:0},frameElement:{value:null},opener:{value:null},onload:{get:function(){return this._getEventHandler("load")},set:function(A){this._setEventHandler("load",A)}},getComputedStyle:{value:function A(B){return B.style}}});qF1.expose(g_B(),$f1);qF1.expose(GM0(),$f1)});
var GM0=E(($F1,u_B)=>{var ZM0=zZ();$F1=u_B.exports={CSSStyleDeclaration:Gf1(),CharacterData:JF1(),Comment:SL0(),DOMException:cb1(),DOMImplementation:wF1(),DOMTokenList:VL0(),Document:Wf1(),DocumentFragment:kL0(),DocumentType:Xf1(),Element:y01(),HTMLParser:Uf1(),NamedNodeMap:UL0(),Node:GW(),NodeList:Vd(),NodeFilter:KF1(),ProcessingInstruction:_L0(),Text:TL0(),Window:FM0()};ZM0.merge($F1,dL0());ZM0.merge($F1,If1().elements);ZM0.merge($F1,aL0().elements)});
var GW=E((Lz3,YkB)=>{YkB.exports=ND;var IkB=rN0(),pb1=oN0(),DkB=tN0(),z5=zZ();function ND(){IkB.call(this),this.parentNode=null,this._nextSibling=this._previousSibling=this,this._index=void 0}var ZC=ND.ELEMENT_NODE=1,eN0=ND.ATTRIBUTE_NODE=2,ib1=ND.TEXT_NODE=3,CJ8=ND.CDATA_SECTION_NODE=4,KJ8=ND.ENTITY_REFERENCE_NODE=5,AL0=ND.ENTITY_NODE=6,ZkB=ND.PROCESSING_INSTRUCTION_NODE=7,GkB=ND.COMMENT_NODE=8,DF1=ND.DOCUMENT_NODE=9,YU=ND.DOCUMENT_TYPE_NODE=10,gv=ND.DOCUMENT_FRAGMENT_NODE=11,BL0=ND.NOTATION_NODE=12,QL0=ND.DOCUMENT_POSITION_DISCONNECTED=1,DL0=ND.DOCUMENT_POSITION_PRECEDING=2,ZL0=ND.DOCUMENT_POSITION_FOLLOWING=4,FkB=ND.DOCUMENT_POSITION_CONTAINS=8,GL0=ND.DOCUMENT_POSITION_CONTAINED_BY=16,FL0=ND.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;ND.prototype=Object.create(IkB.prototype,{baseURI:{get:z5.nyi},parentElement:{get:function(){return this.parentNode&&this.parentNode.nodeType===ZC?this.parentNode:null}},hasChildNodes:{value:z5.shouldOverride},firstChild:{get:z5.shouldOverride},lastChild:{get:z5.shouldOverride},isConnected:{get:function(){let A=this;while(A!=null){if(A.nodeType===ND.DOCUMENT_NODE)return!0;if(A=A.parentNode,A!=null&&A.nodeType===ND.DOCUMENT_FRAGMENT_NODE)A=A.host}return!1}},previousSibling:{get:function(){var A=this.parentNode;if(!A)return null;if(this===A.firstChild)return null;return this._previousSibling}},nextSibling:{get:function(){var A=this.parentNode,B=this._nextSibling;if(!A)return null;if(B===A.firstChild)return null;return B}},textContent:{get:function(){return null},set:function(A){}},innerText:{get:function(){return null},set:function(A){}},_countChildrenOfType:{value:function(A){var B=0;for(var Q=this.firstChild;Q!==null;Q=Q.nextSibling)if(Q.nodeType===A)B++;return B}},_ensureInsertValid:{value:function A(B,Q,D){var Z=this,G,F;if(!B.nodeType)throw new TypeError("not a node");switch(Z.nodeType){case DF1:case gv:case ZC:break;default:z5.HierarchyRequestError()}if(B.isAncestor(Z))z5.HierarchyRequestError();if(Q!==null||!D){if(Q.parentNode!==Z)z5.NotFoundError()}switch(B.nodeType){case gv:case YU:case ZC:case ib1:case ZkB:case GkB:break;default:z5.HierarchyRequestError()}if(Z.nodeType===DF1)switch(B.nodeType){case ib1:z5.HierarchyRequestError();break;case gv:if(B._countChildrenOfType(ib1)>0)z5.HierarchyRequestError();switch(B._countChildrenOfType(ZC)){case 0:break;case 1:if(Q!==null){if(D&&Q.nodeType===YU)z5.HierarchyRequestError();for(F=Q.nextSibling;F!==null;F=F.nextSibling)if(F.nodeType===YU)z5.HierarchyRequestError()}if(G=Z._countChildrenOfType(ZC),D){if(G>0)z5.HierarchyRequestError()}else if(G>1||G===1&&Q.nodeType!==ZC)z5.HierarchyRequestError();break;default:z5.HierarchyRequestError()}break;case ZC:if(Q!==null){if(D&&Q.nodeType===YU)z5.HierarchyRequestError();for(F=Q.nextSibling;F!==null;F=F.nextSibling)if(F.nodeType===YU)z5.HierarchyRequestError()}if(G=Z._countChildrenOfType(ZC),D){if(G>0)z5.HierarchyRequestError()}else if(G>1||G===1&&Q.nodeType!==ZC)z5.HierarchyRequestError();break;case YU:if(Q===null){if(Z._countChildrenOfType(ZC))z5.HierarchyRequestError()}else for(F=Z.firstChild;F!==null;F=F.nextSibling){if(F===Q)break;if(F.nodeType===ZC)z5.HierarchyRequestError()}if(G=Z._countChildrenOfType(YU),D){if(G>0)z5.HierarchyRequestError()}else if(G>1||G===1&&Q.nodeType!==YU)z5.HierarchyRequestError();break}else if(B.nodeType===YU)z5.HierarchyRequestError()}},insertBefore:{value:function A(B,Q){var D=this;D._ensureInsertValid(B,Q,!0);var Z=Q;if(Z===B)Z=B.nextSibling;return D.doc.adoptNode(B),B._insertOrReplace(D,Z,!1),B}},appendChild:{value:function(A){return this.insertBefore(A,null)}},_appendChild:{value:function(A){A._insertOrReplace(this,null,!1)}},removeChild:{value:function A(B){var Q=this;if(!B.nodeType)throw new TypeError("not a node");if(B.parentNode!==Q)z5.NotFoundError();return B.remove(),B}},replaceChild:{value:function A(B,Q){var D=this;if(D._ensureInsertValid(B,Q,!1),B.doc!==D.doc)D.doc.adoptNode(B);return B._insertOrReplace(D,Q,!0),Q}},contains:{value:function A(B){if(B===null)return!1;if(this===B)return!0;return(this.compareDocumentPosition(B)&GL0)!==0}},compareDocumentPosition:{value:function A(B){if(this===B)return 0;if(this.doc!==B.doc||this.rooted!==B.rooted)return QL0+FL0;var Q=[],D=[];for(var Z=this;Z!==null;Z=Z.parentNode)Q.push(Z);for(Z=B;Z!==null;Z=Z.parentNode)D.push(Z);if(Q.reverse(),D.reverse(),Q[0]!==D[0])return QL0+FL0;Z=Math.min(Q.length,D.length);for(var G=1;G<Z;G++)if(Q[G]!==D[G])if(Q[G].index<D[G].index)return ZL0;else return DL0;if(Q.length<D.length)return ZL0+GL0;else return DL0+FkB}},isSameNode:{value:function A(B){return this===B}},isEqualNode:{value:function A(B){if(!B)return!1;if(B.nodeType!==this.nodeType)return!1;if(!this.isEqual(B))return!1;for(var Q=this.firstChild,D=B.firstChild;Q&&D;Q=Q.nextSibling,D=D.nextSibling)if(!Q.isEqualNode(D))return!1;return Q===null&&D===null}},cloneNode:{value:function(A){var B=this.clone();if(A)for(var Q=this.firstChild;Q!==null;Q=Q.nextSibling)B._appendChild(Q.cloneNode(!0));return B}},lookupPrefix:{value:function A(B){var Q;if(B===""||B===null||B===void 0)return null;switch(this.nodeType){case ZC:return this._lookupNamespacePrefix(B,this);case DF1:return Q=this.documentElement,Q?Q.lookupPrefix(B):null;case AL0:case BL0:case gv:case YU:return null;case eN0:return Q=this.ownerElement,Q?Q.lookupPrefix(B):null;default:return Q=this.parentElement,Q?Q.lookupPrefix(B):null}}},lookupNamespaceURI:{value:function A(B){if(B===""||B===void 0)B=null;var Q;switch(this.nodeType){case ZC:return z5.shouldOverride();case DF1:return Q=this.documentElement,Q?Q.lookupNamespaceURI(B):null;case AL0:case BL0:case YU:case gv:return null;case eN0:return Q=this.ownerElement,Q?Q.lookupNamespaceURI(B):null;default:return Q=this.parentElement,Q?Q.lookupNamespaceURI(B):null}}},isDefaultNamespace:{value:function A(B){if(B===""||B===void 0)B=null;var Q=this.lookupNamespaceURI(null);return Q===B}},index:{get:function(){var A=this.parentNode;if(this===A.firstChild)return 0;var B=A.childNodes;if(this._index===void 0||B[this._index]!==this){for(var Q=0;Q<B.length;Q++)B[Q]._index=Q;z5.assert(B[this._index]===this)}return this._index}},isAncestor:{value:function(A){if(this.doc!==A.doc)return!1;if(this.rooted!==A.rooted)return!1;for(var B=A;B;B=B.parentNode)if(B===this)return!0;return!1}},ensureSameDoc:{value:function(A){if(A.ownerDocument===null)A.ownerDocument=this.doc;else if(A.ownerDocument!==this.doc)z5.WrongDocumentError()}},removeChildren:{value:z5.shouldOverride},_insertOrReplace:{value:function A(B,Q,D){var Z=this,G,F;if(Z.nodeType===gv&&Z.rooted)z5.HierarchyRequestError();if(B._childNodes){if(G=Q===null?B._childNodes.length:Q.index,Z.parentNode===B){var I=Z.index;if(I<G)G--}}if(D){if(Q.rooted)Q.doc.mutateRemove(Q);Q.parentNode=null}var Y=Q;if(Y===null)Y=B.firstChild;var W=Z.rooted&&B.rooted;if(Z.nodeType===gv){var J=[0,D?1:0],X;for(var V=Z.firstChild;V!==null;V=X)X=V.nextSibling,J.push(V),V.parentNode=B;var C=J.length;if(D)pb1.replace(Y,C>2?J[2]:null);else if(C>2&&Y!==null)pb1.insertBefore(J[2],Y);if(B._childNodes){J[0]=Q===null?B._childNodes.length:Q._index,B._childNodes.splice.apply(B._childNodes,J);for(F=2;F<C;F++)J[F]._index=J[0]+(F-2)}else if(B._firstChild===Q){if(C>2)B._firstChild=J[2];else if(D)B._firstChild=null}if(Z._childNodes)Z._childNodes.length=0;else Z._firstChild=null;if(B.rooted){B.modify();for(F=2;F<C;F++)B.doc.mutateInsert(J[F])}}else{if(Q===Z)return;if(W)Z._remove();else if(Z.parentNode)Z.remove();if(Z.parentNode=B,D){if(pb1.replace(Y,Z),B._childNodes)Z._index=G,B._childNodes[G]=Z;else if(B._firstChild===Q)B._firstChild=Z}else{if(Y!==null)pb1.insertBefore(Z,Y);if(B._childNodes)Z._index=G,B._childNodes.splice(G,0,Z);else if(B._firstChild===Q)B._firstChild=Z}if(W)B.modify(),B.doc.mutateMove(Z);else if(B.rooted)B.modify(),B.doc.mutateInsert(Z)}}},lastModTime:{get:function(){if(!this._lastModTime)this._lastModTime=this.doc.modclock;return this._lastModTime}},modify:{value:function(){if(this.doc.modclock){var A=++this.doc.modclock;for(var B=this;B;B=B.parentElement)if(B._lastModTime)B._lastModTime=A}}},doc:{get:function(){return this.ownerDocument||this}},rooted:{get:function(){return!!this._nid}},normalize:{value:function(){var A;for(var B=this.firstChild;B!==null;B=A){if(A=B.nextSibling,B.normalize)B.normalize();if(B.nodeType!==ND.TEXT_NODE)continue;if(B.nodeValue===""){this.removeChild(B);continue}var Q=B.previousSibling;if(Q===null)continue;else if(Q.nodeType===ND.TEXT_NODE)Q.appendData(B.nodeValue),this.removeChild(B)}}},serialize:{value:function(){if(this._innerHTML)return this._innerHTML;var A="";for(var B=this.firstChild;B!==null;B=B.nextSibling)A+=DkB.serializeOne(B,this);return A}},outerHTML:{get:function(){return DkB.serializeOne(this,{nodeType:0})},set:z5.nyi},ELEMENT_NODE:{value:ZC},ATTRIBUTE_NODE:{value:eN0},TEXT_NODE:{value:ib1},CDATA_SECTION_NODE:{value:CJ8},ENTITY_REFERENCE_NODE:{value:KJ8},ENTITY_NODE:{value:AL0},PROCESSING_INSTRUCTION_NODE:{value:ZkB},COMMENT_NODE:{value:GkB},DOCUMENT_NODE:{value:DF1},DOCUMENT_TYPE_NODE:{value:YU},DOCUMENT_FRAGMENT_NODE:{value:gv},NOTATION_NODE:{value:BL0},DOCUMENT_POSITION_DISCONNECTED:{value:QL0},DOCUMENT_POSITION_PRECEDING:{value:DL0},DOCUMENT_POSITION_FOLLOWING:{value:ZL0},DOCUMENT_POSITION_CONTAINS:{value:FkB},DOCUMENT_POSITION_CONTAINED_BY:{value:GL0},DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:{value:FL0}})});
var Gf1=E((oz3,fyB)=>{var{parse:UX8}=yyB();fyB.exports=function(A){let B=new byB(A);return new Proxy(B,{get:function(D,Z){return Z in D?D[Z]:D.getPropertyValue(_yB(Z))},has:function(D,Z){return!0},set:function(D,Z,G){if(Z in D)D[Z]=G;else D.setProperty(_yB(Z),G??void 0);return!0}})};function _yB(A){return A.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function byB(A){this._element=A}var xyB="!important";function vyB(A){let B={property:{},priority:{}};if(!A)return B;let Q=UX8(A);if(Q.length<2)return B;for(let D=0;D<Q.length;D+=2){let Z=Q[D],G=Q[D+1];if(G.endsWith(xyB))B.priority[Z]="important",G=G.slice(0,-xyB.length).trim();B.property[Z]=G}return B}var _01={};byB.prototype=Object.create(Object.prototype,{_parsed:{get:function(){if(!this._parsedStyles||this.cssText!==this._lastParsedText){var A=this.cssText;this._parsedStyles=vyB(A),this._lastParsedText=A,delete this._names}return this._parsedStyles}},_serialize:{value:function(){var A=this._parsed,B="";for(var Q in A.property){if(B)B+=" ";if(B+=Q+": "+A.property[Q],A.priority[Q])B+=" !"+A.priority[Q];B+=";"}this.cssText=B,this._lastParsedText=B,delete this._names}},cssText:{get:function(){return this._element.getAttribute("style")},set:function(A){this._element.setAttribute("style",A)}},length:{get:function(){if(!this._names)this._names=Object.getOwnPropertyNames(this._parsed.property);return this._names.length}},item:{value:function(A){if(!this._names)this._names=Object.getOwnPropertyNames(this._parsed.property);return this._names[A]}},getPropertyValue:{value:function(A){return A=A.toLowerCase(),this._parsed.property[A]||""}},getPropertyPriority:{value:function(A){return A=A.toLowerCase(),this._parsed.priority[A]||""}},setProperty:{value:function(A,B,Q){if(A=A.toLowerCase(),B===null||B===void 0)B="";if(Q===null||Q===void 0)Q="";if(B!==_01)B=""+B;if(B=B.trim(),B===""){this.removeProperty(A);return}if(Q!==""&&Q!==_01&&!/^important$/i.test(Q))return;var D=this._parsed;if(B===_01){if(!D.property[A])return;if(Q!=="")D.priority[A]="important";else delete D.priority[A]}else{if(B.indexOf(";")!==-1)return;var Z=vyB(A+":"+B);if(Object.getOwnPropertyNames(Z.property).length===0)return;if(Object.getOwnPropertyNames(Z.priority).length!==0)return;for(var G in Z.property)if(D.property[G]=Z.property[G],Q===_01)continue;else if(Q!=="")D.priority[G]="important";else if(D.priority[G])delete D.priority[G]}this._serialize()}},setPropertyValue:{value:function(A,B){return this.setProperty(A,B,_01)}},setPropertyPriority:{value:function(A,B){return this.setProperty(A,_01,B)}},removeProperty:{value:function(A){A=A.toLowerCase();var B=this._parsed;if(A in B.property)delete B.property[A],delete B.priority[A],this._serialize()}}})});
var If1=E((OX8)=>{var pL0=GW(),dyB=y01(),NX8=Gf1(),lK=zZ(),cyB=cL0(),LX8=lL0(),KS=OX8.elements={},zF1=Object.create(null);OX8.createElement=function(A,B,Q){var D=zF1[B]||RX8;return new D(A,B,Q)};function AB(A){return LX8(A,L9,KS,zF1)}function EZ(A){return{get:function(){var B=this._getattr(A);if(B===null)return"";var Q=this.doc._resolve(B);return Q===null?B:Q},set:function(B){this._setattr(A,B)}}}function Ff1(A){return{get:function(){var B=this._getattr(A);if(B===null)return null;if(B.toLowerCase()==="use-credentials")return"use-credentials";return"anonymous"},set:function(B){if(B===null||B===void 0)this.removeAttribute(A);else this._setattr(A,B)}}}var x01={type:["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"],missing:""},MX8={A:!0,LINK:!0,BUTTON:!0,INPUT:!0,SELECT:!0,TEXTAREA:!0,COMMAND:!0},b$=function(A,B,Q){L9.call(this,A,B,Q),this._form=null},L9=OX8.HTMLElement=AB({superclass:dyB,name:"HTMLElement",ctor:function A(B,Q,D){dyB.call(this,B,Q,lK.NAMESPACE.HTML,D)},props:{dangerouslySetInnerHTML:{set:function(A){this._innerHTML=A}},innerHTML:{get:function(){return this.serialize()},set:function(A){var B=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,this);B.parse(A===null?"":String(A),!0);var Q=this instanceof zF1.template?this.content:this;while(Q.hasChildNodes())Q.removeChild(Q.firstChild);Q.appendChild(B._asDocumentFragment())}},style:{get:function(){if(!this._style)this._style=new NX8(this);return this._style},set:function(A){if(A===null||A===void 0)A="";this._setattr("style",String(A))}},blur:{value:function(){}},focus:{value:function(){}},forceSpellCheck:{value:function(){}},click:{value:function(){if(this._click_in_progress)return;this._click_in_progress=!0;try{if(this._pre_click_activation_steps)this._pre_click_activation_steps();var A=this.ownerDocument.createEvent("MouseEvent");A.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,0,0,0,0,!1,!1,!1,!1,0,null);var B=this.dispatchEvent(A);if(B){if(this._post_click_activation_steps)this._post_click_activation_steps(A)}else if(this._cancelled_activation_steps)this._cancelled_activation_steps()}finally{this._click_in_progress=!1}}},submit:{value:lK.nyi}},attributes:{title:String,lang:String,dir:{type:["ltr","rtl","auto"],missing:""},draggable:{type:["true","false"],treatNullAsEmptyString:!0},spellcheck:{type:["true","false"],missing:""},enterKeyHint:{type:["enter","done","go","next","previous","search","send"],missing:""},autoCapitalize:{type:["off","on","none","sentences","words","characters"],missing:""},autoFocus:Boolean,accessKey:String,nonce:String,hidden:Boolean,translate:{type:["no","yes"],missing:""},tabIndex:{type:"long",default:function(){if(this.tagName in MX8||this.contentEditable)return 0;else return-1}}},events:["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"]}),RX8=AB({name:"HTMLUnknownElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)}}),f$={form:{get:function(){return this._form}}};AB({tag:"a",name:"HTMLAnchorElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{_post_click_activation_steps:{value:function(A){if(this.href)this.ownerDocument.defaultView.location=this.href}}},attributes:{href:EZ,ping:String,download:String,target:String,rel:String,media:String,hreflang:String,type:String,referrerPolicy:x01,coords:String,charset:String,name:String,rev:String,shape:String}});cyB._inherit(zF1.a.prototype);AB({tag:"area",name:"HTMLAreaElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{alt:String,target:String,download:String,rel:String,media:String,href:EZ,hreflang:String,type:String,shape:String,coords:String,ping:String,referrerPolicy:x01,noHref:Boolean}});cyB._inherit(zF1.area.prototype);AB({tag:"br",name:"HTMLBRElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{clear:String}});AB({tag:"base",name:"HTMLBaseElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{target:String}});AB({tag:"body",name:"HTMLBodyElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},events:["afterprint","beforeprint","beforeunload","blur","error","focus","hashchange","load","message","offline","online","pagehide","pageshow","popstate","resize","scroll","storage","unload"],attributes:{text:{type:String,treatNullAsEmptyString:!0},link:{type:String,treatNullAsEmptyString:!0},vLink:{type:String,treatNullAsEmptyString:!0},aLink:{type:String,treatNullAsEmptyString:!0},bgColor:{type:String,treatNullAsEmptyString:!0},background:String}});AB({tag:"button",name:"HTMLButtonElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$,attributes:{name:String,value:String,disabled:Boolean,autofocus:Boolean,type:{type:["submit","reset","button","menu"],missing:"submit"},formTarget:String,formAction:EZ,formNoValidate:Boolean,formMethod:{type:["get","post","dialog"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""}}});AB({tag:"dl",name:"HTMLDListElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{compact:Boolean}});AB({tag:"data",name:"HTMLDataElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{value:String}});AB({tag:"datalist",name:"HTMLDataListElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)}});AB({tag:"details",name:"HTMLDetailsElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{open:Boolean}});AB({tag:"div",name:"HTMLDivElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{align:String}});AB({tag:"embed",name:"HTMLEmbedElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{src:EZ,type:String,width:String,height:String,align:String,name:String}});AB({tag:"fieldset",name:"HTMLFieldSetElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$,attributes:{disabled:Boolean,name:String}});AB({tag:"form",name:"HTMLFormElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{action:String,autocomplete:{type:["on","off"],missing:"on"},name:String,acceptCharset:{name:"accept-charset"},target:String,noValidate:Boolean,method:{type:["get","post","dialog"],invalid:"get",missing:"get"},enctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"},encoding:{name:"enctype",type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"}}});AB({tag:"hr",name:"HTMLHRElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{align:String,color:String,noShade:Boolean,size:String,width:String}});AB({tag:"head",name:"HTMLHeadElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)}});AB({tags:["h1","h2","h3","h4","h5","h6"],name:"HTMLHeadingElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{align:String}});AB({tag:"html",name:"HTMLHtmlElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{xmlns:EZ,version:String}});AB({tag:"iframe",name:"HTMLIFrameElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{src:EZ,srcdoc:String,name:String,width:String,height:String,seamless:Boolean,allow:Boolean,allowFullscreen:Boolean,allowUserMedia:Boolean,allowPaymentRequest:Boolean,referrerPolicy:x01,loading:{type:["eager","lazy"],treatNullAsEmptyString:!0},align:String,scrolling:String,frameBorder:String,longDesc:EZ,marginHeight:{type:String,treatNullAsEmptyString:!0},marginWidth:{type:String,treatNullAsEmptyString:!0}}});AB({tag:"img",name:"HTMLImageElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{alt:String,src:EZ,srcset:String,crossOrigin:Ff1,useMap:String,isMap:Boolean,sizes:String,height:{type:"unsigned long",default:0},width:{type:"unsigned long",default:0},referrerPolicy:x01,loading:{type:["eager","lazy"],missing:""},name:String,lowsrc:EZ,align:String,hspace:{type:"unsigned long",default:0},vspace:{type:"unsigned long",default:0},longDesc:EZ,border:{type:String,treatNullAsEmptyString:!0}}});AB({tag:"input",name:"HTMLInputElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:{form:f$.form,_post_click_activation_steps:{value:function(A){if(this.type==="checkbox")this.checked=!this.checked;else if(this.type==="radio"){var B=this.form.getElementsByName(this.name);for(var Q=B.length-1;Q>=0;Q--){var D=B[Q];D.checked=D===this}}}}},attributes:{name:String,disabled:Boolean,autofocus:Boolean,accept:String,alt:String,max:String,min:String,pattern:String,placeholder:String,step:String,dirName:String,defaultValue:{name:"value"},multiple:Boolean,required:Boolean,readOnly:Boolean,checked:Boolean,value:String,src:EZ,defaultChecked:{name:"checked",type:Boolean},size:{type:"unsigned long",default:20,min:1,setmin:1},width:{type:"unsigned long",min:0,setmin:0,default:0},height:{type:"unsigned long",min:0,setmin:0,default:0},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},autocomplete:String,type:{type:["text","hidden","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"],missing:"text"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""},align:String,useMap:String}});AB({tag:"keygen",name:"HTMLKeygenElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$,attributes:{name:String,disabled:Boolean,autofocus:Boolean,challenge:String,keytype:{type:["rsa"],missing:""}}});AB({tag:"li",name:"HTMLLIElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{value:{type:"long",default:0},type:String}});AB({tag:"label",name:"HTMLLabelElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$,attributes:{htmlFor:{name:"for",type:String}}});AB({tag:"legend",name:"HTMLLegendElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{align:String}});AB({tag:"link",name:"HTMLLinkElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{href:EZ,rel:String,media:String,hreflang:String,type:String,crossOrigin:Ff1,nonce:String,integrity:String,referrerPolicy:x01,imageSizes:String,imageSrcset:String,charset:String,rev:String,target:String}});AB({tag:"map",name:"HTMLMapElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{name:String}});AB({tag:"menu",name:"HTMLMenuElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{type:{type:["context","popup","toolbar"],missing:"toolbar"},label:String,compact:Boolean}});AB({tag:"meta",name:"HTMLMetaElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{name:String,content:String,httpEquiv:{name:"http-equiv",type:String},scheme:String}});AB({tag:"meter",name:"HTMLMeterElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$});AB({tags:["ins","del"],name:"HTMLModElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{cite:EZ,dateTime:String}});AB({tag:"ol",name:"HTMLOListElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{_numitems:{get:function(){var A=0;return this.childNodes.forEach(function(B){if(B.nodeType===pL0.ELEMENT_NODE&&B.tagName==="LI")A++}),A}}},attributes:{type:String,reversed:Boolean,start:{type:"long",default:function(){if(this.reversed)return this._numitems;else return 1}},compact:Boolean}});AB({tag:"object",name:"HTMLObjectElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$,attributes:{data:EZ,type:String,name:String,useMap:String,typeMustMatch:Boolean,width:String,height:String,align:String,archive:String,code:String,declare:Boolean,hspace:{type:"unsigned long",default:0},standby:String,vspace:{type:"unsigned long",default:0},codeBase:EZ,codeType:String,border:{type:String,treatNullAsEmptyString:!0}}});AB({tag:"optgroup",name:"HTMLOptGroupElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{disabled:Boolean,label:String}});AB({tag:"option",name:"HTMLOptionElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{form:{get:function(){var A=this.parentNode;while(A&&A.nodeType===pL0.ELEMENT_NODE){if(A.localName==="select")return A.form;A=A.parentNode}}},value:{get:function(){return this._getattr("value")||this.text},set:function(A){this._setattr("value",A)}},text:{get:function(){return this.textContent.replace(/[ \t\n\f\r]+/g," ").trim()},set:function(A){this.textContent=A}}},attributes:{disabled:Boolean,defaultSelected:{name:"selected",type:Boolean},label:String}});AB({tag:"output",name:"HTMLOutputElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$,attributes:{name:String}});AB({tag:"p",name:"HTMLParagraphElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{align:String}});AB({tag:"param",name:"HTMLParamElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{name:String,value:String,type:String,valueType:String}});AB({tags:["pre","listing","xmp"],name:"HTMLPreElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{width:{type:"long",default:0}}});AB({tag:"progress",name:"HTMLProgressElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:f$,attributes:{max:{type:Number,float:!0,default:1,min:0}}});AB({tags:["q","blockquote"],name:"HTMLQuoteElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{cite:EZ}});AB({tag:"script",name:"HTMLScriptElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{text:{get:function(){var A="";for(var B=0,Q=this.childNodes.length;B<Q;B++){var D=this.childNodes[B];if(D.nodeType===pL0.TEXT_NODE)A+=D._data}return A},set:function(A){if(this.removeChildren(),A!==null&&A!=="")this.appendChild(this.ownerDocument.createTextNode(A))}}},attributes:{src:EZ,type:String,charset:String,referrerPolicy:x01,defer:Boolean,async:Boolean,nomodule:Boolean,crossOrigin:Ff1,nonce:String,integrity:String}});AB({tag:"select",name:"HTMLSelectElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:{form:f$.form,options:{get:function(){return this.getElementsByTagName("option")}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,multiple:Boolean,required:Boolean,size:{type:"unsigned long",default:0}}});AB({tag:"span",name:"HTMLSpanElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)}});AB({tag:"style",name:"HTMLStyleElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{media:String,type:String,scoped:Boolean}});AB({tag:"caption",name:"HTMLTableCaptionElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{align:String}});AB({name:"HTMLTableCellElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{colSpan:{type:"unsigned long",default:1},rowSpan:{type:"unsigned long",default:1},scope:{type:["row","col","rowgroup","colgroup"],missing:""},abbr:String,align:String,axis:String,height:String,width:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},noWrap:Boolean,vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}});AB({tags:["col","colgroup"],name:"HTMLTableColElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{span:{type:"limited unsigned long with fallback",default:1,min:1},align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,width:String}});AB({tag:"table",name:"HTMLTableElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,border:String,frame:String,rules:String,summary:String,width:String,bgColor:{type:String,treatNullAsEmptyString:!0},cellPadding:{type:String,treatNullAsEmptyString:!0},cellSpacing:{type:String,treatNullAsEmptyString:!0}}});AB({tag:"template",name:"HTMLTemplateElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D),this._contentFragment=B._templateDoc.createDocumentFragment()},props:{content:{get:function(){return this._contentFragment}},serialize:{value:function(){return this.content.serialize()}}}});AB({tag:"tr",name:"HTMLTableRowElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{cells:{get:function(){return this.querySelectorAll("td,th")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}});AB({tags:["thead","tfoot","tbody"],name:"HTMLTableSectionElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String}});AB({tag:"textarea",name:"HTMLTextAreaElement",ctor:function A(B,Q,D){b$.call(this,B,Q,D)},props:{form:f$.form,type:{get:function(){return"textarea"}},defaultValue:{get:function(){return this.textContent},set:function(A){this.textContent=A}},value:{get:function(){return this.defaultValue},set:function(A){this.defaultValue=A}},textLength:{get:function(){return this.value.length}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,placeholder:String,wrap:String,dirName:String,required:Boolean,readOnly:Boolean,rows:{type:"limited unsigned long with fallback",default:2},cols:{type:"limited unsigned long with fallback",default:20},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""}}});AB({tag:"time",name:"HTMLTimeElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{dateTime:String,pubDate:Boolean}});AB({tag:"title",name:"HTMLTitleElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{text:{get:function(){return this.textContent}}}});AB({tag:"ul",name:"HTMLUListElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{type:String,compact:Boolean}});AB({name:"HTMLMediaElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{src:EZ,crossOrigin:Ff1,preload:{type:["metadata","none","auto",{value:"",alias:"auto"}],missing:"auto"},loop:Boolean,autoplay:Boolean,mediaGroup:String,controls:Boolean,defaultMuted:{name:"muted",type:Boolean}}});AB({name:"HTMLAudioElement",tag:"audio",superclass:KS.HTMLMediaElement,ctor:function A(B,Q,D){KS.HTMLMediaElement.call(this,B,Q,D)}});AB({name:"HTMLVideoElement",tag:"video",superclass:KS.HTMLMediaElement,ctor:function A(B,Q,D){KS.HTMLMediaElement.call(this,B,Q,D)},attributes:{poster:EZ,width:{type:"unsigned long",min:0,default:0},height:{type:"unsigned long",min:0,default:0}}});AB({tag:"td",name:"HTMLTableDataCellElement",superclass:KS.HTMLTableCellElement,ctor:function A(B,Q,D){KS.HTMLTableCellElement.call(this,B,Q,D)}});AB({tag:"th",name:"HTMLTableHeaderCellElement",superclass:KS.HTMLTableCellElement,ctor:function A(B,Q,D){KS.HTMLTableCellElement.call(this,B,Q,D)}});AB({tag:"frameset",name:"HTMLFrameSetElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)}});AB({tag:"frame",name:"HTMLFrameElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)}});AB({tag:"canvas",name:"HTMLCanvasElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{getContext:{value:lK.nyi},probablySupportsContext:{value:lK.nyi},setContext:{value:lK.nyi},transferControlToProxy:{value:lK.nyi},toDataURL:{value:lK.nyi},toBlob:{value:lK.nyi}},attributes:{width:{type:"unsigned long",default:300},height:{type:"unsigned long",default:150}}});AB({tag:"dialog",name:"HTMLDialogElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{show:{value:lK.nyi},showModal:{value:lK.nyi},close:{value:lK.nyi}},attributes:{open:Boolean,returnValue:String}});AB({tag:"menuitem",name:"HTMLMenuItemElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},props:{_label:{get:function(){var A=this._getattr("label");if(A!==null&&A!=="")return A;return A=this.textContent,A.replace(/[ \t\n\f\r]+/g," ").trim()}},label:{get:function(){var A=this._getattr("label");if(A!==null)return A;return this._label},set:function(A){this._setattr("label",A)}}},attributes:{type:{type:["command","checkbox","radio"],missing:"command"},icon:EZ,disabled:Boolean,checked:Boolean,radiogroup:String,default:Boolean}});AB({tag:"source",name:"HTMLSourceElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{srcset:String,sizes:String,media:String,src:EZ,type:String,width:String,height:String}});AB({tag:"track",name:"HTMLTrackElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{src:EZ,srclang:String,label:String,default:Boolean,kind:{type:["subtitles","captions","descriptions","chapters","metadata"],missing:"subtitles",invalid:"metadata"}},props:{NONE:{get:function(){return 0}},LOADING:{get:function(){return 1}},LOADED:{get:function(){return 2}},ERROR:{get:function(){return 3}},readyState:{get:lK.nyi},track:{get:lK.nyi}}});AB({tag:"font",name:"HTMLFontElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{color:{type:String,treatNullAsEmptyString:!0},face:{type:String},size:{type:String}}});AB({tag:"dir",name:"HTMLDirectoryElement",ctor:function A(B,Q,D){L9.call(this,B,Q,D)},attributes:{compact:Boolean}});AB({tags:["abbr","address","article","aside","b","bdi","bdo","cite","content","code","dd","dfn","dt","em","figcaption","figure","footer","header","hgroup","i","kbd","main","mark","nav","noscript","rb","rp","rt","rtc","ruby","s","samp","section","small","strong","sub","summary","sup","u","var","wbr","acronym","basefont","big","center","nobr","noembed","noframes","plaintext","strike","tt"]})});
var JF1=E((fz3,rkB)=>{rkB.exports=Qf1;var skB=RL0(),akB=zZ(),ZX8=eb1(),GX8=EL0();function Qf1(){skB.call(this)}Qf1.prototype=Object.create(skB.prototype,{substringData:{value:function A(B,Q){if(arguments.length<2)throw new TypeError("Not enough arguments");if(B=B>>>0,Q=Q>>>0,B>this.data.length||B<0||Q<0)akB.IndexSizeError();return this.data.substring(B,B+Q)}},appendData:{value:function A(B){if(arguments.length<1)throw new TypeError("Not enough arguments");this.data+=String(B)}},insertData:{value:function A(B,Q){return this.replaceData(B,0,Q)}},deleteData:{value:function A(B,Q){return this.replaceData(B,Q,"")}},replaceData:{value:function A(B,Q,D){var Z=this.data,G=Z.length;if(B=B>>>0,Q=Q>>>0,D=String(D),B>G||B<0)akB.IndexSizeError();if(B+Q>G)Q=G-B;var F=Z.substring(0,B),I=Z.substring(B+Q);this.data=F+D+I}},isEqual:{value:function A(B){return this._data===B._data}},length:{get:function(){return this.data.length}}});Object.defineProperties(Qf1.prototype,ZX8);Object.defineProperties(Qf1.prototype,GX8)});
var JL0=E((jJ8)=>{var $kB=zZ();jJ8.property=function(A){if(Array.isArray(A.type)){var B=Object.create(null);A.type.forEach(function(Z){B[Z.value||Z]=Z.alias||Z});var Q=A.missing;if(Q===void 0)Q=null;var D=A.invalid;if(D===void 0)D=Q;return{get:function(){var Z=this._getattr(A.name);if(Z===null)return Q;if(Z=B[Z.toLowerCase()],Z!==void 0)return Z;if(D!==null)return D;return Z},set:function(Z){this._setattr(A.name,Z)}}}else if(A.type===Boolean)return{get:function(){return this.hasAttribute(A.name)},set:function(Z){if(Z)this._setattr(A.name,"");else this.removeAttribute(A.name)}};else if(A.type===Number||A.type==="long"||A.type==="unsigned long"||A.type==="limited unsigned long with fallback")return SJ8(A);else if(!A.type||A.type===String)return{get:function(){return this._getattr(A.name)||""},set:function(Z){if(A.treatNullAsEmptyString&&Z===null)Z="";this._setattr(A.name,Z)}};else if(typeof A.type==="function")return A.type(A.name,A);throw new Error("Invalid attribute definition")};function SJ8(A){var B;if(typeof A.default==="function")B=A.default;else if(typeof A.default==="number")B=function(){return A.default};else B=function(){$kB.assert(!1,typeof A.default)};var Q=A.type==="unsigned long",D=A.type==="long",Z=A.type==="limited unsigned long with fallback",G=A.min,F=A.max,I=A.setmin;if(G===void 0){if(Q)G=0;if(D)G=-2147483648;if(Z)G=1}if(F===void 0){if(Q||D||Z)F=2147483647}return{get:function(){var Y=this._getattr(A.name),W=A.float?parseFloat(Y):parseInt(Y,10);if(Y===null||!isFinite(W)||G!==void 0&&W<G||F!==void 0&&W>F)return B.call(this);if(Q||D||Z){if(!/^[ \t\n\f\r]*[-+]?[0-9]/.test(Y))return B.call(this);W=W|0}return W},set:function(Y){if(!A.float)Y=Math.floor(Y);if(I!==void 0&&Y<I)$kB.IndexSizeError(A.name+" set to "+Y);if(Q)Y=Y<0||Y>2147483647?B.call(this):Y|0;else if(Z)Y=Y<1||Y>2147483647?B.call(this):Y|0;else if(D)Y=Y<-2147483648||Y>2147483647?B.call(this):Y|0;this._setattr(A.name,String(Y))}}}jJ8.registerChangeHandler=function(A,B,Q){var D=A.prototype;if(!Object.prototype.hasOwnProperty.call(D,"_attributeChangeHandlers"))D._attributeChangeHandlers=Object.create(D._attributeChangeHandlers||null);D._attributeChangeHandlers[B]=Q}});
var JkB=E((Mz3,WkB)=>{WkB.exports=class A extends Array{constructor(B){super(B&&B.length||0);if(B)for(var Q in B)this[Q]=B[Q]}item(B){return this[B]||null}}});
var KF1=E((dz3,YyB)=>{var xL0={FILTER_ACCEPT:1,FILTER_REJECT:2,FILTER_SKIP:3,SHOW_ALL:4294967295,SHOW_ELEMENT:1,SHOW_ATTRIBUTE:2,SHOW_TEXT:4,SHOW_CDATA_SECTION:8,SHOW_ENTITY_REFERENCE:16,SHOW_ENTITY:32,SHOW_PROCESSING_INSTRUCTION:64,SHOW_COMMENT:128,SHOW_DOCUMENT:256,SHOW_DOCUMENT_TYPE:512,SHOW_DOCUMENT_FRAGMENT:1024,SHOW_NOTATION:2048};YyB.exports=xL0.constructor=xL0.prototype=xL0});
var LkB=E((jz3,NkB)=>{NkB.exports=qkB;var _J8=GW();function qkB(A,B){this.root=A,this.filter=B,this.lastModTime=A.lastModTime,this.done=!1,this.cache=[],this.traverse()}qkB.prototype=Object.create(Object.prototype,{length:{get:function(){if(this.checkcache(),!this.done)this.traverse();return this.cache.length}},item:{value:function(A){if(this.checkcache(),!this.done&&A>=this.cache.length)this.traverse();return this.cache[A]}},checkcache:{value:function(){if(this.lastModTime!==this.root.lastModTime){for(var A=this.cache.length-1;A>=0;A--)this[A]=void 0;this.cache.length=0,this.done=!1,this.lastModTime=this.root.lastModTime}}},traverse:{value:function(A){if(A!==void 0)A++;var B;while((B=this.next())!==null)if(this[this.cache.length]=B,this.cache.push(B),A&&this.cache.length===A)return;this.done=!0}},next:{value:function(){var A=this.cache.length===0?this.root:this.cache[this.cache.length-1],B;if(A.nodeType===_J8.DOCUMENT_NODE)B=A.documentElement;else B=A.nextElement(this.root);while(B){if(this.filter(B))return B;B=B.nextElement(this.root)}return null}}})});
var LyB=E((iz3,NyB)=>{NyB.exports=qyB;var gL0=KF1(),uL0=bL0(),$yB=zZ();function HX8(A,B,Q){if(Q)return uL0.next(A,B);else{if(A===B)return null;return uL0.previous(A,null)}}function UyB(A,B){for(;B;B=B.parentNode)if(A===B)return!0;return!1}function wyB(A,B){var Q,D;Q=A._referenceNode,D=A._pointerBeforeReferenceNode;while(!0){if(D===B)D=!D;else if(Q=HX8(Q,A._root,B),Q===null)return null;var Z=A._internalFilter(Q);if(Z===gL0.FILTER_ACCEPT)break}return A._referenceNode=Q,A._pointerBeforeReferenceNode=D,Q}function qyB(A,B,Q){if(!A||!A.nodeType)$yB.NotSupportedError();this._root=A,this._referenceNode=A,this._pointerBeforeReferenceNode=!0,this._whatToShow=Number(B)||0,this._filter=Q||null,this._active=!1,A.doc._attachNodeIterator(this)}Object.defineProperties(qyB.prototype,{root:{get:function A(){return this._root}},referenceNode:{get:function A(){return this._referenceNode}},pointerBeforeReferenceNode:{get:function A(){return this._pointerBeforeReferenceNode}},whatToShow:{get:function A(){return this._whatToShow}},filter:{get:function A(){return this._filter}},_internalFilter:{value:function A(B){var Q,D;if(this._active)$yB.InvalidStateError();if(!(1<<B.nodeType-1&this._whatToShow))return gL0.FILTER_SKIP;if(D=this._filter,D===null)Q=gL0.FILTER_ACCEPT;else{this._active=!0;try{if(typeof D==="function")Q=D(B);else Q=D.acceptNode(B)}finally{this._active=!1}}return+Q}},_preremove:{value:function A(B){if(UyB(B,this._root))return;if(!UyB(B,this._referenceNode))return;if(this._pointerBeforeReferenceNode){var Q=B;while(Q.lastChild)Q=Q.lastChild;if(Q=uL0.next(Q,this.root),Q){this._referenceNode=Q;return}this._pointerBeforeReferenceNode=!1}if(B.previousSibling===null)this._referenceNode=B.parentNode;else{this._referenceNode=B.previousSibling;var D;for(D=this._referenceNode.lastChild;D;D=this._referenceNode.lastChild)this._referenceNode=D}}},nextNode:{value:function A(){return wyB(this,!0)}},previousNode:{value:function A(){return wyB(this,!1)}},detach:{value:function A(){}},toString:{value:function A(){return"[object NodeIterator]"}}})});
var R01=E((Kz3,gjB)=>{gjB.exports=Wd;Wd.CAPTURING_PHASE=1;Wd.AT_TARGET=2;Wd.BUBBLING_PHASE=3;function Wd(A,B){if(this.type="",this.target=null,this.currentTarget=null,this.eventPhase=Wd.AT_TARGET,this.bubbles=!1,this.cancelable=!1,this.isTrusted=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this._propagationStopped=!1,this._immediatePropagationStopped=!1,this._initialized=!0,this._dispatching=!1,A)this.type=A;if(B)for(var Q in B)this[Q]=B[Q]}Wd.prototype=Object.create(Object.prototype,{constructor:{value:Wd},stopPropagation:{value:function A(){this._propagationStopped=!0}},stopImmediatePropagation:{value:function A(){this._propagationStopped=!0,this._immediatePropagationStopped=!0}},preventDefault:{value:function A(){if(this.cancelable)this.defaultPrevented=!0}},initEvent:{value:function A(B,Q,D){if(this._initialized=!0,this._dispatching)return;this._propagationStopped=!1,this._immediatePropagationStopped=!1,this.defaultPrevented=!1,this.isTrusted=!1,this.target=null,this.type=B,this.bubbles=Q,this.cancelable=D}}})});
var RL0=E((bz3,nkB)=>{nkB.exports=ikB;var lkB=GW(),QX8=Vd(),pkB=zZ(),ckB=pkB.HierarchyRequestError,DX8=pkB.NotFoundError;function ikB(){lkB.call(this)}ikB.prototype=Object.create(lkB.prototype,{hasChildNodes:{value:function(){return!1}},firstChild:{value:null},lastChild:{value:null},insertBefore:{value:function(A,B){if(!A.nodeType)throw new TypeError("not a node");ckB()}},replaceChild:{value:function(A,B){if(!A.nodeType)throw new TypeError("not a node");ckB()}},removeChild:{value:function(A){if(!A.nodeType)throw new TypeError("not a node");DX8()}},removeChildren:{value:function(){}},childNodes:{get:function(){if(!this._childNodes)this._childNodes=new QX8;return this._childNodes}}})});
var SL0=E((gz3,QyB)=>{QyB.exports=PL0;var FX8=GW(),ByB=JF1();function PL0(A,B){ByB.call(this),this.nodeType=FX8.COMMENT_NODE,this.ownerDocument=A,this._data=B}var VF1={get:function(){return this._data},set:function(A){if(A===null||A===void 0)A="";else A=String(A);if(this._data=A,this.rooted)this.ownerDocument.mutateValue(this)}};PL0.prototype=Object.create(ByB.prototype,{nodeName:{value:"#comment"},nodeValue:VF1,textContent:VF1,innerText:VF1,data:{get:VF1.get,set:function(A){VF1.set.call(this,A===null?"":String(A))}},clone:{value:function A(){return new PL0(this.ownerDocument,this._data)}}})});
var TL0=E((hz3,AyB)=>{AyB.exports=OL0;var okB=zZ(),tkB=GW(),ekB=JF1();function OL0(A,B){ekB.call(this),this.nodeType=tkB.TEXT_NODE,this.ownerDocument=A,this._data=B,this._index=void 0}var XF1={get:function(){return this._data},set:function(A){if(A===null||A===void 0)A="";else A=String(A);if(A===this._data)return;if(this._data=A,this.rooted)this.ownerDocument.mutateValue(this);if(this.parentNode&&this.parentNode._textchangehook)this.parentNode._textchangehook(this)}};OL0.prototype=Object.create(ekB.prototype,{nodeName:{value:"#text"},nodeValue:XF1,textContent:XF1,innerText:XF1,data:{get:XF1.get,set:function(A){XF1.set.call(this,A===null?"":String(A))}},splitText:{value:function A(B){if(B>this._data.length||B<0)okB.IndexSizeError();var Q=this._data.substring(B),D=this.ownerDocument.createTextNode(Q);this.data=this.data.substring(0,B);var Z=this.parentNode;if(Z!==null)Z.insertBefore(D,this.nextSibling);return D}},wholeText:{get:function A(){var B=this.textContent;for(var Q=this.nextSibling;Q;Q=Q.nextSibling){if(Q.nodeType!==tkB.TEXT_NODE)break;B+=Q.textContent}return B}},replaceWholeText:{value:okB.nyi},clone:{value:function A(){return new OL0(this.ownerDocument,this._data)}}})});
var TyB=E((az3,OyB)=>{OyB.exports=mL0;var RyB=R01();function mL0(A,B){RyB.call(this,A,B)}mL0.prototype=Object.create(RyB.prototype,{constructor:{value:mL0}})});
var UL0=E((xz3,fkB)=>{fkB.exports=bkB;var j01=zZ();function bkB(A){this.element=A}Object.defineProperties(bkB.prototype,{length:{get:j01.shouldOverride},item:{value:j01.shouldOverride},getNamedItem:{value:function A(B){return this.element.getAttributeNode(B)}},getNamedItemNS:{value:function A(B,Q){return this.element.getAttributeNodeNS(B,Q)}},setNamedItem:{value:j01.nyi},setNamedItemNS:{value:j01.nyi},removeNamedItem:{value:function A(B){var Q=this.element.getAttributeNode(B);if(Q)return this.element.removeAttribute(B),Q;j01.NotFoundError()}},removeNamedItemNS:{value:function A(B,Q){var D=this.element.getAttributeNodeNS(B,Q);if(D)return this.element.removeAttributeNS(B,Q),D;j01.NotFoundError()}}})});
var Uf1=E((YE3,S_B)=>{S_B.exports=M8;var rX8=Wf1(),oX8=Xf1(),oL0=GW(),l9=zZ().NAMESPACE,q_B=If1(),S5=q_B.elements,Kd=Function.prototype.apply.bind(Array.prototype.push),Vf1=-1,h01=1,XJ=2,H6=3,oM=4,tX8=5,eX8=[],AV8=/^HTML$|^-\/\/W3O\/\/DTD W3 HTML Strict 3\.0\/\/EN\/\/$|^-\/W3C\/DTD HTML 4\.0 Transitional\/EN$|^\+\/\/Silmaril\/\/dtd html Pro v0r11 19970101\/\/|^-\/\/AdvaSoft Ltd\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/AS\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict\/\/|^-\/\/IETF\/\/DTD HTML 2\.0\/\/|^-\/\/IETF\/\/DTD HTML 2\.1E\/\/|^-\/\/IETF\/\/DTD HTML 3\.0\/\/|^-\/\/IETF\/\/DTD HTML 3\.2 Final\/\/|^-\/\/IETF\/\/DTD HTML 3\.2\/\/|^-\/\/IETF\/\/DTD HTML 3\/\/|^-\/\/IETF\/\/DTD HTML Level 0\/\/|^-\/\/IETF\/\/DTD HTML Level 1\/\/|^-\/\/IETF\/\/DTD HTML Level 2\/\/|^-\/\/IETF\/\/DTD HTML Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 0\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict\/\/|^-\/\/IETF\/\/DTD HTML\/\/|^-\/\/Metrius\/\/DTD Metrius Presentational\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 Tables\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 Tables\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD HTML\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD Strict HTML\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML 2\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended 1\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended Relaxed 1\.0\/\/|^-\/\/SoftQuad Software\/\/DTD HoTMetaL PRO 6\.0::19990601::extensions to HTML 4\.0\/\/|^-\/\/SoftQuad\/\/DTD HoTMetaL PRO 4\.0::19971010::extensions to HTML 4\.0\/\/|^-\/\/Spyglass\/\/DTD HTML 2\.0 Extended\/\/|^-\/\/SQ\/\/DTD HTML 2\.0 HoTMetaL \+ extensions\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava HTML\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava Strict HTML\/\/|^-\/\/W3C\/\/DTD HTML 3 1995-03-24\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Draft\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Final\/\/|^-\/\/W3C\/\/DTD HTML 3\.2\/\/|^-\/\/W3C\/\/DTD HTML 3\.2S Draft\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Transitional\/\/|^-\/\/W3C\/\/DTD HTML Experimental 19960712\/\/|^-\/\/W3C\/\/DTD HTML Experimental 970421\/\/|^-\/\/W3C\/\/DTD W3 HTML\/\/|^-\/\/W3O\/\/DTD W3 HTML 3\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML 2\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML\/\//i,BV8="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",I_B=/^-\/\/W3C\/\/DTD HTML 4\.01 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.01 Transitional\/\//i,QV8=/^-\/\/W3C\/\/DTD XHTML 1\.0 Frameset\/\/|^-\/\/W3C\/\/DTD XHTML 1\.0 Transitional\/\//i,zd=Object.create(null);zd[l9.HTML]={__proto__:null,address:!0,applet:!0,area:!0,article:!0,aside:!0,base:!0,basefont:!0,bgsound:!0,blockquote:!0,body:!0,br:!0,button:!0,caption:!0,center:!0,col:!0,colgroup:!0,dd:!0,details:!0,dir:!0,div:!0,dl:!0,dt:!0,embed:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,frame:!0,frameset:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,head:!0,header:!0,hgroup:!0,hr:!0,html:!0,iframe:!0,img:!0,input:!0,li:!0,link:!0,listing:!0,main:!0,marquee:!0,menu:!0,meta:!0,nav:!0,noembed:!0,noframes:!0,noscript:!0,object:!0,ol:!0,p:!0,param:!0,plaintext:!0,pre:!0,script:!0,section:!0,select:!0,source:!0,style:!0,summary:!0,table:!0,tbody:!0,td:!0,template:!0,textarea:!0,tfoot:!0,th:!0,thead:!0,title:!0,tr:!0,track:!0,ul:!0,wbr:!0,xmp:!0};zd[l9.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};zd[l9.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};var AM0=Object.create(null);AM0[l9.HTML]={__proto__:null,address:!0,div:!0,p:!0};var N_B=Object.create(null);N_B[l9.HTML]={__proto__:null,dd:!0,dt:!0};var g01=Object.create(null);g01[l9.HTML]={__proto__:null,table:!0,thead:!0,tbody:!0,tfoot:!0,tr:!0};var L_B=Object.create(null);L_B[l9.HTML]={__proto__:null,dd:!0,dt:!0,li:!0,menuitem:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0};var M_B=Object.create(null);M_B[l9.HTML]={__proto__:null,caption:!0,colgroup:!0,dd:!0,dt:!0,li:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0,tbody:!0,td:!0,tfoot:!0,th:!0,thead:!0,tr:!0};var Hf1=Object.create(null);Hf1[l9.HTML]={__proto__:null,table:!0,template:!0,html:!0};var zf1=Object.create(null);zf1[l9.HTML]={__proto__:null,tbody:!0,tfoot:!0,thead:!0,template:!0,html:!0};var BM0=Object.create(null);BM0[l9.HTML]={__proto__:null,tr:!0,template:!0,html:!0};var R_B=Object.create(null);R_B[l9.HTML]={__proto__:null,button:!0,fieldset:!0,input:!0,keygen:!0,object:!0,output:!0,select:!0,textarea:!0,img:!0};var tM=Object.create(null);tM[l9.HTML]={__proto__:null,applet:!0,caption:!0,html:!0,table:!0,td:!0,th:!0,marquee:!0,object:!0,template:!0};tM[l9.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};tM[l9.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var Ef1=Object.create(tM);Ef1[l9.HTML]=Object.create(tM[l9.HTML]);Ef1[l9.HTML].ol=!0;Ef1[l9.HTML].ul=!0;var QM0=Object.create(tM);QM0[l9.HTML]=Object.create(tM[l9.HTML]);QM0[l9.HTML].button=!0;var O_B=Object.create(null);O_B[l9.HTML]={__proto__:null,html:!0,table:!0,template:!0};var DV8=Object.create(null);DV8[l9.HTML]={__proto__:null,optgroup:!0,option:!0};var T_B=Object.create(null);T_B[l9.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0};var P_B=Object.create(null);P_B[l9.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var Y_B={__proto__:null,"xlink:actuate":l9.XLINK,"xlink:arcrole":l9.XLINK,"xlink:href":l9.XLINK,"xlink:role":l9.XLINK,"xlink:show":l9.XLINK,"xlink:title":l9.XLINK,"xlink:type":l9.XLINK,"xml:base":l9.XML,"xml:lang":l9.XML,"xml:space":l9.XML,xmlns:l9.XMLNS,"xmlns:xlink":l9.XMLNS},W_B={__proto__:null,attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},J_B={__proto__:null,altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},X_B={__proto__:null,0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},ZV8={__proto__:null,AElig:198,"AElig;":198,AMP:38,"AMP;":38,Aacute:193,"Aacute;":193,"Abreve;":258,Acirc:194,"Acirc;":194,"Acy;":1040,"Afr;":[55349,56580],Agrave:192,"Agrave;":192,"Alpha;":913,"Amacr;":256,"And;":10835,"Aogon;":260,"Aopf;":[55349,56632],"ApplyFunction;":8289,Aring:197,"Aring;":197,"Ascr;":[55349,56476],"Assign;":8788,Atilde:195,"Atilde;":195,Auml:196,"Auml;":196,"Backslash;":8726,"Barv;":10983,"Barwed;":8966,"Bcy;":1041,"Because;":8757,"Bernoullis;":8492,"Beta;":914,"Bfr;":[55349,56581],"Bopf;":[55349,56633],"Breve;":728,"Bscr;":8492,"Bumpeq;":8782,"CHcy;":1063,COPY:169,"COPY;":169,"Cacute;":262,"Cap;":8914,"CapitalDifferentialD;":8517,"Cayleys;":8493,"Ccaron;":268,Ccedil:199,"Ccedil;":199,"Ccirc;":264,"Cconint;":8752,"Cdot;":266,"Cedilla;":184,"CenterDot;":183,"Cfr;":8493,"Chi;":935,"CircleDot;":8857,"CircleMinus;":8854,"CirclePlus;":8853,"CircleTimes;":8855,"ClockwiseContourIntegral;":8754,"CloseCurlyDoubleQuote;":8221,"CloseCurlyQuote;":8217,"Colon;":8759,"Colone;":10868,"Congruent;":8801,"Conint;":8751,"ContourIntegral;":8750,"Copf;":8450,"Coproduct;":8720,"CounterClockwiseContourIntegral;":8755,"Cross;":10799,"Cscr;":[55349,56478],"Cup;":8915,"CupCap;":8781,"DD;":8517,"DDotrahd;":10513,"DJcy;":1026,"DScy;":1029,"DZcy;":1039,"Dagger;":8225,"Darr;":8609,"Dashv;":10980,"Dcaron;":270,"Dcy;":1044,"Del;":8711,"Delta;":916,"Dfr;":[55349,56583],"DiacriticalAcute;":180,"DiacriticalDot;":729,"DiacriticalDoubleAcute;":733,"DiacriticalGrave;":96,"DiacriticalTilde;":732,"Diamond;":8900,"DifferentialD;":8518,"Dopf;":[55349,56635],"Dot;":168,"DotDot;":8412,"DotEqual;":8784,"DoubleContourIntegral;":8751,"DoubleDot;":168,"DoubleDownArrow;":8659,"DoubleLeftArrow;":8656,"DoubleLeftRightArrow;":8660,"DoubleLeftTee;":10980,"DoubleLongLeftArrow;":10232,"DoubleLongLeftRightArrow;":10234,"DoubleLongRightArrow;":10233,"DoubleRightArrow;":8658,"DoubleRightTee;":8872,"DoubleUpArrow;":8657,"DoubleUpDownArrow;":8661,"DoubleVerticalBar;":8741,"DownArrow;":8595,"DownArrowBar;":10515,"DownArrowUpArrow;":8693,"DownBreve;":785,"DownLeftRightVector;":10576,"DownLeftTeeVector;":10590,"DownLeftVector;":8637,"DownLeftVectorBar;":10582,"DownRightTeeVector;":10591,"DownRightVector;":8641,"DownRightVectorBar;":10583,"DownTee;":8868,"DownTeeArrow;":8615,"Downarrow;":8659,"Dscr;":[55349,56479],"Dstrok;":272,"ENG;":330,ETH:208,"ETH;":208,Eacute:201,"Eacute;":201,"Ecaron;":282,Ecirc:202,"Ecirc;":202,"Ecy;":1069,"Edot;":278,"Efr;":[55349,56584],Egrave:200,"Egrave;":200,"Element;":8712,"Emacr;":274,"EmptySmallSquare;":9723,"EmptyVerySmallSquare;":9643,"Eogon;":280,"Eopf;":[55349,56636],"Epsilon;":917,"Equal;":10869,"EqualTilde;":8770,"Equilibrium;":8652,"Escr;":8496,"Esim;":10867,"Eta;":919,Euml:203,"Euml;":203,"Exists;":8707,"ExponentialE;":8519,"Fcy;":1060,"Ffr;":[55349,56585],"FilledSmallSquare;":9724,"FilledVerySmallSquare;":9642,"Fopf;":[55349,56637],"ForAll;":8704,"Fouriertrf;":8497,"Fscr;":8497,"GJcy;":1027,GT:62,"GT;":62,"Gamma;":915,"Gammad;":988,"Gbreve;":286,"Gcedil;":290,"Gcirc;":284,"Gcy;":1043,"Gdot;":288,"Gfr;":[55349,56586],"Gg;":8921,"Gopf;":[55349,56638],"GreaterEqual;":8805,"GreaterEqualLess;":8923,"GreaterFullEqual;":8807,"GreaterGreater;":10914,"GreaterLess;":8823,"GreaterSlantEqual;":10878,"GreaterTilde;":8819,"Gscr;":[55349,56482],"Gt;":8811,"HARDcy;":1066,"Hacek;":711,"Hat;":94,"Hcirc;":292,"Hfr;":8460,"HilbertSpace;":8459,"Hopf;":8461,"HorizontalLine;":9472,"Hscr;":8459,"Hstrok;":294,"HumpDownHump;":8782,"HumpEqual;":8783,"IEcy;":1045,"IJlig;":306,"IOcy;":1025,Iacute:205,"Iacute;":205,Icirc:206,"Icirc;":206,"Icy;":1048,"Idot;":304,"Ifr;":8465,Igrave:204,"Igrave;":204,"Im;":8465,"Imacr;":298,"ImaginaryI;":8520,"Implies;":8658,"Int;":8748,"Integral;":8747,"Intersection;":8898,"InvisibleComma;":8291,"InvisibleTimes;":8290,"Iogon;":302,"Iopf;":[55349,56640],"Iota;":921,"Iscr;":8464,"Itilde;":296,"Iukcy;":1030,Iuml:207,"Iuml;":207,"Jcirc;":308,"Jcy;":1049,"Jfr;":[55349,56589],"Jopf;":[55349,56641],"Jscr;":[55349,56485],"Jsercy;":1032,"Jukcy;":1028,"KHcy;":1061,"KJcy;":1036,"Kappa;":922,"Kcedil;":310,"Kcy;":1050,"Kfr;":[55349,56590],"Kopf;":[55349,56642],"Kscr;":[55349,56486],"LJcy;":1033,LT:60,"LT;":60,"Lacute;":313,"Lambda;":923,"Lang;":10218,"Laplacetrf;":8466,"Larr;":8606,"Lcaron;":317,"Lcedil;":315,"Lcy;":1051,"LeftAngleBracket;":10216,"LeftArrow;":8592,"LeftArrowBar;":8676,"LeftArrowRightArrow;":8646,"LeftCeiling;":8968,"LeftDoubleBracket;":10214,"LeftDownTeeVector;":10593,"LeftDownVector;":8643,"LeftDownVectorBar;":10585,"LeftFloor;":8970,"LeftRightArrow;":8596,"LeftRightVector;":10574,"LeftTee;":8867,"LeftTeeArrow;":8612,"LeftTeeVector;":10586,"LeftTriangle;":8882,"LeftTriangleBar;":10703,"LeftTriangleEqual;":8884,"LeftUpDownVector;":10577,"LeftUpTeeVector;":10592,"LeftUpVector;":8639,"LeftUpVectorBar;":10584,"LeftVector;":8636,"LeftVectorBar;":10578,"Leftarrow;":8656,"Leftrightarrow;":8660,"LessEqualGreater;":8922,"LessFullEqual;":8806,"LessGreater;":8822,"LessLess;":10913,"LessSlantEqual;":10877,"LessTilde;":8818,"Lfr;":[55349,56591],"Ll;":8920,"Lleftarrow;":8666,"Lmidot;":319,"LongLeftArrow;":10229,"LongLeftRightArrow;":10231,"LongRightArrow;":10230,"Longleftarrow;":10232,"Longleftrightarrow;":10234,"Longrightarrow;":10233,"Lopf;":[55349,56643],"LowerLeftArrow;":8601,"LowerRightArrow;":8600,"Lscr;":8466,"Lsh;":8624,"Lstrok;":321,"Lt;":8810,"Map;":10501,"Mcy;":1052,"MediumSpace;":8287,"Mellintrf;":8499,"Mfr;":[55349,56592],"MinusPlus;":8723,"Mopf;":[55349,56644],"Mscr;":8499,"Mu;":924,"NJcy;":1034,"Nacute;":323,"Ncaron;":327,"Ncedil;":325,"Ncy;":1053,"NegativeMediumSpace;":8203,"NegativeThickSpace;":8203,"NegativeThinSpace;":8203,"NegativeVeryThinSpace;":8203,"NestedGreaterGreater;":8811,"NestedLessLess;":8810,"NewLine;":10,"Nfr;":[55349,56593],"NoBreak;":8288,"NonBreakingSpace;":160,"Nopf;":8469,"Not;":10988,"NotCongruent;":8802,"NotCupCap;":8813,"NotDoubleVerticalBar;":8742,"NotElement;":8713,"NotEqual;":8800,"NotEqualTilde;":[8770,824],"NotExists;":8708,"NotGreater;":8815,"NotGreaterEqual;":8817,"NotGreaterFullEqual;":[8807,824],"NotGreaterGreater;":[8811,824],"NotGreaterLess;":8825,"NotGreaterSlantEqual;":[10878,824],"NotGreaterTilde;":8821,"NotHumpDownHump;":[8782,824],"NotHumpEqual;":[8783,824],"NotLeftTriangle;":8938,"NotLeftTriangleBar;":[10703,824],"NotLeftTriangleEqual;":8940,"NotLess;":8814,"NotLessEqual;":8816,"NotLessGreater;":8824,"NotLessLess;":[8810,824],"NotLessSlantEqual;":[10877,824],"NotLessTilde;":8820,"NotNestedGreaterGreater;":[10914,824],"NotNestedLessLess;":[10913,824],"NotPrecedes;":8832,"NotPrecedesEqual;":[10927,824],"NotPrecedesSlantEqual;":8928,"NotReverseElement;":8716,"NotRightTriangle;":8939,"NotRightTriangleBar;":[10704,824],"NotRightTriangleEqual;":8941,"NotSquareSubset;":[8847,824],"NotSquareSubsetEqual;":8930,"NotSquareSuperset;":[8848,824],"NotSquareSupersetEqual;":8931,"NotSubset;":[8834,8402],"NotSubsetEqual;":8840,"NotSucceeds;":8833,"NotSucceedsEqual;":[10928,824],"NotSucceedsSlantEqual;":8929,"NotSucceedsTilde;":[8831,824],"NotSuperset;":[8835,8402],"NotSupersetEqual;":8841,"NotTilde;":8769,"NotTildeEqual;":8772,"NotTildeFullEqual;":8775,"NotTildeTilde;":8777,"NotVerticalBar;":8740,"Nscr;":[55349,56489],Ntilde:209,"Ntilde;":209,"Nu;":925,"OElig;":338,Oacute:211,"Oacute;":211,Ocirc:212,"Ocirc;":212,"Ocy;":1054,"Odblac;":336,"Ofr;":[55349,56594],Ograve:210,"Ograve;":210,"Omacr;":332,"Omega;":937,"Omicron;":927,"Oopf;":[55349,56646],"OpenCurlyDoubleQuote;":8220,"OpenCurlyQuote;":8216,"Or;":10836,"Oscr;":[55349,56490],Oslash:216,"Oslash;":216,Otilde:213,"Otilde;":213,"Otimes;":10807,Ouml:214,"Ouml;":214,"OverBar;":8254,"OverBrace;":9182,"OverBracket;":9140,"OverParenthesis;":9180,"PartialD;":8706,"Pcy;":1055,"Pfr;":[55349,56595],"Phi;":934,"Pi;":928,"PlusMinus;":177,"Poincareplane;":8460,"Popf;":8473,"Pr;":10939,"Precedes;":8826,"PrecedesEqual;":10927,"PrecedesSlantEqual;":8828,"PrecedesTilde;":8830,"Prime;":8243,"Product;":8719,"Proportion;":8759,"Proportional;":8733,"Pscr;":[55349,56491],"Psi;":936,QUOT:34,"QUOT;":34,"Qfr;":[55349,56596],"Qopf;":8474,"Qscr;":[55349,56492],"RBarr;":10512,REG:174,"REG;":174,"Racute;":340,"Rang;":10219,"Rarr;":8608,"Rarrtl;":10518,"Rcaron;":344,"Rcedil;":342,"Rcy;":1056,"Re;":8476,"ReverseElement;":8715,"ReverseEquilibrium;":8651,"ReverseUpEquilibrium;":10607,"Rfr;":8476,"Rho;":929,"RightAngleBracket;":10217,"RightArrow;":8594,"RightArrowBar;":8677,"RightArrowLeftArrow;":8644,"RightCeiling;":8969,"RightDoubleBracket;":10215,"RightDownTeeVector;":10589,"RightDownVector;":8642,"RightDownVectorBar;":10581,"RightFloor;":8971,"RightTee;":8866,"RightTeeArrow;":8614,"RightTeeVector;":10587,"RightTriangle;":8883,"RightTriangleBar;":10704,"RightTriangleEqual;":8885,"RightUpDownVector;":10575,"RightUpTeeVector;":10588,"RightUpVector;":8638,"RightUpVectorBar;":10580,"RightVector;":8640,"RightVectorBar;":10579,"Rightarrow;":8658,"Ropf;":8477,"RoundImplies;":10608,"Rrightarrow;":8667,"Rscr;":8475,"Rsh;":8625,"RuleDelayed;":10740,"SHCHcy;":1065,"SHcy;":1064,"SOFTcy;":1068,"Sacute;":346,"Sc;":10940,"Scaron;":352,"Scedil;":350,"Scirc;":348,"Scy;":1057,"Sfr;":[55349,56598],"ShortDownArrow;":8595,"ShortLeftArrow;":8592,"ShortRightArrow;":8594,"ShortUpArrow;":8593,"Sigma;":931,"SmallCircle;":8728,"Sopf;":[55349,56650],"Sqrt;":8730,"Square;":9633,"SquareIntersection;":8851,"SquareSubset;":8847,"SquareSubsetEqual;":8849,"SquareSuperset;":8848,"SquareSupersetEqual;":8850,"SquareUnion;":8852,"Sscr;":[55349,56494],"Star;":8902,"Sub;":8912,"Subset;":8912,"SubsetEqual;":8838,"Succeeds;":8827,"SucceedsEqual;":10928,"SucceedsSlantEqual;":8829,"SucceedsTilde;":8831,"SuchThat;":8715,"Sum;":8721,"Sup;":8913,"Superset;":8835,"SupersetEqual;":8839,"Supset;":8913,THORN:222,"THORN;":222,"TRADE;":8482,"TSHcy;":1035,"TScy;":1062,"Tab;":9,"Tau;":932,"Tcaron;":356,"Tcedil;":354,"Tcy;":1058,"Tfr;":[55349,56599],"Therefore;":8756,"Theta;":920,"ThickSpace;":[8287,8202],"ThinSpace;":8201,"Tilde;":8764,"TildeEqual;":8771,"TildeFullEqual;":8773,"TildeTilde;":8776,"Topf;":[55349,56651],"TripleDot;":8411,"Tscr;":[55349,56495],"Tstrok;":358,Uacute:218,"Uacute;":218,"Uarr;":8607,"Uarrocir;":10569,"Ubrcy;":1038,"Ubreve;":364,Ucirc:219,"Ucirc;":219,"Ucy;":1059,"Udblac;":368,"Ufr;":[55349,56600],Ugrave:217,"Ugrave;":217,"Umacr;":362,"UnderBar;":95,"UnderBrace;":9183,"UnderBracket;":9141,"UnderParenthesis;":9181,"Union;":8899,"UnionPlus;":8846,"Uogon;":370,"Uopf;":[55349,56652],"UpArrow;":8593,"UpArrowBar;":10514,"UpArrowDownArrow;":8645,"UpDownArrow;":8597,"UpEquilibrium;":10606,"UpTee;":8869,"UpTeeArrow;":8613,"Uparrow;":8657,"Updownarrow;":8661,"UpperLeftArrow;":8598,"UpperRightArrow;":8599,"Upsi;":978,"Upsilon;":933,"Uring;":366,"Uscr;":[55349,56496],"Utilde;":360,Uuml:220,"Uuml;":220,"VDash;":8875,"Vbar;":10987,"Vcy;":1042,"Vdash;":8873,"Vdashl;":10982,"Vee;":8897,"Verbar;":8214,"Vert;":8214,"VerticalBar;":8739,"VerticalLine;":124,"VerticalSeparator;":10072,"VerticalTilde;":8768,"VeryThinSpace;":8202,"Vfr;":[55349,56601],"Vopf;":[55349,56653],"Vscr;":[55349,56497],"Vvdash;":8874,"Wcirc;":372,"Wedge;":8896,"Wfr;":[55349,56602],"Wopf;":[55349,56654],"Wscr;":[55349,56498],"Xfr;":[55349,56603],"Xi;":926,"Xopf;":[55349,56655],"Xscr;":[55349,56499],"YAcy;":1071,"YIcy;":1031,"YUcy;":1070,Yacute:221,"Yacute;":221,"Ycirc;":374,"Ycy;":1067,"Yfr;":[55349,56604],"Yopf;":[55349,56656],"Yscr;":[55349,56500],"Yuml;":376,"ZHcy;":1046,"Zacute;":377,"Zcaron;":381,"Zcy;":1047,"Zdot;":379,"ZeroWidthSpace;":8203,"Zeta;":918,"Zfr;":8488,"Zopf;":8484,"Zscr;":[55349,56501],aacute:225,"aacute;":225,"abreve;":259,"ac;":8766,"acE;":[8766,819],"acd;":8767,acirc:226,"acirc;":226,acute:180,"acute;":180,"acy;":1072,aelig:230,"aelig;":230,"af;":8289,"afr;":[55349,56606],agrave:224,"agrave;":224,"alefsym;":8501,"aleph;":8501,"alpha;":945,"amacr;":257,"amalg;":10815,amp:38,"amp;":38,"and;":8743,"andand;":10837,"andd;":10844,"andslope;":10840,"andv;":10842,"ang;":8736,"ange;":10660,"angle;":8736,"angmsd;":8737,"angmsdaa;":10664,"angmsdab;":10665,"angmsdac;":10666,"angmsdad;":10667,"angmsdae;":10668,"angmsdaf;":10669,"angmsdag;":10670,"angmsdah;":10671,"angrt;":8735,"angrtvb;":8894,"angrtvbd;":10653,"angsph;":8738,"angst;":197,"angzarr;":9084,"aogon;":261,"aopf;":[55349,56658],"ap;":8776,"apE;":10864,"apacir;":10863,"ape;":8778,"apid;":8779,"apos;":39,"approx;":8776,"approxeq;":8778,aring:229,"aring;":229,"ascr;":[55349,56502],"ast;":42,"asymp;":8776,"asympeq;":8781,atilde:227,"atilde;":227,auml:228,"auml;":228,"awconint;":8755,"awint;":10769,"bNot;":10989,"backcong;":8780,"backepsilon;":1014,"backprime;":8245,"backsim;":8765,"backsimeq;":8909,"barvee;":8893,"barwed;":8965,"barwedge;":8965,"bbrk;":9141,"bbrktbrk;":9142,"bcong;":8780,"bcy;":1073,"bdquo;":8222,"becaus;":8757,"because;":8757,"bemptyv;":10672,"bepsi;":1014,"bernou;":8492,"beta;":946,"beth;":8502,"between;":8812,"bfr;":[55349,56607],"bigcap;":8898,"bigcirc;":9711,"bigcup;":8899,"bigodot;":10752,"bigoplus;":10753,"bigotimes;":10754,"bigsqcup;":10758,"bigstar;":9733,"bigtriangledown;":9661,"bigtriangleup;":9651,"biguplus;":10756,"bigvee;":8897,"bigwedge;":8896,"bkarow;":10509,"blacklozenge;":10731,"blacksquare;":9642,"blacktriangle;":9652,"blacktriangledown;":9662,"blacktriangleleft;":9666,"blacktriangleright;":9656,"blank;":9251,"blk12;":9618,"blk14;":9617,"blk34;":9619,"block;":9608,"bne;":[61,8421],"bnequiv;":[8801,8421],"bnot;":8976,"bopf;":[55349,56659],"bot;":8869,"bottom;":8869,"bowtie;":8904,"boxDL;":9559,"boxDR;":9556,"boxDl;":9558,"boxDr;":9555,"boxH;":9552,"boxHD;":9574,"boxHU;":9577,"boxHd;":9572,"boxHu;":9575,"boxUL;":9565,"boxUR;":9562,"boxUl;":9564,"boxUr;":9561,"boxV;":9553,"boxVH;":9580,"boxVL;":9571,"boxVR;":9568,"boxVh;":9579,"boxVl;":9570,"boxVr;":9567,"boxbox;":10697,"boxdL;":9557,"boxdR;":9554,"boxdl;":9488,"boxdr;":9484,"boxh;":9472,"boxhD;":9573,"boxhU;":9576,"boxhd;":9516,"boxhu;":9524,"boxminus;":8863,"boxplus;":8862,"boxtimes;":8864,"boxuL;":9563,"boxuR;":9560,"boxul;":9496,"boxur;":9492,"boxv;":9474,"boxvH;":9578,"boxvL;":9569,"boxvR;":9566,"boxvh;":9532,"boxvl;":9508,"boxvr;":9500,"bprime;":8245,"breve;":728,brvbar:166,"brvbar;":166,"bscr;":[55349,56503],"bsemi;":8271,"bsim;":8765,"bsime;":8909,"bsol;":92,"bsolb;":10693,"bsolhsub;":10184,"bull;":8226,"bullet;":8226,"bump;":8782,"bumpE;":10926,"bumpe;":8783,"bumpeq;":8783,"cacute;":263,"cap;":8745,"capand;":10820,"capbrcup;":10825,"capcap;":10827,"capcup;":10823,"capdot;":10816,"caps;":[8745,65024],"caret;":8257,"caron;":711,"ccaps;":10829,"ccaron;":269,ccedil:231,"ccedil;":231,"ccirc;":265,"ccups;":10828,"ccupssm;":10832,"cdot;":267,cedil:184,"cedil;":184,"cemptyv;":10674,cent:162,"cent;":162,"centerdot;":183,"cfr;":[55349,56608],"chcy;":1095,"check;":10003,"checkmark;":10003,"chi;":967,"cir;":9675,"cirE;":10691,"circ;":710,"circeq;":8791,"circlearrowleft;":8634,"circlearrowright;":8635,"circledR;":174,"circledS;":9416,"circledast;":8859,"circledcirc;":8858,"circleddash;":8861,"cire;":8791,"cirfnint;":10768,"cirmid;":10991,"cirscir;":10690,"clubs;":9827,"clubsuit;":9827,"colon;":58,"colone;":8788,"coloneq;":8788,"comma;":44,"commat;":64,"comp;":8705,"compfn;":8728,"complement;":8705,"complexes;":8450,"cong;":8773,"congdot;":10861,"conint;":8750,"copf;":[55349,56660],"coprod;":8720,copy:169,"copy;":169,"copysr;":8471,"crarr;":8629,"cross;":10007,"cscr;":[55349,56504],"csub;":10959,"csube;":10961,"csup;":10960,"csupe;":10962,"ctdot;":8943,"cudarrl;":10552,"cudarrr;":10549,"cuepr;":8926,"cuesc;":8927,"cularr;":8630,"cularrp;":10557,"cup;":8746,"cupbrcap;":10824,"cupcap;":10822,"cupcup;":10826,"cupdot;":8845,"cupor;":10821,"cups;":[8746,65024],"curarr;":8631,"curarrm;":10556,"curlyeqprec;":8926,"curlyeqsucc;":8927,"curlyvee;":8910,"curlywedge;":8911,curren:164,"curren;":164,"curvearrowleft;":8630,"curvearrowright;":8631,"cuvee;":8910,"cuwed;":8911,"cwconint;":8754,"cwint;":8753,"cylcty;":9005,"dArr;":8659,"dHar;":10597,"dagger;":8224,"daleth;":8504,"darr;":8595,"dash;":8208,"dashv;":8867,"dbkarow;":10511,"dblac;":733,"dcaron;":271,"dcy;":1076,"dd;":8518,"ddagger;":8225,"ddarr;":8650,"ddotseq;":10871,deg:176,"deg;":176,"delta;":948,"demptyv;":10673,"dfisht;":10623,"dfr;":[55349,56609],"dharl;":8643,"dharr;":8642,"diam;":8900,"diamond;":8900,"diamondsuit;":9830,"diams;":9830,"die;":168,"digamma;":989,"disin;":8946,"div;":247,divide:247,"divide;":247,"divideontimes;":8903,"divonx;":8903,"djcy;":1106,"dlcorn;":8990,"dlcrop;":8973,"dollar;":36,"dopf;":[55349,56661],"dot;":729,"doteq;":8784,"doteqdot;":8785,"dotminus;":8760,"dotplus;":8724,"dotsquare;":8865,"doublebarwedge;":8966,"downarrow;":8595,"downdownarrows;":8650,"downharpoonleft;":8643,"downharpoonright;":8642,"drbkarow;":10512,"drcorn;":8991,"drcrop;":8972,"dscr;":[55349,56505],"dscy;":1109,"dsol;":10742,"dstrok;":273,"dtdot;":8945,"dtri;":9663,"dtrif;":9662,"duarr;":8693,"duhar;":10607,"dwangle;":10662,"dzcy;":1119,"dzigrarr;":10239,"eDDot;":10871,"eDot;":8785,eacute:233,"eacute;":233,"easter;":10862,"ecaron;":283,"ecir;":8790,ecirc:234,"ecirc;":234,"ecolon;":8789,"ecy;":1101,"edot;":279,"ee;":8519,"efDot;":8786,"efr;":[55349,56610],"eg;":10906,egrave:232,"egrave;":232,"egs;":10902,"egsdot;":10904,"el;":10905,"elinters;":9191,"ell;":8467,"els;":10901,"elsdot;":10903,"emacr;":275,"empty;":8709,"emptyset;":8709,"emptyv;":8709,"emsp13;":8196,"emsp14;":8197,"emsp;":8195,"eng;":331,"ensp;":8194,"eogon;":281,"eopf;":[55349,56662],"epar;":8917,"eparsl;":10723,"eplus;":10865,"epsi;":949,"epsilon;":949,"epsiv;":1013,"eqcirc;":8790,"eqcolon;":8789,"eqsim;":8770,"eqslantgtr;":10902,"eqslantless;":10901,"equals;":61,"equest;":8799,"equiv;":8801,"equivDD;":10872,"eqvparsl;":10725,"erDot;":8787,"erarr;":10609,"escr;":8495,"esdot;":8784,"esim;":8770,"eta;":951,eth:240,"eth;":240,euml:235,"euml;":235,"euro;":8364,"excl;":33,"exist;":8707,"expectation;":8496,"exponentiale;":8519,"fallingdotseq;":8786,"fcy;":1092,"female;":9792,"ffilig;":64259,"fflig;":64256,"ffllig;":64260,"ffr;":[55349,56611],"filig;":64257,"fjlig;":[102,106],"flat;":9837,"fllig;":64258,"fltns;":9649,"fnof;":402,"fopf;":[55349,56663],"forall;":8704,"fork;":8916,"forkv;":10969,"fpartint;":10765,frac12:189,"frac12;":189,"frac13;":8531,frac14:188,"frac14;":188,"frac15;":8533,"frac16;":8537,"frac18;":8539,"frac23;":8532,"frac25;":8534,frac34:190,"frac34;":190,"frac35;":8535,"frac38;":8540,"frac45;":8536,"frac56;":8538,"frac58;":8541,"frac78;":8542,"frasl;":8260,"frown;":8994,"fscr;":[55349,56507],"gE;":8807,"gEl;":10892,"gacute;":501,"gamma;":947,"gammad;":989,"gap;":10886,"gbreve;":287,"gcirc;":285,"gcy;":1075,"gdot;":289,"ge;":8805,"gel;":8923,"geq;":8805,"geqq;":8807,"geqslant;":10878,"ges;":10878,"gescc;":10921,"gesdot;":10880,"gesdoto;":10882,"gesdotol;":10884,"gesl;":[8923,65024],"gesles;":10900,"gfr;":[55349,56612],"gg;":8811,"ggg;":8921,"gimel;":8503,"gjcy;":1107,"gl;":8823,"glE;":10898,"gla;":10917,"glj;":10916,"gnE;":8809,"gnap;":10890,"gnapprox;":10890,"gne;":10888,"gneq;":10888,"gneqq;":8809,"gnsim;":8935,"gopf;":[55349,56664],"grave;":96,"gscr;":8458,"gsim;":8819,"gsime;":10894,"gsiml;":10896,gt:62,"gt;":62,"gtcc;":10919,"gtcir;":10874,"gtdot;":8919,"gtlPar;":10645,"gtquest;":10876,"gtrapprox;":10886,"gtrarr;":10616,"gtrdot;":8919,"gtreqless;":8923,"gtreqqless;":10892,"gtrless;":8823,"gtrsim;":8819,"gvertneqq;":[8809,65024],"gvnE;":[8809,65024],"hArr;":8660,"hairsp;":8202,"half;":189,"hamilt;":8459,"hardcy;":1098,"harr;":8596,"harrcir;":10568,"harrw;":8621,"hbar;":8463,"hcirc;":293,"hearts;":9829,"heartsuit;":9829,"hellip;":8230,"hercon;":8889,"hfr;":[55349,56613],"hksearow;":10533,"hkswarow;":10534,"hoarr;":8703,"homtht;":8763,"hookleftarrow;":8617,"hookrightarrow;":8618,"hopf;":[55349,56665],"horbar;":8213,"hscr;":[55349,56509],"hslash;":8463,"hstrok;":295,"hybull;":8259,"hyphen;":8208,iacute:237,"iacute;":237,"ic;":8291,icirc:238,"icirc;":238,"icy;":1080,"iecy;":1077,iexcl:161,"iexcl;":161,"iff;":8660,"ifr;":[55349,56614],igrave:236,"igrave;":236,"ii;":8520,"iiiint;":10764,"iiint;":8749,"iinfin;":10716,"iiota;":8489,"ijlig;":307,"imacr;":299,"image;":8465,"imagline;":8464,"imagpart;":8465,"imath;":305,"imof;":8887,"imped;":437,"in;":8712,"incare;":8453,"infin;":8734,"infintie;":10717,"inodot;":305,"int;":8747,"intcal;":8890,"integers;":8484,"intercal;":8890,"intlarhk;":10775,"intprod;":10812,"iocy;":1105,"iogon;":303,"iopf;":[55349,56666],"iota;":953,"iprod;":10812,iquest:191,"iquest;":191,"iscr;":[55349,56510],"isin;":8712,"isinE;":8953,"isindot;":8949,"isins;":8948,"isinsv;":8947,"isinv;":8712,"it;":8290,"itilde;":297,"iukcy;":1110,iuml:239,"iuml;":239,"jcirc;":309,"jcy;":1081,"jfr;":[55349,56615],"jmath;":567,"jopf;":[55349,56667],"jscr;":[55349,56511],"jsercy;":1112,"jukcy;":1108,"kappa;":954,"kappav;":1008,"kcedil;":311,"kcy;":1082,"kfr;":[55349,56616],"kgreen;":312,"khcy;":1093,"kjcy;":1116,"kopf;":[55349,56668],"kscr;":[55349,56512],"lAarr;":8666,"lArr;":8656,"lAtail;":10523,"lBarr;":10510,"lE;":8806,"lEg;":10891,"lHar;":10594,"lacute;":314,"laemptyv;":10676,"lagran;":8466,"lambda;":955,"lang;":10216,"langd;":10641,"langle;":10216,"lap;":10885,laquo:171,"laquo;":171,"larr;":8592,"larrb;":8676,"larrbfs;":10527,"larrfs;":10525,"larrhk;":8617,"larrlp;":8619,"larrpl;":10553,"larrsim;":10611,"larrtl;":8610,"lat;":10923,"latail;":10521,"late;":10925,"lates;":[10925,65024],"lbarr;":10508,"lbbrk;":10098,"lbrace;":123,"lbrack;":91,"lbrke;":10635,"lbrksld;":10639,"lbrkslu;":10637,"lcaron;":318,"lcedil;":316,"lceil;":8968,"lcub;":123,"lcy;":1083,"ldca;":10550,"ldquo;":8220,"ldquor;":8222,"ldrdhar;":10599,"ldrushar;":10571,"ldsh;":8626,"le;":8804,"leftarrow;":8592,"leftarrowtail;":8610,"leftharpoondown;":8637,"leftharpoonup;":8636,"leftleftarrows;":8647,"leftrightarrow;":8596,"leftrightarrows;":8646,"leftrightharpoons;":8651,"leftrightsquigarrow;":8621,"leftthreetimes;":8907,"leg;":8922,"leq;":8804,"leqq;":8806,"leqslant;":10877,"les;":10877,"lescc;":10920,"lesdot;":10879,"lesdoto;":10881,"lesdotor;":10883,"lesg;":[8922,65024],"lesges;":10899,"lessapprox;":10885,"lessdot;":8918,"lesseqgtr;":8922,"lesseqqgtr;":10891,"lessgtr;":8822,"lesssim;":8818,"lfisht;":10620,"lfloor;":8970,"lfr;":[55349,56617],"lg;":8822,"lgE;":10897,"lhard;":8637,"lharu;":8636,"lharul;":10602,"lhblk;":9604,"ljcy;":1113,"ll;":8810,"llarr;":8647,"llcorner;":8990,"llhard;":10603,"lltri;":9722,"lmidot;":320,"lmoust;":9136,"lmoustache;":9136,"lnE;":8808,"lnap;":10889,"lnapprox;":10889,"lne;":10887,"lneq;":10887,"lneqq;":8808,"lnsim;":8934,"loang;":10220,"loarr;":8701,"lobrk;":10214,"longleftarrow;":10229,"longleftrightarrow;":10231,"longmapsto;":10236,"longrightarrow;":10230,"looparrowleft;":8619,"looparrowright;":8620,"lopar;":10629,"lopf;":[55349,56669],"loplus;":10797,"lotimes;":10804,"lowast;":8727,"lowbar;":95,"loz;":9674,"lozenge;":9674,"lozf;":10731,"lpar;":40,"lparlt;":10643,"lrarr;":8646,"lrcorner;":8991,"lrhar;":8651,"lrhard;":10605,"lrm;":8206,"lrtri;":8895,"lsaquo;":8249,"lscr;":[55349,56513],"lsh;":8624,"lsim;":8818,"lsime;":10893,"lsimg;":10895,"lsqb;":91,"lsquo;":8216,"lsquor;":8218,"lstrok;":322,lt:60,"lt;":60,"ltcc;":10918,"ltcir;":10873,"ltdot;":8918,"lthree;":8907,"ltimes;":8905,"ltlarr;":10614,"ltquest;":10875,"ltrPar;":10646,"ltri;":9667,"ltrie;":8884,"ltrif;":9666,"lurdshar;":10570,"luruhar;":10598,"lvertneqq;":[8808,65024],"lvnE;":[8808,65024],"mDDot;":8762,macr:175,"macr;":175,"male;":9794,"malt;":10016,"maltese;":10016,"map;":8614,"mapsto;":8614,"mapstodown;":8615,"mapstoleft;":8612,"mapstoup;":8613,"marker;":9646,"mcomma;":10793,"mcy;":1084,"mdash;":8212,"measuredangle;":8737,"mfr;":[55349,56618],"mho;":8487,micro:181,"micro;":181,"mid;":8739,"midast;":42,"midcir;":10992,middot:183,"middot;":183,"minus;":8722,"minusb;":8863,"minusd;":8760,"minusdu;":10794,"mlcp;":10971,"mldr;":8230,"mnplus;":8723,"models;":8871,"mopf;":[55349,56670],"mp;":8723,"mscr;":[55349,56514],"mstpos;":8766,"mu;":956,"multimap;":8888,"mumap;":8888,"nGg;":[8921,824],"nGt;":[8811,8402],"nGtv;":[8811,824],"nLeftarrow;":8653,"nLeftrightarrow;":8654,"nLl;":[8920,824],"nLt;":[8810,8402],"nLtv;":[8810,824],"nRightarrow;":8655,"nVDash;":8879,"nVdash;":8878,"nabla;":8711,"nacute;":324,"nang;":[8736,8402],"nap;":8777,"napE;":[10864,824],"napid;":[8779,824],"napos;":329,"napprox;":8777,"natur;":9838,"natural;":9838,"naturals;":8469,nbsp:160,"nbsp;":160,"nbump;":[8782,824],"nbumpe;":[8783,824],"ncap;":10819,"ncaron;":328,"ncedil;":326,"ncong;":8775,"ncongdot;":[10861,824],"ncup;":10818,"ncy;":1085,"ndash;":8211,"ne;":8800,"neArr;":8663,"nearhk;":10532,"nearr;":8599,"nearrow;":8599,"nedot;":[8784,824],"nequiv;":8802,"nesear;":10536,"nesim;":[8770,824],"nexist;":8708,"nexists;":8708,"nfr;":[55349,56619],"ngE;":[8807,824],"nge;":8817,"ngeq;":8817,"ngeqq;":[8807,824],"ngeqslant;":[10878,824],"nges;":[10878,824],"ngsim;":8821,"ngt;":8815,"ngtr;":8815,"nhArr;":8654,"nharr;":8622,"nhpar;":10994,"ni;":8715,"nis;":8956,"nisd;":8954,"niv;":8715,"njcy;":1114,"nlArr;":8653,"nlE;":[8806,824],"nlarr;":8602,"nldr;":8229,"nle;":8816,"nleftarrow;":8602,"nleftrightarrow;":8622,"nleq;":8816,"nleqq;":[8806,824],"nleqslant;":[10877,824],"nles;":[10877,824],"nless;":8814,"nlsim;":8820,"nlt;":8814,"nltri;":8938,"nltrie;":8940,"nmid;":8740,"nopf;":[55349,56671],not:172,"not;":172,"notin;":8713,"notinE;":[8953,824],"notindot;":[8949,824],"notinva;":8713,"notinvb;":8951,"notinvc;":8950,"notni;":8716,"notniva;":8716,"notnivb;":8958,"notnivc;":8957,"npar;":8742,"nparallel;":8742,"nparsl;":[11005,8421],"npart;":[8706,824],"npolint;":10772,"npr;":8832,"nprcue;":8928,"npre;":[10927,824],"nprec;":8832,"npreceq;":[10927,824],"nrArr;":8655,"nrarr;":8603,"nrarrc;":[10547,824],"nrarrw;":[8605,824],"nrightarrow;":8603,"nrtri;":8939,"nrtrie;":8941,"nsc;":8833,"nsccue;":8929,"nsce;":[10928,824],"nscr;":[55349,56515],"nshortmid;":8740,"nshortparallel;":8742,"nsim;":8769,"nsime;":8772,"nsimeq;":8772,"nsmid;":8740,"nspar;":8742,"nsqsube;":8930,"nsqsupe;":8931,"nsub;":8836,"nsubE;":[10949,824],"nsube;":8840,"nsubset;":[8834,8402],"nsubseteq;":8840,"nsubseteqq;":[10949,824],"nsucc;":8833,"nsucceq;":[10928,824],"nsup;":8837,"nsupE;":[10950,824],"nsupe;":8841,"nsupset;":[8835,8402],"nsupseteq;":8841,"nsupseteqq;":[10950,824],"ntgl;":8825,ntilde:241,"ntilde;":241,"ntlg;":8824,"ntriangleleft;":8938,"ntrianglelefteq;":8940,"ntriangleright;":8939,"ntrianglerighteq;":8941,"nu;":957,"num;":35,"numero;":8470,"numsp;":8199,"nvDash;":8877,"nvHarr;":10500,"nvap;":[8781,8402],"nvdash;":8876,"nvge;":[8805,8402],"nvgt;":[62,8402],"nvinfin;":10718,"nvlArr;":10498,"nvle;":[8804,8402],"nvlt;":[60,8402],"nvltrie;":[8884,8402],"nvrArr;":10499,"nvrtrie;":[8885,8402],"nvsim;":[8764,8402],"nwArr;":8662,"nwarhk;":10531,"nwarr;":8598,"nwarrow;":8598,"nwnear;":10535,"oS;":9416,oacute:243,"oacute;":243,"oast;":8859,"ocir;":8858,ocirc:244,"ocirc;":244,"ocy;":1086,"odash;":8861,"odblac;":337,"odiv;":10808,"odot;":8857,"odsold;":10684,"oelig;":339,"ofcir;":10687,"ofr;":[55349,56620],"ogon;":731,ograve:242,"ograve;":242,"ogt;":10689,"ohbar;":10677,"ohm;":937,"oint;":8750,"olarr;":8634,"olcir;":10686,"olcross;":10683,"oline;":8254,"olt;":10688,"omacr;":333,"omega;":969,"omicron;":959,"omid;":10678,"ominus;":8854,"oopf;":[55349,56672],"opar;":10679,"operp;":10681,"oplus;":8853,"or;":8744,"orarr;":8635,"ord;":10845,"order;":8500,"orderof;":8500,ordf:170,"ordf;":170,ordm:186,"ordm;":186,"origof;":8886,"oror;":10838,"orslope;":10839,"orv;":10843,"oscr;":8500,oslash:248,"oslash;":248,"osol;":8856,otilde:245,"otilde;":245,"otimes;":8855,"otimesas;":10806,ouml:246,"ouml;":246,"ovbar;":9021,"par;":8741,para:182,"para;":182,"parallel;":8741,"parsim;":10995,"parsl;":11005,"part;":8706,"pcy;":1087,"percnt;":37,"period;":46,"permil;":8240,"perp;":8869,"pertenk;":8241,"pfr;":[55349,56621],"phi;":966,"phiv;":981,"phmmat;":8499,"phone;":9742,"pi;":960,"pitchfork;":8916,"piv;":982,"planck;":8463,"planckh;":8462,"plankv;":8463,"plus;":43,"plusacir;":10787,"plusb;":8862,"pluscir;":10786,"plusdo;":8724,"plusdu;":10789,"pluse;":10866,plusmn:177,"plusmn;":177,"plussim;":10790,"plustwo;":10791,"pm;":177,"pointint;":10773,"popf;":[55349,56673],pound:163,"pound;":163,"pr;":8826,"prE;":10931,"prap;":10935,"prcue;":8828,"pre;":10927,"prec;":8826,"precapprox;":10935,"preccurlyeq;":8828,"preceq;":10927,"precnapprox;":10937,"precneqq;":10933,"precnsim;":8936,"precsim;":8830,"prime;":8242,"primes;":8473,"prnE;":10933,"prnap;":10937,"prnsim;":8936,"prod;":8719,"profalar;":9006,"profline;":8978,"profsurf;":8979,"prop;":8733,"propto;":8733,"prsim;":8830,"prurel;":8880,"pscr;":[55349,56517],"psi;":968,"puncsp;":8200,"qfr;":[55349,56622],"qint;":10764,"qopf;":[55349,56674],"qprime;":8279,"qscr;":[55349,56518],"quaternions;":8461,"quatint;":10774,"quest;":63,"questeq;":8799,quot:34,"quot;":34,"rAarr;":8667,"rArr;":8658,"rAtail;":10524,"rBarr;":10511,"rHar;":10596,"race;":[8765,817],"racute;":341,"radic;":8730,"raemptyv;":10675,"rang;":10217,"rangd;":10642,"range;":10661,"rangle;":10217,raquo:187,"raquo;":187,"rarr;":8594,"rarrap;":10613,"rarrb;":8677,"rarrbfs;":10528,"rarrc;":10547,"rarrfs;":10526,"rarrhk;":8618,"rarrlp;":8620,"rarrpl;":10565,"rarrsim;":10612,"rarrtl;":8611,"rarrw;":8605,"ratail;":10522,"ratio;":8758,"rationals;":8474,"rbarr;":10509,"rbbrk;":10099,"rbrace;":125,"rbrack;":93,"rbrke;":10636,"rbrksld;":10638,"rbrkslu;":10640,"rcaron;":345,"rcedil;":343,"rceil;":8969,"rcub;":125,"rcy;":1088,"rdca;":10551,"rdldhar;":10601,"rdquo;":8221,"rdquor;":8221,"rdsh;":8627,"real;":8476,"realine;":8475,"realpart;":8476,"reals;":8477,"rect;":9645,reg:174,"reg;":174,"rfisht;":10621,"rfloor;":8971,"rfr;":[55349,56623],"rhard;":8641,"rharu;":8640,"rharul;":10604,"rho;":961,"rhov;":1009,"rightarrow;":8594,"rightarrowtail;":8611,"rightharpoondown;":8641,"rightharpoonup;":8640,"rightleftarrows;":8644,"rightleftharpoons;":8652,"rightrightarrows;":8649,"rightsquigarrow;":8605,"rightthreetimes;":8908,"ring;":730,"risingdotseq;":8787,"rlarr;":8644,"rlhar;":8652,"rlm;":8207,"rmoust;":9137,"rmoustache;":9137,"rnmid;":10990,"roang;":10221,"roarr;":8702,"robrk;":10215,"ropar;":10630,"ropf;":[55349,56675],"roplus;":10798,"rotimes;":10805,"rpar;":41,"rpargt;":10644,"rppolint;":10770,"rrarr;":8649,"rsaquo;":8250,"rscr;":[55349,56519],"rsh;":8625,"rsqb;":93,"rsquo;":8217,"rsquor;":8217,"rthree;":8908,"rtimes;":8906,"rtri;":9657,"rtrie;":8885,"rtrif;":9656,"rtriltri;":10702,"ruluhar;":10600,"rx;":8478,"sacute;":347,"sbquo;":8218,"sc;":8827,"scE;":10932,"scap;":10936,"scaron;":353,"sccue;":8829,"sce;":10928,"scedil;":351,"scirc;":349,"scnE;":10934,"scnap;":10938,"scnsim;":8937,"scpolint;":10771,"scsim;":8831,"scy;":1089,"sdot;":8901,"sdotb;":8865,"sdote;":10854,"seArr;":8664,"searhk;":10533,"searr;":8600,"searrow;":8600,sect:167,"sect;":167,"semi;":59,"seswar;":10537,"setminus;":8726,"setmn;":8726,"sext;":10038,"sfr;":[55349,56624],"sfrown;":8994,"sharp;":9839,"shchcy;":1097,"shcy;":1096,"shortmid;":8739,"shortparallel;":8741,shy:173,"shy;":173,"sigma;":963,"sigmaf;":962,"sigmav;":962,"sim;":8764,"simdot;":10858,"sime;":8771,"simeq;":8771,"simg;":10910,"simgE;":10912,"siml;":10909,"simlE;":10911,"simne;":8774,"simplus;":10788,"simrarr;":10610,"slarr;":8592,"smallsetminus;":8726,"smashp;":10803,"smeparsl;":10724,"smid;":8739,"smile;":8995,"smt;":10922,"smte;":10924,"smtes;":[10924,65024],"softcy;":1100,"sol;":47,"solb;":10692,"solbar;":9023,"sopf;":[55349,56676],"spades;":9824,"spadesuit;":9824,"spar;":8741,"sqcap;":8851,"sqcaps;":[8851,65024],"sqcup;":8852,"sqcups;":[8852,65024],"sqsub;":8847,"sqsube;":8849,"sqsubset;":8847,"sqsubseteq;":8849,"sqsup;":8848,"sqsupe;":8850,"sqsupset;":8848,"sqsupseteq;":8850,"squ;":9633,"square;":9633,"squarf;":9642,"squf;":9642,"srarr;":8594,"sscr;":[55349,56520],"ssetmn;":8726,"ssmile;":8995,"sstarf;":8902,"star;":9734,"starf;":9733,"straightepsilon;":1013,"straightphi;":981,"strns;":175,"sub;":8834,"subE;":10949,"subdot;":10941,"sube;":8838,"subedot;":10947,"submult;":10945,"subnE;":10955,"subne;":8842,"subplus;":10943,"subrarr;":10617,"subset;":8834,"subseteq;":8838,"subseteqq;":10949,"subsetneq;":8842,"subsetneqq;":10955,"subsim;":10951,"subsub;":10965,"subsup;":10963,"succ;":8827,"succapprox;":10936,"succcurlyeq;":8829,"succeq;":10928,"succnapprox;":10938,"succneqq;":10934,"succnsim;":8937,"succsim;":8831,"sum;":8721,"sung;":9834,sup1:185,"sup1;":185,sup2:178,"sup2;":178,sup3:179,"sup3;":179,"sup;":8835,"supE;":10950,"supdot;":10942,"supdsub;":10968,"supe;":8839,"supedot;":10948,"suphsol;":10185,"suphsub;":10967,"suplarr;":10619,"supmult;":10946,"supnE;":10956,"supne;":8843,"supplus;":10944,"supset;":8835,"supseteq;":8839,"supseteqq;":10950,"supsetneq;":8843,"supsetneqq;":10956,"supsim;":10952,"supsub;":10964,"supsup;":10966,"swArr;":8665,"swarhk;":10534,"swarr;":8601,"swarrow;":8601,"swnwar;":10538,szlig:223,"szlig;":223,"target;":8982,"tau;":964,"tbrk;":9140,"tcaron;":357,"tcedil;":355,"tcy;":1090,"tdot;":8411,"telrec;":8981,"tfr;":[55349,56625],"there4;":8756,"therefore;":8756,"theta;":952,"thetasym;":977,"thetav;":977,"thickapprox;":8776,"thicksim;":8764,"thinsp;":8201,"thkap;":8776,"thksim;":8764,thorn:254,"thorn;":254,"tilde;":732,times:215,"times;":215,"timesb;":8864,"timesbar;":10801,"timesd;":10800,"tint;":8749,"toea;":10536,"top;":8868,"topbot;":9014,"topcir;":10993,"topf;":[55349,56677],"topfork;":10970,"tosa;":10537,"tprime;":8244,"trade;":8482,"triangle;":9653,"triangledown;":9663,"triangleleft;":9667,"trianglelefteq;":8884,"triangleq;":8796,"triangleright;":9657,"trianglerighteq;":8885,"tridot;":9708,"trie;":8796,"triminus;":10810,"triplus;":10809,"trisb;":10701,"tritime;":10811,"trpezium;":9186,"tscr;":[55349,56521],"tscy;":1094,"tshcy;":1115,"tstrok;":359,"twixt;":8812,"twoheadleftarrow;":8606,"twoheadrightarrow;":8608,"uArr;":8657,"uHar;":10595,uacute:250,"uacute;":250,"uarr;":8593,"ubrcy;":1118,"ubreve;":365,ucirc:251,"ucirc;":251,"ucy;":1091,"udarr;":8645,"udblac;":369,"udhar;":10606,"ufisht;":10622,"ufr;":[55349,56626],ugrave:249,"ugrave;":249,"uharl;":8639,"uharr;":8638,"uhblk;":9600,"ulcorn;":8988,"ulcorner;":8988,"ulcrop;":8975,"ultri;":9720,"umacr;":363,uml:168,"uml;":168,"uogon;":371,"uopf;":[55349,56678],"uparrow;":8593,"updownarrow;":8597,"upharpoonleft;":8639,"upharpoonright;":8638,"uplus;":8846,"upsi;":965,"upsih;":978,"upsilon;":965,"upuparrows;":8648,"urcorn;":8989,"urcorner;":8989,"urcrop;":8974,"uring;":367,"urtri;":9721,"uscr;":[55349,56522],"utdot;":8944,"utilde;":361,"utri;":9653,"utrif;":9652,"uuarr;":8648,uuml:252,"uuml;":252,"uwangle;":10663,"vArr;":8661,"vBar;":10984,"vBarv;":10985,"vDash;":8872,"vangrt;":10652,"varepsilon;":1013,"varkappa;":1008,"varnothing;":8709,"varphi;":981,"varpi;":982,"varpropto;":8733,"varr;":8597,"varrho;":1009,"varsigma;":962,"varsubsetneq;":[8842,65024],"varsubsetneqq;":[10955,65024],"varsupsetneq;":[8843,65024],"varsupsetneqq;":[10956,65024],"vartheta;":977,"vartriangleleft;":8882,"vartriangleright;":8883,"vcy;":1074,"vdash;":8866,"vee;":8744,"veebar;":8891,"veeeq;":8794,"vellip;":8942,"verbar;":124,"vert;":124,"vfr;":[55349,56627],"vltri;":8882,"vnsub;":[8834,8402],"vnsup;":[8835,8402],"vopf;":[55349,56679],"vprop;":8733,"vrtri;":8883,"vscr;":[55349,56523],"vsubnE;":[10955,65024],"vsubne;":[8842,65024],"vsupnE;":[10956,65024],"vsupne;":[8843,65024],"vzigzag;":10650,"wcirc;":373,"wedbar;":10847,"wedge;":8743,"wedgeq;":8793,"weierp;":8472,"wfr;":[55349,56628],"wopf;":[55349,56680],"wp;":8472,"wr;":8768,"wreath;":8768,"wscr;":[55349,56524],"xcap;":8898,"xcirc;":9711,"xcup;":8899,"xdtri;":9661,"xfr;":[55349,56629],"xhArr;":10234,"xharr;":10231,"xi;":958,"xlArr;":10232,"xlarr;":10229,"xmap;":10236,"xnis;":8955,"xodot;":10752,"xopf;":[55349,56681],"xoplus;":10753,"xotime;":10754,"xrArr;":10233,"xrarr;":10230,"xscr;":[55349,56525],"xsqcup;":10758,"xuplus;":10756,"xutri;":9651,"xvee;":8897,"xwedge;":8896,yacute:253,"yacute;":253,"yacy;":1103,"ycirc;":375,"ycy;":1099,yen:165,"yen;":165,"yfr;":[55349,56630],"yicy;":1111,"yopf;":[55349,56682],"yscr;":[55349,56526],"yucy;":1102,yuml:255,"yuml;":255,"zacute;":378,"zcaron;":382,"zcy;":1079,"zdot;":380,"zeetrf;":8488,"zeta;":950,"zfr;":[55349,56631],"zhcy;":1078,"zigrarr;":8669,"zopf;":[55349,56683],"zscr;":[55349,56527],"zwj;":8205,"zwnj;":8204},V_B=/(A(?:Elig;?|MP;?|acute;?|breve;|c(?:irc;?|y;)|fr;|grave;?|lpha;|macr;|nd;|o(?:gon;|pf;)|pplyFunction;|ring;?|s(?:cr;|sign;)|tilde;?|uml;?)|B(?:a(?:ckslash;|r(?:v;|wed;))|cy;|e(?:cause;|rnoullis;|ta;)|fr;|opf;|reve;|scr;|umpeq;)|C(?:Hcy;|OPY;?|a(?:cute;|p(?:;|italDifferentialD;)|yleys;)|c(?:aron;|edil;?|irc;|onint;)|dot;|e(?:dilla;|nterDot;)|fr;|hi;|ircle(?:Dot;|Minus;|Plus;|Times;)|lo(?:ckwiseContourIntegral;|seCurly(?:DoubleQuote;|Quote;))|o(?:lon(?:;|e;)|n(?:gruent;|int;|tourIntegral;)|p(?:f;|roduct;)|unterClockwiseContourIntegral;)|ross;|scr;|up(?:;|Cap;))|D(?:D(?:;|otrahd;)|Jcy;|Scy;|Zcy;|a(?:gger;|rr;|shv;)|c(?:aron;|y;)|el(?:;|ta;)|fr;|i(?:a(?:critical(?:Acute;|Do(?:t;|ubleAcute;)|Grave;|Tilde;)|mond;)|fferentialD;)|o(?:pf;|t(?:;|Dot;|Equal;)|uble(?:ContourIntegral;|Do(?:t;|wnArrow;)|L(?:eft(?:Arrow;|RightArrow;|Tee;)|ong(?:Left(?:Arrow;|RightArrow;)|RightArrow;))|Right(?:Arrow;|Tee;)|Up(?:Arrow;|DownArrow;)|VerticalBar;)|wn(?:Arrow(?:;|Bar;|UpArrow;)|Breve;|Left(?:RightVector;|TeeVector;|Vector(?:;|Bar;))|Right(?:TeeVector;|Vector(?:;|Bar;))|Tee(?:;|Arrow;)|arrow;))|s(?:cr;|trok;))|E(?:NG;|TH;?|acute;?|c(?:aron;|irc;?|y;)|dot;|fr;|grave;?|lement;|m(?:acr;|pty(?:SmallSquare;|VerySmallSquare;))|o(?:gon;|pf;)|psilon;|qu(?:al(?:;|Tilde;)|ilibrium;)|s(?:cr;|im;)|ta;|uml;?|x(?:ists;|ponentialE;))|F(?:cy;|fr;|illed(?:SmallSquare;|VerySmallSquare;)|o(?:pf;|rAll;|uriertrf;)|scr;)|G(?:Jcy;|T;?|amma(?:;|d;)|breve;|c(?:edil;|irc;|y;)|dot;|fr;|g;|opf;|reater(?:Equal(?:;|Less;)|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|scr;|t;)|H(?:ARDcy;|a(?:cek;|t;)|circ;|fr;|ilbertSpace;|o(?:pf;|rizontalLine;)|s(?:cr;|trok;)|ump(?:DownHump;|Equal;))|I(?:Ecy;|Jlig;|Ocy;|acute;?|c(?:irc;?|y;)|dot;|fr;|grave;?|m(?:;|a(?:cr;|ginaryI;)|plies;)|n(?:t(?:;|e(?:gral;|rsection;))|visible(?:Comma;|Times;))|o(?:gon;|pf;|ta;)|scr;|tilde;|u(?:kcy;|ml;?))|J(?:c(?:irc;|y;)|fr;|opf;|s(?:cr;|ercy;)|ukcy;)|K(?:Hcy;|Jcy;|appa;|c(?:edil;|y;)|fr;|opf;|scr;)|L(?:Jcy;|T;?|a(?:cute;|mbda;|ng;|placetrf;|rr;)|c(?:aron;|edil;|y;)|e(?:ft(?:A(?:ngleBracket;|rrow(?:;|Bar;|RightArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|Right(?:Arrow;|Vector;)|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;|rightarrow;)|ss(?:EqualGreater;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;))|fr;|l(?:;|eftarrow;)|midot;|o(?:ng(?:Left(?:Arrow;|RightArrow;)|RightArrow;|left(?:arrow;|rightarrow;)|rightarrow;)|pf;|wer(?:LeftArrow;|RightArrow;))|s(?:cr;|h;|trok;)|t;)|M(?:ap;|cy;|e(?:diumSpace;|llintrf;)|fr;|inusPlus;|opf;|scr;|u;)|N(?:Jcy;|acute;|c(?:aron;|edil;|y;)|e(?:gative(?:MediumSpace;|Thi(?:ckSpace;|nSpace;)|VeryThinSpace;)|sted(?:GreaterGreater;|LessLess;)|wLine;)|fr;|o(?:Break;|nBreakingSpace;|pf;|t(?:;|C(?:ongruent;|upCap;)|DoubleVerticalBar;|E(?:lement;|qual(?:;|Tilde;)|xists;)|Greater(?:;|Equal;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|Hump(?:DownHump;|Equal;)|Le(?:ftTriangle(?:;|Bar;|Equal;)|ss(?:;|Equal;|Greater;|Less;|SlantEqual;|Tilde;))|Nested(?:GreaterGreater;|LessLess;)|Precedes(?:;|Equal;|SlantEqual;)|R(?:everseElement;|ightTriangle(?:;|Bar;|Equal;))|S(?:quareSu(?:bset(?:;|Equal;)|perset(?:;|Equal;))|u(?:bset(?:;|Equal;)|cceeds(?:;|Equal;|SlantEqual;|Tilde;)|perset(?:;|Equal;)))|Tilde(?:;|Equal;|FullEqual;|Tilde;)|VerticalBar;))|scr;|tilde;?|u;)|O(?:Elig;|acute;?|c(?:irc;?|y;)|dblac;|fr;|grave;?|m(?:acr;|ega;|icron;)|opf;|penCurly(?:DoubleQuote;|Quote;)|r;|s(?:cr;|lash;?)|ti(?:lde;?|mes;)|uml;?|ver(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;))|P(?:artialD;|cy;|fr;|hi;|i;|lusMinus;|o(?:incareplane;|pf;)|r(?:;|ecedes(?:;|Equal;|SlantEqual;|Tilde;)|ime;|o(?:duct;|portion(?:;|al;)))|s(?:cr;|i;))|Q(?:UOT;?|fr;|opf;|scr;)|R(?:Barr;|EG;?|a(?:cute;|ng;|rr(?:;|tl;))|c(?:aron;|edil;|y;)|e(?:;|verse(?:E(?:lement;|quilibrium;)|UpEquilibrium;))|fr;|ho;|ight(?:A(?:ngleBracket;|rrow(?:;|Bar;|LeftArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;)|o(?:pf;|undImplies;)|rightarrow;|s(?:cr;|h;)|uleDelayed;)|S(?:H(?:CHcy;|cy;)|OFTcy;|acute;|c(?:;|aron;|edil;|irc;|y;)|fr;|hort(?:DownArrow;|LeftArrow;|RightArrow;|UpArrow;)|igma;|mallCircle;|opf;|q(?:rt;|uare(?:;|Intersection;|Su(?:bset(?:;|Equal;)|perset(?:;|Equal;))|Union;))|scr;|tar;|u(?:b(?:;|set(?:;|Equal;))|c(?:ceeds(?:;|Equal;|SlantEqual;|Tilde;)|hThat;)|m;|p(?:;|erset(?:;|Equal;)|set;)))|T(?:HORN;?|RADE;|S(?:Hcy;|cy;)|a(?:b;|u;)|c(?:aron;|edil;|y;)|fr;|h(?:e(?:refore;|ta;)|i(?:ckSpace;|nSpace;))|ilde(?:;|Equal;|FullEqual;|Tilde;)|opf;|ripleDot;|s(?:cr;|trok;))|U(?:a(?:cute;?|rr(?:;|ocir;))|br(?:cy;|eve;)|c(?:irc;?|y;)|dblac;|fr;|grave;?|macr;|n(?:der(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;)|ion(?:;|Plus;))|o(?:gon;|pf;)|p(?:Arrow(?:;|Bar;|DownArrow;)|DownArrow;|Equilibrium;|Tee(?:;|Arrow;)|arrow;|downarrow;|per(?:LeftArrow;|RightArrow;)|si(?:;|lon;))|ring;|scr;|tilde;|uml;?)|V(?:Dash;|bar;|cy;|dash(?:;|l;)|e(?:e;|r(?:bar;|t(?:;|ical(?:Bar;|Line;|Separator;|Tilde;))|yThinSpace;))|fr;|opf;|scr;|vdash;)|W(?:circ;|edge;|fr;|opf;|scr;)|X(?:fr;|i;|opf;|scr;)|Y(?:Acy;|Icy;|Ucy;|acute;?|c(?:irc;|y;)|fr;|opf;|scr;|uml;)|Z(?:Hcy;|acute;|c(?:aron;|y;)|dot;|e(?:roWidthSpace;|ta;)|fr;|opf;|scr;)|a(?:acute;?|breve;|c(?:;|E;|d;|irc;?|ute;?|y;)|elig;?|f(?:;|r;)|grave;?|l(?:e(?:fsym;|ph;)|pha;)|m(?:a(?:cr;|lg;)|p;?)|n(?:d(?:;|and;|d;|slope;|v;)|g(?:;|e;|le;|msd(?:;|a(?:a;|b;|c;|d;|e;|f;|g;|h;))|rt(?:;|vb(?:;|d;))|s(?:ph;|t;)|zarr;))|o(?:gon;|pf;)|p(?:;|E;|acir;|e;|id;|os;|prox(?:;|eq;))|ring;?|s(?:cr;|t;|ymp(?:;|eq;))|tilde;?|uml;?|w(?:conint;|int;))|b(?:Not;|a(?:ck(?:cong;|epsilon;|prime;|sim(?:;|eq;))|r(?:vee;|wed(?:;|ge;)))|brk(?:;|tbrk;)|c(?:ong;|y;)|dquo;|e(?:caus(?:;|e;)|mptyv;|psi;|rnou;|t(?:a;|h;|ween;))|fr;|ig(?:c(?:ap;|irc;|up;)|o(?:dot;|plus;|times;)|s(?:qcup;|tar;)|triangle(?:down;|up;)|uplus;|vee;|wedge;)|karow;|l(?:a(?:ck(?:lozenge;|square;|triangle(?:;|down;|left;|right;))|nk;)|k(?:1(?:2;|4;)|34;)|ock;)|n(?:e(?:;|quiv;)|ot;)|o(?:pf;|t(?:;|tom;)|wtie;|x(?:D(?:L;|R;|l;|r;)|H(?:;|D;|U;|d;|u;)|U(?:L;|R;|l;|r;)|V(?:;|H;|L;|R;|h;|l;|r;)|box;|d(?:L;|R;|l;|r;)|h(?:;|D;|U;|d;|u;)|minus;|plus;|times;|u(?:L;|R;|l;|r;)|v(?:;|H;|L;|R;|h;|l;|r;)))|prime;|r(?:eve;|vbar;?)|s(?:cr;|emi;|im(?:;|e;)|ol(?:;|b;|hsub;))|u(?:ll(?:;|et;)|mp(?:;|E;|e(?:;|q;))))|c(?:a(?:cute;|p(?:;|and;|brcup;|c(?:ap;|up;)|dot;|s;)|r(?:et;|on;))|c(?:a(?:ps;|ron;)|edil;?|irc;|ups(?:;|sm;))|dot;|e(?:dil;?|mptyv;|nt(?:;|erdot;|))|fr;|h(?:cy;|eck(?:;|mark;)|i;)|ir(?:;|E;|c(?:;|eq;|le(?:arrow(?:left;|right;)|d(?:R;|S;|ast;|circ;|dash;)))|e;|fnint;|mid;|scir;)|lubs(?:;|uit;)|o(?:lon(?:;|e(?:;|q;))|m(?:ma(?:;|t;)|p(?:;|fn;|le(?:ment;|xes;)))|n(?:g(?:;|dot;)|int;)|p(?:f;|rod;|y(?:;|sr;|)))|r(?:arr;|oss;)|s(?:cr;|u(?:b(?:;|e;)|p(?:;|e;)))|tdot;|u(?:darr(?:l;|r;)|e(?:pr;|sc;)|larr(?:;|p;)|p(?:;|brcap;|c(?:ap;|up;)|dot;|or;|s;)|r(?:arr(?:;|m;)|ly(?:eq(?:prec;|succ;)|vee;|wedge;)|ren;?|vearrow(?:left;|right;))|vee;|wed;)|w(?:conint;|int;)|ylcty;)|d(?:Arr;|Har;|a(?:gger;|leth;|rr;|sh(?:;|v;))|b(?:karow;|lac;)|c(?:aron;|y;)|d(?:;|a(?:gger;|rr;)|otseq;)|e(?:g;?|lta;|mptyv;)|f(?:isht;|r;)|har(?:l;|r;)|i(?:am(?:;|ond(?:;|suit;)|s;)|e;|gamma;|sin;|v(?:;|ide(?:;|ontimes;|)|onx;))|jcy;|lc(?:orn;|rop;)|o(?:llar;|pf;|t(?:;|eq(?:;|dot;)|minus;|plus;|square;)|ublebarwedge;|wn(?:arrow;|downarrows;|harpoon(?:left;|right;)))|r(?:bkarow;|c(?:orn;|rop;))|s(?:c(?:r;|y;)|ol;|trok;)|t(?:dot;|ri(?:;|f;))|u(?:arr;|har;)|wangle;|z(?:cy;|igrarr;))|e(?:D(?:Dot;|ot;)|a(?:cute;?|ster;)|c(?:aron;|ir(?:;|c;?)|olon;|y;)|dot;|e;|f(?:Dot;|r;)|g(?:;|rave;?|s(?:;|dot;))|l(?:;|inters;|l;|s(?:;|dot;))|m(?:acr;|pty(?:;|set;|v;)|sp(?:1(?:3;|4;)|;))|n(?:g;|sp;)|o(?:gon;|pf;)|p(?:ar(?:;|sl;)|lus;|si(?:;|lon;|v;))|q(?:c(?:irc;|olon;)|s(?:im;|lant(?:gtr;|less;))|u(?:als;|est;|iv(?:;|DD;))|vparsl;)|r(?:Dot;|arr;)|s(?:cr;|dot;|im;)|t(?:a;|h;?)|u(?:ml;?|ro;)|x(?:cl;|ist;|p(?:ectation;|onentiale;)))|f(?:allingdotseq;|cy;|emale;|f(?:ilig;|l(?:ig;|lig;)|r;)|ilig;|jlig;|l(?:at;|lig;|tns;)|nof;|o(?:pf;|r(?:all;|k(?:;|v;)))|partint;|r(?:a(?:c(?:1(?:2;?|3;|4;?|5;|6;|8;)|2(?:3;|5;)|3(?:4;?|5;|8;)|45;|5(?:6;|8;)|78;)|sl;)|own;)|scr;)|g(?:E(?:;|l;)|a(?:cute;|mma(?:;|d;)|p;)|breve;|c(?:irc;|y;)|dot;|e(?:;|l;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|l;))|l(?:;|es;)))|fr;|g(?:;|g;)|imel;|jcy;|l(?:;|E;|a;|j;)|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|opf;|rave;|s(?:cr;|im(?:;|e;|l;))|t(?:;|c(?:c;|ir;)|dot;|lPar;|quest;|r(?:a(?:pprox;|rr;)|dot;|eq(?:less;|qless;)|less;|sim;)|)|v(?:ertneqq;|nE;))|h(?:Arr;|a(?:irsp;|lf;|milt;|r(?:dcy;|r(?:;|cir;|w;)))|bar;|circ;|e(?:arts(?:;|uit;)|llip;|rcon;)|fr;|ks(?:earow;|warow;)|o(?:arr;|mtht;|ok(?:leftarrow;|rightarrow;)|pf;|rbar;)|s(?:cr;|lash;|trok;)|y(?:bull;|phen;))|i(?:acute;?|c(?:;|irc;?|y;)|e(?:cy;|xcl;?)|f(?:f;|r;)|grave;?|i(?:;|i(?:int;|nt;)|nfin;|ota;)|jlig;|m(?:a(?:cr;|g(?:e;|line;|part;)|th;)|of;|ped;)|n(?:;|care;|fin(?:;|tie;)|odot;|t(?:;|cal;|e(?:gers;|rcal;)|larhk;|prod;))|o(?:cy;|gon;|pf;|ta;)|prod;|quest;?|s(?:cr;|in(?:;|E;|dot;|s(?:;|v;)|v;))|t(?:;|ilde;)|u(?:kcy;|ml;?))|j(?:c(?:irc;|y;)|fr;|math;|opf;|s(?:cr;|ercy;)|ukcy;)|k(?:appa(?:;|v;)|c(?:edil;|y;)|fr;|green;|hcy;|jcy;|opf;|scr;)|l(?:A(?:arr;|rr;|tail;)|Barr;|E(?:;|g;)|Har;|a(?:cute;|emptyv;|gran;|mbda;|ng(?:;|d;|le;)|p;|quo;?|rr(?:;|b(?:;|fs;)|fs;|hk;|lp;|pl;|sim;|tl;)|t(?:;|ail;|e(?:;|s;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|quo(?:;|r;)|r(?:dhar;|ushar;)|sh;)|e(?:;|ft(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|leftarrows;|right(?:arrow(?:;|s;)|harpoons;|squigarrow;)|threetimes;)|g;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|r;))|g(?:;|es;)|s(?:approx;|dot;|eq(?:gtr;|qgtr;)|gtr;|sim;)))|f(?:isht;|loor;|r;)|g(?:;|E;)|h(?:ar(?:d;|u(?:;|l;))|blk;)|jcy;|l(?:;|arr;|corner;|hard;|tri;)|m(?:idot;|oust(?:;|ache;))|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|o(?:a(?:ng;|rr;)|brk;|ng(?:left(?:arrow;|rightarrow;)|mapsto;|rightarrow;)|oparrow(?:left;|right;)|p(?:ar;|f;|lus;)|times;|w(?:ast;|bar;)|z(?:;|enge;|f;))|par(?:;|lt;)|r(?:arr;|corner;|har(?:;|d;)|m;|tri;)|s(?:aquo;|cr;|h;|im(?:;|e;|g;)|q(?:b;|uo(?:;|r;))|trok;)|t(?:;|c(?:c;|ir;)|dot;|hree;|imes;|larr;|quest;|r(?:Par;|i(?:;|e;|f;))|)|ur(?:dshar;|uhar;)|v(?:ertneqq;|nE;))|m(?:DDot;|a(?:cr;?|l(?:e;|t(?:;|ese;))|p(?:;|sto(?:;|down;|left;|up;))|rker;)|c(?:omma;|y;)|dash;|easuredangle;|fr;|ho;|i(?:cro;?|d(?:;|ast;|cir;|dot;?)|nus(?:;|b;|d(?:;|u;)))|l(?:cp;|dr;)|nplus;|o(?:dels;|pf;)|p;|s(?:cr;|tpos;)|u(?:;|ltimap;|map;))|n(?:G(?:g;|t(?:;|v;))|L(?:eft(?:arrow;|rightarrow;)|l;|t(?:;|v;))|Rightarrow;|V(?:Dash;|dash;)|a(?:bla;|cute;|ng;|p(?:;|E;|id;|os;|prox;)|tur(?:;|al(?:;|s;)))|b(?:sp;?|ump(?:;|e;))|c(?:a(?:p;|ron;)|edil;|ong(?:;|dot;)|up;|y;)|dash;|e(?:;|Arr;|ar(?:hk;|r(?:;|ow;))|dot;|quiv;|s(?:ear;|im;)|xist(?:;|s;))|fr;|g(?:E;|e(?:;|q(?:;|q;|slant;)|s;)|sim;|t(?:;|r;))|h(?:Arr;|arr;|par;)|i(?:;|s(?:;|d;)|v;)|jcy;|l(?:Arr;|E;|arr;|dr;|e(?:;|ft(?:arrow;|rightarrow;)|q(?:;|q;|slant;)|s(?:;|s;))|sim;|t(?:;|ri(?:;|e;)))|mid;|o(?:pf;|t(?:;|in(?:;|E;|dot;|v(?:a;|b;|c;))|ni(?:;|v(?:a;|b;|c;))|))|p(?:ar(?:;|allel;|sl;|t;)|olint;|r(?:;|cue;|e(?:;|c(?:;|eq;))))|r(?:Arr;|arr(?:;|c;|w;)|ightarrow;|tri(?:;|e;))|s(?:c(?:;|cue;|e;|r;)|hort(?:mid;|parallel;)|im(?:;|e(?:;|q;))|mid;|par;|qsu(?:be;|pe;)|u(?:b(?:;|E;|e;|set(?:;|eq(?:;|q;)))|cc(?:;|eq;)|p(?:;|E;|e;|set(?:;|eq(?:;|q;)))))|t(?:gl;|ilde;?|lg;|riangle(?:left(?:;|eq;)|right(?:;|eq;)))|u(?:;|m(?:;|ero;|sp;))|v(?:Dash;|Harr;|ap;|dash;|g(?:e;|t;)|infin;|l(?:Arr;|e;|t(?:;|rie;))|r(?:Arr;|trie;)|sim;)|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|near;))|o(?:S;|a(?:cute;?|st;)|c(?:ir(?:;|c;?)|y;)|d(?:ash;|blac;|iv;|ot;|sold;)|elig;|f(?:cir;|r;)|g(?:on;|rave;?|t;)|h(?:bar;|m;)|int;|l(?:arr;|c(?:ir;|ross;)|ine;|t;)|m(?:acr;|ega;|i(?:cron;|d;|nus;))|opf;|p(?:ar;|erp;|lus;)|r(?:;|arr;|d(?:;|er(?:;|of;)|f;?|m;?)|igof;|or;|slope;|v;)|s(?:cr;|lash;?|ol;)|ti(?:lde;?|mes(?:;|as;))|uml;?|vbar;)|p(?:ar(?:;|a(?:;|llel;|)|s(?:im;|l;)|t;)|cy;|er(?:cnt;|iod;|mil;|p;|tenk;)|fr;|h(?:i(?:;|v;)|mmat;|one;)|i(?:;|tchfork;|v;)|l(?:an(?:ck(?:;|h;)|kv;)|us(?:;|acir;|b;|cir;|d(?:o;|u;)|e;|mn;?|sim;|two;))|m;|o(?:intint;|pf;|und;?)|r(?:;|E;|ap;|cue;|e(?:;|c(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;))|ime(?:;|s;)|n(?:E;|ap;|sim;)|o(?:d;|f(?:alar;|line;|surf;)|p(?:;|to;))|sim;|urel;)|s(?:cr;|i;)|uncsp;)|q(?:fr;|int;|opf;|prime;|scr;|u(?:at(?:ernions;|int;)|est(?:;|eq;)|ot;?))|r(?:A(?:arr;|rr;|tail;)|Barr;|Har;|a(?:c(?:e;|ute;)|dic;|emptyv;|ng(?:;|d;|e;|le;)|quo;?|rr(?:;|ap;|b(?:;|fs;)|c;|fs;|hk;|lp;|pl;|sim;|tl;|w;)|t(?:ail;|io(?:;|nals;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|ldhar;|quo(?:;|r;)|sh;)|e(?:al(?:;|ine;|part;|s;)|ct;|g;?)|f(?:isht;|loor;|r;)|h(?:ar(?:d;|u(?:;|l;))|o(?:;|v;))|i(?:ght(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|left(?:arrows;|harpoons;)|rightarrows;|squigarrow;|threetimes;)|ng;|singdotseq;)|l(?:arr;|har;|m;)|moust(?:;|ache;)|nmid;|o(?:a(?:ng;|rr;)|brk;|p(?:ar;|f;|lus;)|times;)|p(?:ar(?:;|gt;)|polint;)|rarr;|s(?:aquo;|cr;|h;|q(?:b;|uo(?:;|r;)))|t(?:hree;|imes;|ri(?:;|e;|f;|ltri;))|uluhar;|x;)|s(?:acute;|bquo;|c(?:;|E;|a(?:p;|ron;)|cue;|e(?:;|dil;)|irc;|n(?:E;|ap;|sim;)|polint;|sim;|y;)|dot(?:;|b;|e;)|e(?:Arr;|ar(?:hk;|r(?:;|ow;))|ct;?|mi;|swar;|tm(?:inus;|n;)|xt;)|fr(?:;|own;)|h(?:arp;|c(?:hcy;|y;)|ort(?:mid;|parallel;)|y;?)|i(?:gma(?:;|f;|v;)|m(?:;|dot;|e(?:;|q;)|g(?:;|E;)|l(?:;|E;)|ne;|plus;|rarr;))|larr;|m(?:a(?:llsetminus;|shp;)|eparsl;|i(?:d;|le;)|t(?:;|e(?:;|s;)))|o(?:ftcy;|l(?:;|b(?:;|ar;))|pf;)|pa(?:des(?:;|uit;)|r;)|q(?:c(?:ap(?:;|s;)|up(?:;|s;))|su(?:b(?:;|e;|set(?:;|eq;))|p(?:;|e;|set(?:;|eq;)))|u(?:;|ar(?:e;|f;)|f;))|rarr;|s(?:cr;|etmn;|mile;|tarf;)|t(?:ar(?:;|f;)|r(?:aight(?:epsilon;|phi;)|ns;))|u(?:b(?:;|E;|dot;|e(?:;|dot;)|mult;|n(?:E;|e;)|plus;|rarr;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;)))|cc(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;)|m;|ng;|p(?:1;?|2;?|3;?|;|E;|d(?:ot;|sub;)|e(?:;|dot;)|hs(?:ol;|ub;)|larr;|mult;|n(?:E;|e;)|plus;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;))))|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|nwar;)|zlig;?)|t(?:a(?:rget;|u;)|brk;|c(?:aron;|edil;|y;)|dot;|elrec;|fr;|h(?:e(?:re(?:4;|fore;)|ta(?:;|sym;|v;))|i(?:ck(?:approx;|sim;)|nsp;)|k(?:ap;|sim;)|orn;?)|i(?:lde;|mes(?:;|b(?:;|ar;)|d;|)|nt;)|o(?:ea;|p(?:;|bot;|cir;|f(?:;|ork;))|sa;)|prime;|r(?:ade;|i(?:angle(?:;|down;|left(?:;|eq;)|q;|right(?:;|eq;))|dot;|e;|minus;|plus;|sb;|time;)|pezium;)|s(?:c(?:r;|y;)|hcy;|trok;)|w(?:ixt;|ohead(?:leftarrow;|rightarrow;)))|u(?:Arr;|Har;|a(?:cute;?|rr;)|br(?:cy;|eve;)|c(?:irc;?|y;)|d(?:arr;|blac;|har;)|f(?:isht;|r;)|grave;?|h(?:ar(?:l;|r;)|blk;)|l(?:c(?:orn(?:;|er;)|rop;)|tri;)|m(?:acr;|l;?)|o(?:gon;|pf;)|p(?:arrow;|downarrow;|harpoon(?:left;|right;)|lus;|si(?:;|h;|lon;)|uparrows;)|r(?:c(?:orn(?:;|er;)|rop;)|ing;|tri;)|scr;|t(?:dot;|ilde;|ri(?:;|f;))|u(?:arr;|ml;?)|wangle;)|v(?:Arr;|Bar(?:;|v;)|Dash;|a(?:ngrt;|r(?:epsilon;|kappa;|nothing;|p(?:hi;|i;|ropto;)|r(?:;|ho;)|s(?:igma;|u(?:bsetneq(?:;|q;)|psetneq(?:;|q;)))|t(?:heta;|riangle(?:left;|right;))))|cy;|dash;|e(?:e(?:;|bar;|eq;)|llip;|r(?:bar;|t;))|fr;|ltri;|nsu(?:b;|p;)|opf;|prop;|rtri;|s(?:cr;|u(?:bn(?:E;|e;)|pn(?:E;|e;)))|zigzag;)|w(?:circ;|e(?:d(?:bar;|ge(?:;|q;))|ierp;)|fr;|opf;|p;|r(?:;|eath;)|scr;)|x(?:c(?:ap;|irc;|up;)|dtri;|fr;|h(?:Arr;|arr;)|i;|l(?:Arr;|arr;)|map;|nis;|o(?:dot;|p(?:f;|lus;)|time;)|r(?:Arr;|arr;)|s(?:cr;|qcup;)|u(?:plus;|tri;)|vee;|wedge;)|y(?:ac(?:ute;?|y;)|c(?:irc;|y;)|en;?|fr;|icy;|opf;|scr;|u(?:cy;|ml;?))|z(?:acute;|c(?:aron;|y;)|dot;|e(?:etrf;|ta;)|fr;|hcy;|igrarr;|opf;|scr;|w(?:j;|nj;)))|[\s\S]/g,GV8=32,FV8=/[^\r"&\u0000]+/g,IV8=/[^\r'&\u0000]+/g,YV8=/[^\r\t\n\f &>\u0000]+/g,WV8=/[^\r\t\n\f \/>A-Z\u0000]+/g,JV8=/[^\r\t\n\f \/=>A-Z\u0000]+/g,XV8=/[^\]\r\u0000\uffff]*/g,VV8=/[^&<\r\u0000\uffff]*/g,C_B=/[^<\r\u0000\uffff]*/g,CV8=/[^\r\u0000\uffff]*/g,K_B=/(?:(\/)?([a-z]+)>)|[\s\S]/g,H_B=/(?:([-a-z]+)[ \t\n\f]*=[ \t\n\f]*('[^'&\r\u0000]*'|"[^"&\r\u0000]*"|[^\t\n\r\f "&'\u0000>][^&> \t\n\r\f\u0000]*[ \t\n\f]))|[\s\S]/g,Cf1=/[^\x09\x0A\x0C\x0D\x20]/,tL0=/[^\x09\x0A\x0C\x0D\x20]/g,KV8=/[^\x00\x09\x0A\x0C\x0D\x20]/,Hd=/^[\x09\x0A\x0C\x0D\x20]+/,Kf1=/\x00/g;function VJ(A){var B=16384;if(A.length<B)return String.fromCharCode.apply(String,A);var Q="";for(var D=0;D<A.length;D+=B)Q+=String.fromCharCode.apply(String,A.slice(D,D+B));return Q}function HV8(A){var B=[];for(var Q=0;Q<A.length;Q++)B[Q]=A.charCodeAt(Q);return B}function j5(A,B){if(typeof B==="string")return A.namespaceURI===l9.HTML&&A.localName===B;var Q=B[A.namespaceURI];return Q&&Q[A.localName]}function z_B(A){return j5(A,T_B)}function E_B(A){if(j5(A,P_B))return!0;if(A.namespaceURI===l9.MATHML&&A.localName==="annotation-xml"){var B=A.getAttribute("encoding");if(B)B=B.toLowerCase();if(B==="text/html"||B==="application/xhtml+xml")return!0}return!1}function zV8(A){if(A in J_B)return J_B[A];else return A}function U_B(A){for(var B=0,Q=A.length;B<Q;B++)if(A[B][0]in W_B)A[B][0]=W_B[A[B][0]]}function w_B(A){for(var B=0,Q=A.length;B<Q;B++)if(A[B][0]==="definitionurl"){A[B][0]="definitionURL";break}}function eL0(A){for(var B=0,Q=A.length;B<Q;B++)if(A[B][0]in Y_B)A[B].push(Y_B[A[B][0]])}function $_B(A,B){for(var Q=0,D=A.length;Q<D;Q++){var Z=A[Q][0],G=A[Q][1];if(B.hasAttribute(Z))continue;B._setAttribute(Z,G)}}M8.ElementStack=function A(){this.elements=[],this.top=null};M8.ElementStack.prototype.push=function(A){this.elements.push(A),this.top=A};M8.ElementStack.prototype.pop=function(A){this.elements.pop(),this.top=this.elements[this.elements.length-1]};M8.ElementStack.prototype.popTag=function(A){for(var B=this.elements.length-1;B>0;B--){var Q=this.elements[B];if(j5(Q,A))break}this.elements.length=B,this.top=this.elements[B-1]};M8.ElementStack.prototype.popElementType=function(A){for(var B=this.elements.length-1;B>0;B--)if(this.elements[B]instanceof A)break;this.elements.length=B,this.top=this.elements[B-1]};M8.ElementStack.prototype.popElement=function(A){for(var B=this.elements.length-1;B>0;B--)if(this.elements[B]===A)break;this.elements.length=B,this.top=this.elements[B-1]};M8.ElementStack.prototype.removeElement=function(A){if(this.top===A)this.pop();else{var B=this.elements.lastIndexOf(A);if(B!==-1)this.elements.splice(B,1)}};M8.ElementStack.prototype.clearToContext=function(A){for(var B=this.elements.length-1;B>0;B--)if(j5(this.elements[B],A))break;this.elements.length=B+1,this.top=this.elements[B]};M8.ElementStack.prototype.contains=function(A){return this.inSpecificScope(A,Object.create(null))};M8.ElementStack.prototype.inSpecificScope=function(A,B){for(var Q=this.elements.length-1;Q>=0;Q--){var D=this.elements[Q];if(j5(D,A))return!0;if(j5(D,B))return!1}return!1};M8.ElementStack.prototype.elementInSpecificScope=function(A,B){for(var Q=this.elements.length-1;Q>=0;Q--){var D=this.elements[Q];if(D===A)return!0;if(j5(D,B))return!1}return!1};M8.ElementStack.prototype.elementTypeInSpecificScope=function(A,B){for(var Q=this.elements.length-1;Q>=0;Q--){var D=this.elements[Q];if(D instanceof A)return!0;if(j5(D,B))return!1}return!1};M8.ElementStack.prototype.inScope=function(A){return this.inSpecificScope(A,tM)};M8.ElementStack.prototype.elementInScope=function(A){return this.elementInSpecificScope(A,tM)};M8.ElementStack.prototype.elementTypeInScope=function(A){return this.elementTypeInSpecificScope(A,tM)};M8.ElementStack.prototype.inButtonScope=function(A){return this.inSpecificScope(A,QM0)};M8.ElementStack.prototype.inListItemScope=function(A){return this.inSpecificScope(A,Ef1)};M8.ElementStack.prototype.inTableScope=function(A){return this.inSpecificScope(A,O_B)};M8.ElementStack.prototype.inSelectScope=function(A){for(var B=this.elements.length-1;B>=0;B--){var Q=this.elements[B];if(Q.namespaceURI!==l9.HTML)return!1;var D=Q.localName;if(D===A)return!0;if(D!=="optgroup"&&D!=="option")return!1}return!1};M8.ElementStack.prototype.generateImpliedEndTags=function(A,B){var Q=B?M_B:L_B;for(var D=this.elements.length-1;D>=0;D--){var Z=this.elements[D];if(A&&j5(Z,A))break;if(!j5(this.elements[D],Q))break}this.elements.length=D+1,this.top=this.elements[D]};M8.ActiveFormattingElements=function A(){this.list=[],this.attrs=[]};M8.ActiveFormattingElements.prototype.MARKER={localName:"|"};M8.ActiveFormattingElements.prototype.insertMarker=function(){this.list.push(this.MARKER),this.attrs.push(this.MARKER)};M8.ActiveFormattingElements.prototype.push=function(A,B){var Q=0;for(var D=this.list.length-1;D>=0;D--){if(this.list[D]===this.MARKER)break;if(F(A,this.list[D],this.attrs[D])){if(Q++,Q===3){this.list.splice(D,1),this.attrs.splice(D,1);break}}}this.list.push(A);var Z=[];for(var G=0;G<B.length;G++)Z[G]=B[G];this.attrs.push(Z);function F(I,Y,W){if(I.localName!==Y.localName)return!1;if(I._numattrs!==W.length)return!1;for(var J=0,X=W.length;J<X;J++){var V=W[J][0],C=W[J][1];if(!I.hasAttribute(V))return!1;if(I.getAttribute(V)!==C)return!1}return!0}};M8.ActiveFormattingElements.prototype.clearToMarker=function(){for(var A=this.list.length-1;A>=0;A--)if(this.list[A]===this.MARKER)break;if(A<0)A=0;this.list.length=A,this.attrs.length=A};M8.ActiveFormattingElements.prototype.findElementByTag=function(A){for(var B=this.list.length-1;B>=0;B--){var Q=this.list[B];if(Q===this.MARKER)break;if(Q.localName===A)return Q}return null};M8.ActiveFormattingElements.prototype.indexOf=function(A){return this.list.lastIndexOf(A)};M8.ActiveFormattingElements.prototype.remove=function(A){var B=this.list.lastIndexOf(A);if(B!==-1)this.list.splice(B,1),this.attrs.splice(B,1)};M8.ActiveFormattingElements.prototype.replace=function(A,B,Q){var D=this.list.lastIndexOf(A);if(D!==-1)this.list[D]=B,this.attrs[D]=Q};M8.ActiveFormattingElements.prototype.insertAfter=function(A,B){var Q=this.list.lastIndexOf(A);if(Q!==-1)this.list.splice(Q,0,B),this.attrs.splice(Q,0,B)};function M8(A,B,Q){var D=null,Z=0,G=0,F=!1,I=!1,Y=0,W=[],J="",X=!0,V=0,C=Q0,K,H,z="",$="",L=[],N="",O="",R=[],T=[],j=[],f=[],k=[],c=!1,h=CW,n=null,a=[],x=new M8.ElementStack,e=new M8.ActiveFormattingElements,W1=B!==void 0,U1=null,y1=null,W0=!0;if(B)W0=B.ownerDocument._scripting_enabled;if(Q&&Q.scripting_enabled===!1)W0=!1;var F0=!0,g1=!1,K1,G1,L1=[],M1=!1,a1=!1,i1={document:function(){return E0},_asDocumentFragment:function(){var Z1=E0.createDocumentFragment(),Y1=E0.firstChild;while(Y1.hasChildNodes())Z1.appendChild(Y1.firstChild);return Z1},pause:function(){V++},resume:function(){V--,this.parse("")},parse:function(Z1,Y1,e1){var l0;if(V>0)return J+=Z1,!0;if(Y===0){if(J)Z1=J+Z1,J="";if(Y1)Z1+="￿",F=!0;if(D=Z1,Z=Z1.length,G=0,X){if(X=!1,D.charCodeAt(0)===65279)G=1}Y++,l0=I1(e1),J=D.substring(G,Z),Y--}else{if(Y++,W.push(D,Z,G),D=Z1,Z=Z1.length,G=0,I1(),l0=!1,J=D.substring(G,Z),G=W.pop(),Z=W.pop(),D=W.pop(),J)D=J+D.substring(G),Z=D.length,G=0,J="";Y--}return l0}},E0=new rX8(!0,A);if(E0._parser=i1,E0._scripting_enabled=W0,B){if(B.ownerDocument._quirks)E0._quirks=!0;if(B.ownerDocument._limitedQuirks)E0._limitedQuirks=!0;if(B.namespaceURI===l9.HTML)switch(B.localName){case"title":case"textarea":C=u0;break;case"style":case"xmp":case"iframe":case"noembed":case"noframes":case"script":case"plaintext":C=lB;break}var B1=E0.createElement("html");if(E0._appendChild(B1),x.push(B1),B instanceof S5.HTMLTemplateElement)a.push(RD);U5();for(var A1=B;A1!==null;A1=A1.parentElement)if(A1 instanceof S5.HTMLFormElement){y1=A1;break}}function I1(Z1){var Y1,e1,l0,DA;while(G<Z){if(V>0||Z1&&Z1())return!0;switch(typeof C.lookahead){case"undefined":if(Y1=D.charCodeAt(G++),I){if(I=!1,Y1===10){G++;continue}}switch(Y1){case 13:if(G<Z){if(D.charCodeAt(G)===10)G++}else I=!0;C(10);break;case 65535:if(F&&G===Z){C(Vf1);break}default:C(Y1);break}break;case"number":Y1=D.charCodeAt(G);var C2=C.lookahead,F9=!0;if(C2<0)F9=!1,C2=-C2;if(C2<Z-G)e1=F9?D.substring(G,G+C2):null,DA=!1;else if(F){if(e1=F9?D.substring(G,Z):null,DA=!0,Y1===65535&&G===Z-1)Y1=Vf1}else return!0;C(Y1,e1,DA);break;case"string":Y1=D.charCodeAt(G),l0=C.lookahead;var IQ=D.indexOf(l0,G);if(IQ!==-1)e1=D.substring(G,IQ+l0.length),DA=!1;else{if(!F)return!0;if(e1=D.substring(G,Z),Y1===65535&&G===Z-1)Y1=Vf1;DA=!0}C(Y1,e1,DA);break}}return!1}function q1(Z1,Y1){for(var e1=0;e1<k.length;e1++)if(k[e1][0]===Z1)return;if(Y1!==void 0)k.push([Z1,Y1]);else k.push([Z1])}function P1(){H_B.lastIndex=G-1;var Z1=H_B.exec(D);if(!Z1)throw new Error("should never happen");var Y1=Z1[1];if(!Y1)return!1;var e1=Z1[2],l0=e1.length;switch(e1[0]){case'"':case"'":e1=e1.substring(1,l0-1),G+=Z1[0].length-1,C=HA;break;default:C=C0,G+=Z1[0].length-1,e1=e1.substring(0,l0-1);break}for(var DA=0;DA<k.length;DA++)if(k[DA][0]===Y1)return!0;return k.push([Y1,e1]),!0}function Q1(){c=!1,z="",k.length=0}function f1(){c=!0,z="",k.length=0}function l1(){L.length=0}function n1(){N=""}function V0(){O=""}function I0(){R.length=0}function M0(){T.length=0,j=null,f=null}function YA(){j=[]}function m0(){f=[]}function SA(){g1=!0}function v2(){return x.top&&x.top.namespaceURI!=="http://www.w3.org/1999/xhtml"}function Y2(Z1){return $===Z1}function N2(){if(L1.length>0){var Z1=VJ(L1);if(L1.length=0,a1){if(a1=!1,Z1[0]===`
`)Z1=Z1.substring(1);if(Z1.length===0)return}r2(h01,Z1),M1=!1}a1=!1}function b2(Z1){Z1.lastIndex=G-1;var Y1=Z1.exec(D);if(Y1&&Y1.index===G-1){if(Y1=Y1[0],G+=Y1.length-1,F&&G===Z)Y1=Y1.slice(0,-1),G--;return Y1}else throw new Error("should never happen")}function _B(Z1){Z1.lastIndex=G-1;var Y1=Z1.exec(D)[0];if(!Y1)return!1;return W4(Y1),G+=Y1.length-1,!0}function W4(Z1){if(L1.length>0)N2();if(a1){if(a1=!1,Z1[0]===`
`)Z1=Z1.substring(1);if(Z1.length===0)return}r2(h01,Z1)}function gA(){if(c)r2(H6,z);else{var Z1=z;z="",$=Z1,r2(XJ,Z1,k)}}function X2(){if(G===Z)return!1;K_B.lastIndex=G;var Z1=K_B.exec(D);if(!Z1)throw new Error("should never happen");var Y1=Z1[2];if(!Y1)return!1;var e1=Z1[1];if(e1)G+=Y1.length+2,r2(H6,Y1);else G+=Y1.length+1,$=Y1,r2(XJ,Y1,eX8);return!0}function L2(){if(c)r2(H6,z,null,!0);else r2(XJ,z,k,!0)}function lA(){r2(tX8,VJ(T),j?VJ(j):void 0,f?VJ(f):void 0)}function uA(){N2(),h(Vf1),E0.modclock=1}var r2=i1.insertToken=function Z1(Y1,e1,l0,DA){N2();var C2=x.top;if(!C2||C2.namespaceURI===l9.HTML)h(Y1,e1,l0,DA);else if(Y1!==XJ&&Y1!==h01)SX(Y1,e1,l0,DA);else if(z_B(C2)&&(Y1===h01||Y1===XJ&&e1!=="mglyph"&&e1!=="malignmark")||Y1===XJ&&e1==="svg"&&C2.namespaceURI===l9.MATHML&&C2.localName==="annotation-xml"||E_B(C2))G1=!0,h(Y1,e1,l0,DA),G1=!1;else SX(Y1,e1,l0,DA)};function gB(Z1){var Y1=x.top;if(O4&&j5(Y1,g01))O8(function(e1){return e1.createComment(Z1)});else{if(Y1 instanceof S5.HTMLTemplateElement)Y1=Y1.content;Y1._appendChild(Y1.ownerDocument.createComment(Z1))}}function g6(Z1){var Y1=x.top;if(O4&&j5(Y1,g01))O8(function(l0){return l0.createTextNode(Z1)});else{if(Y1 instanceof S5.HTMLTemplateElement)Y1=Y1.content;var e1=Y1.lastChild;if(e1&&e1.nodeType===oL0.TEXT_NODE)e1.appendData(Z1);else Y1._appendChild(Y1.ownerDocument.createTextNode(Z1))}}function k7(Z1,Y1,e1){var l0=q_B.createElement(Z1,Y1,null);if(e1)for(var DA=0,C2=e1.length;DA<C2;DA++)l0._setAttribute(e1[DA][0],e1[DA][1]);return l0}var O4=!1;function GB(Z1,Y1){var e1=T4(function(l0){return k7(l0,Z1,Y1)});if(j5(e1,R_B))e1._form=y1;return e1}function T4(Z1){var Y1;if(O4&&j5(x.top,g01))Y1=O8(Z1);else if(x.top instanceof S5.HTMLTemplateElement)Y1=Z1(x.top.content.ownerDocument),x.top.content._appendChild(Y1);else Y1=Z1(x.top.ownerDocument),x.top._appendChild(Y1);return x.push(Y1),Y1}function d3(Z1,Y1,e1){return T4(function(l0){var DA=l0._createElementNS(Z1,e1,null);if(Y1)for(var C2=0,F9=Y1.length;C2<F9;C2++){var IQ=Y1[C2];if(IQ.length===2)DA._setAttribute(IQ[0],IQ[1]);else DA._setAttributeNS(IQ[2],IQ[0],IQ[1])}return DA})}function a5(Z1){for(var Y1=x.elements.length-1;Y1>=0;Y1--)if(x.elements[Y1]instanceof Z1)return Y1;return-1}function O8(Z1){var Y1,e1,l0=-1,DA=-1,C2;if(l0=a5(S5.HTMLTableElement),DA=a5(S5.HTMLTemplateElement),DA>=0&&(l0<0||DA>l0))Y1=x.elements[DA];else if(l0>=0)if(Y1=x.elements[l0].parentNode,Y1)e1=x.elements[l0];else Y1=x.elements[l0-1];if(!Y1)Y1=x.elements[0];if(Y1 instanceof S5.HTMLTemplateElement)Y1=Y1.content;if(C2=Z1(Y1.ownerDocument),C2.nodeType===oL0.TEXT_NODE){var F9;if(e1)F9=e1.previousSibling;else F9=Y1.lastChild;if(F9&&F9.nodeType===oL0.TEXT_NODE)return F9.appendData(C2.data),C2}if(e1)Y1.insertBefore(C2,e1);else Y1._appendChild(C2);return C2}function U5(){var Z1=!1;for(var Y1=x.elements.length-1;Y1>=0;Y1--){var e1=x.elements[Y1];if(Y1===0){if(Z1=!0,W1)e1=B}if(e1.namespaceURI===l9.HTML){var l0=e1.localName;switch(l0){case"select":for(var DA=Y1;DA>0;){var C2=x.elements[--DA];if(C2 instanceof S5.HTMLTemplateElement)break;else if(C2 instanceof S5.HTMLTableElement){h=$U;return}}h=DF;return;case"tr":h=U6;return;case"tbody":case"tfoot":case"thead":h=KW;return;case"caption":h=XG;return;case"colgroup":h=b7;return;case"table":h=_5;return;case"template":h=a[a.length-1];return;case"body":h=H9;return;case"frameset":h=EJ;return;case"html":if(U1===null)h=v7;else h=Y8;return;default:if(!Z1){if(l0==="head"){h=P4;return}if(l0==="td"||l0==="th"){h=wZ;return}}}}if(Z1){h=H9;return}}}function s5(Z1,Y1){GB(Z1,Y1),C=i0,n=h,h=E6}function y7(Z1,Y1){GB(Z1,Y1),C=u0,n=h,h=E6}function _7(Z1,Y1){return{elt:k7(Z1,e.list[Y1].localName,e.attrs[Y1]),attrs:e.attrs[Y1]}}function pA(){if(e.list.length===0)return;var Z1=e.list[e.list.length-1];if(Z1===e.MARKER)return;if(x.elements.lastIndexOf(Z1)!==-1)return;for(var Y1=e.list.length-2;Y1>=0;Y1--){if(Z1=e.list[Y1],Z1===e.MARKER)break;if(x.elements.lastIndexOf(Z1)!==-1)break}for(Y1=Y1+1;Y1<e.list.length;Y1++){var e1=T4(function(l0){return _7(l0,Y1).elt});e.list[Y1]=e1}}var V2={localName:"BM"};function _9(Z1){if(j5(x.top,Z1)&&e.indexOf(x.top)===-1)return x.pop(),!0;var Y1=0;while(Y1<8){Y1++;var e1=e.findElementByTag(Z1);if(!e1)return!1;var l0=x.elements.lastIndexOf(e1);if(l0===-1)return e.remove(e1),!0;if(!x.elementInScope(e1))return!0;var DA=null,C2;for(var F9=l0+1;F9<x.elements.length;F9++)if(j5(x.elements[F9],zd)){DA=x.elements[F9],C2=F9;break}if(!DA)return x.popElement(e1),e.remove(e1),!0;else{var IQ=x.elements[l0-1];e.insertAfter(e1,V2);var w6=DA,z3=DA,pD=C2,W8,lI=0;while(!0){if(lI++,w6=x.elements[--pD],w6===e1)break;if(W8=e.indexOf(w6),lI>3&&W8!==-1)e.remove(w6),W8=-1;if(W8===-1){x.removeElement(w6);continue}var $Z=_7(IQ.ownerDocument,W8);if(e.replace(w6,$Z.elt,$Z.attrs),x.elements[pD]=$Z.elt,w6=$Z.elt,z3===DA)e.remove(V2),e.insertAfter($Z.elt,V2);w6._appendChild(z3),z3=w6}if(O4&&j5(IQ,g01))O8(function(){return z3});else if(IQ instanceof S5.HTMLTemplateElement)IQ.content._appendChild(z3);else IQ._appendChild(z3);var pI=_7(DA.ownerDocument,e.indexOf(e1));while(DA.hasChildNodes())pI.elt._appendChild(DA.firstChild);DA._appendChild(pI.elt),e.remove(e1),e.replace(V2,pI.elt,pI.attrs),x.removeElement(e1);var ZF=x.elements.lastIndexOf(DA);x.elements.splice(ZF+1,0,pI.elt)}}return!0}function w5(){x.pop(),h=n;return}function Y0(){if(delete E0._parser,x.elements.length=0,E0.defaultView)E0.defaultView.dispatchEvent(new S5.Event("load",{}))}function k1(Z1,Y1){C=Y1,G--}function Q0(Z1){switch(Z1){case 38:K=Q0,C=FH;break;case 60:if(X2())break;C=x9;break;case 0:L1.push(Z1),M1=!0;break;case-1:uA();break;default:_B(VV8)||L1.push(Z1);break}}function u0(Z1){switch(Z1){case 38:K=u0,C=FH;break;case 60:C=xB;break;case 0:L1.push(65533),M1=!0;break;case-1:uA();break;default:L1.push(Z1);break}}function i0(Z1){switch(Z1){case 60:C=oQ;break;case 0:L1.push(65533);break;case-1:uA();break;default:_B(C_B)||L1.push(Z1);break}}function mA(Z1){switch(Z1){case 60:C=_1;break;case 0:L1.push(65533);break;case-1:uA();break;default:_B(C_B)||L1.push(Z1);break}}function lB(Z1){switch(Z1){case 0:L1.push(65533);break;case-1:uA();break;default:_B(CV8)||L1.push(Z1);break}}function x9(Z1){switch(Z1){case 33:C=vB;break;case 47:C=zQ;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:Q1(),k1(Z1,q4);break;case 63:k1(Z1,h2);break;default:L1.push(60),k1(Z1,Q0);break}}function zQ(Z1){switch(Z1){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:f1(),k1(Z1,q4);break;case 62:C=Q0;break;case-1:L1.push(60),L1.push(47),uA();break;default:k1(Z1,h2);break}}function q4(Z1){switch(Z1){case 9:case 10:case 12:case 32:C=C0;break;case 47:C=iA;break;case 62:C=Q0,gA();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:z+=String.fromCharCode(Z1+32);break;case 0:z+=String.fromCharCode(65533);break;case-1:uA();break;default:z+=b2(WV8);break}}function xB(Z1){if(Z1===47)l1(),C=$Q;else L1.push(60),k1(Z1,u0)}function $Q(Z1){switch(Z1){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:f1(),k1(Z1,z6);break;default:L1.push(60),L1.push(47),k1(Z1,u0);break}}function z6(Z1){switch(Z1){case 9:case 10:case 12:case 32:if(Y2(z)){C=C0;return}break;case 47:if(Y2(z)){C=iA;return}break;case 62:if(Y2(z)){C=Q0,gA();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:z+=String.fromCharCode(Z1+32),L.push(Z1);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:z+=String.fromCharCode(Z1),L.push(Z1);return;default:break}L1.push(60),L1.push(47),Kd(L1,L),k1(Z1,u0)}function oQ(Z1){if(Z1===47)l1(),C=U9;else L1.push(60),k1(Z1,i0)}function U9(Z1){switch(Z1){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:f1(),k1(Z1,J4);break;default:L1.push(60),L1.push(47),k1(Z1,i0);break}}function J4(Z1){switch(Z1){case 9:case 10:case 12:case 32:if(Y2(z)){C=C0;return}break;case 47:if(Y2(z)){C=iA;return}break;case 62:if(Y2(z)){C=Q0,gA();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:z+=String.fromCharCode(Z1+32),L.push(Z1);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:z+=String.fromCharCode(Z1),L.push(Z1);return;default:break}L1.push(60),L1.push(47),Kd(L1,L),k1(Z1,i0)}function _1(Z1){switch(Z1){case 47:l1(),C=u1;break;case 33:C=y0,L1.push(60),L1.push(33);break;default:L1.push(60),k1(Z1,mA);break}}function u1(Z1){switch(Z1){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:f1(),k1(Z1,q0);break;default:L1.push(60),L1.push(47),k1(Z1,mA);break}}function q0(Z1){switch(Z1){case 9:case 10:case 12:case 32:if(Y2(z)){C=C0;return}break;case 47:if(Y2(z)){C=iA;return}break;case 62:if(Y2(z)){C=Q0,gA();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:z+=String.fromCharCode(Z1+32),L.push(Z1);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:z+=String.fromCharCode(Z1),L.push(Z1);return;default:break}L1.push(60),L1.push(47),Kd(L1,L),k1(Z1,mA)}function y0(Z1){if(Z1===45)C=U0,L1.push(45);else k1(Z1,mA)}function U0(Z1){if(Z1===45)C=ZA,L1.push(45);else k1(Z1,mA)}function v0(Z1){switch(Z1){case 45:C=EA,L1.push(45);break;case 60:C=VA;break;case 0:L1.push(65533);break;case-1:uA();break;default:L1.push(Z1);break}}function EA(Z1){switch(Z1){case 45:C=ZA,L1.push(45);break;case 60:C=VA;break;case 0:C=v0,L1.push(65533);break;case-1:uA();break;default:C=v0,L1.push(Z1);break}}function ZA(Z1){switch(Z1){case 45:L1.push(45);break;case 60:C=VA;break;case 62:C=mA,L1.push(62);break;case 0:C=v0,L1.push(65533);break;case-1:uA();break;default:C=v0,L1.push(Z1);break}}function VA(Z1){switch(Z1){case 47:l1(),C=AA;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:l1(),L1.push(60),k1(Z1,uB);break;default:L1.push(60),k1(Z1,v0);break}}function AA(Z1){switch(Z1){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:f1(),k1(Z1,UA);break;default:L1.push(60),L1.push(47),k1(Z1,v0);break}}function UA(Z1){switch(Z1){case 9:case 10:case 12:case 32:if(Y2(z)){C=C0;return}break;case 47:if(Y2(z)){C=iA;return}break;case 62:if(Y2(z)){C=Q0,gA();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:z+=String.fromCharCode(Z1+32),L.push(Z1);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:z+=String.fromCharCode(Z1),L.push(Z1);return;default:break}L1.push(60),L1.push(47),Kd(L1,L),k1(Z1,v0)}function uB(Z1){switch(Z1){case 9:case 10:case 12:case 32:case 47:case 62:if(VJ(L)==="script")C=f2;else C=v0;L1.push(Z1);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:L.push(Z1+32),L1.push(Z1);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:L.push(Z1),L1.push(Z1);break;default:k1(Z1,v0);break}}function f2(Z1){switch(Z1){case 45:C=HB,L1.push(45);break;case 60:C=t1,L1.push(60);break;case 0:L1.push(65533);break;case-1:uA();break;default:L1.push(Z1);break}}function HB(Z1){switch(Z1){case 45:C=E1,L1.push(45);break;case 60:C=t1,L1.push(60);break;case 0:C=f2,L1.push(65533);break;case-1:uA();break;default:C=f2,L1.push(Z1);break}}function E1(Z1){switch(Z1){case 45:L1.push(45);break;case 60:C=t1,L1.push(60);break;case 62:C=mA,L1.push(62);break;case 0:C=f2,L1.push(65533);break;case-1:uA();break;default:C=f2,L1.push(Z1);break}}function t1(Z1){if(Z1===47)l1(),C=d1,L1.push(47);else k1(Z1,f2)}function d1(Z1){switch(Z1){case 9:case 10:case 12:case 32:case 47:case 62:if(VJ(L)==="script")C=v0;else C=f2;L1.push(Z1);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:L.push(Z1+32),L1.push(Z1);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:L.push(Z1),L1.push(Z1);break;default:k1(Z1,f2);break}}function C0(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 47:C=iA;break;case 62:C=Q0,gA();break;case-1:uA();break;case 61:n1(),N+=String.fromCharCode(Z1),C=L0;break;default:if(P1())break;n1(),k1(Z1,L0);break}}function L0(Z1){switch(Z1){case 9:case 10:case 12:case 32:case 47:case 62:case-1:k1(Z1,$0);break;case 61:C=QA;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:N+=String.fromCharCode(Z1+32);break;case 0:N+=String.fromCharCode(65533);break;case 34:case 39:case 60:default:N+=b2(JV8);break}}function $0(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 47:q1(N),C=iA;break;case 61:C=QA;break;case 62:C=Q0,q1(N),gA();break;case-1:q1(N),uA();break;default:q1(N),n1(),k1(Z1,L0);break}}function QA(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 34:V0(),C=h0;break;case 39:V0(),C=e0;break;case 62:default:V0(),k1(Z1,XA);break}}function h0(Z1){switch(Z1){case 34:q1(N,O),C=HA;break;case 38:K=h0,C=FH;break;case 0:O+=String.fromCharCode(65533);break;case-1:uA();break;case 10:O+=String.fromCharCode(Z1);break;default:O+=b2(FV8);break}}function e0(Z1){switch(Z1){case 39:q1(N,O),C=HA;break;case 38:K=e0,C=FH;break;case 0:O+=String.fromCharCode(65533);break;case-1:uA();break;case 10:O+=String.fromCharCode(Z1);break;default:O+=b2(IV8);break}}function XA(Z1){switch(Z1){case 9:case 10:case 12:case 32:q1(N,O),C=C0;break;case 38:K=XA,C=FH;break;case 62:q1(N,O),C=Q0,gA();break;case 0:O+=String.fromCharCode(65533);break;case-1:G--,C=Q0;break;case 34:case 39:case 60:case 61:case 96:default:O+=b2(YV8);break}}function HA(Z1){switch(Z1){case 9:case 10:case 12:case 32:C=C0;break;case 47:C=iA;break;case 62:C=Q0,gA();break;case-1:uA();break;default:k1(Z1,C0);break}}function iA(Z1){switch(Z1){case 62:C=Q0,L2(!0);break;case-1:uA();break;default:k1(Z1,C0);break}}function h2(Z1,Y1,e1){var l0=Y1.length;if(e1)G+=l0-1;else G+=l0;var DA=Y1.substring(0,l0-1);DA=DA.replace(/\u0000/g,"�"),DA=DA.replace(/\u000D\u000A/g,`
`),DA=DA.replace(/\u000D/g,`
`),r2(oM,DA),C=Q0}h2.lookahead=">";function vB(Z1,Y1,e1){if(Y1[0]==="-"&&Y1[1]==="-"){G+=2,I0(),C=v9;return}if(Y1.toUpperCase()==="DOCTYPE")G+=7,C=SQ;else if(Y1==="[CDATA["&&v2())G+=7,C=VW;else C=h2}vB.lookahead=7;function v9(Z1){switch(I0(),Z1){case 45:C=FQ;break;case 62:C=Q0,r2(oM,VJ(R));break;default:k1(Z1,qQ);break}}function FQ(Z1){switch(Z1){case 45:C=BF;break;case 62:C=Q0,r2(oM,VJ(R));break;case-1:r2(oM,VJ(R)),uA();break;default:R.push(45),k1(Z1,qQ);break}}function qQ(Z1){switch(Z1){case 60:R.push(Z1),C=o8;break;case 45:C=y5;break;case 0:R.push(65533);break;case-1:r2(oM,VJ(R)),uA();break;default:R.push(Z1);break}}function o8(Z1){switch(Z1){case 33:R.push(Z1),C=u6;break;case 60:R.push(Z1);break;default:k1(Z1,qQ);break}}function u6(Z1){switch(Z1){case 45:C=A6;break;default:k1(Z1,qQ);break}}function A6(Z1){switch(Z1){case 45:C=lD;break;default:k1(Z1,y5);break}}function lD(Z1){switch(Z1){case 62:case-1:k1(Z1,BF);break;default:k1(Z1,BF);break}}function y5(Z1){switch(Z1){case 45:C=BF;break;case-1:r2(oM,VJ(R)),uA();break;default:R.push(45),k1(Z1,qQ);break}}function BF(Z1){switch(Z1){case 62:C=Q0,r2(oM,VJ(R));break;case 33:C=uF;break;case 45:R.push(45);break;case-1:r2(oM,VJ(R)),uA();break;default:R.push(45),R.push(45),k1(Z1,qQ);break}}function uF(Z1){switch(Z1){case 45:R.push(45),R.push(45),R.push(33),C=y5;break;case 62:C=Q0,r2(oM,VJ(R));break;case-1:r2(oM,VJ(R)),uA();break;default:R.push(45),R.push(45),R.push(33),k1(Z1,qQ);break}}function SQ(Z1){switch(Z1){case 9:case 10:case 12:case 32:C=JG;break;case-1:M0(),SA(),lA(),uA();break;default:k1(Z1,JG);break}}function JG(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:M0(),T.push(Z1+32),C=dI;break;case 0:M0(),T.push(65533),C=dI;break;case 62:M0(),SA(),C=Q0,lA();break;case-1:M0(),SA(),lA(),uA();break;default:M0(),T.push(Z1),C=dI;break}}function dI(Z1){switch(Z1){case 9:case 10:case 12:case 32:C=GH;break;case 62:C=Q0,lA();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:T.push(Z1+32);break;case 0:T.push(65533);break;case-1:SA(),lA(),uA();break;default:T.push(Z1);break}}function GH(Z1,Y1,e1){switch(Z1){case 9:case 10:case 12:case 32:G+=1;break;case 62:C=Q0,G+=1,lA();break;case-1:SA(),lA(),uA();break;default:if(Y1=Y1.toUpperCase(),Y1==="PUBLIC")G+=6,C=YR;else if(Y1==="SYSTEM")G+=6,C=Bb;else SA(),C=x7;break}}GH.lookahead=6;function YR(Z1){switch(Z1){case 9:case 10:case 12:case 32:C=HU;break;case 34:YA(),C=c3;break;case 39:YA(),C=zU;break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:SA(),C=x7;break}}function HU(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 34:YA(),C=c3;break;case 39:YA(),C=zU;break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:SA(),C=x7;break}}function c3(Z1){switch(Z1){case 34:C=WR;break;case 0:j.push(65533);break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:j.push(Z1);break}}function zU(Z1){switch(Z1){case 39:C=WR;break;case 0:j.push(65533);break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:j.push(Z1);break}}function WR(Z1){switch(Z1){case 9:case 10:case 12:case 32:C=t$;break;case 62:C=Q0,lA();break;case 34:m0(),C=uQ;break;case 39:m0(),C=EU;break;case-1:SA(),lA(),uA();break;default:SA(),C=x7;break}}function t$(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 62:C=Q0,lA();break;case 34:m0(),C=uQ;break;case 39:m0(),C=EU;break;case-1:SA(),lA(),uA();break;default:SA(),C=x7;break}}function Bb(Z1){switch(Z1){case 9:case 10:case 12:case 32:C=m6;break;case 34:m0(),C=uQ;break;case 39:m0(),C=EU;break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:SA(),C=x7;break}}function m6(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 34:m0(),C=uQ;break;case 39:m0(),C=EU;break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:SA(),C=x7;break}}function uQ(Z1){switch(Z1){case 34:C=kS;break;case 0:f.push(65533);break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:f.push(Z1);break}}function EU(Z1){switch(Z1){case 39:C=kS;break;case 0:f.push(65533);break;case 62:SA(),C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:f.push(Z1);break}}function kS(Z1){switch(Z1){case 9:case 10:case 12:case 32:break;case 62:C=Q0,lA();break;case-1:SA(),lA(),uA();break;default:C=x7;break}}function x7(Z1){switch(Z1){case 62:C=Q0,lA();break;case-1:lA(),uA();break;default:break}}function VW(Z1){switch(Z1){case 93:C=JR;break;case-1:uA();break;case 0:M1=!0;default:_B(XV8)||L1.push(Z1);break}}function JR(Z1){switch(Z1){case 93:C=yS;break;default:L1.push(93),k1(Z1,VW);break}}function yS(Z1){switch(Z1){case 93:L1.push(93);break;case 62:N2(),C=Q0;break;default:L1.push(93),L1.push(93),k1(Z1,VW);break}}function FH(Z1){switch(l1(),L.push(38),Z1){case 9:case 10:case 12:case 32:case 60:case 38:case-1:k1(Z1,d6);break;case 35:L.push(Z1),C=T8;break;default:k1(Z1,_S);break}}function _S(Z1){V_B.lastIndex=G;var Y1=V_B.exec(D);if(!Y1)throw new Error("should never happen");var e1=Y1[1];if(!e1){C=d6;return}switch(G+=e1.length,Kd(L,HV8(e1)),K){case h0:case e0:case XA:if(e1[e1.length-1]!==";"){if(/[=A-Za-z0-9]/.test(D[G])){C=d6;return}}break;default:break}l1();var l0=ZV8[e1];if(typeof l0==="number")L.push(l0);else Kd(L,l0);C=d6}_S.lookahead=-GV8;function T8(Z1){switch(H=0,Z1){case 120:case 88:L.push(Z1),C=VC;break;default:k1(Z1,QF);break}}function VC(Z1){switch(Z1){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 66:case 67:case 68:case 69:case 70:case 97:case 98:case 99:case 100:case 101:case 102:k1(Z1,UU);break;default:k1(Z1,d6);break}}function QF(Z1){switch(Z1){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:k1(Z1,wU);break;default:k1(Z1,d6);break}}function UU(Z1){switch(Z1){case 65:case 66:case 67:case 68:case 69:case 70:H*=16,H+=Z1-55;break;case 97:case 98:case 99:case 100:case 101:case 102:H*=16,H+=Z1-87;break;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:H*=16,H+=Z1-48;break;case 59:C=t8;break;default:k1(Z1,t8);break}}function wU(Z1){switch(Z1){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:H*=10,H+=Z1-48;break;case 59:C=t8;break;default:k1(Z1,t8);break}}function t8(Z1){if(H in X_B)H=X_B[H];else if(H>1114111||H>=55296&&H<57344)H=65533;if(l1(),H<=65535)L.push(H);else H=H-65536,L.push(55296+(H>>10)),L.push(56320+(H&1023));k1(Z1,d6)}function d6(Z1){switch(K){case h0:case e0:case XA:O+=VJ(L);break;default:Kd(L1,L);break}k1(Z1,K)}function CW(Z1,Y1,e1,l0){switch(Z1){case 1:if(Y1=Y1.replace(Hd,""),Y1.length===0)return;break;case 4:E0._appendChild(E0.createComment(Y1));return;case 5:var DA=Y1,C2=e1,F9=l0;if(E0.appendChild(new oX8(E0,DA,C2,F9)),g1||DA.toLowerCase()!=="html"||AV8.test(C2)||F9&&F9.toLowerCase()===BV8||F9===void 0&&I_B.test(C2))E0._quirks=!0;else if(QV8.test(C2)||F9!==void 0&&I_B.test(C2))E0._limitedQuirks=!0;h=H7;return}E0._quirks=!0,h=H7,h(Z1,Y1,e1,l0)}function H7(Z1,Y1,e1,l0){var DA;switch(Z1){case 1:if(Y1=Y1.replace(Hd,""),Y1.length===0)return;break;case 5:return;case 4:E0._appendChild(E0.createComment(Y1));return;case 2:if(Y1==="html"){DA=k7(E0,Y1,e1),x.push(DA),E0.appendChild(DA),h=v7;return}break;case 3:switch(Y1){case"html":case"head":case"body":case"br":break;default:return}}DA=k7(E0,"html",null),x.push(DA),E0.appendChild(DA),h=v7,h(Z1,Y1,e1,l0)}function v7(Z1,Y1,e1,l0){switch(Z1){case 1:if(Y1=Y1.replace(Hd,""),Y1.length===0)return;break;case 5:return;case 4:gB(Y1);return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"head":var DA=GB(Y1,e1);U1=DA,h=P4;return}break;case 3:switch(Y1){case"html":case"head":case"body":case"br":break;default:return}}v7(XJ,"head",null),h(Z1,Y1,e1,l0)}function P4(Z1,Y1,e1,l0){switch(Z1){case 1:var DA=Y1.match(Hd);if(DA)g6(DA[0]),Y1=Y1.substring(DA[0].length);if(Y1.length===0)return;break;case 4:gB(Y1);return;case 5:return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"meta":case"base":case"basefont":case"bgsound":case"link":GB(Y1,e1),x.pop();return;case"title":y7(Y1,e1);return;case"noscript":if(!W0){GB(Y1,e1),h=zJ;return}case"noframes":case"style":s5(Y1,e1);return;case"script":T4(function(C2){var F9=k7(C2,Y1,e1);if(F9._parser_inserted=!0,F9._force_async=!1,W1)F9._already_started=!0;return N2(),F9}),C=mA,n=h,h=E6;return;case"template":GB(Y1,e1),e.insertMarker(),F0=!1,h=RD,a.push(h);return;case"head":return}break;case 3:switch(Y1){case"head":x.pop(),h=Y8;return;case"body":case"html":case"br":break;case"template":if(!x.contains("template"))return;x.generateImpliedEndTags(null,"thorough"),x.popTag("template"),e.clearToMarker(),a.pop(),U5();return;default:return}break}P4(H6,"head",null),h(Z1,Y1,e1,l0)}function zJ(Z1,Y1,e1,l0){switch(Z1){case 5:return;case 4:P4(Z1,Y1);return;case 1:var DA=Y1.match(Hd);if(DA)P4(Z1,DA[0]),Y1=Y1.substring(DA[0].length);if(Y1.length===0)return;break;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"style":P4(Z1,Y1,e1);return;case"head":case"noscript":return}break;case 3:switch(Y1){case"noscript":x.pop(),h=P4;return;case"br":break;default:return}break}zJ(H6,"noscript",null),h(Z1,Y1,e1,l0)}function Y8(Z1,Y1,e1,l0){switch(Z1){case 1:var DA=Y1.match(Hd);if(DA)g6(DA[0]),Y1=Y1.substring(DA[0].length);if(Y1.length===0)return;break;case 4:gB(Y1);return;case 5:return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"body":GB(Y1,e1),F0=!1,h=H9;return;case"frameset":GB(Y1,e1),h=EJ;return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":x.push(U1),P4(XJ,Y1,e1),x.removeElement(U1);return;case"head":return}break;case 3:switch(Y1){case"template":return P4(Z1,Y1,e1,l0);case"body":case"html":case"br":break;default:return}break}Y8(XJ,"body",null),F0=!0,h(Z1,Y1,e1,l0)}function H9(Z1,Y1,e1,l0){var DA,C2,F9,IQ;switch(Z1){case 1:if(M1){if(Y1=Y1.replace(Kf1,""),Y1.length===0)return}if(F0&&Cf1.test(Y1))F0=!1;pA(),g6(Y1);return;case 5:return;case 4:gB(Y1);return;case-1:if(a.length)return RD(Z1);Y0();return;case 2:switch(Y1){case"html":if(x.contains("template"))return;$_B(e1,x.elements[0]);return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":P4(XJ,Y1,e1);return;case"body":if(DA=x.elements[1],!DA||!(DA instanceof S5.HTMLBodyElement)||x.contains("template"))return;F0=!1,$_B(e1,DA);return;case"frameset":if(!F0)return;if(DA=x.elements[1],!DA||!(DA instanceof S5.HTMLBodyElement))return;if(DA.parentNode)DA.parentNode.removeChild(DA);while(!(x.top instanceof S5.HTMLHtmlElement))x.pop();GB(Y1,e1),h=EJ;return;case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"nav":case"ol":case"p":case"section":case"summary":case"ul":if(x.inButtonScope("p"))H9(H6,"p");GB(Y1,e1);return;case"menu":if(x.inButtonScope("p"))H9(H6,"p");if(j5(x.top,"menuitem"))x.pop();GB(Y1,e1);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(x.inButtonScope("p"))H9(H6,"p");if(x.top instanceof S5.HTMLHeadingElement)x.pop();GB(Y1,e1);return;case"pre":case"listing":if(x.inButtonScope("p"))H9(H6,"p");GB(Y1,e1),a1=!0,F0=!1;return;case"form":if(y1&&!x.contains("template"))return;if(x.inButtonScope("p"))H9(H6,"p");if(IQ=GB(Y1,e1),!x.contains("template"))y1=IQ;return;case"li":F0=!1;for(C2=x.elements.length-1;C2>=0;C2--){if(F9=x.elements[C2],F9 instanceof S5.HTMLLIElement){H9(H6,"li");break}if(j5(F9,zd)&&!j5(F9,AM0))break}if(x.inButtonScope("p"))H9(H6,"p");GB(Y1,e1);return;case"dd":case"dt":F0=!1;for(C2=x.elements.length-1;C2>=0;C2--){if(F9=x.elements[C2],j5(F9,N_B)){H9(H6,F9.localName);break}if(j5(F9,zd)&&!j5(F9,AM0))break}if(x.inButtonScope("p"))H9(H6,"p");GB(Y1,e1);return;case"plaintext":if(x.inButtonScope("p"))H9(H6,"p");GB(Y1,e1),C=lB;return;case"button":if(x.inScope("button"))H9(H6,"button"),h(Z1,Y1,e1,l0);else pA(),GB(Y1,e1),F0=!1;return;case"a":var w6=e.findElementByTag("a");if(w6)H9(H6,Y1),e.remove(w6),x.removeElement(w6);case"b":case"big":case"code":case"em":case"font":case"i":case"s":case"small":case"strike":case"strong":case"tt":case"u":pA(),e.push(GB(Y1,e1),e1);return;case"nobr":if(pA(),x.inScope(Y1))H9(H6,Y1),pA();e.push(GB(Y1,e1),e1);return;case"applet":case"marquee":case"object":pA(),GB(Y1,e1),e.insertMarker(),F0=!1;return;case"table":if(!E0._quirks&&x.inButtonScope("p"))H9(H6,"p");GB(Y1,e1),F0=!1,h=_5;return;case"area":case"br":case"embed":case"img":case"keygen":case"wbr":pA(),GB(Y1,e1),x.pop(),F0=!1;return;case"input":pA(),IQ=GB(Y1,e1),x.pop();var z3=IQ.getAttribute("type");if(!z3||z3.toLowerCase()!=="hidden")F0=!1;return;case"param":case"source":case"track":GB(Y1,e1),x.pop();return;case"hr":if(x.inButtonScope("p"))H9(H6,"p");if(j5(x.top,"menuitem"))x.pop();GB(Y1,e1),x.pop(),F0=!1;return;case"image":H9(XJ,"img",e1,l0);return;case"textarea":GB(Y1,e1),a1=!0,F0=!1,C=u0,n=h,h=E6;return;case"xmp":if(x.inButtonScope("p"))H9(H6,"p");pA(),F0=!1,s5(Y1,e1);return;case"iframe":F0=!1,s5(Y1,e1);return;case"noembed":s5(Y1,e1);return;case"select":if(pA(),GB(Y1,e1),F0=!1,h===_5||h===XG||h===KW||h===U6||h===wZ)h=$U;else h=DF;return;case"optgroup":case"option":if(x.top instanceof S5.HTMLOptionElement)H9(H6,"option");pA(),GB(Y1,e1);return;case"menuitem":if(j5(x.top,"menuitem"))x.pop();pA(),GB(Y1,e1);return;case"rb":case"rtc":if(x.inScope("ruby"))x.generateImpliedEndTags();GB(Y1,e1);return;case"rp":case"rt":if(x.inScope("ruby"))x.generateImpliedEndTags("rtc");GB(Y1,e1);return;case"math":if(pA(),w_B(e1),eL0(e1),d3(Y1,e1,l9.MATHML),l0)x.pop();return;case"svg":if(pA(),U_B(e1),eL0(e1),d3(Y1,e1,l9.SVG),l0)x.pop();return;case"caption":case"col":case"colgroup":case"frame":case"head":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}pA(),GB(Y1,e1);return;case 3:switch(Y1){case"template":P4(H6,Y1,e1);return;case"body":if(!x.inScope("body"))return;h=e$;return;case"html":if(!x.inScope("body"))return;h=e$,h(Z1,Y1,e1);return;case"address":case"article":case"aside":case"blockquote":case"button":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"listing":case"main":case"menu":case"nav":case"ol":case"pre":case"section":case"summary":case"ul":if(!x.inScope(Y1))return;x.generateImpliedEndTags(),x.popTag(Y1);return;case"form":if(!x.contains("template")){var pD=y1;if(y1=null,!pD||!x.elementInScope(pD))return;x.generateImpliedEndTags(),x.removeElement(pD)}else{if(!x.inScope("form"))return;x.generateImpliedEndTags(),x.popTag("form")}return;case"p":if(!x.inButtonScope(Y1))H9(XJ,Y1,null),h(Z1,Y1,e1,l0);else x.generateImpliedEndTags(Y1),x.popTag(Y1);return;case"li":if(!x.inListItemScope(Y1))return;x.generateImpliedEndTags(Y1),x.popTag(Y1);return;case"dd":case"dt":if(!x.inScope(Y1))return;x.generateImpliedEndTags(Y1),x.popTag(Y1);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(!x.elementTypeInScope(S5.HTMLHeadingElement))return;x.generateImpliedEndTags(),x.popElementType(S5.HTMLHeadingElement);return;case"sarcasm":break;case"a":case"b":case"big":case"code":case"em":case"font":case"i":case"nobr":case"s":case"small":case"strike":case"strong":case"tt":case"u":var W8=_9(Y1);if(W8)return;break;case"applet":case"marquee":case"object":if(!x.inScope(Y1))return;x.generateImpliedEndTags(),x.popTag(Y1),e.clearToMarker();return;case"br":H9(XJ,Y1,null);return}for(C2=x.elements.length-1;C2>=0;C2--)if(F9=x.elements[C2],j5(F9,Y1)){x.generateImpliedEndTags(Y1),x.popElement(F9);break}else if(j5(F9,zd))return;return}}function E6(Z1,Y1,e1,l0){switch(Z1){case 1:g6(Y1);return;case-1:if(x.top instanceof S5.HTMLScriptElement)x.top._already_started=!0;x.pop(),h=n,h(Z1);return;case 3:if(Y1==="script")w5();else x.pop(),h=n;return;default:return}}function _5(Z1,Y1,e1,l0){function DA(F9){for(var IQ=0,w6=F9.length;IQ<w6;IQ++)if(F9[IQ][0]==="type")return F9[IQ][1].toLowerCase();return null}switch(Z1){case 1:if(G1){H9(Z1,Y1,e1,l0);return}else if(j5(x.top,g01)){K1=[],n=h,h=e8,h(Z1,Y1,e1,l0);return}break;case 4:gB(Y1);return;case 5:return;case 2:switch(Y1){case"caption":x.clearToContext(Hf1),e.insertMarker(),GB(Y1,e1),h=XG;return;case"colgroup":x.clearToContext(Hf1),GB(Y1,e1),h=b7;return;case"col":_5(XJ,"colgroup",null),h(Z1,Y1,e1,l0);return;case"tbody":case"tfoot":case"thead":x.clearToContext(Hf1),GB(Y1,e1),h=KW;return;case"td":case"th":case"tr":_5(XJ,"tbody",null),h(Z1,Y1,e1,l0);return;case"table":if(!x.inTableScope(Y1))return;_5(H6,Y1),h(Z1,Y1,e1,l0);return;case"style":case"script":case"template":P4(Z1,Y1,e1,l0);return;case"input":var C2=DA(e1);if(C2!=="hidden")break;GB(Y1,e1),x.pop();return;case"form":if(y1||x.contains("template"))return;y1=GB(Y1,e1),x.popElement(y1);return}break;case 3:switch(Y1){case"table":if(!x.inTableScope(Y1))return;x.popTag(Y1),U5();return;case"body":case"caption":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return;case"template":P4(Z1,Y1,e1,l0);return}break;case-1:H9(Z1,Y1,e1,l0);return}O4=!0,H9(Z1,Y1,e1,l0),O4=!1}function e8(Z1,Y1,e1,l0){if(Z1===h01){if(M1){if(Y1=Y1.replace(Kf1,""),Y1.length===0)return}K1.push(Y1)}else{var DA=K1.join("");if(K1.length=0,Cf1.test(DA))O4=!0,H9(h01,DA),O4=!1;else g6(DA);h=n,h(Z1,Y1,e1,l0)}}function XG(Z1,Y1,e1,l0){function DA(){if(!x.inTableScope("caption"))return!1;return x.generateImpliedEndTags(),x.popTag("caption"),e.clearToMarker(),h=_5,!0}switch(Z1){case 2:switch(Y1){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":if(DA())h(Z1,Y1,e1,l0);return}break;case 3:switch(Y1){case"caption":DA();return;case"table":if(DA())h(Z1,Y1,e1,l0);return;case"body":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}break}H9(Z1,Y1,e1,l0)}function b7(Z1,Y1,e1,l0){switch(Z1){case 1:var DA=Y1.match(Hd);if(DA)g6(DA[0]),Y1=Y1.substring(DA[0].length);if(Y1.length===0)return;break;case 4:gB(Y1);return;case 5:return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"col":GB(Y1,e1),x.pop();return;case"template":P4(Z1,Y1,e1,l0);return}break;case 3:switch(Y1){case"colgroup":if(!j5(x.top,"colgroup"))return;x.pop(),h=_5;return;case"col":return;case"template":P4(Z1,Y1,e1,l0);return}break;case-1:H9(Z1,Y1,e1,l0);return}if(!j5(x.top,"colgroup"))return;b7(H6,"colgroup"),h(Z1,Y1,e1,l0)}function KW(Z1,Y1,e1,l0){function DA(){if(!x.inTableScope("tbody")&&!x.inTableScope("thead")&&!x.inTableScope("tfoot"))return;x.clearToContext(zf1),KW(H6,x.top.localName,null),h(Z1,Y1,e1,l0)}switch(Z1){case 2:switch(Y1){case"tr":x.clearToContext(zf1),GB(Y1,e1),h=U6;return;case"th":case"td":KW(XJ,"tr",null),h(Z1,Y1,e1,l0);return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":DA();return}break;case 3:switch(Y1){case"table":DA();return;case"tbody":case"tfoot":case"thead":if(x.inTableScope(Y1))x.clearToContext(zf1),x.pop(),h=_5;return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":case"tr":return}break}_5(Z1,Y1,e1,l0)}function U6(Z1,Y1,e1,l0){function DA(){if(!x.inTableScope("tr"))return!1;return x.clearToContext(BM0),x.pop(),h=KW,!0}switch(Z1){case 2:switch(Y1){case"th":case"td":x.clearToContext(BM0),GB(Y1,e1),h=wZ,e.insertMarker();return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":case"tr":if(DA())h(Z1,Y1,e1,l0);return}break;case 3:switch(Y1){case"tr":DA();return;case"table":if(DA())h(Z1,Y1,e1,l0);return;case"tbody":case"tfoot":case"thead":if(x.inTableScope(Y1)){if(DA())h(Z1,Y1,e1,l0)}return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":return}break}_5(Z1,Y1,e1,l0)}function wZ(Z1,Y1,e1,l0){switch(Z1){case 2:switch(Y1){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":if(x.inTableScope("td"))wZ(H6,"td"),h(Z1,Y1,e1,l0);else if(x.inTableScope("th"))wZ(H6,"th"),h(Z1,Y1,e1,l0);return}break;case 3:switch(Y1){case"td":case"th":if(!x.inTableScope(Y1))return;x.generateImpliedEndTags(),x.popTag(Y1),e.clearToMarker(),h=U6;return;case"body":case"caption":case"col":case"colgroup":case"html":return;case"table":case"tbody":case"tfoot":case"thead":case"tr":if(!x.inTableScope(Y1))return;wZ(H6,x.inTableScope("td")?"td":"th"),h(Z1,Y1,e1,l0);return}break}H9(Z1,Y1,e1,l0)}function DF(Z1,Y1,e1,l0){switch(Z1){case 1:if(M1){if(Y1=Y1.replace(Kf1,""),Y1.length===0)return}g6(Y1);return;case 4:gB(Y1);return;case 5:return;case-1:H9(Z1,Y1,e1,l0);return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"option":if(x.top instanceof S5.HTMLOptionElement)DF(H6,Y1);GB(Y1,e1);return;case"optgroup":if(x.top instanceof S5.HTMLOptionElement)DF(H6,"option");if(x.top instanceof S5.HTMLOptGroupElement)DF(H6,Y1);GB(Y1,e1);return;case"select":DF(H6,Y1);return;case"input":case"keygen":case"textarea":if(!x.inSelectScope("select"))return;DF(H6,"select"),h(Z1,Y1,e1,l0);return;case"script":case"template":P4(Z1,Y1,e1,l0);return}break;case 3:switch(Y1){case"optgroup":if(x.top instanceof S5.HTMLOptionElement&&x.elements[x.elements.length-2]instanceof S5.HTMLOptGroupElement)DF(H6,"option");if(x.top instanceof S5.HTMLOptGroupElement)x.pop();return;case"option":if(x.top instanceof S5.HTMLOptionElement)x.pop();return;case"select":if(!x.inSelectScope(Y1))return;x.popTag(Y1),U5();return;case"template":P4(Z1,Y1,e1,l0);return}break}}function $U(Z1,Y1,e1,l0){switch(Y1){case"caption":case"table":case"tbody":case"tfoot":case"thead":case"tr":case"td":case"th":switch(Z1){case 2:$U(H6,"select"),h(Z1,Y1,e1,l0);return;case 3:if(x.inTableScope(Y1))$U(H6,"select"),h(Z1,Y1,e1,l0);return}}DF(Z1,Y1,e1,l0)}function RD(Z1,Y1,e1,l0){function DA(C2){h=C2,a[a.length-1]=h,h(Z1,Y1,e1,l0)}switch(Z1){case 1:case 4:case 5:H9(Z1,Y1,e1,l0);return;case-1:if(!x.contains("template"))Y0();else x.popTag("template"),e.clearToMarker(),a.pop(),U5(),h(Z1,Y1,e1,l0);return;case 2:switch(Y1){case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":P4(Z1,Y1,e1,l0);return;case"caption":case"colgroup":case"tbody":case"tfoot":case"thead":DA(_5);return;case"col":DA(b7);return;case"tr":DA(KW);return;case"td":case"th":DA(U6);return}DA(H9);return;case 3:switch(Y1){case"template":P4(Z1,Y1,e1,l0);return;default:return}}}function e$(Z1,Y1,e1,l0){switch(Z1){case 1:if(Cf1.test(Y1))break;H9(Z1,Y1);return;case 4:x.elements[0]._appendChild(E0.createComment(Y1));return;case 5:return;case-1:Y0();return;case 2:if(Y1==="html"){H9(Z1,Y1,e1,l0);return}break;case 3:if(Y1==="html"){if(W1)return;h=xS;return}break}h=H9,h(Z1,Y1,e1,l0)}function EJ(Z1,Y1,e1,l0){switch(Z1){case 1:if(Y1=Y1.replace(tL0,""),Y1.length>0)g6(Y1);return;case 4:gB(Y1);return;case 5:return;case-1:Y0();return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"frameset":GB(Y1,e1);return;case"frame":GB(Y1,e1),x.pop();return;case"noframes":P4(Z1,Y1,e1,l0);return}break;case 3:if(Y1==="frameset"){if(W1&&x.top instanceof S5.HTMLHtmlElement)return;if(x.pop(),!W1&&!(x.top instanceof S5.HTMLFrameSetElement))h=Aq;return}break}}function Aq(Z1,Y1,e1,l0){switch(Z1){case 1:if(Y1=Y1.replace(tL0,""),Y1.length>0)g6(Y1);return;case 4:gB(Y1);return;case 5:return;case-1:Y0();return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"noframes":P4(Z1,Y1,e1,l0);return}break;case 3:if(Y1==="html"){h=cI;return}break}}function xS(Z1,Y1,e1,l0){switch(Z1){case 1:if(Cf1.test(Y1))break;H9(Z1,Y1,e1,l0);return;case 4:E0._appendChild(E0.createComment(Y1));return;case 5:H9(Z1,Y1,e1,l0);return;case-1:Y0();return;case 2:if(Y1==="html"){H9(Z1,Y1,e1,l0);return}break}h=H9,h(Z1,Y1,e1,l0)}function cI(Z1,Y1,e1,l0){switch(Z1){case 1:if(Y1=Y1.replace(tL0,""),Y1.length>0)H9(Z1,Y1,e1,l0);return;case 4:E0._appendChild(E0.createComment(Y1));return;case 5:H9(Z1,Y1,e1,l0);return;case-1:Y0();return;case 2:switch(Y1){case"html":H9(Z1,Y1,e1,l0);return;case"noframes":P4(Z1,Y1,e1,l0);return}break}}function SX(Z1,Y1,e1,l0){function DA(w6){for(var z3=0,pD=w6.length;z3<pD;z3++)switch(w6[z3][0]){case"color":case"face":case"size":return!0}return!1}var C2;switch(Z1){case 1:if(F0&&KV8.test(Y1))F0=!1;if(M1)Y1=Y1.replace(Kf1,"�");g6(Y1);return;case 4:gB(Y1);return;case 5:return;case 2:switch(Y1){case"font":if(!DA(e1))break;case"b":case"big":case"blockquote":case"body":case"br":case"center":case"code":case"dd":case"div":case"dl":case"dt":case"em":case"embed":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"head":case"hr":case"i":case"img":case"li":case"listing":case"menu":case"meta":case"nobr":case"ol":case"p":case"pre":case"ruby":case"s":case"small":case"span":case"strong":case"strike":case"sub":case"sup":case"table":case"tt":case"u":case"ul":case"var":if(W1)break;do x.pop(),C2=x.top;while(C2.namespaceURI!==l9.HTML&&!z_B(C2)&&!E_B(C2));r2(Z1,Y1,e1,l0);return}if(C2=x.elements.length===1&&W1?B:x.top,C2.namespaceURI===l9.MATHML)w_B(e1);else if(C2.namespaceURI===l9.SVG)Y1=zV8(Y1),U_B(e1);if(eL0(e1),d3(Y1,e1,C2.namespaceURI),l0){if(Y1==="script"&&C2.namespaceURI===l9.SVG);x.pop()}return;case 3:if(C2=x.top,Y1==="script"&&C2.namespaceURI===l9.SVG&&C2.localName==="script")x.pop();else{var F9=x.elements.length-1,IQ=x.elements[F9];for(;;){if(IQ.localName.toLowerCase()===Y1){x.popElement(IQ);break}if(IQ=x.elements[--F9],IQ.namespaceURI!==l9.HTML)continue;h(Z1,Y1,e1,l0);break}}return}}return i1.testTokenizer=function(Z1,Y1,e1,l0){var DA=[];switch(Y1){case"PCDATA state":C=Q0;break;case"RCDATA state":C=u0;break;case"RAWTEXT state":C=i0;break;case"PLAINTEXT state":C=lB;break}if(e1)$=e1;if(r2=function(F9,IQ,w6,z3){switch(N2(),F9){case 1:if(DA.length>0&&DA[DA.length-1][0]==="Character")DA[DA.length-1][1]+=IQ;else DA.push(["Character",IQ]);break;case 4:DA.push(["Comment",IQ]);break;case 5:DA.push(["DOCTYPE",IQ,w6===void 0?null:w6,z3===void 0?null:z3,!g1]);break;case 2:var pD=Object.create(null);for(var W8=0;W8<w6.length;W8++){var lI=w6[W8];if(lI.length===1)pD[lI[0]]="";else pD[lI[0]]=lI[1]}var $Z=["StartTag",IQ,pD];if(z3)$Z.push(!0);DA.push($Z);break;case 3:DA.push(["EndTag",IQ]);break;case-1:break}},!l0)this.parse(Z1,!0);else{for(var C2=0;C2<Z1.length;C2++)this.parse(Z1[C2]);this.parse("",!0)}return DA},i1}});
var VL0=E((kz3,OkB)=>{var XL0=zZ();OkB.exports=RkB;function RkB(A,B){this._getString=A,this._setString=B,this._length=0,this._lastStringValue="",this._update()}Object.defineProperties(RkB.prototype,{length:{get:function(){return this._length}},item:{value:function(A){var B=O01(this);if(A<0||A>=B.length)return null;return B[A]}},contains:{value:function(A){A=String(A);var B=O01(this);return B.indexOf(A)>-1}},add:{value:function(){var A=O01(this);for(var B=0,Q=arguments.length;B<Q;B++){var D=FF1(arguments[B]);if(A.indexOf(D)<0)A.push(D)}this._update(A)}},remove:{value:function(){var A=O01(this);for(var B=0,Q=arguments.length;B<Q;B++){var D=FF1(arguments[B]),Z=A.indexOf(D);if(Z>-1)A.splice(Z,1)}this._update(A)}},toggle:{value:function A(B,Q){if(B=FF1(B),this.contains(B)){if(Q===void 0||Q===!1)return this.remove(B),!1;return!0}else{if(Q===void 0||Q===!0)return this.add(B),!0;return!1}}},replace:{value:function A(B,Q){if(String(Q)==="")XL0.SyntaxError();B=FF1(B),Q=FF1(Q);var D=O01(this),Z=D.indexOf(B);if(Z<0)return!1;var G=D.indexOf(Q);if(G<0)D[Z]=Q;else if(Z<G)D[Z]=Q,D.splice(G,1);else D.splice(Z,1);return this._update(D),!0}},toString:{value:function(){return this._getString()}},value:{get:function(){return this._getString()},set:function(A){this._setString(A),this._update()}},_update:{value:function(A){if(A)MkB(this,A),this._setString(A.join(" ").trim());else MkB(this,O01(this));this._lastStringValue=this._getString()}}});function MkB(A,B){var Q=A._length,D;A._length=B.length;for(D=0;D<B.length;D++)A[D]=B[D];for(;D<Q;D++)A[D]=void 0}function FF1(A){if(A=String(A),A==="")XL0.SyntaxError();if(/[ \t\r\n\f]/.test(A))XL0.InvalidCharacterError();return A}function xJ8(A){var B=A._length,Q=Array(B);for(var D=0;D<B;D++)Q[D]=A[D];return Q}function O01(A){var B=A._getString();if(B===A._lastStringValue)return xJ8(A);var Q=B.replace(/(^[ \t\r\n\f]+)|([ \t\r\n\f]+$)/g,"");if(Q==="")return[];else{var D=Object.create(null);return Q.split(/[ \t\r\n\f]+/g).filter(function(Z){var G="$"+Z;if(D[G])return!1;return D[G]=!0,!0})}}});
var Vd=E((Oz3,CkB)=>{var IL0;try{IL0=JkB()}catch(A){IL0=VkB()}CkB.exports=IL0});
var VkB=E((Rz3,XkB)=>{function HJ8(A){return this[A]||null}function zJ8(A){if(!A)A=[];return A.item=HJ8,A}XkB.exports=zJ8});
var Wf1=E((FE3,Z_B)=>{Z_B.exports=UF1;var JJ=GW(),_X8=Vd(),A_B=nb1(),dv=y01(),xX8=TL0(),vX8=SL0(),EF1=R01(),bX8=kL0(),fX8=_L0(),hX8=wF1(),gX8=EyB(),uX8=LyB(),syB=KF1(),ryB=Zf1(),oyB=tb1(),mX8=dL0(),Yf1=ab1(),sL0=If1(),dX8=aL0(),s8=zZ(),v01=ayB(),f01=s8.NAMESPACE,rL0=lb1().isApiWritable;function UF1(A,B){A_B.call(this),this.nodeType=JJ.DOCUMENT_NODE,this.isHTML=A,this._address=B||"about:blank",this.readyState="loading",this.implementation=new hX8(this),this.ownerDocument=null,this._contentType=A?"text/html":"application/xml",this.doctype=null,this.documentElement=null,this._templateDocCache=null,this._nodeIterators=null,this._nid=1,this._nextnid=2,this._nodes=[null,this],this.byId=Object.create(null),this.modclock=0}var cX8={event:"Event",customevent:"CustomEvent",uievent:"UIEvent",mouseevent:"MouseEvent"},lX8={events:"event",htmlevents:"event",mouseevents:"mouseevent",mutationevents:"mutationevent",uievents:"uievent"},b01=function(A,B,Q){return{get:function(){var D=A.call(this);if(D)return D[B];return Q},set:function(D){var Z=A.call(this);if(Z)Z[B]=D}}};function tyB(A,B){var Q,D,Z;if(A==="")A=null;if(!Yf1.isValidQName(B))s8.InvalidCharacterError();if(Q=null,D=B,Z=B.indexOf(":"),Z>=0)Q=B.substring(0,Z),D=B.substring(Z+1);if(Q!==null&&A===null)s8.NamespaceError();if(Q==="xml"&&A!==f01.XML)s8.NamespaceError();if((Q==="xmlns"||B==="xmlns")&&A!==f01.XMLNS)s8.NamespaceError();if(A===f01.XMLNS&&!(Q==="xmlns"||B==="xmlns"))s8.NamespaceError();return{namespace:A,prefix:Q,localName:D}}UF1.prototype=Object.create(A_B.prototype,{_setMutationHandler:{value:function(A){this.mutationHandler=A}},_dispatchRendererEvent:{value:function(A,B,Q){var D=this._nodes[A];if(!D)return;D._dispatchEvent(new EF1(B,Q),!0)}},nodeName:{value:"#document"},nodeValue:{get:function(){return null},set:function(){}},documentURI:{get:function(){return this._address},set:s8.nyi},compatMode:{get:function(){return this._quirks?"BackCompat":"CSS1Compat"}},createTextNode:{value:function(A){return new xX8(this,String(A))}},createComment:{value:function(A){return new vX8(this,A)}},createDocumentFragment:{value:function(){return new bX8(this)}},createProcessingInstruction:{value:function(A,B){if(!Yf1.isValidName(A)||B.indexOf("?>")!==-1)s8.InvalidCharacterError();return new fX8(this,A,B)}},createAttribute:{value:function(A){if(A=String(A),!Yf1.isValidName(A))s8.InvalidCharacterError();if(this.isHTML)A=s8.toASCIILowerCase(A);return new dv._Attr(null,A,null,null,"")}},createAttributeNS:{value:function(A,B){A=A===null||A===void 0||A===""?null:String(A),B=String(B);var Q=tyB(A,B);return new dv._Attr(null,Q.localName,Q.prefix,Q.namespace,"")}},createElement:{value:function(A){if(A=String(A),!Yf1.isValidName(A))s8.InvalidCharacterError();if(this.isHTML){if(/[A-Z]/.test(A))A=s8.toASCIILowerCase(A);return sL0.createElement(this,A,null)}else if(this.contentType==="application/xhtml+xml")return sL0.createElement(this,A,null);else return new dv(this,A,null,null)},writable:rL0},createElementNS:{value:function(A,B){A=A===null||A===void 0||A===""?null:String(A),B=String(B);var Q=tyB(A,B);return this._createElementNS(Q.localName,Q.namespace,Q.prefix)},writable:rL0},_createElementNS:{value:function(A,B,Q){if(B===f01.HTML)return sL0.createElement(this,A,Q);else if(B===f01.SVG)return dX8.createElement(this,A,Q);return new dv(this,A,B,Q)}},createEvent:{value:function A(B){B=B.toLowerCase();var Q=lX8[B]||B,D=mX8[cX8[Q]];if(D){var Z=new D;return Z._initialized=!1,Z}else s8.NotSupportedError()}},createTreeWalker:{value:function(A,B,Q){if(!A)throw new TypeError("root argument is required");if(!(A instanceof JJ))throw new TypeError("root not a node");return B=B===void 0?syB.SHOW_ALL:+B,Q=Q===void 0?null:Q,new gX8(A,B,Q)}},createNodeIterator:{value:function(A,B,Q){if(!A)throw new TypeError("root argument is required");if(!(A instanceof JJ))throw new TypeError("root not a node");return B=B===void 0?syB.SHOW_ALL:+B,Q=Q===void 0?null:Q,new uX8(A,B,Q)}},_attachNodeIterator:{value:function(A){if(!this._nodeIterators)this._nodeIterators=[];this._nodeIterators.push(A)}},_detachNodeIterator:{value:function(A){var B=this._nodeIterators.indexOf(A);this._nodeIterators.splice(B,1)}},_preremoveNodeIterators:{value:function(A){if(this._nodeIterators)this._nodeIterators.forEach(function(B){B._preremove(A)})}},_updateDocTypeElement:{value:function A(){this.doctype=this.documentElement=null;for(var B=this.firstChild;B!==null;B=B.nextSibling)if(B.nodeType===JJ.DOCUMENT_TYPE_NODE)this.doctype=B;else if(B.nodeType===JJ.ELEMENT_NODE)this.documentElement=B}},insertBefore:{value:function A(B,Q){return JJ.prototype.insertBefore.call(this,B,Q),this._updateDocTypeElement(),B}},replaceChild:{value:function A(B,Q){return JJ.prototype.replaceChild.call(this,B,Q),this._updateDocTypeElement(),Q}},removeChild:{value:function A(B){return JJ.prototype.removeChild.call(this,B),this._updateDocTypeElement(),B}},getElementById:{value:function(A){var B=this.byId[A];if(!B)return null;if(B instanceof HS)return B.getFirst();return B}},_hasMultipleElementsWithId:{value:function(A){return this.byId[A]instanceof HS}},getElementsByName:{value:dv.prototype.getElementsByName},getElementsByTagName:{value:dv.prototype.getElementsByTagName},getElementsByTagNameNS:{value:dv.prototype.getElementsByTagNameNS},getElementsByClassName:{value:dv.prototype.getElementsByClassName},adoptNode:{value:function A(B){if(B.nodeType===JJ.DOCUMENT_NODE)s8.NotSupportedError();if(B.nodeType===JJ.ATTRIBUTE_NODE)return B;if(B.parentNode)B.parentNode.removeChild(B);if(B.ownerDocument!==this)D_B(B,this);return B}},importNode:{value:function A(B,Q){return this.adoptNode(B.cloneNode(Q))},writable:rL0},origin:{get:function A(){return null}},characterSet:{get:function A(){return"UTF-8"}},contentType:{get:function A(){return this._contentType}},URL:{get:function A(){return this._address}},domain:{get:s8.nyi,set:s8.nyi},referrer:{get:s8.nyi},cookie:{get:s8.nyi,set:s8.nyi},lastModified:{get:s8.nyi},location:{get:function(){return this.defaultView?this.defaultView.location:null},set:s8.nyi},_titleElement:{get:function(){return this.getElementsByTagName("title").item(0)||null}},title:{get:function(){var A=this._titleElement,B=A?A.textContent:"";return B.replace(/[ \t\n\r\f]+/g," ").replace(/(^ )|( $)/g,"")},set:function(A){var B=this._titleElement,Q=this.head;if(!B&&!Q)return;if(!B)B=this.createElement("title"),Q.appendChild(B);B.textContent=A}},dir:b01(function(){var A=this.documentElement;if(A&&A.tagName==="HTML")return A},"dir",""),fgColor:b01(function(){return this.body},"text",""),linkColor:b01(function(){return this.body},"link",""),vlinkColor:b01(function(){return this.body},"vLink",""),alinkColor:b01(function(){return this.body},"aLink",""),bgColor:b01(function(){return this.body},"bgColor",""),charset:{get:function(){return this.characterSet}},inputEncoding:{get:function(){return this.characterSet}},scrollingElement:{get:function(){return this._quirks?this.body:this.documentElement}},body:{get:function(){return eyB(this.documentElement,"body")},set:s8.nyi},head:{get:function(){return eyB(this.documentElement,"head")}},images:{get:s8.nyi},embeds:{get:s8.nyi},plugins:{get:s8.nyi},links:{get:s8.nyi},forms:{get:s8.nyi},scripts:{get:s8.nyi},applets:{get:function(){return[]}},activeElement:{get:function(){return null}},innerHTML:{get:function(){return this.serialize()},set:s8.nyi},outerHTML:{get:function(){return this.serialize()},set:s8.nyi},write:{value:function(A){if(!this.isHTML)s8.InvalidStateError();if(!this._parser)return;if(!this._parser);var B=arguments.join("");this._parser.parse(B)}},writeln:{value:function A(B){this.write(Array.prototype.join.call(arguments,"")+`
`)}},open:{value:function(){this.documentElement=null}},close:{value:function(){if(this.readyState="interactive",this._dispatchEvent(new EF1("readystatechange"),!0),this._dispatchEvent(new EF1("DOMContentLoaded"),!0),this.readyState="complete",this._dispatchEvent(new EF1("readystatechange"),!0),this.defaultView)this.defaultView._dispatchEvent(new EF1("load"),!0)}},clone:{value:function A(){var B=new UF1(this.isHTML,this._address);return B._quirks=this._quirks,B._contentType=this._contentType,B}},cloneNode:{value:function A(B){var Q=JJ.prototype.cloneNode.call(this,!1);if(B)for(var D=this.firstChild;D!==null;D=D.nextSibling)Q._appendChild(Q.importNode(D,!0));return Q._updateDocTypeElement(),Q}},isEqual:{value:function A(B){return!0}},mutateValue:{value:function(A){if(this.mutationHandler)this.mutationHandler({type:v01.VALUE,target:A,data:A.data})}},mutateAttr:{value:function(A,B){if(this.mutationHandler)this.mutationHandler({type:v01.ATTR,target:A.ownerElement,attr:A})}},mutateRemoveAttr:{value:function(A){if(this.mutationHandler)this.mutationHandler({type:v01.REMOVE_ATTR,target:A.ownerElement,attr:A})}},mutateRemove:{value:function(A){if(this.mutationHandler)this.mutationHandler({type:v01.REMOVE,target:A.parentNode,node:A});Q_B(A)}},mutateInsert:{value:function(A){if(B_B(A),this.mutationHandler)this.mutationHandler({type:v01.INSERT,target:A.parentNode,node:A})}},mutateMove:{value:function(A){if(this.mutationHandler)this.mutationHandler({type:v01.MOVE,target:A})}},addId:{value:function A(B,Q){var D=this.byId[B];if(!D)this.byId[B]=Q;else{if(!(D instanceof HS))D=new HS(D),this.byId[B]=D;D.add(Q)}}},delId:{value:function A(B,Q){var D=this.byId[B];if(s8.assert(D),D instanceof HS){if(D.del(Q),D.length===1)this.byId[B]=D.downgrade()}else this.byId[B]=void 0}},_resolve:{value:function(A){return new ryB(this._documentBaseURL).resolve(A)}},_documentBaseURL:{get:function(){var A=this._address;if(A==="about:blank")A="/";var B=this.querySelector("base[href]");if(B)return new ryB(A).resolve(B.getAttribute("href"));return A}},_templateDoc:{get:function(){if(!this._templateDocCache){var A=new UF1(this.isHTML,this._address);this._templateDocCache=A._templateDocCache=A}return this._templateDocCache}},querySelector:{value:function(A){return oyB(A,this)[0]}},querySelectorAll:{value:function(A){var B=oyB(A,this);return B.item?B:new _X8(B)}}});var pX8=["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"];pX8.forEach(function(A){Object.defineProperty(UF1.prototype,"on"+A,{get:function(){return this._getEventHandler(A)},set:function(B){this._setEventHandler(A,B)}})});function eyB(A,B){if(A&&A.isHTML){for(var Q=A.firstChild;Q!==null;Q=Q.nextSibling)if(Q.nodeType===JJ.ELEMENT_NODE&&Q.localName===B&&Q.namespaceURI===f01.HTML)return Q}return null}function iX8(A){if(A._nid=A.ownerDocument._nextnid++,A.ownerDocument._nodes[A._nid]=A,A.nodeType===JJ.ELEMENT_NODE){var B=A.getAttribute("id");if(B)A.ownerDocument.addId(B,A);if(A._roothook)A._roothook()}}function nX8(A){if(A.nodeType===JJ.ELEMENT_NODE){var B=A.getAttribute("id");if(B)A.ownerDocument.delId(B,A)}A.ownerDocument._nodes[A._nid]=void 0,A._nid=void 0}function B_B(A){if(iX8(A),A.nodeType===JJ.ELEMENT_NODE)for(var B=A.firstChild;B!==null;B=B.nextSibling)B_B(B)}function Q_B(A){nX8(A);for(var B=A.firstChild;B!==null;B=B.nextSibling)Q_B(B)}function D_B(A,B){if(A.ownerDocument=B,A._lastModTime=void 0,Object.prototype.hasOwnProperty.call(A,"_tagName"))A._tagName=void 0;for(var Q=A.firstChild;Q!==null;Q=Q.nextSibling)D_B(Q,B)}function HS(A){this.nodes=Object.create(null),this.nodes[A._nid]=A,this.length=1,this.firstNode=void 0}HS.prototype.add=function(A){if(!this.nodes[A._nid])this.nodes[A._nid]=A,this.length++,this.firstNode=void 0};HS.prototype.del=function(A){if(this.nodes[A._nid])delete this.nodes[A._nid],this.length--,this.firstNode=void 0};HS.prototype.getFirst=function(){if(!this.firstNode){var A;for(A in this.nodes)if(this.firstNode===void 0||this.firstNode.compareDocumentPosition(this.nodes[A])&JJ.DOCUMENT_POSITION_PRECEDING)this.firstNode=this.nodes[A]}return this.firstNode};HS.prototype.downgrade=function(){if(this.length===1){var A;for(A in this.nodes)return this.nodes[A]}return this}});
var Xf1=E((IE3,F_B)=>{F_B.exports=Jf1;var aX8=GW(),G_B=RL0(),sX8=eb1();function Jf1(A,B,Q,D){G_B.call(this),this.nodeType=aX8.DOCUMENT_TYPE_NODE,this.ownerDocument=A||null,this.name=B,this.publicId=Q||"",this.systemId=D||""}Jf1.prototype=Object.create(G_B.prototype,{nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return null},set:function(){}},clone:{value:function A(){return new Jf1(this.ownerDocument,this.name,this.publicId,this.systemId)}},isEqual:{value:function A(B){return this.name===B.name&&this.publicId===B.publicId&&this.systemId===B.systemId}}});Object.defineProperties(Jf1.prototype,sX8)});
var Zf1=E((nz3,MyB)=>{MyB.exports=IW;function IW(A){if(!A)return Object.create(IW.prototype);this.url=A.replace(/^[ \t\n\r\f]+|[ \t\n\r\f]+$/g,"");var B=IW.pattern.exec(this.url);if(B){if(B[2])this.scheme=B[2];if(B[4]){var Q=B[4].match(IW.userinfoPattern);if(Q)this.username=Q[1],this.password=Q[3],B[4]=B[4].substring(Q[0].length);if(B[4].match(IW.portPattern)){var D=B[4].lastIndexOf(":");this.host=B[4].substring(0,D),this.port=B[4].substring(D+1)}else this.host=B[4]}if(B[5])this.path=B[5];if(B[6])this.query=B[7];if(B[8])this.fragment=B[9]}}IW.pattern=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/;IW.userinfoPattern=/^([^@:]*)(:([^@]*))?@/;IW.portPattern=/:\d+$/;IW.authorityPattern=/^[^:\/?#]+:\/\//;IW.hierarchyPattern=/^[^:\/?#]+:\//;IW.percentEncode=function A(B){var Q=B.charCodeAt(0);if(Q<256)return"%"+Q.toString(16);else throw Error("can't percent-encode codepoints > 255 yet")};IW.prototype={constructor:IW,isAbsolute:function(){return!!this.scheme},isAuthorityBased:function(){return IW.authorityPattern.test(this.url)},isHierarchical:function(){return IW.hierarchyPattern.test(this.url)},toString:function(){var A="";if(this.scheme!==void 0)A+=this.scheme+":";if(this.isAbsolute()){if(A+="//",this.username||this.password){if(A+=this.username||"",this.password)A+=":"+this.password;A+="@"}if(this.host)A+=this.host}if(this.port!==void 0)A+=":"+this.port;if(this.path!==void 0)A+=this.path;if(this.query!==void 0)A+="?"+this.query;if(this.fragment!==void 0)A+="#"+this.fragment;return A},resolve:function(A){var B=this,Q=new IW(A),D=new IW;if(Q.scheme!==void 0)D.scheme=Q.scheme,D.username=Q.username,D.password=Q.password,D.host=Q.host,D.port=Q.port,D.path=G(Q.path),D.query=Q.query;else if(D.scheme=B.scheme,Q.host!==void 0)D.username=Q.username,D.password=Q.password,D.host=Q.host,D.port=Q.port,D.path=G(Q.path),D.query=Q.query;else if(D.username=B.username,D.password=B.password,D.host=B.host,D.port=B.port,!Q.path)if(D.path=B.path,Q.query!==void 0)D.query=Q.query;else D.query=B.query;else{if(Q.path.charAt(0)==="/")D.path=G(Q.path);else D.path=Z(B.path,Q.path),D.path=G(D.path);D.query=Q.query}return D.fragment=Q.fragment,D.toString();function Z(F,I){if(B.host!==void 0&&!B.path)return"/"+I;var Y=F.lastIndexOf("/");if(Y===-1)return I;else return F.substring(0,Y+1)+I}function G(F){if(!F)return F;var I="";while(F.length>0){if(F==="."||F===".."){F="";break}var Y=F.substring(0,2),W=F.substring(0,3),J=F.substring(0,4);if(W==="../")F=F.substring(3);else if(Y==="./")F=F.substring(2);else if(W==="/./")F="/"+F.substring(3);else if(Y==="/."&&F.length===2)F="/";else if(J==="/../"||W==="/.."&&F.length===3)F="/"+F.substring(4),I=I.replace(/\/?[^\/]*$/,"");else{var X=F.match(/(\/?([^\/]*))/)[0];I+=X,F=F.substring(X.length)}}return I}}}});
var ZxB=E((zE3,DxB)=>{function yV8(A){for(var B=1;B<arguments.length;B++){var Q=arguments[B];for(var D in Q)if(Q.hasOwnProperty(D))A[D]=Q[D]}return A}function JM0(A,B){return Array(B+1).join(A)}function _V8(A){return A.replace(/^\n*/,"")}function xV8(A){var B=A.length;while(B>0&&A[B-1]===`
`)B--;return A.substring(0,B)}var vV8=["ADDRESS","ARTICLE","ASIDE","AUDIO","BLOCKQUOTE","BODY","CANVAS","CENTER","DD","DIR","DIV","DL","DT","FIELDSET","FIGCAPTION","FIGURE","FOOTER","FORM","FRAMESET","H1","H2","H3","H4","H5","H6","HEADER","HGROUP","HR","HTML","ISINDEX","LI","MAIN","MENU","NAV","NOFRAMES","NOSCRIPT","OL","OUTPUT","P","PRE","SECTION","TABLE","TBODY","TD","TFOOT","TH","THEAD","TR","UL"];function XM0(A){return VM0(A,vV8)}var s_B=["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"];function r_B(A){return VM0(A,s_B)}function bV8(A){return t_B(A,s_B)}var o_B=["A","TABLE","THEAD","TBODY","TFOOT","TH","TD","IFRAME","SCRIPT","AUDIO","VIDEO"];function fV8(A){return VM0(A,o_B)}function hV8(A){return t_B(A,o_B)}function VM0(A,B){return B.indexOf(A.nodeName)>=0}function t_B(A,B){return A.getElementsByTagName&&B.some(function(Q){return A.getElementsByTagName(Q).length})}var CJ={};CJ.paragraph={filter:"p",replacement:function(A){return`

`+A+`

`}};CJ.lineBreak={filter:"br",replacement:function(A,B,Q){return Q.br+`
`}};CJ.heading={filter:["h1","h2","h3","h4","h5","h6"],replacement:function(A,B,Q){var D=Number(B.nodeName.charAt(1));if(Q.headingStyle==="setext"&&D<3){var Z=JM0(D===1?"=":"-",A.length);return`

`+A+`
`+Z+`

`}else return`

`+JM0("#",D)+" "+A+`

`}};CJ.blockquote={filter:"blockquote",replacement:function(A){return A=A.replace(/^\n+|\n+$/g,""),A=A.replace(/^/gm,"> "),`

`+A+`

`}};CJ.list={filter:["ul","ol"],replacement:function(A,B){var Q=B.parentNode;if(Q.nodeName==="LI"&&Q.lastElementChild===B)return`
`+A;else return`

`+A+`

`}};CJ.listItem={filter:"li",replacement:function(A,B,Q){A=A.replace(/^\n+/,"").replace(/\n+$/,`
`).replace(/\n/gm,`
    `);var D=Q.bulletListMarker+"   ",Z=B.parentNode;if(Z.nodeName==="OL"){var G=Z.getAttribute("start"),F=Array.prototype.indexOf.call(Z.children,B);D=(G?Number(G)+F:F+1)+".  "}return D+A+(B.nextSibling&&!/\n$/.test(A)?`
`:"")}};CJ.indentedCodeBlock={filter:function(A,B){return B.codeBlockStyle==="indented"&&A.nodeName==="PRE"&&A.firstChild&&A.firstChild.nodeName==="CODE"},replacement:function(A,B,Q){return`

    `+B.firstChild.textContent.replace(/\n/g,`
    `)+`

`}};CJ.fencedCodeBlock={filter:function(A,B){return B.codeBlockStyle==="fenced"&&A.nodeName==="PRE"&&A.firstChild&&A.firstChild.nodeName==="CODE"},replacement:function(A,B,Q){var D=B.firstChild.getAttribute("class")||"",Z=(D.match(/language-(\S+)/)||[null,""])[1],G=B.firstChild.textContent,F=Q.fence.charAt(0),I=3,Y=new RegExp("^"+F+"{3,}","gm"),W;while(W=Y.exec(G))if(W[0].length>=I)I=W[0].length+1;var J=JM0(F,I);return`

`+J+Z+`
`+G.replace(/\n$/,"")+`
`+J+`

`}};CJ.horizontalRule={filter:"hr",replacement:function(A,B,Q){return`

`+Q.hr+`

`}};CJ.inlineLink={filter:function(A,B){return B.linkStyle==="inlined"&&A.nodeName==="A"&&A.getAttribute("href")},replacement:function(A,B){var Q=B.getAttribute("href");if(Q)Q=Q.replace(/([()])/g,"\\$1");var D=qf1(B.getAttribute("title"));if(D)D=' "'+D.replace(/"/g,"\\\"")+'"';return"["+A+"]("+Q+D+")"}};CJ.referenceLink={filter:function(A,B){return B.linkStyle==="referenced"&&A.nodeName==="A"&&A.getAttribute("href")},replacement:function(A,B,Q){var D=B.getAttribute("href"),Z=qf1(B.getAttribute("title"));if(Z)Z=' "'+Z+'"';var G,F;switch(Q.linkReferenceStyle){case"collapsed":G="["+A+"][]",F="["+A+"]: "+D+Z;break;case"shortcut":G="["+A+"]",F="["+A+"]: "+D+Z;break;default:var I=this.references.length+1;G="["+A+"]["+I+"]",F="["+I+"]: "+D+Z}return this.references.push(F),G},references:[],append:function(A){var B="";if(this.references.length)B=`

`+this.references.join(`
`)+`

`,this.references=[];return B}};CJ.emphasis={filter:["em","i"],replacement:function(A,B,Q){if(!A.trim())return"";return Q.emDelimiter+A+Q.emDelimiter}};CJ.strong={filter:["strong","b"],replacement:function(A,B,Q){if(!A.trim())return"";return Q.strongDelimiter+A+Q.strongDelimiter}};CJ.code={filter:function(A){var B=A.previousSibling||A.nextSibling,Q=A.parentNode.nodeName==="PRE"&&!B;return A.nodeName==="CODE"&&!Q},replacement:function(A){if(!A)return"";A=A.replace(/\r?\n|\r/g," ");var B=/^`|^ .*?[^ ].* $|`$/.test(A)?" ":"",Q="`",D=A.match(/`+/gm)||[];while(D.indexOf(Q)!==-1)Q=Q+"`";return Q+B+A+B+Q}};CJ.image={filter:"img",replacement:function(A,B){var Q=qf1(B.getAttribute("alt")),D=B.getAttribute("src")||"",Z=qf1(B.getAttribute("title")),G=Z?' "'+Z+'"':"";return D?"!["+Q+"]("+D+G+")":""}};function qf1(A){return A?A.replace(/(\n+\s*)+/g,`
`):""}function e_B(A){this.options=A,this._keep=[],this._remove=[],this.blankRule={replacement:A.blankReplacement},this.keepReplacement=A.keepReplacement,this.defaultRule={replacement:A.defaultReplacement},this.array=[];for(var B in A.rules)this.array.push(A.rules[B])}e_B.prototype={add:function(A,B){this.array.unshift(B)},keep:function(A){this._keep.unshift({filter:A,replacement:this.keepReplacement})},remove:function(A){this._remove.unshift({filter:A,replacement:function(){return""}})},forNode:function(A){if(A.isBlank)return this.blankRule;var B;if(B=IM0(this.array,A,this.options))return B;if(B=IM0(this._keep,A,this.options))return B;if(B=IM0(this._remove,A,this.options))return B;return this.defaultRule},forEach:function(A){for(var B=0;B<this.array.length;B++)A(this.array[B],B)}};function IM0(A,B,Q){for(var D=0;D<A.length;D++){var Z=A[D];if(gV8(Z,B,Q))return Z}return}function gV8(A,B,Q){var D=A.filter;if(typeof D==="string"){if(D===B.nodeName.toLowerCase())return!0}else if(Array.isArray(D)){if(D.indexOf(B.nodeName.toLowerCase())>-1)return!0}else if(typeof D==="function"){if(D.call(A,B,Q))return!0}else throw new TypeError("`filter` needs to be a string, array, or function")}function uV8(A){var{element:B,isBlock:Q,isVoid:D}=A,Z=A.isPre||function(X){return X.nodeName==="PRE"};if(!B.firstChild||Z(B))return;var G=null,F=!1,I=null,Y=n_B(I,B,Z);while(Y!==B){if(Y.nodeType===3||Y.nodeType===4){var W=Y.data.replace(/[ \r\n\t]+/g," ");if((!G||/ $/.test(G.data))&&!F&&W[0]===" ")W=W.substr(1);if(!W){Y=YM0(Y);continue}Y.data=W,G=Y}else if(Y.nodeType===1){if(Q(Y)||Y.nodeName==="BR"){if(G)G.data=G.data.replace(/ $/,"");G=null,F=!1}else if(D(Y)||Z(Y))G=null,F=!0;else if(G)F=!1}else{Y=YM0(Y);continue}var J=n_B(I,Y,Z);I=Y,Y=J}if(G){if(G.data=G.data.replace(/ $/,""),!G.data)YM0(G)}}function YM0(A){var B=A.nextSibling||A.parentNode;return A.parentNode.removeChild(A),B}function n_B(A,B,Q){if(A&&A.parentNode===B||Q(B))return B.nextSibling||B.parentNode;return B.firstChild||B.nextSibling||B.parentNode}var AxB=typeof window!=="undefined"?window:{};function mV8(){var A=AxB.DOMParser,B=!1;try{if(new A().parseFromString("","text/html"))B=!0}catch(Q){}return B}function dV8(){var A=function(){};{var B=i_B();A.prototype.parseFromString=function(Q){return B.createDocument(Q)}}return A}var cV8=mV8()?AxB.DOMParser:dV8();function lV8(A,B){var Q;if(typeof A==="string"){var D=pV8().parseFromString('<x-turndown id="turndown-root">'+A+"</x-turndown>","text/html");Q=D.getElementById("turndown-root")}else Q=A.cloneNode(!0);return uV8({element:Q,isBlock:XM0,isVoid:r_B,isPre:B.preformattedCode?iV8:null}),Q}var WM0;function pV8(){return WM0=WM0||new cV8,WM0}function iV8(A){return A.nodeName==="PRE"||A.nodeName==="CODE"}function nV8(A,B){return A.isBlock=XM0(A),A.isCode=A.nodeName==="CODE"||A.parentNode.isCode,A.isBlank=aV8(A),A.flankingWhitespace=sV8(A,B),A}function aV8(A){return!r_B(A)&&!fV8(A)&&/^\s*$/i.test(A.textContent)&&!bV8(A)&&!hV8(A)}function sV8(A,B){if(A.isBlock||B.preformattedCode&&A.isCode)return{leading:"",trailing:""};var Q=rV8(A.textContent);if(Q.leadingAscii&&a_B("left",A,B))Q.leading=Q.leadingNonAscii;if(Q.trailingAscii&&a_B("right",A,B))Q.trailing=Q.trailingNonAscii;return{leading:Q.leading,trailing:Q.trailing}}function rV8(A){var B=A.match(/^(([ \t\r\n]*)(\s*))(?:(?=\S)[\s\S]*\S)?((\s*?)([ \t\r\n]*))$/);return{leading:B[1],leadingAscii:B[2],leadingNonAscii:B[3],trailing:B[4],trailingNonAscii:B[5],trailingAscii:B[6]}}function a_B(A,B,Q){var D,Z,G;if(A==="left")D=B.previousSibling,Z=/ $/;else D=B.nextSibling,Z=/^ /;if(D){if(D.nodeType===3)G=Z.test(D.nodeValue);else if(Q.preformattedCode&&D.nodeName==="CODE")G=!1;else if(D.nodeType===1&&!XM0(D))G=Z.test(D.textContent)}return G}var oV8=Array.prototype.reduce,tV8=[[/\\/g,"\\\\"],[/\*/g,"\\*"],[/^-/g,"\\-"],[/^\+ /g,"\\+ "],[/^(=+)/g,"\\$1"],[/^(#{1,6}) /g,"\\$1 "],[/`/g,"\\`"],[/^~~~/g,"\\~~~"],[/\[/g,"\\["],[/\]/g,"\\]"],[/^>/g,"\\>"],[/_/g,"\\_"],[/^(\d+)\. /g,"$1\\. "]];function Nf1(A){if(!(this instanceof Nf1))return new Nf1(A);var B={rules:CJ,headingStyle:"setext",hr:"* * *",bulletListMarker:"*",codeBlockStyle:"indented",fence:"```",emDelimiter:"_",strongDelimiter:"**",linkStyle:"inlined",linkReferenceStyle:"full",br:"  ",preformattedCode:!1,blankReplacement:function(Q,D){return D.isBlock?`

`:""},keepReplacement:function(Q,D){return D.isBlock?`

`+D.outerHTML+`

`:D.outerHTML},defaultReplacement:function(Q,D){return D.isBlock?`

`+Q+`

`:Q}};this.options=yV8({},B,A),this.rules=new e_B(this.options)}Nf1.prototype={turndown:function(A){if(!BC8(A))throw new TypeError(A+" is not a string, or an element/document/fragment node.");if(A==="")return"";var B=BxB.call(this,new lV8(A,this.options));return eV8.call(this,B)},use:function(A){if(Array.isArray(A))for(var B=0;B<A.length;B++)this.use(A[B]);else if(typeof A==="function")A(this);else throw new TypeError("plugin must be a Function or an Array of Functions");return this},addRule:function(A,B){return this.rules.add(A,B),this},keep:function(A){return this.rules.keep(A),this},remove:function(A){return this.rules.remove(A),this},escape:function(A){return tV8.reduce(function(B,Q){return B.replace(Q[0],Q[1])},A)}};function BxB(A){var B=this;return oV8.call(A.childNodes,function(Q,D){D=new nV8(D,B.options);var Z="";if(D.nodeType===3)Z=D.isCode?D.nodeValue:B.escape(D.nodeValue);else if(D.nodeType===1)Z=AC8.call(B,D);return QxB(Q,Z)},"")}function eV8(A){var B=this;return this.rules.forEach(function(Q){if(typeof Q.append==="function")A=QxB(A,Q.append(B.options))}),A.replace(/^[\t\r\n]+/,"").replace(/[\t\r\n\s]+$/,"")}function AC8(A){var B=this.rules.forNode(A),Q=BxB.call(this,A),D=A.flankingWhitespace;if(D.leading||D.trailing)Q=Q.trim();return D.leading+B.replacement(Q,A,this.options)+D.trailing}function QxB(A,B){var Q=xV8(A),D=_V8(B),Z=Math.max(A.length-Q.length,B.length-D.length),G=`

`.substring(0,Z);return Q+G+D}function BC8(A){return A!=null&&(typeof A==="string"||A.nodeType&&(A.nodeType===1||A.nodeType===9||A.nodeType===11))}DxB.exports=Nf1});
var _L0=E((mz3,IyB)=>{IyB.exports=yL0;var JX8=GW(),FyB=JF1();function yL0(A,B,Q){FyB.call(this),this.nodeType=JX8.PROCESSING_INSTRUCTION_NODE,this.ownerDocument=A,this.target=B,this._data=Q}var CF1={get:function(){return this._data},set:function(A){if(A===null||A===void 0)A="";else A=String(A);if(this._data=A,this.rooted)this.ownerDocument.mutateValue(this)}};yL0.prototype=Object.create(FyB.prototype,{nodeName:{get:function(){return this.target}},nodeValue:CF1,textContent:CF1,innerText:CF1,data:{get:CF1.get,set:function(A){CF1.set.call(this,A===null?"":String(A))}},clone:{value:function A(){return new yL0(this.ownerDocument,this.target,this._data)}},isEqual:{value:function A(B){return this.target===B.target&&this._data===B._data}}})});
var aL0=E((yX8)=>{var lyB=y01(),PX8=lL0(),SX8=zZ(),jX8=Gf1(),kX8=yX8.elements={},pyB=Object.create(null);yX8.createElement=function(A,B,Q){var D=pyB[B]||nL0;return new D(A,B,Q)};function iL0(A){return PX8(A,nL0,kX8,pyB)}var nL0=iL0({superclass:lyB,name:"SVGElement",ctor:function A(B,Q,D){lyB.call(this,B,Q,SX8.NAMESPACE.SVG,D)},props:{style:{get:function(){if(!this._style)this._style=new jX8(this);return this._style}}}});iL0({name:"SVGSVGElement",ctor:function A(B,Q,D){nL0.call(this,B,Q,D)},tag:"svg",props:{createSVGRect:{value:function(){return yX8.createElement(this.ownerDocument,"rect",null)}}}});iL0({tags:["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"]})});
var ab1=E((OJ8)=>{OJ8.isValidName=MJ8;OJ8.isValidQName=RJ8;var UJ8=/^[_:A-Za-z][-.:\w]+$/,wJ8=/^([_A-Za-z][-.\w]+|[_A-Za-z][-.\w]+:[_A-Za-z][-.\w]+)$/,ZF1="_A-Za-zÀ-ÖØ-öø-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�",GF1="-._A-Za-z0-9·À-ÖØ-öø-˿̀-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�",Cd="["+ZF1+"]["+GF1+"]*",YL0=ZF1+":",WL0=GF1+":",$J8=new RegExp("^["+YL0+"]["+WL0+"]*$"),qJ8=new RegExp("^("+Cd+"|"+Cd+":"+Cd+")$"),EkB=/[\uD800-\uDB7F\uDC00-\uDFFF]/,UkB=/[\uD800-\uDB7F\uDC00-\uDFFF]/g,wkB=/[\uD800-\uDB7F][\uDC00-\uDFFF]/g;ZF1+="\uD800-\uDB7F\uDC00-\uDFFF";GF1+="\uD800-\uDB7F\uDC00-\uDFFF";Cd="["+ZF1+"]["+GF1+"]*";YL0=ZF1+":";WL0=GF1+":";var NJ8=new RegExp("^["+YL0+"]["+WL0+"]*$"),LJ8=new RegExp("^("+Cd+"|"+Cd+":"+Cd+")$");function MJ8(A){if(UJ8.test(A))return!0;if($J8.test(A))return!0;if(!EkB.test(A))return!1;if(!NJ8.test(A))return!1;var B=A.match(UkB),Q=A.match(wkB);return Q!==null&&2*Q.length===B.length}function RJ8(A){if(wJ8.test(A))return!0;if(qJ8.test(A))return!0;if(!EkB.test(A))return!1;if(!LJ8.test(A))return!1;var B=A.match(UkB),Q=A.match(wkB);return Q!==null&&2*Q.length===B.length}});
var ayB=E((GE3,nyB)=>{nyB.exports={VALUE:1,ATTR:2,REMOVE_ATTR:3,REMOVE:4,MOVE:5,INSERT:6}});
var bL0=E((lz3,JyB)=>{var cz3=JyB.exports={nextSkippingChildren:XX8,nextAncestorSibling:vL0,next:VX8,previous:CX8,deepLastChild:WyB};function XX8(A,B){if(A===B)return null;if(A.nextSibling!==null)return A.nextSibling;return vL0(A,B)}function vL0(A,B){for(A=A.parentNode;A!==null;A=A.parentNode){if(A===B)return null;if(A.nextSibling!==null)return A.nextSibling}return null}function VX8(A,B){var Q=A.firstChild;if(Q!==null)return Q;if(A===B)return null;if(Q=A.nextSibling,Q!==null)return Q;return vL0(A,B)}function WyB(A){while(A.lastChild)A=A.lastChild;return A}function CX8(A,B){var Q=A.previousSibling;if(Q!==null)return WyB(Q);if(Q=A.parentNode,Q===B)return null;return Q}});
var cL0=E((tz3,hyB)=>{var xI=Zf1();hyB.exports=HF1;function HF1(){}HF1.prototype=Object.create(Object.prototype,{_url:{get:function(){return new xI(this.href)}},protocol:{get:function(){var A=this._url;if(A&&A.scheme)return A.scheme+":";else return":"},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute()){if(A=A.replace(/:+$/,""),A=A.replace(/[^-+\.a-zA-Z0-9]/g,xI.percentEncode),A.length>0)Q.scheme=A,B=Q.toString()}this.href=B}},host:{get:function(){var A=this._url;if(A.isAbsolute()&&A.isAuthorityBased())return A.host+(A.port?":"+A.port:"");else return""},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute()&&Q.isAuthorityBased()){if(A=A.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,xI.percentEncode),A.length>0)Q.host=A,delete Q.port,B=Q.toString()}this.href=B}},hostname:{get:function(){var A=this._url;if(A.isAbsolute()&&A.isAuthorityBased())return A.host;else return""},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute()&&Q.isAuthorityBased()){if(A=A.replace(/^\/+/,""),A=A.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,xI.percentEncode),A.length>0)Q.host=A,B=Q.toString()}this.href=B}},port:{get:function(){var A=this._url;if(A.isAbsolute()&&A.isAuthorityBased()&&A.port!==void 0)return A.port;else return""},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute()&&Q.isAuthorityBased()){if(A=""+A,A=A.replace(/[^0-9].*$/,""),A=A.replace(/^0+/,""),A.length===0)A="0";if(parseInt(A,10)<=65535)Q.port=A,B=Q.toString()}this.href=B}},pathname:{get:function(){var A=this._url;if(A.isAbsolute()&&A.isHierarchical())return A.path;else return""},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute()&&Q.isHierarchical()){if(A.charAt(0)!=="/")A="/"+A;A=A.replace(/[^-+\._~!$&'()*,;:=@\/a-zA-Z0-9]/g,xI.percentEncode),Q.path=A,B=Q.toString()}this.href=B}},search:{get:function(){var A=this._url;if(A.isAbsolute()&&A.isHierarchical()&&A.query!==void 0)return"?"+A.query;else return""},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute()&&Q.isHierarchical()){if(A.charAt(0)==="?")A=A.substring(1);A=A.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,xI.percentEncode),Q.query=A,B=Q.toString()}this.href=B}},hash:{get:function(){var A=this._url;if(A==null||A.fragment==null||A.fragment==="")return"";else return"#"+A.fragment},set:function(A){var B=this.href,Q=new xI(B);if(A.charAt(0)==="#")A=A.substring(1);A=A.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,xI.percentEncode),Q.fragment=A,B=Q.toString(),this.href=B}},username:{get:function(){var A=this._url;return A.username||""},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute())A=A.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\:]/g,xI.percentEncode),Q.username=A,B=Q.toString();this.href=B}},password:{get:function(){var A=this._url;return A.password||""},set:function(A){var B=this.href,Q=new xI(B);if(Q.isAbsolute()){if(A==="")Q.password=null;else A=A.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\]/g,xI.percentEncode),Q.password=A;B=Q.toString()}this.href=B}},origin:{get:function(){var A=this._url;if(A==null)return"";var B=function(Q){var D=[A.scheme,A.host,+A.port||Q];return D[0]+"://"+D[1]+(D[2]===Q?"":":"+D[2])};switch(A.scheme){case"ftp":return B(21);case"gopher":return B(70);case"http":case"ws":return B(80);case"https":case"wss":return B(443);default:return A.scheme+"://"}}}});HF1._inherit=function(A){Object.getOwnPropertyNames(HF1.prototype).forEach(function(B){if(B==="constructor"||B==="href")return;var Q=Object.getOwnPropertyDescriptor(HF1.prototype,B);Object.defineProperty(A,B,Q)})}});
var cb1=E((Ez3,pjB)=>{pjB.exports=db1;var BW8=1,QW8=3,DW8=4,ZW8=5,GW8=7,FW8=8,IW8=9,YW8=11,WW8=12,JW8=13,XW8=14,VW8=15,CW8=17,KW8=18,HW8=19,zW8=20,EW8=21,UW8=22,wW8=23,$W8=24,qW8=25,NW8=[null,"INDEX_SIZE_ERR",null,"HIERARCHY_REQUEST_ERR","WRONG_DOCUMENT_ERR","INVALID_CHARACTER_ERR",null,"NO_MODIFICATION_ALLOWED_ERR","NOT_FOUND_ERR","NOT_SUPPORTED_ERR","INUSE_ATTRIBUTE_ERR","INVALID_STATE_ERR","SYNTAX_ERR","INVALID_MODIFICATION_ERR","NAMESPACE_ERR","INVALID_ACCESS_ERR",null,"TYPE_MISMATCH_ERR","SECURITY_ERR","NETWORK_ERR","ABORT_ERR","URL_MISMATCH_ERR","QUOTA_EXCEEDED_ERR","TIMEOUT_ERR","INVALID_NODE_TYPE_ERR","DATA_CLONE_ERR"],LW8=[null,"INDEX_SIZE_ERR (1): the index is not in the allowed range",null,"HIERARCHY_REQUEST_ERR (3): the operation would yield an incorrect nodes model","WRONG_DOCUMENT_ERR (4): the object is in the wrong Document, a call to importNode is required","INVALID_CHARACTER_ERR (5): the string contains invalid characters",null,"NO_MODIFICATION_ALLOWED_ERR (7): the object can not be modified","NOT_FOUND_ERR (8): the object can not be found here","NOT_SUPPORTED_ERR (9): this operation is not supported","INUSE_ATTRIBUTE_ERR (10): setAttributeNode called on owned Attribute","INVALID_STATE_ERR (11): the object is in an invalid state","SYNTAX_ERR (12): the string did not match the expected pattern","INVALID_MODIFICATION_ERR (13): the object can not be modified in this way","NAMESPACE_ERR (14): the operation is not allowed by Namespaces in XML","INVALID_ACCESS_ERR (15): the object does not support the operation or argument",null,"TYPE_MISMATCH_ERR (17): the type of the object does not match the expected type","SECURITY_ERR (18): the operation is insecure","NETWORK_ERR (19): a network error occurred","ABORT_ERR (20): the user aborted an operation","URL_MISMATCH_ERR (21): the given URL does not match another URL","QUOTA_EXCEEDED_ERR (22): the quota has been exceeded","TIMEOUT_ERR (23): a timeout occurred","INVALID_NODE_TYPE_ERR (24): the supplied node is invalid or has an invalid ancestor for this operation","DATA_CLONE_ERR (25): the object can not be cloned."],ljB={INDEX_SIZE_ERR:BW8,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:QW8,WRONG_DOCUMENT_ERR:DW8,INVALID_CHARACTER_ERR:ZW8,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:GW8,NOT_FOUND_ERR:FW8,NOT_SUPPORTED_ERR:IW8,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:YW8,SYNTAX_ERR:WW8,INVALID_MODIFICATION_ERR:JW8,NAMESPACE_ERR:XW8,INVALID_ACCESS_ERR:VW8,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:CW8,SECURITY_ERR:KW8,NETWORK_ERR:HW8,ABORT_ERR:zW8,URL_MISMATCH_ERR:EW8,QUOTA_EXCEEDED_ERR:UW8,TIMEOUT_ERR:wW8,INVALID_NODE_TYPE_ERR:$W8,DATA_CLONE_ERR:qW8};function db1(A){Error.call(this),Error.captureStackTrace(this,this.constructor),this.code=A,this.message=LW8[A],this.name=NW8[A]}db1.prototype.__proto__=Error.prototype;for(QF1 in ljB)mb1={value:ljB[QF1]},Object.defineProperty(db1,QF1,mb1),Object.defineProperty(db1.prototype,QF1,mb1);var mb1,QF1});
var dL0=E((sz3,PyB)=>{PyB.exports={Event:R01(),UIEvent:nN0(),MouseEvent:sN0(),CustomEvent:TyB()}});
var eb1=E((yz3,_kB)=>{var cJ8=GW(),lJ8=oN0(),zL0=function(A,B){var Q=A.createDocumentFragment();for(var D=0;D<B.length;D++){var Z=B[D],G=Z instanceof cJ8;Q.appendChild(G?Z:A.createTextNode(String(Z)))}return Q},pJ8={after:{value:function A(){var B=Array.prototype.slice.call(arguments),Q=this.parentNode,D=this.nextSibling;if(Q===null)return;while(D&&B.some(function(G){return G===D}))D=D.nextSibling;var Z=zL0(this.doc,B);Q.insertBefore(Z,D)}},before:{value:function A(){var B=Array.prototype.slice.call(arguments),Q=this.parentNode,D=this.previousSibling;if(Q===null)return;while(D&&B.some(function(F){return F===D}))D=D.previousSibling;var Z=zL0(this.doc,B),G=D?D.nextSibling:Q.firstChild;Q.insertBefore(Z,G)}},remove:{value:function A(){if(this.parentNode===null)return;if(this.doc){if(this.doc._preremoveNodeIterators(this),this.rooted)this.doc.mutateRemove(this)}this._remove(),this.parentNode=null}},_remove:{value:function A(){var B=this.parentNode;if(B===null)return;if(B._childNodes)B._childNodes.splice(this.index,1);else if(B._firstChild===this)if(this._nextSibling===this)B._firstChild=null;else B._firstChild=this._nextSibling;lJ8.remove(this),B.modify()}},replaceWith:{value:function A(){var B=Array.prototype.slice.call(arguments),Q=this.parentNode,D=this.nextSibling;if(Q===null)return;while(D&&B.some(function(G){return G===D}))D=D.nextSibling;var Z=zL0(this.doc,B);if(this.parentNode===Q)Q.replaceChild(Z,this);else Q.insertBefore(Z,D)}}};_kB.exports=pJ8});
var f_B=E((XE3,b_B)=>{var NV8=Object.create(null,{appCodeName:{value:"Mozilla"},appName:{value:"Netscape"},appVersion:{value:"4.0"},platform:{value:""},product:{value:"Gecko"},productSub:{value:"20100101"},userAgent:{value:""},vendor:{value:""},vendorSub:{value:""},taintEnabled:{value:function(){return!1}}});b_B.exports=NV8});
var g_B=E((VE3,h_B)=>{var LV8={setTimeout,clearTimeout,setInterval,clearInterval};h_B.exports=LV8});
var i_B=E((TV8)=>{var d_B=wF1(),c_B=Uf1(),KE3=FM0(),l_B=GM0();TV8.createDOMImplementation=function(){return new d_B(null)};TV8.createDocument=function(A,B){if(A||B){var Q=new c_B;return Q.parse(A||"",!0),Q.document()}return new d_B(null).createHTMLDocument("")};TV8.createIncrementalHTMLParser=function(){var A=new c_B;return{write:function(B){if(B.length>0)A.parse(B,!1,function(){return!0})},end:function(B){A.parse(B||"",!0,function(){return!0})},process:function(B){return A.parse("",!1,B)},document:function(){return A.document()}}};TV8.createWindow=function(A,B){var Q=TV8.createDocument(A);if(B!==void 0)Q._address=B;return new l_B.Window(Q)};TV8.impl=l_B});
var kL0=E((uz3,GyB)=>{GyB.exports=jL0;var IX8=GW(),YX8=Vd(),ZyB=nb1(),Df1=y01(),WX8=tb1(),DyB=zZ();function jL0(A){ZyB.call(this),this.nodeType=IX8.DOCUMENT_FRAGMENT_NODE,this.ownerDocument=A}jL0.prototype=Object.create(ZyB.prototype,{nodeName:{value:"#document-fragment"},nodeValue:{get:function(){return null},set:function(){}},textContent:Object.getOwnPropertyDescriptor(Df1.prototype,"textContent"),innerText:Object.getOwnPropertyDescriptor(Df1.prototype,"innerText"),querySelector:{value:function(A){var B=this.querySelectorAll(A);return B.length?B[0]:null}},querySelectorAll:{value:function(A){var B=Object.create(this);B.isHTML=!0,B.getElementsByTagName=Df1.prototype.getElementsByTagName,B.nextElement=Object.getOwnPropertyDescriptor(Df1.prototype,"firstElementChild").get;var Q=WX8(A,B);return Q.item?Q:new YX8(Q)}},clone:{value:function A(){return new jL0(this.ownerDocument)}},isEqual:{value:function A(B){return!0}},innerHTML:{get:function(){return this.serialize()},set:DyB.nyi},outerHTML:{get:function(){return this.serialize()},set:DyB.nyi}})});
var lL0=E((ez3,myB)=>{var gyB=JL0(),wX8=lb1().isApiWritable;myB.exports=function(A,B,Q,D){var Z=A.ctor;if(Z){var G=A.props||{};if(A.attributes)for(var F in A.attributes){var I=A.attributes[F];if(typeof I!=="object"||Array.isArray(I))I={type:I};if(!I.name)I.name=F.toLowerCase();G[F]=gyB.property(I)}if(G.constructor={value:Z,writable:wX8},Z.prototype=Object.create((A.superclass||B).prototype,G),A.events)qX8(Z,A.events);Q[A.name]=Z}else Z=B;return(A.tags||A.tag&&[A.tag]||[]).forEach(function(Y){D[Y]=Z}),Z};function uyB(A,B,Q,D){this.body=A,this.document=B,this.form=Q,this.element=D}uyB.prototype.build=function(){return()=>{}};function $X8(A,B,Q,D){var Z=A.ownerDocument||Object.create(null),G=A.form||Object.create(null);A[B]=new uyB(D,Z,G,A).build()}function qX8(A,B){var Q=A.prototype;B.forEach(function(D){Object.defineProperty(Q,"on"+D,{get:function(){return this._getEventHandler(D)},set:function(Z){this._setEventHandler(D,Z)}}),gyB.registerChangeHandler(A,"on"+D,$X8)})}});
var lb1=E((MW8)=>{MW8.isApiWritable=!globalThis.__domino_frozen__});
var nN0=E((Hz3,mjB)=>{var ujB=R01();mjB.exports=iN0;function iN0(){ujB.call(this),this.view=null,this.detail=0}iN0.prototype=Object.create(ujB.prototype,{constructor:{value:iN0},initUIEvent:{value:function(A,B,Q,D,Z){this.initEvent(A,B,Q),this.view=D,this.detail=Z}}})});
var nb1=E((Tz3,zkB)=>{zkB.exports=HkB;var KkB=GW(),EJ8=Vd();function HkB(){KkB.call(this),this._firstChild=this._childNodes=null}HkB.prototype=Object.create(KkB.prototype,{hasChildNodes:{value:function(){if(this._childNodes)return this._childNodes.length>0;return this._firstChild!==null}},childNodes:{get:function(){return this._ensureChildNodes(),this._childNodes}},firstChild:{get:function(){if(this._childNodes)return this._childNodes.length===0?null:this._childNodes[0];return this._firstChild}},lastChild:{get:function(){var A=this._childNodes,B;if(A)return A.length===0?null:A[A.length-1];if(B=this._firstChild,B===null)return null;return B._previousSibling}},_ensureChildNodes:{value:function(){if(this._childNodes)return;var A=this._firstChild,B=A,Q=this._childNodes=new EJ8;if(A)do Q.push(B),B=B._nextSibling;while(B!==A);this._firstChild=null}},removeChildren:{value:function A(){var B=this.rooted?this.ownerDocument:null,Q=this.firstChild,D;while(Q!==null){if(D=Q,Q=D.nextSibling,B)B.mutateRemove(D);D.parentNode=null}if(this._childNodes)this._childNodes.length=0;else this._firstChild=null;this.modify()}}})});
var oN0=E((qz3,ajB)=>{var CS=zZ(),IU=ajB.exports={valid:function(A){return CS.assert(A,"list falsy"),CS.assert(A._previousSibling,"previous falsy"),CS.assert(A._nextSibling,"next falsy"),!0},insertBefore:function(A,B){CS.assert(IU.valid(A)&&IU.valid(B));var Q=A,D=A._previousSibling,Z=B,G=B._previousSibling;Q._previousSibling=G,D._nextSibling=Z,G._nextSibling=Q,Z._previousSibling=D,CS.assert(IU.valid(A)&&IU.valid(B))},replace:function(A,B){if(CS.assert(IU.valid(A)&&(B===null||IU.valid(B))),B!==null)IU.insertBefore(B,A);IU.remove(A),CS.assert(IU.valid(A)&&(B===null||IU.valid(B)))},remove:function(A){CS.assert(IU.valid(A));var B=A._previousSibling;if(B===A)return;var Q=A._nextSibling;B._nextSibling=Q,Q._previousSibling=B,A._previousSibling=A._nextSibling=A,CS.assert(IU.valid(A))}}});
var rN0=E(($z3,njB)=>{var Jd=R01(),ZJ8=sN0(),GJ8=zZ();njB.exports=ijB;function ijB(){}ijB.prototype={addEventListener:function A(B,Q,D){if(!Q)return;if(D===void 0)D=!1;if(!this._listeners)this._listeners=Object.create(null);if(!this._listeners[B])this._listeners[B]=[];var Z=this._listeners[B];for(var G=0,F=Z.length;G<F;G++){var I=Z[G];if(I.listener===Q&&I.capture===D)return}var Y={listener:Q,capture:D};if(typeof Q==="function")Y.f=Q;Z.push(Y)},removeEventListener:function A(B,Q,D){if(D===void 0)D=!1;if(this._listeners){var Z=this._listeners[B];if(Z)for(var G=0,F=Z.length;G<F;G++){var I=Z[G];if(I.listener===Q&&I.capture===D){if(Z.length===1)this._listeners[B]=void 0;else Z.splice(G,1);return}}}},dispatchEvent:function A(B){return this._dispatchEvent(B,!1)},_dispatchEvent:function A(B,Q){if(typeof Q!=="boolean")Q=!1;function D(W,J){var{type:X,eventPhase:V}=J;if(J.currentTarget=W,V!==Jd.CAPTURING_PHASE&&W._handlers&&W._handlers[X]){var C=W._handlers[X],K;if(typeof C==="function")K=C.call(J.currentTarget,J);else{var H=C.handleEvent;if(typeof H!=="function")throw new TypeError("handleEvent property of event handler object isnot a function.");K=H.call(C,J)}switch(J.type){case"mouseover":if(K===!0)J.preventDefault();break;case"beforeunload":default:if(K===!1)J.preventDefault();break}}var z=W._listeners&&W._listeners[X];if(!z)return;z=z.slice();for(var $=0,L=z.length;$<L;$++){if(J._immediatePropagationStopped)return;var N=z[$];if(V===Jd.CAPTURING_PHASE&&!N.capture||V===Jd.BUBBLING_PHASE&&N.capture)continue;if(N.f)N.f.call(J.currentTarget,J);else{var O=N.listener.handleEvent;if(typeof O!=="function")throw new TypeError("handleEvent property of event listener object is not a function.");O.call(N.listener,J)}}}if(!B._initialized||B._dispatching)GJ8.InvalidStateError();B.isTrusted=Q,B._dispatching=!0,B.target=this;var Z=[];for(var G=this.parentNode;G;G=G.parentNode)Z.push(G);B.eventPhase=Jd.CAPTURING_PHASE;for(var F=Z.length-1;F>=0;F--)if(D(Z[F],B),B._propagationStopped)break;if(!B._propagationStopped)B.eventPhase=Jd.AT_TARGET,D(this,B);if(B.bubbles&&!B._propagationStopped){B.eventPhase=Jd.BUBBLING_PHASE;for(var I=0,Y=Z.length;I<Y;I++)if(D(Z[I],B),B._propagationStopped)break}if(B._dispatching=!1,B.eventPhase=Jd.AT_TARGET,B.currentTarget=null,Q&&!B.defaultPrevented&&B instanceof ZJ8)switch(B.type){case"mousedown":this._armed={x:B.clientX,y:B.clientY,t:B.timeStamp};break;case"mouseout":case"mouseover":this._armed=null;break;case"mouseup":if(this._isClick(B))this._doClick(B);this._armed=null;break}return!B.defaultPrevented},_isClick:function(A){return this._armed!==null&&A.type==="mouseup"&&A.isTrusted&&A.button===0&&A.timeStamp-this._armed.t<1000&&Math.abs(A.clientX-this._armed.x)<10&&Math.abs(A.clientY-this._armed.Y)<10},_doClick:function(A){if(this._click_in_progress)return;this._click_in_progress=!0;var B=this;while(B&&!B._post_click_activation_steps)B=B.parentNode;if(B&&B._pre_click_activation_steps)B._pre_click_activation_steps();var Q=this.ownerDocument.createEvent("MouseEvent");Q.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,A.screenX,A.screenY,A.clientX,A.clientY,A.ctrlKey,A.altKey,A.shiftKey,A.metaKey,A.button,null);var D=this._dispatchEvent(Q,!0);if(B){if(D){if(B._post_click_activation_steps)B._post_click_activation_steps(Q)}else if(B._cancelled_activation_steps)B._cancelled_activation_steps()}},_setEventHandler:function A(B,Q){if(!this._handlers)this._handlers=Object.create(null);this._handlers[B]=Q},_getEventHandler:function A(B){return this._handlers&&this._handlers[B]||null}}});
var sN0=E((zz3,cjB)=>{var djB=nN0();cjB.exports=aN0;function aN0(){djB.call(this),this.screenX=this.screenY=this.clientX=this.clientY=0,this.ctrlKey=this.altKey=this.shiftKey=this.metaKey=!1,this.button=0,this.buttons=1,this.relatedTarget=null}aN0.prototype=Object.create(djB.prototype,{constructor:{value:aN0},initMouseEvent:{value:function(A,B,Q,D,Z,G,F,I,Y,W,J,X,V,C,K){switch(this.initEvent(A,B,Q,D,Z),this.screenX=G,this.screenY=F,this.clientX=I,this.clientY=Y,this.ctrlKey=W,this.altKey=J,this.shiftKey=X,this.metaKey=V,this.button=C,C){case 0:this.buttons=1;break;case 1:this.buttons=4;break;case 2:this.buttons=2;break;default:this.buttons=0;break}this.relatedTarget=K}},getModifierState:{value:function(A){switch(A){case"Alt":return this.altKey;case"Control":return this.ctrlKey;case"Shift":return this.shiftKey;case"Meta":return this.metaKey;default:return!1}}}})});
var tN0=E((Nz3,QkB)=>{QkB.exports={serializeOne:VJ8,ɵescapeMatchingClosingTag:ejB,ɵescapeClosingCommentTag:AkB,ɵescapeProcessingInstructionContent:BkB};var tjB=zZ(),Xd=tjB.NAMESPACE,sjB={STYLE:!0,SCRIPT:!0,XMP:!0,IFRAME:!0,NOEMBED:!0,NOFRAMES:!0,PLAINTEXT:!0},FJ8={area:!0,base:!0,basefont:!0,bgsound:!0,br:!0,col:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},IJ8={},rjB=/[&<>\u00A0]/g,ojB=/[&"<>\u00A0]/g;function YJ8(A){if(!rjB.test(A))return A;return A.replace(rjB,(B)=>{switch(B){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case" ":return"&nbsp;"}})}function WJ8(A){if(!ojB.test(A))return A;return A.replace(ojB,(B)=>{switch(B){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case" ":return"&nbsp;"}})}function JJ8(A){var B=A.namespaceURI;if(!B)return A.localName;if(B===Xd.XML)return"xml:"+A.localName;if(B===Xd.XLINK)return"xlink:"+A.localName;if(B===Xd.XMLNS)if(A.localName==="xmlns")return"xmlns";else return"xmlns:"+A.localName;return A.name}function ejB(A,B){let Q="</"+B;if(!A.toLowerCase().includes(Q))return A;let D=[...A],Z=A.matchAll(new RegExp(Q,"ig"));for(let G of Z)D[G.index]="&lt;";return D.join("")}var XJ8=/--!?>/;function AkB(A){if(!XJ8.test(A))return A;return A.replace(/(--\!?)>/g,"$1&gt;")}function BkB(A){return A.includes(">")?A.replaceAll(">","&gt;"):A}function VJ8(A,B){var Q="";switch(A.nodeType){case 1:var D=A.namespaceURI,Z=D===Xd.HTML,G=Z||D===Xd.SVG||D===Xd.MATHML?A.localName:A.tagName;Q+="<"+G;for(var F=0,I=A._numattrs;F<I;F++){var Y=A._attr(F);if(Q+=" "+JJ8(Y),Y.value!==void 0)Q+='="'+WJ8(Y.value)+'"'}if(Q+=">",!(Z&&FJ8[G])){var W=A.serialize();if(sjB[G.toUpperCase()])W=ejB(W,G);if(Z&&IJ8[G]&&W.charAt(0)===`
`)Q+=`
`;Q+=W,Q+="</"+G+">"}break;case 3:case 4:var J;if(B.nodeType===1&&B.namespaceURI===Xd.HTML)J=B.tagName;else J="";if(sjB[J]||J==="NOSCRIPT"&&B.ownerDocument._scripting_enabled)Q+=A.data;else Q+=YJ8(A.data);break;case 8:Q+="<!--"+AkB(A.data)+"-->";break;case 7:let X=BkB(A.data);Q+="<?"+A.target+" "+X+"?>";break;case 10:Q+="<!DOCTYPE "+A.name,Q+=">";break;default:tjB.InvalidStateError()}return Q}});
var tb1=E((S01,ykB)=>{var sb1=Object.create(null,{location:{get:function(){throw new Error("window.location is not supported.")}}}),vJ8=function(A,B){return A.compareDocumentPosition(B)},bJ8=function(A,B){return vJ8(A,B)&2?1:-1},ob1=function(A){while((A=A.nextSibling)&&A.nodeType!==1);return A},P01=function(A){while((A=A.previousSibling)&&A.nodeType!==1);return A},fJ8=function(A){if(A=A.firstChild)while(A.nodeType!==1&&(A=A.nextSibling));return A},hJ8=function(A){if(A=A.lastChild)while(A.nodeType!==1&&(A=A.previousSibling));return A},T01=function(A){if(!A.parentNode)return!1;var B=A.parentNode.nodeType;return B===1||B===9},TkB=function(A){if(!A)return A;var B=A[0];if(B==='"'||B==="'"){if(A[A.length-1]===B)A=A.slice(1,-1);else A=A.slice(1);return A.replace(KQ.str_escape,function(Q){var D=/^\\(?:([0-9A-Fa-f]+)|([\r\n\f]+))/.exec(Q);if(!D)return Q.slice(1);if(D[2])return"";var Z=parseInt(D[1],16);return String.fromCodePoint?String.fromCodePoint(Z):String.fromCharCode(Z)})}else if(KQ.ident.test(A))return uv(A);else return A},uv=function(A){return A.replace(KQ.escape,function(B){var Q=/^\\([0-9A-Fa-f]+)/.exec(B);if(!Q)return B[1];var D=parseInt(Q[1],16);return String.fromCodePoint?String.fromCodePoint(D):String.fromCharCode(D)})},gJ8=function(){if(Array.prototype.indexOf)return Array.prototype.indexOf;return function(A,B){var Q=this.length;while(Q--)if(this[Q]===B)return Q;return-1}}(),SkB=function(A,B){var Q=KQ.inside.source.replace(/</g,A).replace(/>/g,B);return new RegExp(Q)},GC=function(A,B,Q){return A=A.source,A=A.replace(B,Q.source||Q),new RegExp(A)},PkB=function(A,B){return A.replace(/^(?:\w+:\/\/|\/+)/,"").replace(/(?:\/+|\/*#.*?)$/,"").split("/",B).join("/")},uJ8=function(A,B){var Q=A.replace(/\s+/g,""),D;if(Q==="even")Q="2n+0";else if(Q==="odd")Q="2n+1";else if(Q.indexOf("n")===-1)Q="0n"+Q;return D=/^([+-])?(\d+)?n([+-])?(\d+)?$/.exec(Q),{group:D[1]==="-"?-(D[2]||1):+(D[2]||1),offset:D[4]?D[3]==="-"?-D[4]:+D[4]:0}},CL0=function(A,B,Q){var D=uJ8(A),Z=D.group,G=D.offset,F=!Q?fJ8:hJ8,I=!Q?ob1:P01;return function(Y){if(!T01(Y))return;var W=F(Y.parentNode),J=0;while(W){if(B(W,Y))J++;if(W===Y)return J-=G,Z&&J?J%Z===0&&J<0===Z<0:!J;W=I(W)}}},_I={"*":function(){return function(){return!0}}(),type:function(A){return A=A.toLowerCase(),function(B){return B.nodeName.toLowerCase()===A}},attr:function(A,B,Q,D){return B=jkB[B],function(Z){var G;switch(A){case"for":G=Z.htmlFor;break;case"class":if(G=Z.className,G===""&&Z.getAttribute("class")==null)G=null;break;case"href":case"src":G=Z.getAttribute(A,2);break;case"title":G=Z.getAttribute("title")||null;break;case"id":case"lang":case"dir":case"accessKey":case"hidden":case"tabIndex":case"style":if(Z.getAttribute){G=Z.getAttribute(A);break}default:if(Z.hasAttribute&&!Z.hasAttribute(A))break;G=Z[A]!=null?Z[A]:Z.getAttribute&&Z.getAttribute(A);break}if(G==null)return;if(G=G+"",D)G=G.toLowerCase(),Q=Q.toLowerCase();return B(G,Q)}},":first-child":function(A){return!P01(A)&&T01(A)},":last-child":function(A){return!ob1(A)&&T01(A)},":only-child":function(A){return!P01(A)&&!ob1(A)&&T01(A)},":nth-child":function(A,B){return CL0(A,function(){return!0},B)},":nth-last-child":function(A){return _I[":nth-child"](A,!0)},":root":function(A){return A.ownerDocument.documentElement===A},":empty":function(A){return!A.firstChild},":not":function(A){var B=HL0(A);return function(Q){return!B(Q)}},":first-of-type":function(A){if(!T01(A))return;var B=A.nodeName;while(A=P01(A))if(A.nodeName===B)return;return!0},":last-of-type":function(A){if(!T01(A))return;var B=A.nodeName;while(A=ob1(A))if(A.nodeName===B)return;return!0},":only-of-type":function(A){return _I[":first-of-type"](A)&&_I[":last-of-type"](A)},":nth-of-type":function(A,B){return CL0(A,function(Q,D){return Q.nodeName===D.nodeName},B)},":nth-last-of-type":function(A){return _I[":nth-of-type"](A,!0)},":checked":function(A){return!!(A.checked||A.selected)},":indeterminate":function(A){return!_I[":checked"](A)},":enabled":function(A){return!A.disabled&&A.type!=="hidden"},":disabled":function(A){return!!A.disabled},":target":function(A){return A.id===sb1.location.hash.substring(1)},":focus":function(A){return A===A.ownerDocument.activeElement},":is":function(A){return HL0(A)},":matches":function(A){return _I[":is"](A)},":nth-match":function(A,B){var Q=A.split(/\s*,\s*/),D=Q.shift(),Z=HL0(Q.join(","));return CL0(D,Z,B)},":nth-last-match":function(A){return _I[":nth-match"](A,!0)},":links-here":function(A){return A+""===sb1.location+""},":lang":function(A){return function(B){while(B){if(B.lang)return B.lang.indexOf(A)===0;B=B.parentNode}}},":dir":function(A){return function(B){while(B){if(B.dir)return B.dir===A;B=B.parentNode}}},":scope":function(A,B){var Q=B||A.ownerDocument;if(Q.nodeType===9)return A===Q.documentElement;return A===Q},":any-link":function(A){return typeof A.href==="string"},":local-link":function(A){if(A.nodeName)return A.href&&A.host===sb1.location.host;var B=+A+1;return function(Q){if(!Q.href)return;var D=sb1.location+"",Z=Q+"";return PkB(D,B)===PkB(Z,B)}},":default":function(A){return!!A.defaultSelected},":valid":function(A){return A.willValidate||A.validity&&A.validity.valid},":invalid":function(A){return!_I[":valid"](A)},":in-range":function(A){return A.value>A.min&&A.value<=A.max},":out-of-range":function(A){return!_I[":in-range"](A)},":required":function(A){return!!A.required},":optional":function(A){return!A.required},":read-only":function(A){if(A.readOnly)return!0;var B=A.getAttribute("contenteditable"),Q=A.contentEditable,D=A.nodeName.toLowerCase();return D=D!=="input"&&D!=="textarea",(D||A.disabled)&&B==null&&Q!=="true"},":read-write":function(A){return!_I[":read-only"](A)},":hover":function(){throw new Error(":hover is not supported.")},":active":function(){throw new Error(":active is not supported.")},":link":function(){throw new Error(":link is not supported.")},":visited":function(){throw new Error(":visited is not supported.")},":column":function(){throw new Error(":column is not supported.")},":nth-column":function(){throw new Error(":nth-column is not supported.")},":nth-last-column":function(){throw new Error(":nth-last-column is not supported.")},":current":function(){throw new Error(":current is not supported.")},":past":function(){throw new Error(":past is not supported.")},":future":function(){throw new Error(":future is not supported.")},":contains":function(A){return function(B){var Q=B.innerText||B.textContent||B.value||"";return Q.indexOf(A)!==-1}},":has":function(A){return function(B){return kkB(A,B).length>0}}},jkB={"-":function(){return!0},"=":function(A,B){return A===B},"*=":function(A,B){return A.indexOf(B)!==-1},"~=":function(A,B){var Q,D,Z,G;for(D=0;;D=Q+1){if(Q=A.indexOf(B,D),Q===-1)return!1;if(Z=A[Q-1],G=A[Q+B.length],(!Z||Z===" ")&&(!G||G===" "))return!0}},"|=":function(A,B){var Q=A.indexOf(B),D;if(Q!==0)return;return D=A[Q+B.length],D==="-"||!D},"^=":function(A,B){return A.indexOf(B)===0},"$=":function(A,B){var Q=A.lastIndexOf(B);return Q!==-1&&Q+B.length===A.length},"!=":function(A,B){return A!==B}},IF1={" ":function(A){return function(B){while(B=B.parentNode)if(A(B))return B}},">":function(A){return function(B){if(B=B.parentNode)return A(B)&&B}},"+":function(A){return function(B){if(B=P01(B))return A(B)&&B}},"~":function(A){return function(B){while(B=P01(B))if(A(B))return B}},noop:function(A){return function(B){return A(B)&&B}},ref:function(A,B){var Q;function D(Z){var G=Z.ownerDocument,F=G.getElementsByTagName("*"),I=F.length;while(I--)if(Q=F[I],D.test(Z))return Q=null,!0;Q=null}return D.combinator=function(Z){if(!Q||!Q.getAttribute)return;var G=Q.getAttribute(B)||"";if(G[0]==="#")G=G.substring(1);if(G===Z.id&&A(Q))return Q},D}},KQ={escape:/\\(?:[^0-9A-Fa-f\r\n]|[0-9A-Fa-f]{1,6}[\r\n\t ]?)/g,str_escape:/(escape)|\\(\n|\r\n?|\f)/g,nonascii:/[\u00A0-\uFFFF]/,cssid:/(?:(?!-?[0-9])(?:escape|nonascii|[-_a-zA-Z0-9])+)/,qname:/^ *(cssid|\*)/,simple:/^(?:([.#]cssid)|pseudo|attr)/,ref:/^ *\/(cssid)\/ */,combinator:/^(?: +([^ \w*.#\\]) +|( )+|([^ \w*.#\\]))(?! *$)/,attr:/^\[(cssid)(?:([^\w]?=)(inside))?\]/,pseudo:/^(:cssid)(?:\((inside)\))?/,inside:/(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|<[^"'>]*>|\\["'>]|[^"'>])*/,ident:/^(cssid)$/};KQ.cssid=GC(KQ.cssid,"nonascii",KQ.nonascii);KQ.cssid=GC(KQ.cssid,"escape",KQ.escape);KQ.qname=GC(KQ.qname,"cssid",KQ.cssid);KQ.simple=GC(KQ.simple,"cssid",KQ.cssid);KQ.ref=GC(KQ.ref,"cssid",KQ.cssid);KQ.attr=GC(KQ.attr,"cssid",KQ.cssid);KQ.pseudo=GC(KQ.pseudo,"cssid",KQ.cssid);KQ.inside=GC(KQ.inside,`[^"'>]*`,KQ.inside);KQ.attr=GC(KQ.attr,"inside",SkB("\\[","\\]"));KQ.pseudo=GC(KQ.pseudo,"inside",SkB("\\(","\\)"));KQ.simple=GC(KQ.simple,"pseudo",KQ.pseudo);KQ.simple=GC(KQ.simple,"attr",KQ.attr);KQ.ident=GC(KQ.ident,"cssid",KQ.cssid);KQ.str_escape=GC(KQ.str_escape,"escape",KQ.escape);var YF1=function(A){var B=A.replace(/^\s+|\s+$/g,""),Q,D=[],Z=[],G,F,I,Y,W;while(B){if(I=KQ.qname.exec(B))B=B.substring(I[0].length),F=uv(I[1]),Z.push(rb1(F,!0));else if(I=KQ.simple.exec(B))B=B.substring(I[0].length),F="*",Z.push(rb1(F,!0)),Z.push(rb1(I));else throw new SyntaxError("Invalid selector.");while(I=KQ.simple.exec(B))B=B.substring(I[0].length),Z.push(rb1(I));if(B[0]==="!")B=B.substring(1),G=dJ8(),G.qname=F,Z.push(G.simple);if(I=KQ.ref.exec(B)){B=B.substring(I[0].length),W=IF1.ref(KL0(Z),uv(I[1])),D.push(W.combinator),Z=[];continue}if(I=KQ.combinator.exec(B)){if(B=B.substring(I[0].length),Y=I[1]||I[2]||I[3],Y===","){D.push(IF1.noop(KL0(Z)));break}}else Y="noop";if(!IF1[Y])throw new SyntaxError("Bad combinator.");D.push(IF1[Y](KL0(Z))),Z=[]}if(Q=mJ8(D),Q.qname=F,Q.sel=B,G)G.lname=Q.qname,G.test=Q,G.qname=G.qname,G.sel=Q.sel,Q=G;if(W)W.test=Q,W.qname=Q.qname,W.sel=Q.sel,Q=W;return Q},rb1=function(A,B){if(B)return A==="*"?_I["*"]:_I.type(A);if(A[1])return A[1][0]==="."?_I.attr("class","~=",uv(A[1].substring(1)),!1):_I.attr("id","=",uv(A[1].substring(1)),!1);if(A[2])return A[3]?_I[uv(A[2])](TkB(A[3])):_I[uv(A[2])];if(A[4]){var Q=A[6],D=/["'\s]\s*I$/i.test(Q);if(D)Q=Q.replace(/\s*I$/i,"");return _I.attr(uv(A[4]),A[5]||"-",TkB(Q),D)}throw new SyntaxError("Unknown Selector.")},KL0=function(A){var B=A.length,Q;if(B<2)return A[0];return function(D){if(!D)return;for(Q=0;Q<B;Q++)if(!A[Q](D))return;return!0}},mJ8=function(A){if(A.length<2)return function(B){return!!A[0](B)};return function(B){var Q=A.length;while(Q--)if(!(B=A[Q](B)))return;return!0}},dJ8=function(){var A;function B(Q){var D=Q.ownerDocument,Z=D.getElementsByTagName(B.lname),G=Z.length;while(G--)if(B.test(Z[G])&&A===Q)return A=null,!0;A=null}return B.simple=function(Q){return A=Q,!0},B},HL0=function(A){var B=YF1(A),Q=[B];while(B.sel)B=YF1(B.sel),Q.push(B);if(Q.length<2)return B;return function(D){var Z=Q.length,G=0;for(;G<Z;G++)if(Q[G](D))return!0}},kkB=function(A,B){var Q=[],D=YF1(A),Z=B.getElementsByTagName(D.qname),G=0,F;while(F=Z[G++])if(D(F))Q.push(F);if(D.sel){while(D.sel){D=YF1(D.sel),Z=B.getElementsByTagName(D.qname),G=0;while(F=Z[G++])if(D(F)&&gJ8.call(Q,F)===-1)Q.push(F)}Q.sort(bJ8)}return Q};ykB.exports=S01=function(A,B){var Q,D;if(B.nodeType!==11&&A.indexOf(" ")===-1){if(A[0]==="#"&&B.rooted&&/^#[A-Z_][-A-Z0-9_]*$/i.test(A)){if(B.doc._hasMultipleElementsWithId){if(Q=A.substring(1),!B.doc._hasMultipleElementsWithId(Q))return D=B.doc.getElementById(Q),D?[D]:[]}}if(A[0]==="."&&/^\.\w+$/.test(A))return B.getElementsByClassName(A.substring(1));if(/^\w+$/.test(A))return B.getElementsByTagName(A)}return kkB(A,B)};S01.selectors=_I;S01.operators=jkB;S01.combinators=IF1;S01.matches=function(A,B){var Q={sel:B};do if(Q=YF1(Q.sel),Q(A))return!0;while(Q.sel);return!1}});
var v_B=E((JE3,x_B)=>{var $V8=Zf1(),qV8=cL0();x_B.exports=DM0;function DM0(A,B){this._window=A,this._href=B}DM0.prototype=Object.create(qV8.prototype,{constructor:{value:DM0},href:{get:function(){return this._href},set:function(A){this.assign(A)}},assign:{value:function(A){var B=new $V8(this._href),Q=B.resolve(A);this._href=Q}},replace:{value:function(A){this.assign(A)}},reload:{value:function(){this.assign(this.href)}},toString:{value:function(){return this.href}}})});
var wF1=E((WE3,__B)=>{__B.exports=y_B;var j_B=Wf1(),k_B=Xf1(),EV8=Uf1(),wf1=zZ(),UV8=ab1();function y_B(A){this.contextObject=A}var wV8={xml:{"":!0,"1.0":!0,"2.0":!0},core:{"":!0,"2.0":!0},html:{"":!0,"1.0":!0,"2.0":!0},xhtml:{"":!0,"1.0":!0,"2.0":!0}};y_B.prototype={hasFeature:function A(B,Q){var D=wV8[(B||"").toLowerCase()];return D&&D[Q||""]||!1},createDocumentType:function A(B,Q,D){if(!UV8.isValidQName(B))wf1.InvalidCharacterError();return new k_B(this.contextObject,B,Q,D)},createDocument:function A(B,Q,D){var Z=new j_B(!1,null),G;if(Q)G=Z.createElementNS(B,Q);else G=null;if(D)Z.appendChild(D);if(G)Z.appendChild(G);if(B===wf1.NAMESPACE.HTML)Z._contentType="application/xhtml+xml";else if(B===wf1.NAMESPACE.SVG)Z._contentType="image/svg+xml";else Z._contentType="application/xml";return Z},createHTMLDocument:function A(B){var Q=new j_B(!0,null);Q.appendChild(new k_B(Q,"html"));var D=Q.createElement("html");Q.appendChild(D);var Z=Q.createElement("head");if(D.appendChild(Z),B!==void 0){var G=Q.createElement("title");Z.appendChild(G),G.appendChild(Q.createTextNode(B))}return D.appendChild(Q.createElement("body")),Q.modclock=1,Q},mozSetOutputMutationHandler:function(A,B){A.mutationHandler=B},mozGetInputMutationHandler:function(A){wf1.nyi()},mozHTMLParser:EV8}});
var y01=E((vz3,dkB)=>{dkB.exports=mv;var wL0=ab1(),dD=zZ(),rM=dD.NAMESPACE,Bf1=JL0(),v$=GW(),$L0=Vd(),nJ8=tN0(),Af1=LkB(),k01=cb1(),aJ8=VL0(),qL0=tb1(),gkB=nb1(),sJ8=eb1(),rJ8=EL0(),ukB=UL0(),hkB=Object.create(null);function mv(A,B,Q,D){gkB.call(this),this.nodeType=v$.ELEMENT_NODE,this.ownerDocument=A,this.localName=B,this.namespaceURI=Q,this.prefix=D,this._tagName=void 0,this._attrsByQName=Object.create(null),this._attrsByLName=Object.create(null),this._attrKeys=[]}function NL0(A,B){if(A.nodeType===v$.TEXT_NODE)B.push(A._data);else for(var Q=0,D=A.childNodes.length;Q<D;Q++)NL0(A.childNodes[Q],B)}mv.prototype=Object.create(gkB.prototype,{isHTML:{get:function A(){return this.namespaceURI===rM.HTML&&this.ownerDocument.isHTML}},tagName:{get:function A(){if(this._tagName===void 0){var B;if(this.prefix===null)B=this.localName;else B=this.prefix+":"+this.localName;if(this.isHTML){var Q=hkB[B];if(!Q)hkB[B]=Q=dD.toASCIIUpperCase(B);B=Q}this._tagName=B}return this._tagName}},nodeName:{get:function(){return this.tagName}},nodeValue:{get:function(){return null},set:function(){}},textContent:{get:function(){var A=[];return NL0(this,A),A.join("")},set:function(A){if(this.removeChildren(),A!==null&&A!==void 0&&A!=="")this._appendChild(this.ownerDocument.createTextNode(A))}},innerText:{get:function(){var A=[];return NL0(this,A),A.join("").replace(/[ \t\n\f\r]+/g," ").trim()},set:function(A){if(this.removeChildren(),A!==null&&A!==void 0&&A!=="")this._appendChild(this.ownerDocument.createTextNode(A))}},innerHTML:{get:function(){return this.serialize()},set:dD.nyi},outerHTML:{get:function(){return nJ8.serializeOne(this,{nodeType:0})},set:function(A){var B=this.ownerDocument,Q=this.parentNode;if(Q===null)return;if(Q.nodeType===v$.DOCUMENT_NODE)dD.NoModificationAllowedError();if(Q.nodeType===v$.DOCUMENT_FRAGMENT_NODE)Q=Q.ownerDocument.createElement("body");var D=B.implementation.mozHTMLParser(B._address,Q);D.parse(A===null?"":String(A),!0),this.replaceWith(D._asDocumentFragment())}},_insertAdjacent:{value:function A(B,Q){var D=!1;switch(B){case"beforebegin":D=!0;case"afterend":var Z=this.parentNode;if(Z===null)return null;return Z.insertBefore(Q,D?this:this.nextSibling);case"afterbegin":D=!0;case"beforeend":return this.insertBefore(Q,D?this.firstChild:null);default:return dD.SyntaxError()}}},insertAdjacentElement:{value:function A(B,Q){if(Q.nodeType!==v$.ELEMENT_NODE)throw new TypeError("not an element");return B=dD.toASCIILowerCase(String(B)),this._insertAdjacent(B,Q)}},insertAdjacentText:{value:function A(B,Q){var D=this.ownerDocument.createTextNode(Q);B=dD.toASCIILowerCase(String(B)),this._insertAdjacent(B,D)}},insertAdjacentHTML:{value:function A(B,Q){B=dD.toASCIILowerCase(String(B)),Q=String(Q);var D;switch(B){case"beforebegin":case"afterend":if(D=this.parentNode,D===null||D.nodeType===v$.DOCUMENT_NODE)dD.NoModificationAllowedError();break;case"afterbegin":case"beforeend":D=this;break;default:dD.SyntaxError()}if(!(D instanceof mv)||D.ownerDocument.isHTML&&D.localName==="html"&&D.namespaceURI===rM.HTML)D=D.ownerDocument.createElementNS(rM.HTML,"body");var Z=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,D);Z.parse(Q,!0),this._insertAdjacent(B,Z._asDocumentFragment())}},children:{get:function(){if(!this._children)this._children=new mkB(this);return this._children}},attributes:{get:function(){if(!this._attributes)this._attributes=new ML0(this);return this._attributes}},firstElementChild:{get:function(){for(var A=this.firstChild;A!==null;A=A.nextSibling)if(A.nodeType===v$.ELEMENT_NODE)return A;return null}},lastElementChild:{get:function(){for(var A=this.lastChild;A!==null;A=A.previousSibling)if(A.nodeType===v$.ELEMENT_NODE)return A;return null}},childElementCount:{get:function(){return this.children.length}},nextElement:{value:function(A){if(!A)A=this.ownerDocument.documentElement;var B=this.firstElementChild;if(!B){if(this===A)return null;B=this.nextElementSibling}if(B)return B;for(var Q=this.parentElement;Q&&Q!==A;Q=Q.parentElement)if(B=Q.nextElementSibling,B)return B;return null}},getElementsByTagName:{value:function A(B){var Q;if(!B)return new $L0;if(B==="*")Q=function(){return!0};else if(this.isHTML)Q=oJ8(B);else Q=LL0(B);return new Af1(this,Q)}},getElementsByTagNameNS:{value:function A(B,Q){var D;if(B==="*"&&Q==="*")D=function(){return!0};else if(B==="*")D=LL0(Q);else if(Q==="*")D=tJ8(B);else D=eJ8(B,Q);return new Af1(this,D)}},getElementsByClassName:{value:function A(B){if(B=String(B).trim(),B===""){var Q=new $L0;return Q}return B=B.split(/[ \t\r\n\f]+/),new Af1(this,AX8(B))}},getElementsByName:{value:function A(B){return new Af1(this,BX8(String(B)))}},clone:{value:function A(){var B;if(this.namespaceURI!==rM.HTML||this.prefix||!this.ownerDocument.isHTML)B=this.ownerDocument.createElementNS(this.namespaceURI,this.prefix!==null?this.prefix+":"+this.localName:this.localName);else B=this.ownerDocument.createElement(this.localName);for(var Q=0,D=this._attrKeys.length;Q<D;Q++){var Z=this._attrKeys[Q],G=this._attrsByLName[Z],F=G.cloneNode();F._setOwnerElement(B),B._attrsByLName[Z]=F,B._addQName(F)}return B._attrKeys=this._attrKeys.concat(),B}},isEqual:{value:function A(B){if(this.localName!==B.localName||this.namespaceURI!==B.namespaceURI||this.prefix!==B.prefix||this._numattrs!==B._numattrs)return!1;for(var Q=0,D=this._numattrs;Q<D;Q++){var Z=this._attr(Q);if(!B.hasAttributeNS(Z.namespaceURI,Z.localName))return!1;if(B.getAttributeNS(Z.namespaceURI,Z.localName)!==Z.value)return!1}return!0}},_lookupNamespacePrefix:{value:function A(B,Q){if(this.namespaceURI&&this.namespaceURI===B&&this.prefix!==null&&Q.lookupNamespaceURI(this.prefix)===B)return this.prefix;for(var D=0,Z=this._numattrs;D<Z;D++){var G=this._attr(D);if(G.prefix==="xmlns"&&G.value===B&&Q.lookupNamespaceURI(G.localName)===B)return G.localName}var F=this.parentElement;return F?F._lookupNamespacePrefix(B,Q):null}},lookupNamespaceURI:{value:function A(B){if(B===""||B===void 0)B=null;if(this.namespaceURI!==null&&this.prefix===B)return this.namespaceURI;for(var Q=0,D=this._numattrs;Q<D;Q++){var Z=this._attr(Q);if(Z.namespaceURI===rM.XMLNS){if(Z.prefix==="xmlns"&&Z.localName===B||B===null&&Z.prefix===null&&Z.localName==="xmlns")return Z.value||null}}var G=this.parentElement;return G?G.lookupNamespaceURI(B):null}},getAttribute:{value:function A(B){var Q=this.getAttributeNode(B);return Q?Q.value:null}},getAttributeNS:{value:function A(B,Q){var D=this.getAttributeNodeNS(B,Q);return D?D.value:null}},getAttributeNode:{value:function A(B){if(B=String(B),/[A-Z]/.test(B)&&this.isHTML)B=dD.toASCIILowerCase(B);var Q=this._attrsByQName[B];if(!Q)return null;if(Array.isArray(Q))Q=Q[0];return Q}},getAttributeNodeNS:{value:function A(B,Q){B=B===void 0||B===null?"":String(B),Q=String(Q);var D=this._attrsByLName[B+"|"+Q];return D?D:null}},hasAttribute:{value:function A(B){if(B=String(B),/[A-Z]/.test(B)&&this.isHTML)B=dD.toASCIILowerCase(B);return this._attrsByQName[B]!==void 0}},hasAttributeNS:{value:function A(B,Q){B=B===void 0||B===null?"":String(B),Q=String(Q);var D=B+"|"+Q;return this._attrsByLName[D]!==void 0}},hasAttributes:{value:function A(){return this._numattrs>0}},toggleAttribute:{value:function A(B,Q){if(B=String(B),!wL0.isValidName(B))dD.InvalidCharacterError();if(/[A-Z]/.test(B)&&this.isHTML)B=dD.toASCIILowerCase(B);var D=this._attrsByQName[B];if(D===void 0){if(Q===void 0||Q===!0)return this._setAttribute(B,""),!0;return!1}else{if(Q===void 0||Q===!1)return this.removeAttribute(B),!1;return!0}}},_setAttribute:{value:function A(B,Q){var D=this._attrsByQName[B],Z;if(!D)D=this._newattr(B),Z=!0;else if(Array.isArray(D))D=D[0];if(D.value=Q,this._attributes)this._attributes[B]=D;if(Z&&this._newattrhook)this._newattrhook(B,Q)}},setAttribute:{value:function A(B,Q){if(B=String(B),!wL0.isValidName(B))dD.InvalidCharacterError();if(/[A-Z]/.test(B)&&this.isHTML)B=dD.toASCIILowerCase(B);this._setAttribute(B,String(Q))}},_setAttributeNS:{value:function A(B,Q,D){var Z=Q.indexOf(":"),G,F;if(Z<0)G=null,F=Q;else G=Q.substring(0,Z),F=Q.substring(Z+1);if(B===""||B===void 0)B=null;var I=(B===null?"":B)+"|"+F,Y=this._attrsByLName[I],W;if(!Y){if(Y=new WF1(this,F,G,B),W=!0,this._attrsByLName[I]=Y,this._attributes)this._attributes[this._attrKeys.length]=Y;this._attrKeys.push(I),this._addQName(Y)}if(Y.value=D,W&&this._newattrhook)this._newattrhook(Q,D)}},setAttributeNS:{value:function A(B,Q,D){if(B=B===null||B===void 0||B===""?null:String(B),Q=String(Q),!wL0.isValidQName(Q))dD.InvalidCharacterError();var Z=Q.indexOf(":"),G=Z<0?null:Q.substring(0,Z);if(G!==null&&B===null||G==="xml"&&B!==rM.XML||(Q==="xmlns"||G==="xmlns")&&B!==rM.XMLNS||B===rM.XMLNS&&!(Q==="xmlns"||G==="xmlns"))dD.NamespaceError();this._setAttributeNS(B,Q,String(D))}},setAttributeNode:{value:function A(B){if(B.ownerElement!==null&&B.ownerElement!==this)throw new k01(k01.INUSE_ATTRIBUTE_ERR);var Q=null,D=this._attrsByQName[B.name];if(D){if(!Array.isArray(D))D=[D];if(D.some(function(Z){return Z===B}))return B;else if(B.ownerElement!==null)throw new k01(k01.INUSE_ATTRIBUTE_ERR);D.forEach(function(Z){this.removeAttributeNode(Z)},this),Q=D[0]}return this.setAttributeNodeNS(B),Q}},setAttributeNodeNS:{value:function A(B){if(B.ownerElement!==null)throw new k01(k01.INUSE_ATTRIBUTE_ERR);var Q=B.namespaceURI,D=(Q===null?"":Q)+"|"+B.localName,Z=this._attrsByLName[D];if(Z)this.removeAttributeNode(Z);if(B._setOwnerElement(this),this._attrsByLName[D]=B,this._attributes)this._attributes[this._attrKeys.length]=B;if(this._attrKeys.push(D),this._addQName(B),this._newattrhook)this._newattrhook(B.name,B.value);return Z||null}},removeAttribute:{value:function A(B){if(B=String(B),/[A-Z]/.test(B)&&this.isHTML)B=dD.toASCIILowerCase(B);var Q=this._attrsByQName[B];if(!Q)return;if(Array.isArray(Q))if(Q.length>2)Q=Q.shift();else this._attrsByQName[B]=Q[1],Q=Q[0];else this._attrsByQName[B]=void 0;var D=Q.namespaceURI,Z=(D===null?"":D)+"|"+Q.localName;this._attrsByLName[Z]=void 0;var G=this._attrKeys.indexOf(Z);if(this._attributes)Array.prototype.splice.call(this._attributes,G,1),this._attributes[B]=void 0;this._attrKeys.splice(G,1);var F=Q.onchange;if(Q._setOwnerElement(null),F)F.call(Q,this,Q.localName,Q.value,null);if(this.rooted)this.ownerDocument.mutateRemoveAttr(Q)}},removeAttributeNS:{value:function A(B,Q){B=B===void 0||B===null?"":String(B),Q=String(Q);var D=B+"|"+Q,Z=this._attrsByLName[D];if(!Z)return;this._attrsByLName[D]=void 0;var G=this._attrKeys.indexOf(D);if(this._attributes)Array.prototype.splice.call(this._attributes,G,1);this._attrKeys.splice(G,1),this._removeQName(Z);var F=Z.onchange;if(Z._setOwnerElement(null),F)F.call(Z,this,Z.localName,Z.value,null);if(this.rooted)this.ownerDocument.mutateRemoveAttr(Z)}},removeAttributeNode:{value:function A(B){var Q=B.namespaceURI,D=(Q===null?"":Q)+"|"+B.localName;if(this._attrsByLName[D]!==B)dD.NotFoundError();return this.removeAttributeNS(Q,B.localName),B}},getAttributeNames:{value:function A(){var B=this;return this._attrKeys.map(function(Q){return B._attrsByLName[Q].name})}},_getattr:{value:function A(B){var Q=this._attrsByQName[B];return Q?Q.value:null}},_setattr:{value:function A(B,Q){var D=this._attrsByQName[B],Z;if(!D)D=this._newattr(B),Z=!0;if(D.value=String(Q),this._attributes)this._attributes[B]=D;if(Z&&this._newattrhook)this._newattrhook(B,Q)}},_newattr:{value:function A(B){var Q=new WF1(this,B,null,null),D="|"+B;if(this._attrsByQName[B]=Q,this._attrsByLName[D]=Q,this._attributes)this._attributes[this._attrKeys.length]=Q;return this._attrKeys.push(D),Q}},_addQName:{value:function(A){var B=A.name,Q=this._attrsByQName[B];if(!Q)this._attrsByQName[B]=A;else if(Array.isArray(Q))Q.push(A);else this._attrsByQName[B]=[Q,A];if(this._attributes)this._attributes[B]=A}},_removeQName:{value:function(A){var B=A.name,Q=this._attrsByQName[B];if(Array.isArray(Q)){var D=Q.indexOf(A);if(dD.assert(D!==-1),Q.length===2){if(this._attrsByQName[B]=Q[1-D],this._attributes)this._attributes[B]=this._attrsByQName[B]}else if(Q.splice(D,1),this._attributes&&this._attributes[B]===A)this._attributes[B]=Q[0]}else if(dD.assert(Q===A),this._attrsByQName[B]=void 0,this._attributes)this._attributes[B]=void 0}},_numattrs:{get:function(){return this._attrKeys.length}},_attr:{value:function(A){return this._attrsByLName[this._attrKeys[A]]}},id:Bf1.property({name:"id"}),className:Bf1.property({name:"class"}),classList:{get:function(){var A=this;if(this._classList)return this._classList;var B=new aJ8(function(){return A.className||""},function(Q){A.className=Q});return this._classList=B,B},set:function(A){this.className=A}},matches:{value:function(A){return qL0.matches(this,A)}},closest:{value:function(A){var B=this;do{if(B.matches&&B.matches(A))return B;B=B.parentElement||B.parentNode}while(B!==null&&B.nodeType===v$.ELEMENT_NODE);return null}},querySelector:{value:function(A){return qL0(A,this)[0]}},querySelectorAll:{value:function(A){var B=qL0(A,this);return B.item?B:new $L0(B)}}});Object.defineProperties(mv.prototype,sJ8);Object.defineProperties(mv.prototype,rJ8);Bf1.registerChangeHandler(mv,"id",function(A,B,Q,D){if(A.rooted){if(Q)A.ownerDocument.delId(Q,A);if(D)A.ownerDocument.addId(D,A)}});Bf1.registerChangeHandler(mv,"class",function(A,B,Q,D){if(A._classList)A._classList._update()});function WF1(A,B,Q,D,Z){this.localName=B,this.prefix=Q===null||Q===""?null:""+Q,this.namespaceURI=D===null||D===""?null:""+D,this.data=Z,this._setOwnerElement(A)}WF1.prototype=Object.create(Object.prototype,{ownerElement:{get:function(){return this._ownerElement}},_setOwnerElement:{value:function A(B){if(this._ownerElement=B,this.prefix===null&&this.namespaceURI===null&&B)this.onchange=B._attributeChangeHandlers[this.localName];else this.onchange=null}},name:{get:function(){return this.prefix?this.prefix+":"+this.localName:this.localName}},specified:{get:function(){return!0}},value:{get:function(){return this.data},set:function(A){var B=this.data;if(A=A===void 0?"":A+"",A===B)return;if(this.data=A,this.ownerElement){if(this.onchange)this.onchange(this.ownerElement,this.localName,B,A);if(this.ownerElement.rooted)this.ownerElement.ownerDocument.mutateAttr(this,B)}}},cloneNode:{value:function A(B){return new WF1(null,this.localName,this.prefix,this.namespaceURI,this.data)}},nodeType:{get:function(){return v$.ATTRIBUTE_NODE}},nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return this.value},set:function(A){this.value=A}},textContent:{get:function(){return this.value},set:function(A){if(A===null||A===void 0)A="";this.value=A}},innerText:{get:function(){return this.value},set:function(A){if(A===null||A===void 0)A="";this.value=A}}});mv._Attr=WF1;function ML0(A){ukB.call(this,A);for(var B in A._attrsByQName)this[B]=A._attrsByQName[B];for(var Q=0;Q<A._attrKeys.length;Q++)this[Q]=A._attrsByLName[A._attrKeys[Q]]}ML0.prototype=Object.create(ukB.prototype,{length:{get:function(){return this.element._attrKeys.length},set:function(){}},item:{value:function(A){if(A=A>>>0,A>=this.length)return null;return this.element._attrsByLName[this.element._attrKeys[A]]}}});if(globalThis.Symbol?.iterator)ML0.prototype[globalThis.Symbol.iterator]=function(){var A=0,B=this.length,Q=this;return{next:function(){if(A<B)return{value:Q.item(A++)};return{done:!0}}}};function mkB(A){this.element=A,this.updateCache()}mkB.prototype=Object.create(Object.prototype,{length:{get:function(){return this.updateCache(),this.childrenByNumber.length}},item:{value:function A(B){return this.updateCache(),this.childrenByNumber[B]||null}},namedItem:{value:function A(B){return this.updateCache(),this.childrenByName[B]||null}},namedItems:{get:function(){return this.updateCache(),this.childrenByName}},updateCache:{value:function A(){var B=/^(a|applet|area|embed|form|frame|frameset|iframe|img|object)$/;if(this.lastModTime!==this.element.lastModTime){this.lastModTime=this.element.lastModTime;var Q=this.childrenByNumber&&this.childrenByNumber.length||0;for(var D=0;D<Q;D++)this[D]=void 0;this.childrenByNumber=[],this.childrenByName=Object.create(null);for(var Z=this.element.firstChild;Z!==null;Z=Z.nextSibling)if(Z.nodeType===v$.ELEMENT_NODE){this[this.childrenByNumber.length]=Z,this.childrenByNumber.push(Z);var G=Z.getAttribute("id");if(G&&!this.childrenByName[G])this.childrenByName[G]=Z;var F=Z.getAttribute("name");if(F&&this.element.namespaceURI===rM.HTML&&B.test(this.element.localName)&&!this.childrenByName[F])this.childrenByName[G]=Z}}}}});function LL0(A){return function(B){return B.localName===A}}function oJ8(A){var B=dD.toASCIILowerCase(A);if(B===A)return LL0(A);return function(Q){return Q.isHTML?Q.localName===B:Q.localName===A}}function tJ8(A){return function(B){return B.namespaceURI===A}}function eJ8(A,B){return function(Q){return Q.namespaceURI===A&&Q.localName===B}}function AX8(A){return function(B){return A.every(function(Q){return B.classList.contains(Q)})}}function BX8(A){return function(B){if(B.namespaceURI!==rM.HTML)return!1;return B.getAttribute("name")===A}}});
var yyB=E((jyB)=>{Object.defineProperty(jyB,"__esModule",{value:!0});jyB.hyphenate=jyB.parse=void 0;function zX8(A){let B=[],Q=0,D=0,Z=0,G=0,F=0,I=null;while(Q<A.length)switch(A.charCodeAt(Q++)){case 40:D++;break;case 41:D--;break;case 39:if(Z===0)Z=39;else if(Z===39&&A.charCodeAt(Q-1)!==92)Z=0;break;case 34:if(Z===0)Z=34;else if(Z===34&&A.charCodeAt(Q-1)!==92)Z=0;break;case 58:if(!I&&D===0&&Z===0)I=SyB(A.substring(F,Q-1).trim()),G=Q;break;case 59:if(I&&G>0&&D===0&&Z===0){let W=A.substring(G,Q-1).trim();B.push(I,W),F=Q,G=0,I=null}break}if(I&&G){let Y=A.slice(G).trim();B.push(I,Y)}return B}jyB.parse=zX8;function SyB(A){return A.replace(/[a-z][A-Z]/g,(B)=>{return B.charAt(0)+"-"+B.charAt(1)}).toLowerCase()}jyB.hyphenate=SyB});
var zZ=E((TW8)=>{var HZ=cb1(),YG=HZ,OW8=lb1().isApiWritable;TW8.NAMESPACE={HTML:"http://www.w3.org/1999/xhtml",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink"};TW8.IndexSizeError=function(){throw new HZ(YG.INDEX_SIZE_ERR)};TW8.HierarchyRequestError=function(){throw new HZ(YG.HIERARCHY_REQUEST_ERR)};TW8.WrongDocumentError=function(){throw new HZ(YG.WRONG_DOCUMENT_ERR)};TW8.InvalidCharacterError=function(){throw new HZ(YG.INVALID_CHARACTER_ERR)};TW8.NoModificationAllowedError=function(){throw new HZ(YG.NO_MODIFICATION_ALLOWED_ERR)};TW8.NotFoundError=function(){throw new HZ(YG.NOT_FOUND_ERR)};TW8.NotSupportedError=function(){throw new HZ(YG.NOT_SUPPORTED_ERR)};TW8.InvalidStateError=function(){throw new HZ(YG.INVALID_STATE_ERR)};TW8.SyntaxError=function(){throw new HZ(YG.SYNTAX_ERR)};TW8.InvalidModificationError=function(){throw new HZ(YG.INVALID_MODIFICATION_ERR)};TW8.NamespaceError=function(){throw new HZ(YG.NAMESPACE_ERR)};TW8.InvalidAccessError=function(){throw new HZ(YG.INVALID_ACCESS_ERR)};TW8.TypeMismatchError=function(){throw new HZ(YG.TYPE_MISMATCH_ERR)};TW8.SecurityError=function(){throw new HZ(YG.SECURITY_ERR)};TW8.NetworkError=function(){throw new HZ(YG.NETWORK_ERR)};TW8.AbortError=function(){throw new HZ(YG.ABORT_ERR)};TW8.UrlMismatchError=function(){throw new HZ(YG.URL_MISMATCH_ERR)};TW8.QuotaExceededError=function(){throw new HZ(YG.QUOTA_EXCEEDED_ERR)};TW8.TimeoutError=function(){throw new HZ(YG.TIMEOUT_ERR)};TW8.InvalidNodeTypeError=function(){throw new HZ(YG.INVALID_NODE_TYPE_ERR)};TW8.DataCloneError=function(){throw new HZ(YG.DATA_CLONE_ERR)};TW8.nyi=function(){throw new Error("NotYetImplemented")};TW8.shouldOverride=function(){throw new Error("Abstract function; should be overriding in subclass.")};TW8.assert=function(A,B){if(!A)throw new Error("Assertion failed: "+(B||"")+`
`+new Error().stack)};TW8.expose=function(A,B){for(var Q in A)Object.defineProperty(B.prototype,Q,{value:A[Q],writable:OW8})};TW8.merge=function(A,B){for(var Q in B)A[Q]=B[Q]};TW8.documentOrder=function(A,B){return 3-(A.compareDocumentPosition(B)&6)};TW8.toASCIILowerCase=function(A){return A.replace(/[A-Z]+/g,function(B){return B.toLowerCase()})};TW8.toASCIIUpperCase=function(A){return A.replace(/[a-z]+/g,function(B){return B.toUpperCase()})}});

module.exports = ZxB;
