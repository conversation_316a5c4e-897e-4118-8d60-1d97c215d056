// Package extracted with entry point: KS1

var $2B=E((Vu5,w2B)=>{var fX0=J1("node:path"),p1=YM(),vt=Y71(),H2B=new Map([["heic","heif"],["heif","heif"],["avif","avif"],["jpeg","jpeg"],["jpg","jpeg"],["jpe","jpeg"],["tile","tile"],["dz","tile"],["png","png"],["raw","raw"],["tiff","tiff"],["tif","tiff"],["webp","webp"],["gif","gif"],["jp2","jp2"],["jpx","jp2"],["j2k","jp2"],["j2c","jp2"],["jxl","jxl"]]),DN6=/\.(jp[2x]|j2[kc])$/i,z2B=()=>new Error("JP2 output requires libvips with support for OpenJPEG"),E2B=(A)=>1<<31-Math.clz32(Math.ceil(Math.log2(A)));function ZN6(A,B){let Q;if(!p1.string(A))Q=new Error("Missing output file path");else if(p1.string(this.options.input.file)&&fX0.resolve(this.options.input.file)===fX0.resolve(A))Q=new Error("Cannot use same file for input and output");else if(DN6.test(fX0.extname(A))&&!this.constructor.format.jp2k.output.file)Q=z2B();if(Q)if(p1.fn(B))B(Q);else return Promise.reject(Q);else{this.options.fileOut=A;let D=Error();return this._pipeline(B,D)}return this}function GN6(A,B){if(p1.object(A))this._setBooleanOption("resolveWithObject",A.resolveWithObject);else if(this.options.resolveWithObject)this.options.resolveWithObject=!1;this.options.fileOut="";let Q=Error();return this._pipeline(p1.fn(A)?A:B,Q)}function FN6(){return this.options.keepMetadata|=1,this}function IN6(A){if(p1.object(A))for(let[B,Q]of Object.entries(A))if(p1.object(Q))for(let[D,Z]of Object.entries(Q))if(p1.string(Z))this.options.withExif[`exif-${B.toLowerCase()}-${D}`]=Z;else throw p1.invalidParameterError(`${B}.${D}`,"string",Z);else throw p1.invalidParameterError(B,"object",Q);else throw p1.invalidParameterError("exif","object",A);return this.options.withExifMerge=!1,this.keepExif()}function YN6(A){return this.withExif(A),this.options.withExifMerge=!0,this}function WN6(){return this.options.keepMetadata|=8,this}function JN6(A,B){if(p1.string(A))this.options.withIccProfile=A;else throw p1.invalidParameterError("icc","string",A);if(this.keepIccProfile(),p1.object(B)){if(p1.defined(B.attach))if(p1.bool(B.attach)){if(!B.attach)this.options.keepMetadata&=-9}else throw p1.invalidParameterError("attach","boolean",B.attach)}return this}function XN6(){return this.options.keepMetadata=31,this}function VN6(A){if(this.keepMetadata(),this.withIccProfile("srgb"),p1.object(A)){if(p1.defined(A.orientation))if(p1.integer(A.orientation)&&p1.inRange(A.orientation,1,8))this.options.withMetadataOrientation=A.orientation;else throw p1.invalidParameterError("orientation","integer between 1 and 8",A.orientation);if(p1.defined(A.density))if(p1.number(A.density)&&A.density>0)this.options.withMetadataDensity=A.density;else throw p1.invalidParameterError("density","positive number",A.density);if(p1.defined(A.icc))this.withIccProfile(A.icc);if(p1.defined(A.exif))this.withExifMerge(A.exif)}return this}function CN6(A,B){let Q=H2B.get((p1.object(A)&&p1.string(A.id)?A.id:A).toLowerCase());if(!Q)throw p1.invalidParameterError("format",`one of: ${[...H2B.keys()].join(", ")}`,A);return this[Q](B)}function KN6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.jpegQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.progressive))this._setBooleanOption("jpegProgressive",A.progressive);if(p1.defined(A.chromaSubsampling))if(p1.string(A.chromaSubsampling)&&p1.inArray(A.chromaSubsampling,["4:2:0","4:4:4"]))this.options.jpegChromaSubsampling=A.chromaSubsampling;else throw p1.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",A.chromaSubsampling);let B=p1.bool(A.optimizeCoding)?A.optimizeCoding:A.optimiseCoding;if(p1.defined(B))this._setBooleanOption("jpegOptimiseCoding",B);if(p1.defined(A.mozjpeg))if(p1.bool(A.mozjpeg)){if(A.mozjpeg)this.options.jpegTrellisQuantisation=!0,this.options.jpegOvershootDeringing=!0,this.options.jpegOptimiseScans=!0,this.options.jpegProgressive=!0,this.options.jpegQuantisationTable=3}else throw p1.invalidParameterError("mozjpeg","boolean",A.mozjpeg);let Q=p1.bool(A.trellisQuantization)?A.trellisQuantization:A.trellisQuantisation;if(p1.defined(Q))this._setBooleanOption("jpegTrellisQuantisation",Q);if(p1.defined(A.overshootDeringing))this._setBooleanOption("jpegOvershootDeringing",A.overshootDeringing);let D=p1.bool(A.optimizeScans)?A.optimizeScans:A.optimiseScans;if(p1.defined(D)){if(this._setBooleanOption("jpegOptimiseScans",D),D)this.options.jpegProgressive=!0}let Z=p1.number(A.quantizationTable)?A.quantizationTable:A.quantisationTable;if(p1.defined(Z))if(p1.integer(Z)&&p1.inRange(Z,0,8))this.options.jpegQuantisationTable=Z;else throw p1.invalidParameterError("quantisationTable","integer between 0 and 8",Z)}return this._updateFormatOut("jpeg",A)}function HN6(A){if(p1.object(A)){if(p1.defined(A.progressive))this._setBooleanOption("pngProgressive",A.progressive);if(p1.defined(A.compressionLevel))if(p1.integer(A.compressionLevel)&&p1.inRange(A.compressionLevel,0,9))this.options.pngCompressionLevel=A.compressionLevel;else throw p1.invalidParameterError("compressionLevel","integer between 0 and 9",A.compressionLevel);if(p1.defined(A.adaptiveFiltering))this._setBooleanOption("pngAdaptiveFiltering",A.adaptiveFiltering);let B=A.colours||A.colors;if(p1.defined(B))if(p1.integer(B)&&p1.inRange(B,2,256))this.options.pngBitdepth=E2B(B);else throw p1.invalidParameterError("colours","integer between 2 and 256",B);if(p1.defined(A.palette))this._setBooleanOption("pngPalette",A.palette);else if([A.quality,A.effort,A.colours,A.colors,A.dither].some(p1.defined))this._setBooleanOption("pngPalette",!0);if(this.options.pngPalette){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,0,100))this.options.pngQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 0 and 100",A.quality);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,1,10))this.options.pngEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 1 and 10",A.effort);if(p1.defined(A.dither))if(p1.number(A.dither)&&p1.inRange(A.dither,0,1))this.options.pngDither=A.dither;else throw p1.invalidParameterError("dither","number between 0.0 and 1.0",A.dither)}}return this._updateFormatOut("png",A)}function zN6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.webpQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.alphaQuality))if(p1.integer(A.alphaQuality)&&p1.inRange(A.alphaQuality,0,100))this.options.webpAlphaQuality=A.alphaQuality;else throw p1.invalidParameterError("alphaQuality","integer between 0 and 100",A.alphaQuality);if(p1.defined(A.lossless))this._setBooleanOption("webpLossless",A.lossless);if(p1.defined(A.nearLossless))this._setBooleanOption("webpNearLossless",A.nearLossless);if(p1.defined(A.smartSubsample))this._setBooleanOption("webpSmartSubsample",A.smartSubsample);if(p1.defined(A.preset))if(p1.string(A.preset)&&p1.inArray(A.preset,["default","photo","picture","drawing","icon","text"]))this.options.webpPreset=A.preset;else throw p1.invalidParameterError("preset","one of: default, photo, picture, drawing, icon, text",A.preset);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,0,6))this.options.webpEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 0 and 6",A.effort);if(p1.defined(A.minSize))this._setBooleanOption("webpMinSize",A.minSize);if(p1.defined(A.mixed))this._setBooleanOption("webpMixed",A.mixed)}return U2B(A,this.options),this._updateFormatOut("webp",A)}function EN6(A){if(p1.object(A)){if(p1.defined(A.reuse))this._setBooleanOption("gifReuse",A.reuse);if(p1.defined(A.progressive))this._setBooleanOption("gifProgressive",A.progressive);let B=A.colours||A.colors;if(p1.defined(B))if(p1.integer(B)&&p1.inRange(B,2,256))this.options.gifBitdepth=E2B(B);else throw p1.invalidParameterError("colours","integer between 2 and 256",B);if(p1.defined(A.effort))if(p1.number(A.effort)&&p1.inRange(A.effort,1,10))this.options.gifEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 1 and 10",A.effort);if(p1.defined(A.dither))if(p1.number(A.dither)&&p1.inRange(A.dither,0,1))this.options.gifDither=A.dither;else throw p1.invalidParameterError("dither","number between 0.0 and 1.0",A.dither);if(p1.defined(A.interFrameMaxError))if(p1.number(A.interFrameMaxError)&&p1.inRange(A.interFrameMaxError,0,32))this.options.gifInterFrameMaxError=A.interFrameMaxError;else throw p1.invalidParameterError("interFrameMaxError","number between 0.0 and 32.0",A.interFrameMaxError);if(p1.defined(A.interPaletteMaxError))if(p1.number(A.interPaletteMaxError)&&p1.inRange(A.interPaletteMaxError,0,256))this.options.gifInterPaletteMaxError=A.interPaletteMaxError;else throw p1.invalidParameterError("interPaletteMaxError","number between 0.0 and 256.0",A.interPaletteMaxError)}return U2B(A,this.options),this._updateFormatOut("gif",A)}function UN6(A){if(!this.constructor.format.jp2k.output.buffer)throw z2B();if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.jp2Quality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.lossless))if(p1.bool(A.lossless))this.options.jp2Lossless=A.lossless;else throw p1.invalidParameterError("lossless","boolean",A.lossless);if(p1.defined(A.tileWidth))if(p1.integer(A.tileWidth)&&p1.inRange(A.tileWidth,1,32768))this.options.jp2TileWidth=A.tileWidth;else throw p1.invalidParameterError("tileWidth","integer between 1 and 32768",A.tileWidth);if(p1.defined(A.tileHeight))if(p1.integer(A.tileHeight)&&p1.inRange(A.tileHeight,1,32768))this.options.jp2TileHeight=A.tileHeight;else throw p1.invalidParameterError("tileHeight","integer between 1 and 32768",A.tileHeight);if(p1.defined(A.chromaSubsampling))if(p1.string(A.chromaSubsampling)&&p1.inArray(A.chromaSubsampling,["4:2:0","4:4:4"]))this.options.jp2ChromaSubsampling=A.chromaSubsampling;else throw p1.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",A.chromaSubsampling)}return this._updateFormatOut("jp2",A)}function U2B(A,B){if(p1.object(A)&&p1.defined(A.loop))if(p1.integer(A.loop)&&p1.inRange(A.loop,0,65535))B.loop=A.loop;else throw p1.invalidParameterError("loop","integer between 0 and 65535",A.loop);if(p1.object(A)&&p1.defined(A.delay))if(p1.integer(A.delay)&&p1.inRange(A.delay,0,65535))B.delay=[A.delay];else if(Array.isArray(A.delay)&&A.delay.every(p1.integer)&&A.delay.every((Q)=>p1.inRange(Q,0,65535)))B.delay=A.delay;else throw p1.invalidParameterError("delay","integer or an array of integers between 0 and 65535",A.delay)}function wN6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.tiffQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.bitdepth))if(p1.integer(A.bitdepth)&&p1.inArray(A.bitdepth,[1,2,4,8]))this.options.tiffBitdepth=A.bitdepth;else throw p1.invalidParameterError("bitdepth","1, 2, 4 or 8",A.bitdepth);if(p1.defined(A.tile))this._setBooleanOption("tiffTile",A.tile);if(p1.defined(A.tileWidth))if(p1.integer(A.tileWidth)&&A.tileWidth>0)this.options.tiffTileWidth=A.tileWidth;else throw p1.invalidParameterError("tileWidth","integer greater than zero",A.tileWidth);if(p1.defined(A.tileHeight))if(p1.integer(A.tileHeight)&&A.tileHeight>0)this.options.tiffTileHeight=A.tileHeight;else throw p1.invalidParameterError("tileHeight","integer greater than zero",A.tileHeight);if(p1.defined(A.miniswhite))this._setBooleanOption("tiffMiniswhite",A.miniswhite);if(p1.defined(A.pyramid))this._setBooleanOption("tiffPyramid",A.pyramid);if(p1.defined(A.xres))if(p1.number(A.xres)&&A.xres>0)this.options.tiffXres=A.xres;else throw p1.invalidParameterError("xres","number greater than zero",A.xres);if(p1.defined(A.yres))if(p1.number(A.yres)&&A.yres>0)this.options.tiffYres=A.yres;else throw p1.invalidParameterError("yres","number greater than zero",A.yres);if(p1.defined(A.compression))if(p1.string(A.compression)&&p1.inArray(A.compression,["none","jpeg","deflate","packbits","ccittfax4","lzw","webp","zstd","jp2k"]))this.options.tiffCompression=A.compression;else throw p1.invalidParameterError("compression","one of: none, jpeg, deflate, packbits, ccittfax4, lzw, webp, zstd, jp2k",A.compression);if(p1.defined(A.predictor))if(p1.string(A.predictor)&&p1.inArray(A.predictor,["none","horizontal","float"]))this.options.tiffPredictor=A.predictor;else throw p1.invalidParameterError("predictor","one of: none, horizontal, float",A.predictor);if(p1.defined(A.resolutionUnit))if(p1.string(A.resolutionUnit)&&p1.inArray(A.resolutionUnit,["inch","cm"]))this.options.tiffResolutionUnit=A.resolutionUnit;else throw p1.invalidParameterError("resolutionUnit","one of: inch, cm",A.resolutionUnit)}return this._updateFormatOut("tiff",A)}function $N6(A){return this.heif({...A,compression:"av1"})}function qN6(A){if(p1.object(A)){if(p1.string(A.compression)&&p1.inArray(A.compression,["av1","hevc"]))this.options.heifCompression=A.compression;else throw p1.invalidParameterError("compression","one of: av1, hevc",A.compression);if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.heifQuality=A.quality;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);if(p1.defined(A.lossless))if(p1.bool(A.lossless))this.options.heifLossless=A.lossless;else throw p1.invalidParameterError("lossless","boolean",A.lossless);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,0,9))this.options.heifEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 0 and 9",A.effort);if(p1.defined(A.chromaSubsampling))if(p1.string(A.chromaSubsampling)&&p1.inArray(A.chromaSubsampling,["4:2:0","4:4:4"]))this.options.heifChromaSubsampling=A.chromaSubsampling;else throw p1.invalidParameterError("chromaSubsampling","one of: 4:2:0, 4:4:4",A.chromaSubsampling);if(p1.defined(A.bitdepth))if(p1.integer(A.bitdepth)&&p1.inArray(A.bitdepth,[8,10,12])){if(A.bitdepth!==8&&this.constructor.versions.heif)throw p1.invalidParameterError("bitdepth when using prebuilt binaries",8,A.bitdepth);this.options.heifBitdepth=A.bitdepth}else throw p1.invalidParameterError("bitdepth","8, 10 or 12",A.bitdepth)}else throw p1.invalidParameterError("options","Object",A);return this._updateFormatOut("heif",A)}function NN6(A){if(p1.object(A)){if(p1.defined(A.quality))if(p1.integer(A.quality)&&p1.inRange(A.quality,1,100))this.options.jxlDistance=A.quality>=30?0.1+(100-A.quality)*0.09:0.017666666666666667*A.quality*A.quality-1.15*A.quality+25;else throw p1.invalidParameterError("quality","integer between 1 and 100",A.quality);else if(p1.defined(A.distance))if(p1.number(A.distance)&&p1.inRange(A.distance,0,15))this.options.jxlDistance=A.distance;else throw p1.invalidParameterError("distance","number between 0.0 and 15.0",A.distance);if(p1.defined(A.decodingTier))if(p1.integer(A.decodingTier)&&p1.inRange(A.decodingTier,0,4))this.options.jxlDecodingTier=A.decodingTier;else throw p1.invalidParameterError("decodingTier","integer between 0 and 4",A.decodingTier);if(p1.defined(A.lossless))if(p1.bool(A.lossless))this.options.jxlLossless=A.lossless;else throw p1.invalidParameterError("lossless","boolean",A.lossless);if(p1.defined(A.effort))if(p1.integer(A.effort)&&p1.inRange(A.effort,3,9))this.options.jxlEffort=A.effort;else throw p1.invalidParameterError("effort","integer between 3 and 9",A.effort)}return this._updateFormatOut("jxl",A)}function LN6(A){if(p1.object(A)){if(p1.defined(A.depth))if(p1.string(A.depth)&&p1.inArray(A.depth,["char","uchar","short","ushort","int","uint","float","complex","double","dpcomplex"]))this.options.rawDepth=A.depth;else throw p1.invalidParameterError("depth","one of: char, uchar, short, ushort, int, uint, float, complex, double, dpcomplex",A.depth)}return this._updateFormatOut("raw")}function MN6(A){if(p1.object(A)){if(p1.defined(A.size))if(p1.integer(A.size)&&p1.inRange(A.size,1,8192))this.options.tileSize=A.size;else throw p1.invalidParameterError("size","integer between 1 and 8192",A.size);if(p1.defined(A.overlap))if(p1.integer(A.overlap)&&p1.inRange(A.overlap,0,8192)){if(A.overlap>this.options.tileSize)throw p1.invalidParameterError("overlap",`<= size (${this.options.tileSize})`,A.overlap);this.options.tileOverlap=A.overlap}else throw p1.invalidParameterError("overlap","integer between 0 and 8192",A.overlap);if(p1.defined(A.container))if(p1.string(A.container)&&p1.inArray(A.container,["fs","zip"]))this.options.tileContainer=A.container;else throw p1.invalidParameterError("container","one of: fs, zip",A.container);if(p1.defined(A.layout))if(p1.string(A.layout)&&p1.inArray(A.layout,["dz","google","iiif","iiif3","zoomify"]))this.options.tileLayout=A.layout;else throw p1.invalidParameterError("layout","one of: dz, google, iiif, iiif3, zoomify",A.layout);if(p1.defined(A.angle))if(p1.integer(A.angle)&&!(A.angle%90))this.options.tileAngle=A.angle;else throw p1.invalidParameterError("angle","positive/negative multiple of 90",A.angle);if(this._setBackgroundColourOption("tileBackground",A.background),p1.defined(A.depth))if(p1.string(A.depth)&&p1.inArray(A.depth,["onepixel","onetile","one"]))this.options.tileDepth=A.depth;else throw p1.invalidParameterError("depth","one of: onepixel, onetile, one",A.depth);if(p1.defined(A.skipBlanks))if(p1.integer(A.skipBlanks)&&p1.inRange(A.skipBlanks,-1,65535))this.options.tileSkipBlanks=A.skipBlanks;else throw p1.invalidParameterError("skipBlanks","integer between -1 and 255/65535",A.skipBlanks);else if(p1.defined(A.layout)&&A.layout==="google")this.options.tileSkipBlanks=5;let B=p1.bool(A.center)?A.center:A.centre;if(p1.defined(B))this._setBooleanOption("tileCentre",B);if(p1.defined(A.id))if(p1.string(A.id))this.options.tileId=A.id;else throw p1.invalidParameterError("id","string",A.id);if(p1.defined(A.basename))if(p1.string(A.basename))this.options.tileBasename=A.basename;else throw p1.invalidParameterError("basename","string",A.basename)}if(p1.inArray(this.options.formatOut,["jpeg","png","webp"]))this.options.tileFormat=this.options.formatOut;else if(this.options.formatOut!=="input")throw p1.invalidParameterError("format","one of: jpeg, png, webp",this.options.formatOut);return this._updateFormatOut("dz")}function RN6(A){if(!p1.plainObject(A))throw p1.invalidParameterError("options","object",A);if(p1.integer(A.seconds)&&p1.inRange(A.seconds,0,3600))this.options.timeoutSeconds=A.seconds;else throw p1.invalidParameterError("seconds","integer between 0 and 3600",A.seconds);return this}function ON6(A,B){if(!(p1.object(B)&&B.force===!1))this.options.formatOut=A;return this}function TN6(A,B){if(p1.bool(B))this.options[A]=B;else throw p1.invalidParameterError(A,"boolean",B)}function PN6(){if(!this.options.streamOut){this.options.streamOut=!0;let A=Error();this._pipeline(void 0,A)}}function SN6(A,B){if(typeof A==="function"){if(this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),vt.pipeline(this.options,(Q,D,Z)=>{if(Q)A(p1.nativeError(Q,B));else A(null,D,Z)})});else vt.pipeline(this.options,(Q,D,Z)=>{if(Q)A(p1.nativeError(Q,B));else A(null,D,Z)});return this}else if(this.options.streamOut){if(this._isStreamInput()){if(this.once("finish",()=>{this._flattenBufferIn(),vt.pipeline(this.options,(Q,D,Z)=>{if(Q)this.emit("error",p1.nativeError(Q,B));else this.emit("info",Z),this.push(D);this.push(null),this.on("end",()=>this.emit("close"))})}),this.streamInFinished)this.emit("finish")}else vt.pipeline(this.options,(Q,D,Z)=>{if(Q)this.emit("error",p1.nativeError(Q,B));else this.emit("info",Z),this.push(D);this.push(null),this.on("end",()=>this.emit("close"))});return this}else if(this._isStreamInput())return new Promise((Q,D)=>{this.once("finish",()=>{this._flattenBufferIn(),vt.pipeline(this.options,(Z,G,F)=>{if(Z)D(p1.nativeError(Z,B));else if(this.options.resolveWithObject)Q({data:G,info:F});else Q(G)})})});else return new Promise((Q,D)=>{vt.pipeline(this.options,(Z,G,F)=>{if(Z)D(p1.nativeError(Z,B));else if(this.options.resolveWithObject)Q({data:G,info:F});else Q(G)})})}w2B.exports=function(A){Object.assign(A.prototype,{toFile:ZN6,toBuffer:GN6,keepExif:FN6,withExif:IN6,withExifMerge:YN6,keepIccProfile:WN6,withIccProfile:JN6,keepMetadata:XN6,withMetadata:VN6,toFormat:CN6,jpeg:KN6,jp2:UN6,png:HN6,webp:zN6,tiff:wN6,avif:$N6,heif:qN6,jxl:NN6,gif:EN6,raw:LN6,tile:MN6,timeout:RN6,_updateFormatOut:ON6,_setBooleanOption:TN6,_read:PN6,_pipeline:SN6})}});
var AJ=E((kg5,i0B)=>{var oP1=B71(),{MAX_LENGTH:c0B,MAX_SAFE_INTEGER:tP1}=Q71(),{safeRe:l0B,safeSrc:p0B,t:eP1}=St(),Lw6=rP1(),{compareIdentifiers:jt}=HX0();class Q${constructor(A,B){if(B=Lw6(B),A instanceof Q$)if(A.loose===!!B.loose&&A.includePrerelease===!!B.includePrerelease)return A;else A=A.version;else if(typeof A!=="string")throw new TypeError(`Invalid version. Must be a string. Got type "${typeof A}".`);if(A.length>c0B)throw new TypeError(`version is longer than ${c0B} characters`);oP1("SemVer",A,B),this.options=B,this.loose=!!B.loose,this.includePrerelease=!!B.includePrerelease;let Q=A.trim().match(B.loose?l0B[eP1.LOOSE]:l0B[eP1.FULL]);if(!Q)throw new TypeError(`Invalid Version: ${A}`);if(this.raw=A,this.major=+Q[1],this.minor=+Q[2],this.patch=+Q[3],this.major>tP1||this.major<0)throw new TypeError("Invalid major version");if(this.minor>tP1||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>tP1||this.patch<0)throw new TypeError("Invalid patch version");if(!Q[4])this.prerelease=[];else this.prerelease=Q[4].split(".").map((D)=>{if(/^[0-9]+$/.test(D)){let Z=+D;if(Z>=0&&Z<tP1)return Z}return D});this.build=Q[5]?Q[5].split("."):[],this.format()}format(){if(this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length)this.version+=`-${this.prerelease.join(".")}`;return this.version}toString(){return this.version}compare(A){if(oP1("SemVer.compare",this.version,this.options,A),!(A instanceof Q$)){if(typeof A==="string"&&A===this.version)return 0;A=new Q$(A,this.options)}if(A.version===this.version)return 0;return this.compareMain(A)||this.comparePre(A)}compareMain(A){if(!(A instanceof Q$))A=new Q$(A,this.options);return jt(this.major,A.major)||jt(this.minor,A.minor)||jt(this.patch,A.patch)}comparePre(A){if(!(A instanceof Q$))A=new Q$(A,this.options);if(this.prerelease.length&&!A.prerelease.length)return-1;else if(!this.prerelease.length&&A.prerelease.length)return 1;else if(!this.prerelease.length&&!A.prerelease.length)return 0;let B=0;do{let Q=this.prerelease[B],D=A.prerelease[B];if(oP1("prerelease compare",B,Q,D),Q===void 0&&D===void 0)return 0;else if(D===void 0)return 1;else if(Q===void 0)return-1;else if(Q===D)continue;else return jt(Q,D)}while(++B)}compareBuild(A){if(!(A instanceof Q$))A=new Q$(A,this.options);let B=0;do{let Q=this.build[B],D=A.build[B];if(oP1("build compare",B,Q,D),Q===void 0&&D===void 0)return 0;else if(D===void 0)return 1;else if(Q===void 0)return-1;else if(Q===D)continue;else return jt(Q,D)}while(++B)}inc(A,B,Q){if(A.startsWith("pre")){if(!B&&Q===!1)throw new Error("invalid increment argument: identifier is empty");if(B){let D=new RegExp(`^${this.options.loose?p0B[eP1.PRERELEASELOOSE]:p0B[eP1.PRERELEASE]}$`),Z=`-${B}`.match(D);if(!Z||Z[1]!==B)throw new Error(`invalid identifier: ${B}`)}}switch(A){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",B,Q);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",B,Q);break;case"prepatch":this.prerelease.length=0,this.inc("patch",B,Q),this.inc("pre",B,Q);break;case"prerelease":if(this.prerelease.length===0)this.inc("patch",B,Q);this.inc("pre",B,Q);break;case"release":if(this.prerelease.length===0)throw new Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":if(this.minor!==0||this.patch!==0||this.prerelease.length===0)this.major++;this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":if(this.patch!==0||this.prerelease.length===0)this.minor++;this.patch=0,this.prerelease=[];break;case"patch":if(this.prerelease.length===0)this.patch++;this.prerelease=[];break;case"pre":{let D=Number(Q)?1:0;if(this.prerelease.length===0)this.prerelease=[D];else{let Z=this.prerelease.length;while(--Z>=0)if(typeof this.prerelease[Z]==="number")this.prerelease[Z]++,Z=-2;if(Z===-1){if(B===this.prerelease.join(".")&&Q===!1)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(D)}}if(B){let Z=[B,D];if(Q===!1)Z=[B];if(jt(this.prerelease[0],B)===0){if(isNaN(this.prerelease[1]))this.prerelease=Z}else this.prerelease=Z}break}default:throw new Error(`invalid increment argument: ${A}`)}if(this.raw=this.format(),this.build.length)this.raw+=`+${this.build.join(".")}`;return this}}i0B.exports=Q$});
var B71=E((Tg5,b0B)=>{var Iw6=typeof process==="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...A)=>console.error("SEMVER",...A):()=>{};b0B.exports=Iw6});
var BAB=E((bg5,AAB)=>{class e0B{constructor(){this.max=1000,this.map=new Map}get(A){let B=this.map.get(A);if(B===void 0)return;else return this.map.delete(A),this.map.set(A,B),B}delete(A){return this.map.delete(A)}set(A,B){if(!this.delete(A)&&B!==void 0){if(this.map.size>=this.max){let D=this.map.keys().next().value;this.delete(D)}this.map.set(A,B)}return this}}AAB.exports=e0B});
var D71=E((vg5,t0B)=>{var Sw6=qE(),jw6=(A,B,Q)=>Sw6(A,B,Q)>=0;t0B.exports=jw6});
var DS1=E((mg5,FAB)=>{var gw6=qE(),uw6=(A,B,Q)=>gw6(A,B,Q)<=0;FAB.exports=uw6});
var EX0=E((fg5,QAB)=>{var kw6=qE(),yw6=(A,B,Q)=>kw6(A,B,Q)===0;QAB.exports=yw6});
var F2B=E((Yu5,G2B)=>{var Z3=YM(),bX0={clear:"clear",source:"source",over:"over",in:"in",out:"out",atop:"atop",dest:"dest","dest-over":"dest-over","dest-in":"dest-in","dest-out":"dest-out","dest-atop":"dest-atop",xor:"xor",add:"add",saturate:"saturate",multiply:"multiply",screen:"screen",overlay:"overlay",darken:"darken",lighten:"lighten","colour-dodge":"colour-dodge","color-dodge":"colour-dodge","colour-burn":"colour-burn","color-burn":"colour-burn","hard-light":"hard-light","soft-light":"soft-light",difference:"difference",exclusion:"exclusion"};function $q6(A){if(!Array.isArray(A))throw Z3.invalidParameterError("images to composite","array",A);return this.options.composite=A.map((B)=>{if(!Z3.object(B))throw Z3.invalidParameterError("image to composite","object",B);let Q=this._inputOptionsFromObject(B),D={input:this._createInputDescriptor(B.input,Q,{allowStream:!1}),blend:"over",tile:!1,left:0,top:0,hasOffset:!1,gravity:0,premultiplied:!1};if(Z3.defined(B.blend))if(Z3.string(bX0[B.blend]))D.blend=bX0[B.blend];else throw Z3.invalidParameterError("blend","valid blend name",B.blend);if(Z3.defined(B.tile))if(Z3.bool(B.tile))D.tile=B.tile;else throw Z3.invalidParameterError("tile","boolean",B.tile);if(Z3.defined(B.left))if(Z3.integer(B.left))D.left=B.left;else throw Z3.invalidParameterError("left","integer",B.left);if(Z3.defined(B.top))if(Z3.integer(B.top))D.top=B.top;else throw Z3.invalidParameterError("top","integer",B.top);if(Z3.defined(B.top)!==Z3.defined(B.left))throw new Error("Expected both left and top to be set");else D.hasOffset=Z3.integer(B.top)&&Z3.integer(B.left);if(Z3.defined(B.gravity))if(Z3.integer(B.gravity)&&Z3.inRange(B.gravity,0,8))D.gravity=B.gravity;else if(Z3.string(B.gravity)&&Z3.integer(this.constructor.gravity[B.gravity]))D.gravity=this.constructor.gravity[B.gravity];else throw Z3.invalidParameterError("gravity","valid gravity",B.gravity);if(Z3.defined(B.premultiplied))if(Z3.bool(B.premultiplied))D.premultiplied=B.premultiplied;else throw Z3.invalidParameterError("premultiplied","boolean",B.premultiplied);return D}),this}G2B.exports=function(A){A.prototype.composite=$q6,A.blend=bX0}});
var F71=E((cg5,CAB)=>{var G71=Symbol("SemVer ANY");class ZS1{static get ANY(){return G71}constructor(A,B){if(B=YAB(B),A instanceof ZS1)if(A.loose===!!B.loose)return A;else A=A.value;if(A=A.trim().split(/\s+/).join(" "),qX0("comparator",A,B),this.options=B,this.loose=!!B.loose,this.parse(A),this.semver===G71)this.value="";else this.value=this.operator+this.semver.version;qX0("comp",this)}parse(A){let B=this.options.loose?WAB[JAB.COMPARATORLOOSE]:WAB[JAB.COMPARATOR],Q=A.match(B);if(!Q)throw new TypeError(`Invalid comparator: ${A}`);if(this.operator=Q[1]!==void 0?Q[1]:"",this.operator==="=")this.operator="";if(!Q[2])this.semver=G71;else this.semver=new XAB(Q[2],this.options.loose)}toString(){return this.value}test(A){if(qX0("Comparator.test",A,this.options.loose),this.semver===G71||A===G71)return!0;if(typeof A==="string")try{A=new XAB(A,this.options)}catch(B){return!1}return $X0(A,this.operator,this.semver,this.options)}intersects(A,B){if(!(A instanceof ZS1))throw new TypeError("a Comparator is required");if(this.operator===""){if(this.value==="")return!0;return new VAB(A.value,B).test(this.value)}else if(A.operator===""){if(A.value==="")return!0;return new VAB(this.value,B).test(A.semver)}if(B=YAB(B),B.includePrerelease&&(this.value==="<0.0.0-0"||A.value==="<0.0.0-0"))return!1;if(!B.includePrerelease&&(this.value.startsWith("<0.0.0")||A.value.startsWith("<0.0.0")))return!1;if(this.operator.startsWith(">")&&A.operator.startsWith(">"))return!0;if(this.operator.startsWith("<")&&A.operator.startsWith("<"))return!0;if(this.semver.version===A.semver.version&&this.operator.includes("=")&&A.operator.includes("="))return!0;if($X0(this.semver,"<",A.semver,B)&&this.operator.startsWith(">")&&A.operator.startsWith("<"))return!0;if($X0(this.semver,">",A.semver,B)&&this.operator.startsWith("<")&&A.operator.startsWith(">"))return!0;return!1}}CAB.exports=ZS1;var YAB=rP1(),{safeRe:WAB,t:JAB}=St(),$X0=wX0(),qX0=B71(),XAB=AJ(),VAB=NE()});
var HX0=E((jg5,d0B)=>{var u0B=/^[0-9]+$/,m0B=(A,B)=>{let Q=u0B.test(A),D=u0B.test(B);if(Q&&D)A=+A,B=+B;return A===B?0:Q&&!D?-1:D&&!Q?1:A<B?-1:1},Nw6=(A,B)=>m0B(B,A);d0B.exports={compareIdentifiers:m0B,rcompareIdentifiers:Nw6}});
var K2B=E((Xu5,C2B)=>{var VM=YM(),oq6={and:"and",or:"or",eor:"eor"};function tq6(){return this.options.removeAlpha=!0,this}function eq6(A){if(VM.defined(A))if(VM.number(A)&&VM.inRange(A,0,1))this.options.ensureAlpha=A;else throw VM.invalidParameterError("alpha","number between 0 and 1",A);else this.options.ensureAlpha=1;return this}function AN6(A){let B={red:0,green:1,blue:2,alpha:3};if(Object.keys(B).includes(A))A=B[A];if(VM.integer(A)&&VM.inRange(A,0,4))this.options.extractChannel=A;else throw VM.invalidParameterError("channel","integer or one of: red, green, blue, alpha",A);return this}function BN6(A,B){if(Array.isArray(A))A.forEach(function(Q){this.options.joinChannelIn.push(this._createInputDescriptor(Q,B))},this);else this.options.joinChannelIn.push(this._createInputDescriptor(A,B));return this}function QN6(A){if(VM.string(A)&&VM.inArray(A,["and","or","eor"]))this.options.bandBoolOp=A;else throw VM.invalidParameterError("boolOp","one of: and, or, eor",A);return this}C2B.exports=function(A){Object.assign(A.prototype,{removeAlpha:tq6,ensureAlpha:eq6,extractChannel:AN6,joinChannel:BN6,bandbool:QN6}),A.bool=oq6}});
var KS1=E((Hu5,R2B)=>{var $P=yAB();tAB()($P);Z2B()($P);F2B()($P);W2B()($P);V2B()($P);K2B()($P);$2B()($P);M2B()($P);R2B.exports=$P});
var LX0=E((ig5,E$6)=>{E$6.exports={name:"sharp",description:"High performance Node.js image processing, the fastest module to resize JPEG, PNG, WebP, GIF, AVIF and TIFF images",version:"0.33.5",author:"Lovell Fuller <<EMAIL>>",homepage:"https://sharp.pixelplumbing.com",contributors:["Pierre Inglebert <<EMAIL>>","Jonathan Ong <<EMAIL>>","Chanon Sajjamanochai <<EMAIL>>","Juliano Julio <<EMAIL>>","Daniel Gasienica <<EMAIL>>","Julian Walker <<EMAIL>>","Amit Pitaru <<EMAIL>>","Brandon Aaron <<EMAIL>>","Andreas Lind <<EMAIL>>","Maurus Cuelenaere <<EMAIL>>","Linus Unnebäck <<EMAIL>>","Victor Mateevitsi <<EMAIL>>","Alaric Holloway <<EMAIL>>","Bernhard K. Weisshuhn <<EMAIL>>","Chris Riley <<EMAIL>>","David Carley <<EMAIL>>","John Tobin <<EMAIL>>","Kenton Gray <<EMAIL>>","Felix Bünemann <<EMAIL>>","Samy Al Zahrani <<EMAIL>>","Chintan Thakkar <<EMAIL>>","F. Orlando Galashan <<EMAIL>>","Kleis Auke Wolthuizen <<EMAIL>>","Matt Hirsch <<EMAIL>>","Matthias Thoemmes <<EMAIL>>","Patrick Paskaris <<EMAIL>>","Jérémy Lal <<EMAIL>>","Rahul Nanwani <<EMAIL>>","Alice Monday <<EMAIL>>","Kristo Jorgenson <<EMAIL>>","YvesBos <<EMAIL>>","Guy Maliar <<EMAIL>>","Nicolas Coden <<EMAIL>>","Matt Parrish <<EMAIL>>","Marcel Bretschneider <<EMAIL>>","Matthew McEachen <<EMAIL>>","Jarda Kotěšovec <<EMAIL>>","Kenric D'Souza <<EMAIL>>","Oleh Aleinyk <<EMAIL>>","Marcel Bretschneider <<EMAIL>>","Andrea Bianco <<EMAIL>>","Rik Heywood <<EMAIL>>","Thomas Parisot <<EMAIL>>","Nathan Graves <<EMAIL>>","Tom Lokhorst <<EMAIL>>","Espen Hovlandsdal <<EMAIL>>","Sylvain Dumont <<EMAIL>>","Alun Davies <<EMAIL>>","Aidan Hoolachan <<EMAIL>>","Axel Eirola <<EMAIL>>","Freezy <<EMAIL>>","Daiz <<EMAIL>>","Julian Aubourg <<EMAIL>>","Keith Belovay <<EMAIL>>","Michael B. Klein <<EMAIL>>","Jordan Prudhomme <<EMAIL>>","Ilya Ovdin <<EMAIL>>","Andargor <<EMAIL>>","Paul Neave <<EMAIL>>","Brendan Kennedy <<EMAIL>>","Brychan Bennett-Odlum <**************>","Edward Silverton <<EMAIL>>","Roman Malieiev <<EMAIL>>","Tomas Szabo <<EMAIL>>","Robert O'Rourke <<EMAIL>>","Guillermo Alfonso Varela Chouciño <<EMAIL>>","Christian Flintrup <<EMAIL>>","Manan Jadhav <<EMAIL>>","Leon Radley <<EMAIL>>","alza54 <<EMAIL>>","Jacob Smith <<EMAIL>>","Michael Nutt <<EMAIL>>","Brad Parham <<EMAIL>>","Taneli Vatanen <<EMAIL>>","Joris Dugué <<EMAIL>>","Chris Banks <<EMAIL>>","Ompal Singh <<EMAIL>>","Brodan <<EMAIL>>","Ankur Parihar <<EMAIL>>","Brahim Ait elhaj <<EMAIL>>","Mart Jansink <<EMAIL>>","Lachlan Newman <<EMAIL>>","Dennis Beatty <<EMAIL>>","Ingvar Stepanyan <<EMAIL>>","Don Denton <<EMAIL>>"],scripts:{install:"node install/check",clean:"rm -rf src/build/ .nyc_output/ coverage/ test/fixtures/output.*",test:"npm run test-lint && npm run test-unit && npm run test-licensing && npm run test-types","test-lint":"semistandard && cpplint","test-unit":"nyc --reporter=lcov --reporter=text --check-coverage --branches=100 mocha","test-licensing":'license-checker --production --summary --onlyAllow="Apache-2.0;BSD;ISC;LGPL-3.0-or-later;MIT"',"test-leak":"./test/leak/leak.sh","test-types":"tsd","package-from-local-build":"node npm/from-local-build","package-from-github-release":"node npm/from-github-release","docs-build":"node docs/build && node docs/search-index/build","docs-serve":"cd docs && npx serve","docs-publish":"cd docs && npx firebase-tools deploy --project pixelplumbing --only hosting:pixelplumbing-sharp"},type:"commonjs",main:"lib/index.js",types:"lib/index.d.ts",files:["install","lib","src/*.{cc,h,gyp}"],repository:{type:"git",url:"git://github.com/lovell/sharp.git"},keywords:["jpeg","png","webp","avif","tiff","gif","svg","jp2","dzi","image","resize","thumbnail","crop","embed","libvips","vips"],dependencies:{color:"^4.2.3","detect-libc":"^2.0.3",semver:"^7.6.3"},optionalDependencies:{"@img/sharp-darwin-arm64":"0.33.5","@img/sharp-darwin-x64":"0.33.5","@img/sharp-libvips-darwin-arm64":"1.0.4","@img/sharp-libvips-darwin-x64":"1.0.4","@img/sharp-libvips-linux-arm":"1.0.5","@img/sharp-libvips-linux-arm64":"1.0.4","@img/sharp-libvips-linux-s390x":"1.0.4","@img/sharp-libvips-linux-x64":"1.0.4","@img/sharp-libvips-linuxmusl-arm64":"1.0.4","@img/sharp-libvips-linuxmusl-x64":"1.0.4","@img/sharp-linux-arm":"0.33.5","@img/sharp-linux-arm64":"0.33.5","@img/sharp-linux-s390x":"0.33.5","@img/sharp-linux-x64":"0.33.5","@img/sharp-linuxmusl-arm64":"0.33.5","@img/sharp-linuxmusl-x64":"0.33.5","@img/sharp-wasm32":"0.33.5","@img/sharp-win32-ia32":"0.33.5","@img/sharp-win32-x64":"0.33.5"},devDependencies:{"@emnapi/runtime":"^1.2.0","@img/sharp-libvips-dev":"1.0.4","@img/sharp-libvips-dev-wasm32":"1.0.5","@img/sharp-libvips-win32-ia32":"1.0.4","@img/sharp-libvips-win32-x64":"1.0.4","@types/node":"*",async:"^3.2.5",cc:"^3.0.1",emnapi:"^1.2.0","exif-reader":"^2.0.1","extract-zip":"^2.0.1",icc:"^3.0.0","jsdoc-to-markdown":"^8.0.3","license-checker":"^25.0.1",mocha:"^10.7.3","node-addon-api":"^8.1.0",nyc:"^17.0.0",prebuild:"^13.0.1",semistandard:"^17.0.0","tar-fs":"^3.0.6",tsd:"^0.31.1"},license:"Apache-2.0",engines:{node:"^18.17.0 || ^20.3.0 || >=21.0.0"},config:{libvips:">=8.15.3"},funding:{url:"https://opencollective.com/libvips"},binary:{napi_versions:[9]},semistandard:{env:["mocha"]},cc:{linelength:"120",filter:["build/include"]},nyc:{include:["lib"]},tsd:{directory:"test/types/"}}});
var M2B=E((Cu5,L2B)=>{var jN6=J1("node:events"),CS1=sP1(),LE=YM(),{runtimePlatformArch:kN6}=RX0(),eJ=Y71(),q2B=kN6(),hX0=eJ.libvipsVersion(),Cx=eJ.format();Cx.heif.output.alias=["avif","heic"];Cx.jpeg.output.alias=["jpe","jpg"];Cx.tiff.output.alias=["tif"];Cx.jp2k.output.alias=["j2c","j2k","jp2","jpx"];var yN6={nearest:"nearest",bilinear:"bilinear",bicubic:"bicubic",locallyBoundedBicubic:"lbb",nohalo:"nohalo",vertexSplitQuadraticBasisSpline:"vsqbs"},bt={vips:hX0.semver};if(!hX0.isGlobal)if(!hX0.isWasm)try{bt=J1(`@img/sharp-${q2B}/versions`)}catch(A){try{bt=J1(`@img/sharp-libvips-${q2B}/versions`)}catch(B){}}else try{bt=(()=>{throw new Error("Cannot require module "+"@img/sharp-wasm32/versions");})()}catch(A){}bt.sharp=LX0().version;if(bt.heif&&Cx.heif)Cx.heif.input.fileSuffix=[".avif"],Cx.heif.output.alias=["avif"];function N2B(A){if(LE.bool(A))if(A)return eJ.cache(50,20,100);else return eJ.cache(0,0,0);else if(LE.object(A))return eJ.cache(A.memory,A.files,A.items);else return eJ.cache()}N2B(!0);function _N6(A){return eJ.concurrency(LE.integer(A)?A:null)}if(CS1.familySync()===CS1.GLIBC&&!eJ._isUsingJemalloc())eJ.concurrency(1);else if(CS1.familySync()===CS1.MUSL&&eJ.concurrency()===1024)eJ.concurrency(J1("node:os").availableParallelism());var xN6=new jN6.EventEmitter;function vN6(){return eJ.counters()}function bN6(A){return eJ.simd(LE.bool(A)?A:null)}function fN6(A){if(LE.object(A))if(Array.isArray(A.operation)&&A.operation.every(LE.string))eJ.block(A.operation,!0);else throw LE.invalidParameterError("operation","Array<string>",A.operation);else throw LE.invalidParameterError("options","object",A)}function hN6(A){if(LE.object(A))if(Array.isArray(A.operation)&&A.operation.every(LE.string))eJ.block(A.operation,!1);else throw LE.invalidParameterError("operation","Array<string>",A.operation);else throw LE.invalidParameterError("options","object",A)}L2B.exports=function(A){A.cache=N2B,A.concurrency=_N6,A.counters=vN6,A.simd=bN6,A.format=Cx,A.interpolators=yN6,A.versions=bt,A.queue=xN6,A.block=fN6,A.unblock=hN6}});
var NE=E((lg5,EAB)=>{var aw6=/\s+/g;class I71{constructor(A,B){if(B=rw6(B),A instanceof I71)if(A.loose===!!B.loose&&A.includePrerelease===!!B.includePrerelease)return A;else return new I71(A.raw,B);if(A instanceof NX0)return this.raw=A.value,this.set=[[A]],this.formatted=void 0,this;if(this.options=B,this.loose=!!B.loose,this.includePrerelease=!!B.includePrerelease,this.raw=A.trim().replace(aw6," "),this.set=this.raw.split("||").map((Q)=>this.parseRange(Q.trim())).filter((Q)=>Q.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let Q=this.set[0];if(this.set=this.set.filter((D)=>!HAB(D[0])),this.set.length===0)this.set=[Q];else if(this.set.length>1){for(let D of this.set)if(D.length===1&&D$6(D[0])){this.set=[D];break}}}this.formatted=void 0}get range(){if(this.formatted===void 0){this.formatted="";for(let A=0;A<this.set.length;A++){if(A>0)this.formatted+="||";let B=this.set[A];for(let Q=0;Q<B.length;Q++){if(Q>0)this.formatted+=" ";this.formatted+=B[Q].toString().trim()}}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(A){let Q=((this.options.includePrerelease&&B$6)|(this.options.loose&&Q$6))+":"+A,D=KAB.get(Q);if(D)return D;let Z=this.options.loose,G=Z?_V[oJ.HYPHENRANGELOOSE]:_V[oJ.HYPHENRANGE];A=A.replace(G,C$6(this.options.includePrerelease)),N7("hyphen replace",A),A=A.replace(_V[oJ.COMPARATORTRIM],tw6),N7("comparator trim",A),A=A.replace(_V[oJ.TILDETRIM],ew6),N7("tilde trim",A),A=A.replace(_V[oJ.CARETTRIM],A$6),N7("caret trim",A);let F=A.split(" ").map((J)=>Z$6(J,this.options)).join(" ").split(/\s+/).map((J)=>V$6(J,this.options));if(Z)F=F.filter((J)=>{return N7("loose invalid filter",J,this.options),!!J.match(_V[oJ.COMPARATORLOOSE])});N7("range list",F);let I=new Map,Y=F.map((J)=>new NX0(J,this.options));for(let J of Y){if(HAB(J))return[J];I.set(J.value,J)}if(I.size>1&&I.has(""))I.delete("");let W=[...I.values()];return KAB.set(Q,W),W}intersects(A,B){if(!(A instanceof I71))throw new TypeError("a Range is required");return this.set.some((Q)=>{return zAB(Q,B)&&A.set.some((D)=>{return zAB(D,B)&&Q.every((Z)=>{return D.every((G)=>{return Z.intersects(G,B)})})})})}test(A){if(!A)return!1;if(typeof A==="string")try{A=new ow6(A,this.options)}catch(B){return!1}for(let B=0;B<this.set.length;B++)if(K$6(this.set[B],A,this.options))return!0;return!1}}EAB.exports=I71;var sw6=BAB(),KAB=new sw6,rw6=rP1(),NX0=F71(),N7=B71(),ow6=AJ(),{safeRe:_V,t:oJ,comparatorTrimReplace:tw6,tildeTrimReplace:ew6,caretTrimReplace:A$6}=St(),{FLAG_INCLUDE_PRERELEASE:B$6,FLAG_LOOSE:Q$6}=Q71(),HAB=(A)=>A.value==="<0.0.0-0",D$6=(A)=>A.value==="",zAB=(A,B)=>{let Q=!0,D=A.slice(),Z=D.pop();while(Q&&D.length)Q=D.every((G)=>{return Z.intersects(G,B)}),Z=D.pop();return Q},Z$6=(A,B)=>{return N7("comp",A,B),A=I$6(A,B),N7("caret",A),A=G$6(A,B),N7("tildes",A),A=W$6(A,B),N7("xrange",A),A=X$6(A,B),N7("stars",A),A},tJ=(A)=>!A||A.toLowerCase()==="x"||A==="*",G$6=(A,B)=>{return A.trim().split(/\s+/).map((Q)=>F$6(Q,B)).join(" ")},F$6=(A,B)=>{let Q=B.loose?_V[oJ.TILDELOOSE]:_V[oJ.TILDE];return A.replace(Q,(D,Z,G,F,I)=>{N7("tilde",A,D,Z,G,F,I);let Y;if(tJ(Z))Y="";else if(tJ(G))Y=`>=${Z}.0.0 <${+Z+1}.0.0-0`;else if(tJ(F))Y=`>=${Z}.${G}.0 <${Z}.${+G+1}.0-0`;else if(I)N7("replaceTilde pr",I),Y=`>=${Z}.${G}.${F}-${I} <${Z}.${+G+1}.0-0`;else Y=`>=${Z}.${G}.${F} <${Z}.${+G+1}.0-0`;return N7("tilde return",Y),Y})},I$6=(A,B)=>{return A.trim().split(/\s+/).map((Q)=>Y$6(Q,B)).join(" ")},Y$6=(A,B)=>{N7("caret",A,B);let Q=B.loose?_V[oJ.CARETLOOSE]:_V[oJ.CARET],D=B.includePrerelease?"-0":"";return A.replace(Q,(Z,G,F,I,Y)=>{N7("caret",A,Z,G,F,I,Y);let W;if(tJ(G))W="";else if(tJ(F))W=`>=${G}.0.0${D} <${+G+1}.0.0-0`;else if(tJ(I))if(G==="0")W=`>=${G}.${F}.0${D} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.0${D} <${+G+1}.0.0-0`;else if(Y)if(N7("replaceCaret pr",Y),G==="0")if(F==="0")W=`>=${G}.${F}.${I}-${Y} <${G}.${F}.${+I+1}-0`;else W=`>=${G}.${F}.${I}-${Y} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.${I}-${Y} <${+G+1}.0.0-0`;else if(N7("no pr"),G==="0")if(F==="0")W=`>=${G}.${F}.${I}${D} <${G}.${F}.${+I+1}-0`;else W=`>=${G}.${F}.${I}${D} <${G}.${+F+1}.0-0`;else W=`>=${G}.${F}.${I} <${+G+1}.0.0-0`;return N7("caret return",W),W})},W$6=(A,B)=>{return N7("replaceXRanges",A,B),A.split(/\s+/).map((Q)=>J$6(Q,B)).join(" ")},J$6=(A,B)=>{A=A.trim();let Q=B.loose?_V[oJ.XRANGELOOSE]:_V[oJ.XRANGE];return A.replace(Q,(D,Z,G,F,I,Y)=>{N7("xRange",A,D,Z,G,F,I,Y);let W=tJ(G),J=W||tJ(F),X=J||tJ(I),V=X;if(Z==="="&&V)Z="";if(Y=B.includePrerelease?"-0":"",W)if(Z===">"||Z==="<")D="<0.0.0-0";else D="*";else if(Z&&V){if(J)F=0;if(I=0,Z===">")if(Z=">=",J)G=+G+1,F=0,I=0;else F=+F+1,I=0;else if(Z==="<=")if(Z="<",J)G=+G+1;else F=+F+1;if(Z==="<")Y="-0";D=`${Z+G}.${F}.${I}${Y}`}else if(J)D=`>=${G}.0.0${Y} <${+G+1}.0.0-0`;else if(X)D=`>=${G}.${F}.0${Y} <${G}.${+F+1}.0-0`;return N7("xRange return",D),D})},X$6=(A,B)=>{return N7("replaceStars",A,B),A.trim().replace(_V[oJ.STAR],"")},V$6=(A,B)=>{return N7("replaceGTE0",A,B),A.trim().replace(_V[B.includePrerelease?oJ.GTE0PRE:oJ.GTE0],"")},C$6=(A)=>(B,Q,D,Z,G,F,I,Y,W,J,X,V)=>{if(tJ(D))Q="";else if(tJ(Z))Q=`>=${D}.0.0${A?"-0":""}`;else if(tJ(G))Q=`>=${D}.${Z}.0${A?"-0":""}`;else if(F)Q=`>=${Q}`;else Q=`>=${Q}${A?"-0":""}`;if(tJ(W))Y="";else if(tJ(J))Y=`<${+W+1}.0.0-0`;else if(tJ(X))Y=`<${W}.${+J+1}.0-0`;else if(V)Y=`<=${W}.${J}.${X}-${V}`;else if(A)Y=`<${W}.${J}.${+X+1}-0`;else Y=`<=${Y}`;return`${Q} ${Y}`.trim()},K$6=(A,B,Q)=>{for(let D=0;D<A.length;D++)if(!A[D].test(B))return!1;if(B.prerelease.length&&!Q.includePrerelease){for(let D=0;D<A.length;D++){if(N7(A[D].semver),A[D].semver===NX0.ANY)continue;if(A[D].semver.prerelease.length>0){let Z=A[D].semver;if(Z.major===B.major&&Z.minor===B.minor&&Z.patch===B.patch)return!0}}return!1}return!0}});
var PX0=E((tg5,_AB)=>{_AB.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}});
var Q71=E((Pg5,f0B)=>{var Yw6=Number.MAX_SAFE_INTEGER||9007199254740991,Ww6=["major","premajor","minor","preminor","patch","prepatch","prerelease"];f0B.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Yw6,RELEASE_TYPES:Ww6,SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}});
var QS1=E((ug5,GAB)=>{var fw6=qE(),hw6=(A,B,Q)=>fw6(A,B,Q)<0;GAB.exports=hw6});
var RX0=E((ng5,PAB)=>{var{spawnSync:GS1}=J1("node:child_process"),{createHash:U$6}=J1("node:crypto"),NAB=zX0(),w$6=D71(),$$6=kt(),wAB=sP1(),{config:q$6,engines:$AB,optionalDependencies:N$6}=LX0(),L$6=process.env.npm_package_config_libvips||q$6.libvips,LAB=NAB(L$6).version,M$6=["darwin-arm64","darwin-x64","linux-arm","linux-arm64","linux-s390x","linux-x64","linuxmusl-arm64","linuxmusl-x64","win32-ia32","win32-x64"],FS1={encoding:"utf8",shell:!0},R$6=(A)=>{if(A instanceof Error)console.error(`sharp: Installation error: ${A.message}`);else console.log(`sharp: ${A}`)},MAB=()=>wAB.isNonGlibcLinuxSync()?wAB.familySync():"",O$6=()=>`${process.platform}${MAB()}-${process.arch}`,yt=()=>{if(RAB())return"wasm32";let{npm_config_arch:A,npm_config_platform:B,npm_config_libc:Q}=process.env,D=typeof Q==="string"?Q:MAB();return`${B||process.platform}${D}-${A||process.arch}`},T$6=()=>{try{return J1(`@img/sharp-libvips-dev-${yt()}/include`)}catch{try{return (()=>{throw new Error("Cannot require module "+"@img/sharp-libvips-dev/include");})()}catch{}}return""},P$6=()=>{try{return (()=>{throw new Error("Cannot require module "+"@img/sharp-libvips-dev/cplusplus");})()}catch{}return""},S$6=()=>{try{return J1(`@img/sharp-libvips-dev-${yt()}/lib`)}catch{try{return J1(`@img/sharp-libvips-${yt()}/lib`)}catch{}}return""},j$6=()=>{if(process.release?.name==="node"&&process.versions){if(!$$6(process.versions.node,$AB.node))return{found:process.versions.node,expected:$AB.node}}},RAB=()=>{let{CC:A}=process.env;return Boolean(A&&A.endsWith("/emcc"))},k$6=()=>{if(process.platform==="darwin"&&process.arch==="x64")return(GS1("sysctl sysctl.proc_translated",FS1).stdout||"").trim()==="sysctl.proc_translated: 1";return!1},qAB=(A)=>U$6("sha512").update(A).digest("hex"),y$6=()=>{try{let A=qAB(`imgsharp-libvips-${yt()}`),B=NAB(N$6[`@img/sharp-libvips-${yt()}`]).version;return qAB(`${A}npm:${B}`).slice(0,10)}catch{}return""},_$6=()=>GS1(`node-gyp rebuild --directory=src ${RAB()?"--nodedir=emscripten":""}`,{...FS1,stdio:"inherit"}).status,OAB=()=>{if(process.platform!=="win32")return(GS1("pkg-config --modversion vips-cpp",{...FS1,env:{...process.env,PKG_CONFIG_PATH:TAB()}}).stdout||"").trim();else return""},TAB=()=>{if(process.platform!=="win32")return[(GS1('which brew >/dev/null 2>&1 && brew environment --plain | grep PKG_CONFIG_LIBDIR | cut -d" " -f2',FS1).stdout||"").trim(),process.env.PKG_CONFIG_PATH,"/usr/local/lib/pkgconfig","/usr/lib/pkgconfig","/usr/local/libdata/pkgconfig","/usr/libdata/pkgconfig"].filter(Boolean).join(":");else return""},MX0=(A,B,Q)=>{if(Q)Q(`Detected ${B}, skipping search for globally-installed libvips`);return A},x$6=(A)=>{if(Boolean(process.env.SHARP_IGNORE_GLOBAL_LIBVIPS)===!0)return MX0(!1,"SHARP_IGNORE_GLOBAL_LIBVIPS",A);if(Boolean(process.env.SHARP_FORCE_GLOBAL_LIBVIPS)===!0)return MX0(!0,"SHARP_FORCE_GLOBAL_LIBVIPS",A);if(k$6())return MX0(!1,"Rosetta",A);let B=OAB();return!!B&&w$6(B,LAB)};PAB.exports={minimumLibvipsVersion:LAB,prebuiltPlatforms:M$6,buildPlatformArch:yt,buildSharpLibvipsIncludeDir:T$6,buildSharpLibvipsCPlusPlusDir:P$6,buildSharpLibvipsLibDir:S$6,isUnsupportedNodeRuntime:j$6,runtimePlatformArch:O$6,log:R$6,yarnLocator:y$6,spawnRebuild:_$6,globalLibvipsVersion:OAB,pkgConfigPath:TAB,useGlobalLibvips:x$6}});
var SX0=E((Qu5,lAB)=>{var V71=PX0(),cAB={};for(let A of Object.keys(V71))cAB[V71[A]]=A;var J9={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};lAB.exports=J9;for(let A of Object.keys(J9)){if(!("channels"in J9[A]))throw new Error("missing channels property: "+A);if(!("labels"in J9[A]))throw new Error("missing channel labels property: "+A);if(J9[A].labels.length!==J9[A].channels)throw new Error("channel and label counts mismatch: "+A);let{channels:B,labels:Q}=J9[A];delete J9[A].channels,delete J9[A].labels,Object.defineProperty(J9[A],"channels",{value:B}),Object.defineProperty(J9[A],"labels",{value:Q})}J9.rgb.hsl=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255,Z=Math.min(B,Q,D),G=Math.max(B,Q,D),F=G-Z,I,Y;if(G===Z)I=0;else if(B===G)I=(Q-D)/F;else if(Q===G)I=2+(D-B)/F;else if(D===G)I=4+(B-Q)/F;if(I=Math.min(I*60,360),I<0)I+=360;let W=(Z+G)/2;if(G===Z)Y=0;else if(W<=0.5)Y=F/(G+Z);else Y=F/(2-G-Z);return[I,Y*100,W*100]};J9.rgb.hsv=function(A){let B,Q,D,Z,G,F=A[0]/255,I=A[1]/255,Y=A[2]/255,W=Math.max(F,I,Y),J=W-Math.min(F,I,Y),X=function(V){return(W-V)/6/J+0.5};if(J===0)Z=0,G=0;else{if(G=J/W,B=X(F),Q=X(I),D=X(Y),F===W)Z=D-Q;else if(I===W)Z=0.3333333333333333+B-D;else if(Y===W)Z=0.6666666666666666+Q-B;if(Z<0)Z+=1;else if(Z>1)Z-=1}return[Z*360,G*100,W*100]};J9.rgb.hwb=function(A){let B=A[0],Q=A[1],D=A[2],Z=J9.rgb.hsl(A)[0],G=0.00392156862745098*Math.min(B,Math.min(Q,D));return D=1-0.00392156862745098*Math.max(B,Math.max(Q,D)),[Z,G*100,D*100]};J9.rgb.cmyk=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255,Z=Math.min(1-B,1-Q,1-D),G=(1-B-Z)/(1-Z)||0,F=(1-Q-Z)/(1-Z)||0,I=(1-D-Z)/(1-Z)||0;return[G*100,F*100,I*100,Z*100]};function a$6(A,B){return(A[0]-B[0])**2+(A[1]-B[1])**2+(A[2]-B[2])**2}J9.rgb.keyword=function(A){let B=cAB[A];if(B)return B;let Q=1/0,D;for(let Z of Object.keys(V71)){let G=V71[Z],F=a$6(A,G);if(F<Q)Q=F,D=Z}return D};J9.keyword.rgb=function(A){return V71[A]};J9.rgb.xyz=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255;B=B>0.04045?((B+0.055)/1.055)**2.4:B/12.92,Q=Q>0.04045?((Q+0.055)/1.055)**2.4:Q/12.92,D=D>0.04045?((D+0.055)/1.055)**2.4:D/12.92;let Z=B*0.4124+Q*0.3576+D*0.1805,G=B*0.2126+Q*0.7152+D*0.0722,F=B*0.0193+Q*0.1192+D*0.9505;return[Z*100,G*100,F*100]};J9.rgb.lab=function(A){let B=J9.rgb.xyz(A),Q=B[0],D=B[1],Z=B[2];Q/=95.047,D/=100,Z/=108.883,Q=Q>0.008856?Q**0.3333333333333333:7.787*Q+0.13793103448275862,D=D>0.008856?D**0.3333333333333333:7.787*D+0.13793103448275862,Z=Z>0.008856?Z**0.3333333333333333:7.787*Z+0.13793103448275862;let G=116*D-16,F=500*(Q-D),I=200*(D-Z);return[G,F,I]};J9.hsl.rgb=function(A){let B=A[0]/360,Q=A[1]/100,D=A[2]/100,Z,G,F;if(Q===0)return F=D*255,[F,F,F];if(D<0.5)Z=D*(1+Q);else Z=D+Q-D*Q;let I=2*D-Z,Y=[0,0,0];for(let W=0;W<3;W++){if(G=B+0.3333333333333333*-(W-1),G<0)G++;if(G>1)G--;if(6*G<1)F=I+(Z-I)*6*G;else if(2*G<1)F=Z;else if(3*G<2)F=I+(Z-I)*(0.6666666666666666-G)*6;else F=I;Y[W]=F*255}return Y};J9.hsl.hsv=function(A){let B=A[0],Q=A[1]/100,D=A[2]/100,Z=Q,G=Math.max(D,0.01);D*=2,Q*=D<=1?D:2-D,Z*=G<=1?G:2-G;let F=(D+Q)/2,I=D===0?2*Z/(G+Z):2*Q/(D+Q);return[B,I*100,F*100]};J9.hsv.rgb=function(A){let B=A[0]/60,Q=A[1]/100,D=A[2]/100,Z=Math.floor(B)%6,G=B-Math.floor(B),F=255*D*(1-Q),I=255*D*(1-Q*G),Y=255*D*(1-Q*(1-G));switch(D*=255,Z){case 0:return[D,Y,F];case 1:return[I,D,F];case 2:return[F,D,Y];case 3:return[F,I,D];case 4:return[Y,F,D];case 5:return[D,F,I]}};J9.hsv.hsl=function(A){let B=A[0],Q=A[1]/100,D=A[2]/100,Z=Math.max(D,0.01),G,F;F=(2-Q)*D;let I=(2-Q)*Z;return G=Q*Z,G/=I<=1?I:2-I,G=G||0,F/=2,[B,G*100,F*100]};J9.hwb.rgb=function(A){let B=A[0]/360,Q=A[1]/100,D=A[2]/100,Z=Q+D,G;if(Z>1)Q/=Z,D/=Z;let F=Math.floor(6*B),I=1-D;if(G=6*B-F,(F&1)!==0)G=1-G;let Y=Q+G*(I-Q),W,J,X;switch(F){default:case 6:case 0:W=I,J=Y,X=Q;break;case 1:W=Y,J=I,X=Q;break;case 2:W=Q,J=I,X=Y;break;case 3:W=Q,J=Y,X=I;break;case 4:W=Y,J=Q,X=I;break;case 5:W=I,J=Q,X=Y;break}return[W*255,J*255,X*255]};J9.cmyk.rgb=function(A){let B=A[0]/100,Q=A[1]/100,D=A[2]/100,Z=A[3]/100,G=1-Math.min(1,B*(1-Z)+Z),F=1-Math.min(1,Q*(1-Z)+Z),I=1-Math.min(1,D*(1-Z)+Z);return[G*255,F*255,I*255]};J9.xyz.rgb=function(A){let B=A[0]/100,Q=A[1]/100,D=A[2]/100,Z,G,F;return Z=B*3.2406+Q*-1.5372+D*-0.4986,G=B*-0.9689+Q*1.8758+D*0.0415,F=B*0.0557+Q*-0.204+D*1.057,Z=Z>0.0031308?1.055*Z**0.4166666666666667-0.055:Z*12.92,G=G>0.0031308?1.055*G**0.4166666666666667-0.055:G*12.92,F=F>0.0031308?1.055*F**0.4166666666666667-0.055:F*12.92,Z=Math.min(Math.max(0,Z),1),G=Math.min(Math.max(0,G),1),F=Math.min(Math.max(0,F),1),[Z*255,G*255,F*255]};J9.xyz.lab=function(A){let B=A[0],Q=A[1],D=A[2];B/=95.047,Q/=100,D/=108.883,B=B>0.008856?B**0.3333333333333333:7.787*B+0.13793103448275862,Q=Q>0.008856?Q**0.3333333333333333:7.787*Q+0.13793103448275862,D=D>0.008856?D**0.3333333333333333:7.787*D+0.13793103448275862;let Z=116*Q-16,G=500*(B-Q),F=200*(Q-D);return[Z,G,F]};J9.lab.xyz=function(A){let B=A[0],Q=A[1],D=A[2],Z,G,F;G=(B+16)/116,Z=Q/500+G,F=G-D/200;let I=G**3,Y=Z**3,W=F**3;return G=I>0.008856?I:(G-0.13793103448275862)/7.787,Z=Y>0.008856?Y:(Z-0.13793103448275862)/7.787,F=W>0.008856?W:(F-0.13793103448275862)/7.787,Z*=95.047,G*=100,F*=108.883,[Z,G,F]};J9.lab.lch=function(A){let B=A[0],Q=A[1],D=A[2],Z;if(Z=Math.atan2(D,Q)*360/2/Math.PI,Z<0)Z+=360;let F=Math.sqrt(Q*Q+D*D);return[B,F,Z]};J9.lch.lab=function(A){let B=A[0],Q=A[1],Z=A[2]/360*2*Math.PI,G=Q*Math.cos(Z),F=Q*Math.sin(Z);return[B,G,F]};J9.rgb.ansi16=function(A,B=null){let[Q,D,Z]=A,G=B===null?J9.rgb.hsv(A)[2]:B;if(G=Math.round(G/50),G===0)return 30;let F=30+(Math.round(Z/255)<<2|Math.round(D/255)<<1|Math.round(Q/255));if(G===2)F+=60;return F};J9.hsv.ansi16=function(A){return J9.rgb.ansi16(J9.hsv.rgb(A),A[2])};J9.rgb.ansi256=function(A){let B=A[0],Q=A[1],D=A[2];if(B===Q&&Q===D){if(B<8)return 16;if(B>248)return 231;return Math.round((B-8)/247*24)+232}return 16+36*Math.round(B/255*5)+6*Math.round(Q/255*5)+Math.round(D/255*5)};J9.ansi16.rgb=function(A){let B=A%10;if(B===0||B===7){if(A>50)B+=3.5;return B=B/10.5*255,[B,B,B]}let Q=(~~(A>50)+1)*0.5,D=(B&1)*Q*255,Z=(B>>1&1)*Q*255,G=(B>>2&1)*Q*255;return[D,Z,G]};J9.ansi256.rgb=function(A){if(A>=232){let G=(A-232)*10+8;return[G,G,G]}A-=16;let B,Q=Math.floor(A/36)/5*255,D=Math.floor((B=A%36)/6)/5*255,Z=B%6/5*255;return[Q,D,Z]};J9.rgb.hex=function(A){let Q=(((Math.round(A[0])&255)<<16)+((Math.round(A[1])&255)<<8)+(Math.round(A[2])&255)).toString(16).toUpperCase();return"000000".substring(Q.length)+Q};J9.hex.rgb=function(A){let B=A.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!B)return[0,0,0];let Q=B[0];if(B[0].length===3)Q=Q.split("").map((I)=>{return I+I}).join("");let D=parseInt(Q,16),Z=D>>16&255,G=D>>8&255,F=D&255;return[Z,G,F]};J9.rgb.hcg=function(A){let B=A[0]/255,Q=A[1]/255,D=A[2]/255,Z=Math.max(Math.max(B,Q),D),G=Math.min(Math.min(B,Q),D),F=Z-G,I,Y;if(F<1)I=G/(1-F);else I=0;if(F<=0)Y=0;else if(Z===B)Y=(Q-D)/F%6;else if(Z===Q)Y=2+(D-B)/F;else Y=4+(B-Q)/F;return Y/=6,Y%=1,[Y*360,F*100,I*100]};J9.hsl.hcg=function(A){let B=A[1]/100,Q=A[2]/100,D=Q<0.5?2*B*Q:2*B*(1-Q),Z=0;if(D<1)Z=(Q-0.5*D)/(1-D);return[A[0],D*100,Z*100]};J9.hsv.hcg=function(A){let B=A[1]/100,Q=A[2]/100,D=B*Q,Z=0;if(D<1)Z=(Q-D)/(1-D);return[A[0],D*100,Z*100]};J9.hcg.rgb=function(A){let B=A[0]/360,Q=A[1]/100,D=A[2]/100;if(Q===0)return[D*255,D*255,D*255];let Z=[0,0,0],G=B%1*6,F=G%1,I=1-F,Y=0;switch(Math.floor(G)){case 0:Z[0]=1,Z[1]=F,Z[2]=0;break;case 1:Z[0]=I,Z[1]=1,Z[2]=0;break;case 2:Z[0]=0,Z[1]=1,Z[2]=F;break;case 3:Z[0]=0,Z[1]=I,Z[2]=1;break;case 4:Z[0]=F,Z[1]=0,Z[2]=1;break;default:Z[0]=1,Z[1]=0,Z[2]=I}return Y=(1-Q)*D,[(Q*Z[0]+Y)*255,(Q*Z[1]+Y)*255,(Q*Z[2]+Y)*255]};J9.hcg.hsv=function(A){let B=A[1]/100,Q=A[2]/100,D=B+Q*(1-B),Z=0;if(D>0)Z=B/D;return[A[0],Z*100,D*100]};J9.hcg.hsl=function(A){let B=A[1]/100,D=A[2]/100*(1-B)+0.5*B,Z=0;if(D>0&&D<0.5)Z=B/(2*D);else if(D>=0.5&&D<1)Z=B/(2*(1-D));return[A[0],Z*100,D*100]};J9.hcg.hwb=function(A){let B=A[1]/100,Q=A[2]/100,D=B+Q*(1-B);return[A[0],(D-B)*100,(1-D)*100]};J9.hwb.hcg=function(A){let B=A[1]/100,D=1-A[2]/100,Z=D-B,G=0;if(Z<1)G=(D-Z)/(1-Z);return[A[0],Z*100,G*100]};J9.apple.rgb=function(A){return[A[0]/65535*255,A[1]/65535*255,A[2]/65535*255]};J9.rgb.apple=function(A){return[A[0]/255*65535,A[1]/255*65535,A[2]/255*65535]};J9.gray.rgb=function(A){return[A[0]/100*255,A[0]/100*255,A[0]/100*255]};J9.gray.hsl=function(A){return[0,0,A[0]]};J9.gray.hsv=J9.gray.hsl;J9.gray.hwb=function(A){return[0,100,A[0]]};J9.gray.cmyk=function(A){return[0,0,0,A[0]]};J9.gray.lab=function(A){return[A[0],0,0]};J9.gray.hex=function(A){let B=Math.round(A[0]/100*255)&255,D=((B<<16)+(B<<8)+B).toString(16).toUpperCase();return"000000".substring(D.length)+D};J9.rgb.gray=function(A){return[(A[0]+A[1]+A[2])/3/255*100]}});
var St=E((XM,h0B)=>{var{MAX_SAFE_COMPONENT_LENGTH:CX0,MAX_SAFE_BUILD_LENGTH:Jw6,MAX_LENGTH:Xw6}=Q71(),Vw6=B71();XM=h0B.exports={};var Cw6=XM.re=[],Kw6=XM.safeRe=[],CB=XM.src=[],Hw6=XM.safeSrc=[],KB=XM.t={},zw6=0,KX0="[a-zA-Z0-9-]",Ew6=[["\\s",1],["\\d",Xw6],[KX0,Jw6]],Uw6=(A)=>{for(let[B,Q]of Ew6)A=A.split(`${B}*`).join(`${B}{0,${Q}}`).split(`${B}+`).join(`${B}{1,${Q}}`);return A},iQ=(A,B,Q)=>{let D=Uw6(B),Z=zw6++;Vw6(A,Z,B),KB[A]=Z,CB[Z]=B,Hw6[Z]=D,Cw6[Z]=new RegExp(B,Q?"g":void 0),Kw6[Z]=new RegExp(D,Q?"g":void 0)};iQ("NUMERICIDENTIFIER","0|[1-9]\\d*");iQ("NUMERICIDENTIFIERLOOSE","\\d+");iQ("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${KX0}*`);iQ("MAINVERSION",`(${CB[KB.NUMERICIDENTIFIER]})\\.(${CB[KB.NUMERICIDENTIFIER]})\\.(${CB[KB.NUMERICIDENTIFIER]})`);iQ("MAINVERSIONLOOSE",`(${CB[KB.NUMERICIDENTIFIERLOOSE]})\\.(${CB[KB.NUMERICIDENTIFIERLOOSE]})\\.(${CB[KB.NUMERICIDENTIFIERLOOSE]})`);iQ("PRERELEASEIDENTIFIER",`(?:${CB[KB.NUMERICIDENTIFIER]}|${CB[KB.NONNUMERICIDENTIFIER]})`);iQ("PRERELEASEIDENTIFIERLOOSE",`(?:${CB[KB.NUMERICIDENTIFIERLOOSE]}|${CB[KB.NONNUMERICIDENTIFIER]})`);iQ("PRERELEASE",`(?:-(${CB[KB.PRERELEASEIDENTIFIER]}(?:\\.${CB[KB.PRERELEASEIDENTIFIER]})*))`);iQ("PRERELEASELOOSE",`(?:-?(${CB[KB.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${CB[KB.PRERELEASEIDENTIFIERLOOSE]})*))`);iQ("BUILDIDENTIFIER",`${KX0}+`);iQ("BUILD",`(?:\\+(${CB[KB.BUILDIDENTIFIER]}(?:\\.${CB[KB.BUILDIDENTIFIER]})*))`);iQ("FULLPLAIN",`v?${CB[KB.MAINVERSION]}${CB[KB.PRERELEASE]}?${CB[KB.BUILD]}?`);iQ("FULL",`^${CB[KB.FULLPLAIN]}$`);iQ("LOOSEPLAIN",`[v=\\s]*${CB[KB.MAINVERSIONLOOSE]}${CB[KB.PRERELEASELOOSE]}?${CB[KB.BUILD]}?`);iQ("LOOSE",`^${CB[KB.LOOSEPLAIN]}$`);iQ("GTLT","((?:<|>)?=?)");iQ("XRANGEIDENTIFIERLOOSE",`${CB[KB.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`);iQ("XRANGEIDENTIFIER",`${CB[KB.NUMERICIDENTIFIER]}|x|X|\\*`);iQ("XRANGEPLAIN",`[v=\\s]*(${CB[KB.XRANGEIDENTIFIER]})(?:\\.(${CB[KB.XRANGEIDENTIFIER]})(?:\\.(${CB[KB.XRANGEIDENTIFIER]})(?:${CB[KB.PRERELEASE]})?${CB[KB.BUILD]}?)?)?`);iQ("XRANGEPLAINLOOSE",`[v=\\s]*(${CB[KB.XRANGEIDENTIFIERLOOSE]})(?:\\.(${CB[KB.XRANGEIDENTIFIERLOOSE]})(?:\\.(${CB[KB.XRANGEIDENTIFIERLOOSE]})(?:${CB[KB.PRERELEASELOOSE]})?${CB[KB.BUILD]}?)?)?`);iQ("XRANGE",`^${CB[KB.GTLT]}\\s*${CB[KB.XRANGEPLAIN]}$`);iQ("XRANGELOOSE",`^${CB[KB.GTLT]}\\s*${CB[KB.XRANGEPLAINLOOSE]}$`);iQ("COERCEPLAIN",`(^|[^\\d])(\\d{1,${CX0}})(?:\\.(\\d{1,${CX0}}))?(?:\\.(\\d{1,${CX0}}))?`);iQ("COERCE",`${CB[KB.COERCEPLAIN]}(?:$|[^\\d])`);iQ("COERCEFULL",CB[KB.COERCEPLAIN]+`(?:${CB[KB.PRERELEASE]})?(?:${CB[KB.BUILD]})?(?:$|[^\\d])`);iQ("COERCERTL",CB[KB.COERCE],!0);iQ("COERCERTLFULL",CB[KB.COERCEFULL],!0);iQ("LONETILDE","(?:~>?)");iQ("TILDETRIM",`(\\s*)${CB[KB.LONETILDE]}\\s+`,!0);XM.tildeTrimReplace="$1~";iQ("TILDE",`^${CB[KB.LONETILDE]}${CB[KB.XRANGEPLAIN]}$`);iQ("TILDELOOSE",`^${CB[KB.LONETILDE]}${CB[KB.XRANGEPLAINLOOSE]}$`);iQ("LONECARET","(?:\\^)");iQ("CARETTRIM",`(\\s*)${CB[KB.LONECARET]}\\s+`,!0);XM.caretTrimReplace="$1^";iQ("CARET",`^${CB[KB.LONECARET]}${CB[KB.XRANGEPLAIN]}$`);iQ("CARETLOOSE",`^${CB[KB.LONECARET]}${CB[KB.XRANGEPLAINLOOSE]}$`);iQ("COMPARATORLOOSE",`^${CB[KB.GTLT]}\\s*(${CB[KB.LOOSEPLAIN]})$|^$`);iQ("COMPARATOR",`^${CB[KB.GTLT]}\\s*(${CB[KB.FULLPLAIN]})$|^$`);iQ("COMPARATORTRIM",`(\\s*)${CB[KB.GTLT]}\\s*(${CB[KB.LOOSEPLAIN]}|${CB[KB.XRANGEPLAIN]})`,!0);XM.comparatorTrimReplace="$1$2$3";iQ("HYPHENRANGE",`^\\s*(${CB[KB.XRANGEPLAIN]})\\s+-\\s+(${CB[KB.XRANGEPLAIN]})\\s*$`);iQ("HYPHENRANGELOOSE",`^\\s*(${CB[KB.XRANGEPLAINLOOSE]})\\s+-\\s+(${CB[KB.XRANGEPLAINLOOSE]})\\s*$`);iQ("STAR","(<|>)?=?\\s*\\*");iQ("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$");iQ("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")});
var UX0=E((hg5,DAB)=>{var _w6=qE(),xw6=(A,B,Q)=>_w6(A,B,Q)!==0;DAB.exports=xw6});
var V2B=E((Ju5,X2B)=>{var dq6=XS1(),wP=YM(),J2B={multiband:"multiband","b-w":"b-w",bw:"b-w",cmyk:"cmyk",srgb:"srgb"};function cq6(A){return this._setBackgroundColourOption("tint",A),this}function lq6(A){return this.options.greyscale=wP.bool(A)?A:!0,this}function pq6(A){return this.greyscale(A)}function iq6(A){if(!wP.string(A))throw wP.invalidParameterError("colourspace","string",A);return this.options.colourspacePipeline=A,this}function nq6(A){return this.pipelineColourspace(A)}function aq6(A){if(!wP.string(A))throw wP.invalidParameterError("colourspace","string",A);return this.options.colourspace=A,this}function sq6(A){return this.toColourspace(A)}function rq6(A,B){if(wP.defined(B))if(wP.object(B)||wP.string(B)){let Q=dq6(B);this.options[A]=[Q.red(),Q.green(),Q.blue(),Math.round(Q.alpha()*255)]}else throw wP.invalidParameterError("background","object or string",B)}X2B.exports=function(A){Object.assign(A.prototype,{tint:cq6,greyscale:lq6,grayscale:pq6,pipelineColourspace:iq6,pipelineColorspace:nq6,toColourspace:aq6,toColorspace:sq6,_setBackgroundColourOption:rq6}),A.colourspace=J2B,A.colorspace=J2B}});
var W2B=E((Wu5,Y2B)=>{var qq6=XS1(),a0=YM(),I2B={integer:"integer",float:"float",approximate:"approximate"};function Nq6(A,B){if(this.options.useExifOrientation||this.options.angle||this.options.rotationAngle)this.options.debuglog("ignoring previous rotate options");if(!a0.defined(A))this.options.useExifOrientation=!0;else if(a0.integer(A)&&!(A%90))this.options.angle=A;else if(a0.number(A)){if(this.options.rotationAngle=A,a0.object(B)&&B.background){let Q=qq6(B.background);this.options.rotationBackground=[Q.red(),Q.green(),Q.blue(),Math.round(Q.alpha()*255)]}}else throw a0.invalidParameterError("angle","numeric",A);return this}function Lq6(A){return this.options.flip=a0.bool(A)?A:!0,this}function Mq6(A){return this.options.flop=a0.bool(A)?A:!0,this}function Rq6(A,B){let Q=[].concat(...A);if(Q.length===4&&Q.every(a0.number))this.options.affineMatrix=Q;else throw a0.invalidParameterError("matrix","1x4 or 2x2 array",A);if(a0.defined(B))if(a0.object(B)){if(this._setBackgroundColourOption("affineBackground",B.background),a0.defined(B.idx))if(a0.number(B.idx))this.options.affineIdx=B.idx;else throw a0.invalidParameterError("options.idx","number",B.idx);if(a0.defined(B.idy))if(a0.number(B.idy))this.options.affineIdy=B.idy;else throw a0.invalidParameterError("options.idy","number",B.idy);if(a0.defined(B.odx))if(a0.number(B.odx))this.options.affineOdx=B.odx;else throw a0.invalidParameterError("options.odx","number",B.odx);if(a0.defined(B.ody))if(a0.number(B.ody))this.options.affineOdy=B.ody;else throw a0.invalidParameterError("options.ody","number",B.ody);if(a0.defined(B.interpolator))if(a0.inArray(B.interpolator,Object.values(this.constructor.interpolators)))this.options.affineInterpolator=B.interpolator;else throw a0.invalidParameterError("options.interpolator","valid interpolator name",B.interpolator)}else throw a0.invalidParameterError("options","object",B);return this}function Oq6(A,B,Q){if(!a0.defined(A))this.options.sharpenSigma=-1;else if(a0.bool(A))this.options.sharpenSigma=A?-1:0;else if(a0.number(A)&&a0.inRange(A,0.01,1e4)){if(this.options.sharpenSigma=A,a0.defined(B))if(a0.number(B)&&a0.inRange(B,0,1e4))this.options.sharpenM1=B;else throw a0.invalidParameterError("flat","number between 0 and 10000",B);if(a0.defined(Q))if(a0.number(Q)&&a0.inRange(Q,0,1e4))this.options.sharpenM2=Q;else throw a0.invalidParameterError("jagged","number between 0 and 10000",Q)}else if(a0.plainObject(A)){if(a0.number(A.sigma)&&a0.inRange(A.sigma,0.000001,10))this.options.sharpenSigma=A.sigma;else throw a0.invalidParameterError("options.sigma","number between 0.000001 and 10",A.sigma);if(a0.defined(A.m1))if(a0.number(A.m1)&&a0.inRange(A.m1,0,1e6))this.options.sharpenM1=A.m1;else throw a0.invalidParameterError("options.m1","number between 0 and 1000000",A.m1);if(a0.defined(A.m2))if(a0.number(A.m2)&&a0.inRange(A.m2,0,1e6))this.options.sharpenM2=A.m2;else throw a0.invalidParameterError("options.m2","number between 0 and 1000000",A.m2);if(a0.defined(A.x1))if(a0.number(A.x1)&&a0.inRange(A.x1,0,1e6))this.options.sharpenX1=A.x1;else throw a0.invalidParameterError("options.x1","number between 0 and 1000000",A.x1);if(a0.defined(A.y2))if(a0.number(A.y2)&&a0.inRange(A.y2,0,1e6))this.options.sharpenY2=A.y2;else throw a0.invalidParameterError("options.y2","number between 0 and 1000000",A.y2);if(a0.defined(A.y3))if(a0.number(A.y3)&&a0.inRange(A.y3,0,1e6))this.options.sharpenY3=A.y3;else throw a0.invalidParameterError("options.y3","number between 0 and 1000000",A.y3)}else throw a0.invalidParameterError("sigma","number between 0.01 and 10000",A);return this}function Tq6(A){if(!a0.defined(A))this.options.medianSize=3;else if(a0.integer(A)&&a0.inRange(A,1,1000))this.options.medianSize=A;else throw a0.invalidParameterError("size","integer between 1 and 1000",A);return this}function Pq6(A){let B;if(a0.number(A))B=A;else if(a0.plainObject(A)){if(!a0.number(A.sigma))throw a0.invalidParameterError("options.sigma","number between 0.3 and 1000",B);if(B=A.sigma,"precision"in A)if(a0.string(I2B[A.precision]))this.options.precision=I2B[A.precision];else throw a0.invalidParameterError("precision","one of: integer, float, approximate",A.precision);if("minAmplitude"in A)if(a0.number(A.minAmplitude)&&a0.inRange(A.minAmplitude,0.001,1))this.options.minAmpl=A.minAmplitude;else throw a0.invalidParameterError("minAmplitude","number between 0.001 and 1",A.minAmplitude)}if(!a0.defined(A))this.options.blurSigma=-1;else if(a0.bool(A))this.options.blurSigma=A?-1:0;else if(a0.number(B)&&a0.inRange(B,0.3,1000))this.options.blurSigma=B;else throw a0.invalidParameterError("sigma","number between 0.3 and 1000",B);return this}function Sq6(A){if(this.options.flatten=a0.bool(A)?A:!0,a0.object(A))this._setBackgroundColourOption("flattenBackground",A.background);return this}function jq6(){return this.options.unflatten=!0,this}function kq6(A,B){if(!a0.defined(A))this.options.gamma=2.2;else if(a0.number(A)&&a0.inRange(A,1,3))this.options.gamma=A;else throw a0.invalidParameterError("gamma","number between 1.0 and 3.0",A);if(!a0.defined(B))this.options.gammaOut=this.options.gamma;else if(a0.number(B)&&a0.inRange(B,1,3))this.options.gammaOut=B;else throw a0.invalidParameterError("gammaOut","number between 1.0 and 3.0",B);return this}function yq6(A){if(this.options.negate=a0.bool(A)?A:!0,a0.plainObject(A)&&"alpha"in A)if(!a0.bool(A.alpha))throw a0.invalidParameterError("alpha","should be boolean value",A.alpha);else this.options.negateAlpha=A.alpha;return this}function _q6(A){if(a0.plainObject(A)){if(a0.defined(A.lower))if(a0.number(A.lower)&&a0.inRange(A.lower,0,99))this.options.normaliseLower=A.lower;else throw a0.invalidParameterError("lower","number between 0 and 99",A.lower);if(a0.defined(A.upper))if(a0.number(A.upper)&&a0.inRange(A.upper,1,100))this.options.normaliseUpper=A.upper;else throw a0.invalidParameterError("upper","number between 1 and 100",A.upper)}if(this.options.normaliseLower>=this.options.normaliseUpper)throw a0.invalidParameterError("range","lower to be less than upper",`${this.options.normaliseLower} >= ${this.options.normaliseUpper}`);return this.options.normalise=!0,this}function xq6(A){return this.normalise(A)}function vq6(A){if(a0.plainObject(A)){if(a0.integer(A.width)&&A.width>0)this.options.claheWidth=A.width;else throw a0.invalidParameterError("width","integer greater than zero",A.width);if(a0.integer(A.height)&&A.height>0)this.options.claheHeight=A.height;else throw a0.invalidParameterError("height","integer greater than zero",A.height);if(a0.defined(A.maxSlope))if(a0.integer(A.maxSlope)&&a0.inRange(A.maxSlope,0,100))this.options.claheMaxSlope=A.maxSlope;else throw a0.invalidParameterError("maxSlope","integer between 0 and 100",A.maxSlope)}else throw a0.invalidParameterError("options","plain object",A);return this}function bq6(A){if(!a0.object(A)||!Array.isArray(A.kernel)||!a0.integer(A.width)||!a0.integer(A.height)||!a0.inRange(A.width,3,1001)||!a0.inRange(A.height,3,1001)||A.height*A.width!==A.kernel.length)throw new Error("Invalid convolution kernel");if(!a0.integer(A.scale))A.scale=A.kernel.reduce(function(B,Q){return B+Q},0);if(A.scale<1)A.scale=1;if(!a0.integer(A.offset))A.offset=0;return this.options.convKernel=A,this}function fq6(A,B){if(!a0.defined(A))this.options.threshold=128;else if(a0.bool(A))this.options.threshold=A?128:0;else if(a0.integer(A)&&a0.inRange(A,0,255))this.options.threshold=A;else throw a0.invalidParameterError("threshold","integer between 0 and 255",A);if(!a0.object(B)||B.greyscale===!0||B.grayscale===!0)this.options.thresholdGrayscale=!0;else this.options.thresholdGrayscale=!1;return this}function hq6(A,B,Q){if(this.options.boolean=this._createInputDescriptor(A,Q),a0.string(B)&&a0.inArray(B,["and","or","eor"]))this.options.booleanOp=B;else throw a0.invalidParameterError("operator","one of: and, or, eor",B);return this}function gq6(A,B){if(!a0.defined(A)&&a0.number(B))A=1;else if(a0.number(A)&&!a0.defined(B))B=0;if(!a0.defined(A))this.options.linearA=[];else if(a0.number(A))this.options.linearA=[A];else if(Array.isArray(A)&&A.length&&A.every(a0.number))this.options.linearA=A;else throw a0.invalidParameterError("a","number or array of numbers",A);if(!a0.defined(B))this.options.linearB=[];else if(a0.number(B))this.options.linearB=[B];else if(Array.isArray(B)&&B.length&&B.every(a0.number))this.options.linearB=B;else throw a0.invalidParameterError("b","number or array of numbers",B);if(this.options.linearA.length!==this.options.linearB.length)throw new Error("Expected a and b to be arrays of the same length");return this}function uq6(A){if(!Array.isArray(A))throw a0.invalidParameterError("inputMatrix","array",A);if(A.length!==3&&A.length!==4)throw a0.invalidParameterError("inputMatrix","3x3 or 4x4 array",A.length);let B=A.flat().map(Number);if(B.length!==9&&B.length!==16)throw a0.invalidParameterError("inputMatrix","cardinality of 9 or 16",B.length);return this.options.recombMatrix=B,this}function mq6(A){if(!a0.plainObject(A))throw a0.invalidParameterError("options","plain object",A);if("brightness"in A)if(a0.number(A.brightness)&&A.brightness>=0)this.options.brightness=A.brightness;else throw a0.invalidParameterError("brightness","number above zero",A.brightness);if("saturation"in A)if(a0.number(A.saturation)&&A.saturation>=0)this.options.saturation=A.saturation;else throw a0.invalidParameterError("saturation","number above zero",A.saturation);if("hue"in A)if(a0.integer(A.hue))this.options.hue=A.hue%360;else throw a0.invalidParameterError("hue","number",A.hue);if("lightness"in A)if(a0.number(A.lightness))this.options.lightness=A.lightness;else throw a0.invalidParameterError("lightness","number",A.lightness);return this}Y2B.exports=function(A){Object.assign(A.prototype,{rotate:Nq6,flip:Lq6,flop:Mq6,affine:Rq6,sharpen:Oq6,median:Tq6,blur:Pq6,flatten:Sq6,unflatten:jq6,gamma:kq6,negate:yq6,normalise:_q6,normalize:xq6,clahe:vq6,convolve:bq6,threshold:fq6,boolean:hq6,linear:gq6,recomb:uq6,modulate:mq6})}});
var XS1=E((Gu5,sAB)=>{var xt=dAB(),kK=kX0(),aAB=["keyword","gray","hex"],yX0={};for(let A of Object.keys(kK))yX0[[...kK[A].labels].sort().join("")]=A;var JS1={};function EI(A,B){if(!(this instanceof EI))return new EI(A,B);if(B&&B in aAB)B=null;if(B&&!(B in kK))throw new Error("Unknown model: "+B);let Q,D;if(A==null)this.model="rgb",this.color=[0,0,0],this.valpha=1;else if(A instanceof EI)this.model=A.model,this.color=[...A.color],this.valpha=A.valpha;else if(typeof A==="string"){let Z=xt.get(A);if(Z===null)throw new Error("Unable to parse color from string: "+A);this.model=Z.model,D=kK[this.model].channels,this.color=Z.value.slice(0,D),this.valpha=typeof Z.value[D]==="number"?Z.value[D]:1}else if(A.length>0){this.model=B||"rgb",D=kK[this.model].channels;let Z=Array.prototype.slice.call(A,0,D);this.color=_X0(Z,D),this.valpha=typeof A[D]==="number"?A[D]:1}else if(typeof A==="number")this.model="rgb",this.color=[A>>16&255,A>>8&255,A&255],this.valpha=1;else{this.valpha=1;let Z=Object.keys(A);if("alpha"in A)Z.splice(Z.indexOf("alpha"),1),this.valpha=typeof A.alpha==="number"?A.alpha:0;let G=Z.sort().join("");if(!(G in yX0))throw new Error("Unable to parse color from object: "+JSON.stringify(A));this.model=yX0[G];let{labels:F}=kK[this.model],I=[];for(Q=0;Q<F.length;Q++)I.push(A[F[Q]]);this.color=_X0(I)}if(JS1[this.model]){D=kK[this.model].channels;for(Q=0;Q<D;Q++){let Z=JS1[this.model][Q];if(Z)this.color[Q]=Z(this.color[Q])}}if(this.valpha=Math.max(0,Math.min(1,this.valpha)),Object.freeze)Object.freeze(this)}EI.prototype={toString(){return this.string()},toJSON(){return this[this.model]()},string(A){let B=this.model in xt.to?this:this.rgb();B=B.round(typeof A==="number"?A:1);let Q=B.valpha===1?B.color:[...B.color,this.valpha];return xt.to[B.model](Q)},percentString(A){let B=this.rgb().round(typeof A==="number"?A:1),Q=B.valpha===1?B.color:[...B.color,this.valpha];return xt.to.rgb.percent(Q)},array(){return this.valpha===1?[...this.color]:[...this.color,this.valpha]},object(){let A={},{channels:B}=kK[this.model],{labels:Q}=kK[this.model];for(let D=0;D<B;D++)A[Q[D]]=this.color[D];if(this.valpha!==1)A.alpha=this.valpha;return A},unitArray(){let A=this.rgb().color;if(A[0]/=255,A[1]/=255,A[2]/=255,this.valpha!==1)A.push(this.valpha);return A},unitObject(){let A=this.rgb().object();if(A.r/=255,A.g/=255,A.b/=255,this.valpha!==1)A.alpha=this.valpha;return A},round(A){return A=Math.max(A||0,0),new EI([...this.color.map(Zq6(A)),this.valpha],this.model)},alpha(A){if(A!==void 0)return new EI([...this.color,Math.max(0,Math.min(1,A))],this.model);return this.valpha},red:FZ("rgb",0,RF(255)),green:FZ("rgb",1,RF(255)),blue:FZ("rgb",2,RF(255)),hue:FZ(["hsl","hsv","hsl","hwb","hcg"],0,(A)=>(A%360+360)%360),saturationl:FZ("hsl",1,RF(100)),lightness:FZ("hsl",2,RF(100)),saturationv:FZ("hsv",1,RF(100)),value:FZ("hsv",2,RF(100)),chroma:FZ("hcg",1,RF(100)),gray:FZ("hcg",2,RF(100)),white:FZ("hwb",1,RF(100)),wblack:FZ("hwb",2,RF(100)),cyan:FZ("cmyk",0,RF(100)),magenta:FZ("cmyk",1,RF(100)),yellow:FZ("cmyk",2,RF(100)),black:FZ("cmyk",3,RF(100)),x:FZ("xyz",0,RF(95.047)),y:FZ("xyz",1,RF(100)),z:FZ("xyz",2,RF(108.833)),l:FZ("lab",0,RF(100)),a:FZ("lab",1),b:FZ("lab",2),keyword(A){if(A!==void 0)return new EI(A);return kK[this.model].keyword(this.color)},hex(A){if(A!==void 0)return new EI(A);return xt.to.hex(this.rgb().round().color)},hexa(A){if(A!==void 0)return new EI(A);let B=this.rgb().round().color,Q=Math.round(this.valpha*255).toString(16).toUpperCase();if(Q.length===1)Q="0"+Q;return xt.to.hex(B)+Q},rgbNumber(){let A=this.rgb().color;return(A[0]&255)<<16|(A[1]&255)<<8|A[2]&255},luminosity(){let A=this.rgb().color,B=[];for(let[Q,D]of A.entries()){let Z=D/255;B[Q]=Z<=0.04045?Z/12.92:((Z+0.055)/1.055)**2.4}return 0.2126*B[0]+0.7152*B[1]+0.0722*B[2]},contrast(A){let B=this.luminosity(),Q=A.luminosity();if(B>Q)return(B+0.05)/(Q+0.05);return(Q+0.05)/(B+0.05)},level(A){let B=this.contrast(A);if(B>=7)return"AAA";return B>=4.5?"AA":""},isDark(){let A=this.rgb().color;return(A[0]*2126+A[1]*7152+A[2]*722)/1e4<128},isLight(){return!this.isDark()},negate(){let A=this.rgb();for(let B=0;B<3;B++)A.color[B]=255-A.color[B];return A},lighten(A){let B=this.hsl();return B.color[2]+=B.color[2]*A,B},darken(A){let B=this.hsl();return B.color[2]-=B.color[2]*A,B},saturate(A){let B=this.hsl();return B.color[1]+=B.color[1]*A,B},desaturate(A){let B=this.hsl();return B.color[1]-=B.color[1]*A,B},whiten(A){let B=this.hwb();return B.color[1]+=B.color[1]*A,B},blacken(A){let B=this.hwb();return B.color[2]+=B.color[2]*A,B},grayscale(){let A=this.rgb().color,B=A[0]*0.3+A[1]*0.59+A[2]*0.11;return EI.rgb(B,B,B)},fade(A){return this.alpha(this.valpha-this.valpha*A)},opaquer(A){return this.alpha(this.valpha+this.valpha*A)},rotate(A){let B=this.hsl(),Q=B.color[0];return Q=(Q+A)%360,Q=Q<0?360+Q:Q,B.color[0]=Q,B},mix(A,B){if(!A||!A.rgb)throw new Error('Argument to "mix" was not a Color instance, but rather an instance of '+typeof A);let Q=A.rgb(),D=this.rgb(),Z=B===void 0?0.5:B,G=2*Z-1,F=Q.alpha()-D.alpha(),I=((G*F===-1?G:(G+F)/(1+G*F))+1)/2,Y=1-I;return EI.rgb(I*Q.red()+Y*D.red(),I*Q.green()+Y*D.green(),I*Q.blue()+Y*D.blue(),Q.alpha()*Z+D.alpha()*(1-Z))}};for(let A of Object.keys(kK)){if(aAB.includes(A))continue;let{channels:B}=kK[A];EI.prototype[A]=function(...Q){if(this.model===A)return new EI(this);if(Q.length>0)return new EI(Q,A);return new EI([...Gq6(kK[this.model][A].raw(this.color)),this.valpha],A)},EI[A]=function(...Q){let D=Q[0];if(typeof D==="number")D=_X0(Q,B);return new EI(D,A)}}function Dq6(A,B){return Number(A.toFixed(B))}function Zq6(A){return function(B){return Dq6(B,A)}}function FZ(A,B,Q){A=Array.isArray(A)?A:[A];for(let D of A)(JS1[D]||(JS1[D]=[]))[B]=Q;return A=A[0],function(D){let Z;if(D!==void 0){if(Q)D=Q(D);return Z=this[A](),Z.color[B]=D,Z}if(Z=this[A]().color[B],Q)Z=Q(Z);return Z}}function RF(A){return function(B){return Math.max(0,Math.min(A,B))}}function Gq6(A){return Array.isArray(A)?A:[A]}function _X0(A,B){for(let Q=0;Q<B;Q++)if(typeof A[Q]!=="number")A[Q]=0;return A}sAB.exports=EI});
var Y71=E((sg5,jAB)=>{var{familySync:v$6,versionSync:b$6}=sP1(),{runtimePlatformArch:f$6,isUnsupportedNodeRuntime:SAB,prebuiltPlatforms:h$6,minimumLibvipsVersion:g$6}=RX0(),ou=f$6(),u$6=[`../src/build/Release/sharp-${ou}.node`,"../src/build/Release/sharp-wasm32.node",`@img/sharp-${ou}/sharp.node`,"@img/sharp-wasm32/sharp.node"],OX0,IS1=[];for(let A of u$6)try{OX0=J1(A);break}catch(B){IS1.push(B)}if(OX0)jAB.exports=OX0;else{let[A,B,Q]=["linux","darwin","win32"].map((G)=>ou.startsWith(G)),D=[`Could not load the "sharp" module using the ${ou} runtime`];IS1.forEach((G)=>{if(G.code!=="MODULE_NOT_FOUND")D.push(`${G.code}: ${G.message}`)});let Z=IS1.map((G)=>G.message).join(" ");if(D.push("Possible solutions:"),SAB()){let{found:G,expected:F}=SAB();D.push("- Please upgrade Node.js:",`    Found ${G}`,`    Requires ${F}`)}else if(h$6.includes(ou)){let[G,F]=ou.split("-"),I=G.endsWith("musl")?" --libc=musl":"";D.push("- Ensure optional dependencies can be installed:","    npm install --include=optional sharp","- Ensure your package manager supports multi-platform installation:","    See https://sharp.pixelplumbing.com/install#cross-platform","- Add platform-specific dependencies:",`    npm install --os=${G.replace("musl","")}${I} --cpu=${F} sharp`)}else D.push(`- Manually install libvips >= ${g$6}`,"- Add experimental WebAssembly-based dependencies:","    npm install --cpu=wasm32 sharp","    npm install @img/sharp-wasm32");if(A&&/(symbol not found|CXXABI_)/i.test(Z))try{let{config:G}=J1(`@img/sharp-libvips-${ou}/package`),F=`${v$6()} ${b$6()}`,I=`${G.musl?"musl":"glibc"} ${G.musl||G.glibc}`;D.push("- Update your OS:",`    Found ${F}`,`    Requires ${I}`)}catch(G){}if(A&&/\/snap\/core[0-9]{2}/.test(Z))D.push("- Remove the Node.js Snap, which does not support native modules","    snap remove node");if(B&&/Incompatible library version/.test(Z))D.push("- Update Homebrew:","    brew update && brew upgrade vips");if(IS1.some((G)=>G.code==="ERR_DLOPEN_DISABLED"))D.push("- Run Node.js without using the --no-addons flag");if(Q&&/The specified procedure could not be found/.test(Z))D.push("- Using the canvas package on Windows?","    See https://sharp.pixelplumbing.com/install#canvas-and-windows","- Check for outdated versions of sharp in the dependency tree:","    npm ls sharp");throw D.push("- Consult the installation documentation:","    See https://sharp.pixelplumbing.com/install"),new Error(D.join(`
`))}});
var YM=E((Lg5,C0B)=>{var V0B=function(A){return typeof A!=="undefined"&&A!==null},vU6=function(A){return typeof A==="object"},bU6=function(A){return Object.prototype.toString.call(A)==="[object Object]"},fU6=function(A){return typeof A==="function"},hU6=function(A){return typeof A==="boolean"},gU6=function(A){return A instanceof Buffer},uU6=function(A){if(V0B(A))switch(A.constructor){case Uint8Array:case Uint8ClampedArray:case Int8Array:case Uint16Array:case Int16Array:case Uint32Array:case Int32Array:case Float32Array:case Float64Array:return!0}return!1},mU6=function(A){return A instanceof ArrayBuffer},dU6=function(A){return typeof A==="string"&&A.length>0},cU6=function(A){return typeof A==="number"&&!Number.isNaN(A)},lU6=function(A){return Number.isInteger(A)},pU6=function(A,B,Q){return A>=B&&A<=Q},iU6=function(A,B){return B.includes(A)},nU6=function(A,B,Q){return new Error(`Expected ${B} for ${A} but received ${Q} of type ${typeof Q}`)},aU6=function(A,B){return B.message=A.message,B};C0B.exports={defined:V0B,object:vU6,plainObject:bU6,fn:fU6,bool:hU6,buffer:gU6,typedArray:uU6,arrayBuffer:mU6,string:dU6,number:cU6,integer:lU6,inRange:pU6,inArray:iU6,invalidParameterError:nU6,nativeError:aU6}});
var Z2B=E((Iu5,D2B)=>{var q9=YM(),A2B={center:0,centre:0,north:1,east:2,south:3,west:4,northeast:5,southeast:6,southwest:7,northwest:8},B2B={top:1,right:2,bottom:3,left:4,"right top":5,"right bottom":6,"left bottom":7,"left top":8},eAB={background:"background",copy:"copy",repeat:"repeat",mirror:"mirror"},Q2B={entropy:16,attention:17},xX0={nearest:"nearest",linear:"linear",cubic:"cubic",mitchell:"mitchell",lanczos2:"lanczos2",lanczos3:"lanczos3"},Kq6={contain:"contain",cover:"cover",fill:"fill",inside:"inside",outside:"outside"},Hq6={contain:"embed",cover:"crop",fill:"ignore_aspect",inside:"max",outside:"min"};function vX0(A){return A.angle%360!==0||A.useExifOrientation===!0||A.rotationAngle!==0}function VS1(A){return A.width!==-1||A.height!==-1}function zq6(A,B,Q){if(VS1(this.options))this.options.debuglog("ignoring previous resize options");if(this.options.widthPost!==-1)this.options.debuglog("operation order will be: extract, resize, extract");if(q9.defined(A))if(q9.object(A)&&!q9.defined(Q))Q=A;else if(q9.integer(A)&&A>0)this.options.width=A;else throw q9.invalidParameterError("width","positive integer",A);else this.options.width=-1;if(q9.defined(B))if(q9.integer(B)&&B>0)this.options.height=B;else throw q9.invalidParameterError("height","positive integer",B);else this.options.height=-1;if(q9.object(Q)){if(q9.defined(Q.width))if(q9.integer(Q.width)&&Q.width>0)this.options.width=Q.width;else throw q9.invalidParameterError("width","positive integer",Q.width);if(q9.defined(Q.height))if(q9.integer(Q.height)&&Q.height>0)this.options.height=Q.height;else throw q9.invalidParameterError("height","positive integer",Q.height);if(q9.defined(Q.fit)){let D=Hq6[Q.fit];if(q9.string(D))this.options.canvas=D;else throw q9.invalidParameterError("fit","valid fit",Q.fit)}if(q9.defined(Q.position)){let D=q9.integer(Q.position)?Q.position:Q2B[Q.position]||B2B[Q.position]||A2B[Q.position];if(q9.integer(D)&&(q9.inRange(D,0,8)||q9.inRange(D,16,17)))this.options.position=D;else throw q9.invalidParameterError("position","valid position/gravity/strategy",Q.position)}if(this._setBackgroundColourOption("resizeBackground",Q.background),q9.defined(Q.kernel))if(q9.string(xX0[Q.kernel]))this.options.kernel=xX0[Q.kernel];else throw q9.invalidParameterError("kernel","valid kernel name",Q.kernel);if(q9.defined(Q.withoutEnlargement))this._setBooleanOption("withoutEnlargement",Q.withoutEnlargement);if(q9.defined(Q.withoutReduction))this._setBooleanOption("withoutReduction",Q.withoutReduction);if(q9.defined(Q.fastShrinkOnLoad))this._setBooleanOption("fastShrinkOnLoad",Q.fastShrinkOnLoad)}if(vX0(this.options)&&VS1(this.options))this.options.rotateBeforePreExtract=!0;return this}function Eq6(A){if(q9.integer(A)&&A>0)this.options.extendTop=A,this.options.extendBottom=A,this.options.extendLeft=A,this.options.extendRight=A;else if(q9.object(A)){if(q9.defined(A.top))if(q9.integer(A.top)&&A.top>=0)this.options.extendTop=A.top;else throw q9.invalidParameterError("top","positive integer",A.top);if(q9.defined(A.bottom))if(q9.integer(A.bottom)&&A.bottom>=0)this.options.extendBottom=A.bottom;else throw q9.invalidParameterError("bottom","positive integer",A.bottom);if(q9.defined(A.left))if(q9.integer(A.left)&&A.left>=0)this.options.extendLeft=A.left;else throw q9.invalidParameterError("left","positive integer",A.left);if(q9.defined(A.right))if(q9.integer(A.right)&&A.right>=0)this.options.extendRight=A.right;else throw q9.invalidParameterError("right","positive integer",A.right);if(this._setBackgroundColourOption("extendBackground",A.background),q9.defined(A.extendWith))if(q9.string(eAB[A.extendWith]))this.options.extendWith=eAB[A.extendWith];else throw q9.invalidParameterError("extendWith","one of: background, copy, repeat, mirror",A.extendWith)}else throw q9.invalidParameterError("extend","integer or object",A);return this}function Uq6(A){let B=VS1(this.options)||this.options.widthPre!==-1?"Post":"Pre";if(this.options[`width${B}`]!==-1)this.options.debuglog("ignoring previous extract options");if(["left","top","width","height"].forEach(function(Q){let D=A[Q];if(q9.integer(D)&&D>=0)this.options[Q+(Q==="left"||Q==="top"?"Offset":"")+B]=D;else throw q9.invalidParameterError(Q,"integer",D)},this),vX0(this.options)&&!VS1(this.options)){if(this.options.widthPre===-1||this.options.widthPost===-1)this.options.rotateBeforePreExtract=!0}return this}function wq6(A){if(this.options.trimThreshold=10,q9.defined(A))if(q9.object(A)){if(q9.defined(A.background))this._setBackgroundColourOption("trimBackground",A.background);if(q9.defined(A.threshold))if(q9.number(A.threshold)&&A.threshold>=0)this.options.trimThreshold=A.threshold;else throw q9.invalidParameterError("threshold","positive number",A.threshold);if(q9.defined(A.lineArt))this._setBooleanOption("trimLineArt",A.lineArt)}else throw q9.invalidParameterError("trim","object",A);if(vX0(this.options))this.options.rotateBeforePreExtract=!0;return this}D2B.exports=function(A){Object.assign(A.prototype,{resize:zq6,extend:Eq6,extract:Uq6,trim:wq6}),A.gravity=A2B,A.strategy=Q2B,A.kernel=xX0,A.fit=Kq6,A.position=B2B}});
var Z71=E((gg5,ZAB)=>{var vw6=qE(),bw6=(A,B,Q)=>vw6(A,B,Q)>0;ZAB.exports=bw6});
var dAB=E((Bu5,mAB)=>{var J71=PX0(),X71=hAB(),gAB=Object.hasOwnProperty,uAB=Object.create(null);for(W71 in J71)if(gAB.call(J71,W71))uAB[J71[W71]]=W71;var W71,jK=mAB.exports={to:{},get:{}};jK.get=function(A){var B=A.substring(0,3).toLowerCase(),Q,D;switch(B){case"hsl":Q=jK.get.hsl(A),D="hsl";break;case"hwb":Q=jK.get.hwb(A),D="hwb";break;default:Q=jK.get.rgb(A),D="rgb";break}if(!Q)return null;return{model:D,value:Q}};jK.get.rgb=function(A){if(!A)return null;var B=/^#([a-f0-9]{3,4})$/i,Q=/^#([a-f0-9]{6})([a-f0-9]{2})?$/i,D=/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,Z=/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/,G=/^(\w+)$/,F=[0,0,0,1],I,Y,W;if(I=A.match(Q)){W=I[2],I=I[1];for(Y=0;Y<3;Y++){var J=Y*2;F[Y]=parseInt(I.slice(J,J+2),16)}if(W)F[3]=parseInt(W,16)/255}else if(I=A.match(B)){I=I[1],W=I[3];for(Y=0;Y<3;Y++)F[Y]=parseInt(I[Y]+I[Y],16);if(W)F[3]=parseInt(W+W,16)/255}else if(I=A.match(D)){for(Y=0;Y<3;Y++)F[Y]=parseInt(I[Y+1],0);if(I[4])if(I[5])F[3]=parseFloat(I[4])*0.01;else F[3]=parseFloat(I[4])}else if(I=A.match(Z)){for(Y=0;Y<3;Y++)F[Y]=Math.round(parseFloat(I[Y+1])*2.55);if(I[4])if(I[5])F[3]=parseFloat(I[4])*0.01;else F[3]=parseFloat(I[4])}else if(I=A.match(G)){if(I[1]==="transparent")return[0,0,0,0];if(!gAB.call(J71,I[1]))return null;return F=J71[I[1]],F[3]=1,F}else return null;for(Y=0;Y<3;Y++)F[Y]=Xx(F[Y],0,255);return F[3]=Xx(F[3],0,1),F};jK.get.hsl=function(A){if(!A)return null;var B=/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,Q=A.match(B);if(Q){var D=parseFloat(Q[4]),Z=(parseFloat(Q[1])%360+360)%360,G=Xx(parseFloat(Q[2]),0,100),F=Xx(parseFloat(Q[3]),0,100),I=Xx(isNaN(D)?1:D,0,1);return[Z,G,F,I]}return null};jK.get.hwb=function(A){if(!A)return null;var B=/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/,Q=A.match(B);if(Q){var D=parseFloat(Q[4]),Z=(parseFloat(Q[1])%360+360)%360,G=Xx(parseFloat(Q[2]),0,100),F=Xx(parseFloat(Q[3]),0,100),I=Xx(isNaN(D)?1:D,0,1);return[Z,G,F,I]}return null};jK.to.hex=function(){var A=X71(arguments);return"#"+YS1(A[0])+YS1(A[1])+YS1(A[2])+(A[3]<1?YS1(Math.round(A[3]*255)):"")};jK.to.rgb=function(){var A=X71(arguments);return A.length<4||A[3]===1?"rgb("+Math.round(A[0])+", "+Math.round(A[1])+", "+Math.round(A[2])+")":"rgba("+Math.round(A[0])+", "+Math.round(A[1])+", "+Math.round(A[2])+", "+A[3]+")"};jK.to.rgb.percent=function(){var A=X71(arguments),B=Math.round(A[0]/255*100),Q=Math.round(A[1]/255*100),D=Math.round(A[2]/255*100);return A.length<4||A[3]===1?"rgb("+B+"%, "+Q+"%, "+D+"%)":"rgba("+B+"%, "+Q+"%, "+D+"%, "+A[3]+")"};jK.to.hsl=function(){var A=X71(arguments);return A.length<4||A[3]===1?"hsl("+A[0]+", "+A[1]+"%, "+A[2]+"%)":"hsla("+A[0]+", "+A[1]+"%, "+A[2]+"%, "+A[3]+")"};jK.to.hwb=function(){var A=X71(arguments),B="";if(A.length>=4&&A[3]!==1)B=", "+A[3];return"hwb("+A[0]+", "+A[1]+"%, "+A[2]+"%"+B+")"};jK.to.keyword=function(A){return uAB[A.slice(0,3)]};function Xx(A,B,Q){return Math.min(Math.max(B,A),Q)}function YS1(A){var B=Math.round(A).toString(16).toUpperCase();return B.length<2?"0"+B:B}});
var hAB=E((Au5,fAB)=>{var p$6=vAB(),i$6=Array.prototype.concat,n$6=Array.prototype.slice,bAB=fAB.exports=function A(B){var Q=[];for(var D=0,Z=B.length;D<Z;D++){var G=B[D];if(p$6(G))Q=i$6.call(Q,n$6.call(G));else Q.push(G)}return Q};bAB.wrap=function(A){return function(){return A(bAB(arguments))}}});
var iAB=E((Du5,pAB)=>{var WS1=SX0();function s$6(){let A={},B=Object.keys(WS1);for(let Q=B.length,D=0;D<Q;D++)A[B[D]]={distance:-1,parent:null};return A}function r$6(A){let B=s$6(),Q=[A];B[A].distance=0;while(Q.length){let D=Q.pop(),Z=Object.keys(WS1[D]);for(let G=Z.length,F=0;F<G;F++){let I=Z[F],Y=B[I];if(Y.distance===-1)Y.distance=B[D].distance+1,Y.parent=D,Q.unshift(I)}}return B}function o$6(A,B){return function(Q){return B(A(Q))}}function t$6(A,B){let Q=[B[A].parent,A],D=WS1[B[A].parent][A],Z=B[A].parent;while(B[Z].parent)Q.unshift(B[Z].parent),D=o$6(WS1[B[Z].parent][Z],D),Z=B[Z].parent;return D.conversion=Q,D}pAB.exports=function(A){let B=r$6(A),Q={},D=Object.keys(B);for(let Z=D.length,G=0;G<Z;G++){let F=D[G];if(B[F].parent===null)continue;Q[F]=t$6(F,B)}return Q}});
var kX0=E((Zu5,nAB)=>{var jX0=SX0(),e$6=iAB(),_t={},Aq6=Object.keys(jX0);function Bq6(A){let B=function(...Q){let D=Q[0];if(D===void 0||D===null)return D;if(D.length>1)Q=D;return A(Q)};if("conversion"in A)B.conversion=A.conversion;return B}function Qq6(A){let B=function(...Q){let D=Q[0];if(D===void 0||D===null)return D;if(D.length>1)Q=D;let Z=A(Q);if(typeof Z==="object")for(let G=Z.length,F=0;F<G;F++)Z[F]=Math.round(Z[F]);return Z};if("conversion"in A)B.conversion=A.conversion;return B}Aq6.forEach((A)=>{_t[A]={},Object.defineProperty(_t[A],"channels",{value:jX0[A].channels}),Object.defineProperty(_t[A],"labels",{value:jX0[A].labels});let B=e$6(A);Object.keys(B).forEach((D)=>{let Z=B[D];_t[A][D]=Qq6(Z),_t[A][D].raw=Bq6(Z)})});nAB.exports=_t});
var kt=E((pg5,UAB)=>{var H$6=NE(),z$6=(A,B,Q)=>{try{B=new H$6(B,Q)}catch(D){return!1}return B.test(A)};UAB.exports=z$6});
var qE=E((xg5,o0B)=>{var r0B=AJ(),Pw6=(A,B,Q)=>new r0B(A,Q).compare(new r0B(B,Q));o0B.exports=Pw6});
var rP1=E((Sg5,g0B)=>{var ww6=Object.freeze({loose:!0}),$w6=Object.freeze({}),qw6=(A)=>{if(!A)return $w6;if(typeof A!=="object")return ww6;return A};g0B.exports=qw6});
var ru=E((yg5,a0B)=>{var n0B=AJ(),Mw6=(A,B,Q=!1)=>{if(A instanceof n0B)return A;try{return new n0B(A,B)}catch(D){if(!Q)return null;throw D}};a0B.exports=Mw6});
var sP1=E((Og5,v0B)=>{var q0B=J1("child_process"),{isLinux:Pt,getReport:N0B}=z0B(),{LDD_PATH:aP1,readFile:L0B,readFileSync:M0B}=w0B(),WM,JM,Wx="",R0B=()=>{if(!Wx)return new Promise((A)=>{q0B.exec("getconf GNU_LIBC_VERSION 2>&1 || true; ldd --version 2>&1 || true",(B,Q)=>{Wx=B?" ":Q,A(Wx)})});return Wx},O0B=()=>{if(!Wx)try{Wx=q0B.execSync("getconf GNU_LIBC_VERSION 2>&1 || true; ldd --version 2>&1 || true",{encoding:"utf8"})}catch(A){Wx=" "}return Wx},Jx="glibc",T0B=/LIBC[a-z0-9 \-).]*?(\d+\.\d+)/i,Tt="musl",tU6=(A)=>A.includes("libc.musl-")||A.includes("ld-musl-"),P0B=()=>{let A=N0B();if(A.header&&A.header.glibcVersionRuntime)return Jx;if(Array.isArray(A.sharedObjects)){if(A.sharedObjects.some(tU6))return Tt}return null},S0B=(A)=>{let[B,Q]=A.split(/[\r\n]+/);if(B&&B.includes(Jx))return Jx;if(Q&&Q.includes(Tt))return Tt;return null},j0B=(A)=>{if(A.includes("musl"))return Tt;if(A.includes("GNU C Library"))return Jx;return null},eU6=async()=>{if(WM!==void 0)return WM;WM=null;try{let A=await L0B(aP1);WM=j0B(A)}catch(A){}return WM},Aw6=()=>{if(WM!==void 0)return WM;WM=null;try{let A=M0B(aP1);WM=j0B(A)}catch(A){}return WM},k0B=async()=>{let A=null;if(Pt()){if(A=await eU6(),!A)A=P0B();if(!A){let B=await R0B();A=S0B(B)}}return A},y0B=()=>{let A=null;if(Pt()){if(A=Aw6(),!A)A=P0B();if(!A){let B=O0B();A=S0B(B)}}return A},Bw6=async()=>Pt()&&await k0B()!==Jx,Qw6=()=>Pt()&&y0B()!==Jx,Dw6=async()=>{if(JM!==void 0)return JM;JM=null;try{let B=(await L0B(aP1)).match(T0B);if(B)JM=B[1]}catch(A){}return JM},Zw6=()=>{if(JM!==void 0)return JM;JM=null;try{let B=M0B(aP1).match(T0B);if(B)JM=B[1]}catch(A){}return JM},_0B=()=>{let A=N0B();if(A.header&&A.header.glibcVersionRuntime)return A.header.glibcVersionRuntime;return null},$0B=(A)=>A.trim().split(/\s+/)[1],x0B=(A)=>{let[B,Q,D]=A.split(/[\r\n]+/);if(B&&B.includes(Jx))return $0B(B);if(Q&&D&&Q.includes(Tt))return $0B(D);return null},Gw6=async()=>{let A=null;if(Pt()){if(A=await Dw6(),!A)A=_0B();if(!A){let B=await R0B();A=x0B(B)}}return A},Fw6=()=>{let A=null;if(Pt()){if(A=Zw6(),!A)A=_0B();if(!A){let B=O0B();A=x0B(B)}}return A};v0B.exports={GLIBC:Jx,MUSL:Tt,family:k0B,familySync:y0B,isNonGlibcLinux:Bw6,isNonGlibcLinuxSync:Qw6,version:Gw6,versionSync:Fw6}});
var tAB=E((Fu5,oAB)=>{var Fq6=XS1(),JA=YM(),Vx=Y71(),Iq6={left:"low",center:"centre",centre:"centre",right:"high"};function rAB(A){let{raw:B,density:Q,limitInputPixels:D,ignoreIcc:Z,unlimited:G,sequentialRead:F,failOn:I,failOnError:Y,animated:W,page:J,pages:X,subifd:V}=A;return[B,Q,D,Z,G,F,I,Y,W,J,X,V].some(JA.defined)?{raw:B,density:Q,limitInputPixels:D,ignoreIcc:Z,unlimited:G,sequentialRead:F,failOn:I,failOnError:Y,animated:W,page:J,pages:X,subifd:V}:void 0}function Yq6(A,B,Q){let D={failOn:"warning",limitInputPixels:Math.pow(16383,2),ignoreIcc:!1,unlimited:!1,sequentialRead:!0};if(JA.string(A))D.file=A;else if(JA.buffer(A)){if(A.length===0)throw Error("Input Buffer is empty");D.buffer=A}else if(JA.arrayBuffer(A)){if(A.byteLength===0)throw Error("Input bit Array is empty");D.buffer=Buffer.from(A,0,A.byteLength)}else if(JA.typedArray(A)){if(A.length===0)throw Error("Input Bit Array is empty");D.buffer=Buffer.from(A.buffer,A.byteOffset,A.byteLength)}else if(JA.plainObject(A)&&!JA.defined(B)){if(B=A,rAB(B))D.buffer=[]}else if(!JA.defined(A)&&!JA.defined(B)&&JA.object(Q)&&Q.allowStream)D.buffer=[];else throw new Error(`Unsupported input '${A}' of type ${typeof A}${JA.defined(B)?` when also providing options of type ${typeof B}`:""}`);if(JA.object(B)){if(JA.defined(B.failOnError))if(JA.bool(B.failOnError))D.failOn=B.failOnError?"warning":"none";else throw JA.invalidParameterError("failOnError","boolean",B.failOnError);if(JA.defined(B.failOn))if(JA.string(B.failOn)&&JA.inArray(B.failOn,["none","truncated","error","warning"]))D.failOn=B.failOn;else throw JA.invalidParameterError("failOn","one of: none, truncated, error, warning",B.failOn);if(JA.defined(B.density))if(JA.inRange(B.density,1,1e5))D.density=B.density;else throw JA.invalidParameterError("density","number between 1 and 100000",B.density);if(JA.defined(B.ignoreIcc))if(JA.bool(B.ignoreIcc))D.ignoreIcc=B.ignoreIcc;else throw JA.invalidParameterError("ignoreIcc","boolean",B.ignoreIcc);if(JA.defined(B.limitInputPixels))if(JA.bool(B.limitInputPixels))D.limitInputPixels=B.limitInputPixels?Math.pow(16383,2):0;else if(JA.integer(B.limitInputPixels)&&JA.inRange(B.limitInputPixels,0,Number.MAX_SAFE_INTEGER))D.limitInputPixels=B.limitInputPixels;else throw JA.invalidParameterError("limitInputPixels","positive integer",B.limitInputPixels);if(JA.defined(B.unlimited))if(JA.bool(B.unlimited))D.unlimited=B.unlimited;else throw JA.invalidParameterError("unlimited","boolean",B.unlimited);if(JA.defined(B.sequentialRead))if(JA.bool(B.sequentialRead))D.sequentialRead=B.sequentialRead;else throw JA.invalidParameterError("sequentialRead","boolean",B.sequentialRead);if(JA.defined(B.raw))if(JA.object(B.raw)&&JA.integer(B.raw.width)&&B.raw.width>0&&JA.integer(B.raw.height)&&B.raw.height>0&&JA.integer(B.raw.channels)&&JA.inRange(B.raw.channels,1,4))switch(D.rawWidth=B.raw.width,D.rawHeight=B.raw.height,D.rawChannels=B.raw.channels,D.rawPremultiplied=!!B.raw.premultiplied,A.constructor){case Uint8Array:case Uint8ClampedArray:D.rawDepth="uchar";break;case Int8Array:D.rawDepth="char";break;case Uint16Array:D.rawDepth="ushort";break;case Int16Array:D.rawDepth="short";break;case Uint32Array:D.rawDepth="uint";break;case Int32Array:D.rawDepth="int";break;case Float32Array:D.rawDepth="float";break;case Float64Array:D.rawDepth="double";break;default:D.rawDepth="uchar";break}else throw new Error("Expected width, height and channels for raw pixel input");if(JA.defined(B.animated))if(JA.bool(B.animated))D.pages=B.animated?-1:1;else throw JA.invalidParameterError("animated","boolean",B.animated);if(JA.defined(B.pages))if(JA.integer(B.pages)&&JA.inRange(B.pages,-1,1e5))D.pages=B.pages;else throw JA.invalidParameterError("pages","integer between -1 and 100000",B.pages);if(JA.defined(B.page))if(JA.integer(B.page)&&JA.inRange(B.page,0,1e5))D.page=B.page;else throw JA.invalidParameterError("page","integer between 0 and 100000",B.page);if(JA.defined(B.level))if(JA.integer(B.level)&&JA.inRange(B.level,0,256))D.level=B.level;else throw JA.invalidParameterError("level","integer between 0 and 256",B.level);if(JA.defined(B.subifd))if(JA.integer(B.subifd)&&JA.inRange(B.subifd,-1,1e5))D.subifd=B.subifd;else throw JA.invalidParameterError("subifd","integer between -1 and 100000",B.subifd);if(JA.defined(B.create))if(JA.object(B.create)&&JA.integer(B.create.width)&&B.create.width>0&&JA.integer(B.create.height)&&B.create.height>0&&JA.integer(B.create.channels)){if(D.createWidth=B.create.width,D.createHeight=B.create.height,D.createChannels=B.create.channels,JA.defined(B.create.noise)){if(!JA.object(B.create.noise))throw new Error("Expected noise to be an object");if(!JA.inArray(B.create.noise.type,["gaussian"]))throw new Error("Only gaussian noise is supported at the moment");if(!JA.inRange(B.create.channels,1,4))throw JA.invalidParameterError("create.channels","number between 1 and 4",B.create.channels);if(D.createNoiseType=B.create.noise.type,JA.number(B.create.noise.mean)&&JA.inRange(B.create.noise.mean,0,1e4))D.createNoiseMean=B.create.noise.mean;else throw JA.invalidParameterError("create.noise.mean","number between 0 and 10000",B.create.noise.mean);if(JA.number(B.create.noise.sigma)&&JA.inRange(B.create.noise.sigma,0,1e4))D.createNoiseSigma=B.create.noise.sigma;else throw JA.invalidParameterError("create.noise.sigma","number between 0 and 10000",B.create.noise.sigma)}else if(JA.defined(B.create.background)){if(!JA.inRange(B.create.channels,3,4))throw JA.invalidParameterError("create.channels","number between 3 and 4",B.create.channels);let Z=Fq6(B.create.background);D.createBackground=[Z.red(),Z.green(),Z.blue(),Math.round(Z.alpha()*255)]}else throw new Error("Expected valid noise or background to create a new input image");delete D.buffer}else throw new Error("Expected valid width, height and channels to create a new input image");if(JA.defined(B.text))if(JA.object(B.text)&&JA.string(B.text.text)){if(D.textValue=B.text.text,JA.defined(B.text.height)&&JA.defined(B.text.dpi))throw new Error("Expected only one of dpi or height");if(JA.defined(B.text.font))if(JA.string(B.text.font))D.textFont=B.text.font;else throw JA.invalidParameterError("text.font","string",B.text.font);if(JA.defined(B.text.fontfile))if(JA.string(B.text.fontfile))D.textFontfile=B.text.fontfile;else throw JA.invalidParameterError("text.fontfile","string",B.text.fontfile);if(JA.defined(B.text.width))if(JA.integer(B.text.width)&&B.text.width>0)D.textWidth=B.text.width;else throw JA.invalidParameterError("text.width","positive integer",B.text.width);if(JA.defined(B.text.height))if(JA.integer(B.text.height)&&B.text.height>0)D.textHeight=B.text.height;else throw JA.invalidParameterError("text.height","positive integer",B.text.height);if(JA.defined(B.text.align))if(JA.string(B.text.align)&&JA.string(this.constructor.align[B.text.align]))D.textAlign=this.constructor.align[B.text.align];else throw JA.invalidParameterError("text.align","valid alignment",B.text.align);if(JA.defined(B.text.justify))if(JA.bool(B.text.justify))D.textJustify=B.text.justify;else throw JA.invalidParameterError("text.justify","boolean",B.text.justify);if(JA.defined(B.text.dpi))if(JA.integer(B.text.dpi)&&JA.inRange(B.text.dpi,1,1e6))D.textDpi=B.text.dpi;else throw JA.invalidParameterError("text.dpi","integer between 1 and 1000000",B.text.dpi);if(JA.defined(B.text.rgba))if(JA.bool(B.text.rgba))D.textRgba=B.text.rgba;else throw JA.invalidParameterError("text.rgba","bool",B.text.rgba);if(JA.defined(B.text.spacing))if(JA.integer(B.text.spacing)&&JA.inRange(B.text.spacing,-1e6,1e6))D.textSpacing=B.text.spacing;else throw JA.invalidParameterError("text.spacing","integer between -1000000 and 1000000",B.text.spacing);if(JA.defined(B.text.wrap))if(JA.string(B.text.wrap)&&JA.inArray(B.text.wrap,["word","char","word-char","none"]))D.textWrap=B.text.wrap;else throw JA.invalidParameterError("text.wrap","one of: word, char, word-char, none",B.text.wrap);delete D.buffer}else throw new Error("Expected a valid string to create an image with text.")}else if(JA.defined(B))throw new Error("Invalid input options "+B);return D}function Wq6(A,B,Q){if(Array.isArray(this.options.input.buffer))if(JA.buffer(A)){if(this.options.input.buffer.length===0)this.on("finish",()=>{this.streamInFinished=!0});this.options.input.buffer.push(A),Q()}else Q(new Error("Non-Buffer data on Writable Stream"));else Q(new Error("Unexpected data on Writable Stream"))}function Jq6(){if(this._isStreamInput())this.options.input.buffer=Buffer.concat(this.options.input.buffer)}function Xq6(){return Array.isArray(this.options.input.buffer)}function Vq6(A){let B=Error();if(JA.fn(A)){if(this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),Vx.metadata(this.options,(Q,D)=>{if(Q)A(JA.nativeError(Q,B));else A(null,D)})});else Vx.metadata(this.options,(Q,D)=>{if(Q)A(JA.nativeError(Q,B));else A(null,D)});return this}else if(this._isStreamInput())return new Promise((Q,D)=>{let Z=()=>{this._flattenBufferIn(),Vx.metadata(this.options,(G,F)=>{if(G)D(JA.nativeError(G,B));else Q(F)})};if(this.writableFinished)Z();else this.once("finish",Z)});else return new Promise((Q,D)=>{Vx.metadata(this.options,(Z,G)=>{if(Z)D(JA.nativeError(Z,B));else Q(G)})})}function Cq6(A){let B=Error();if(JA.fn(A)){if(this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),Vx.stats(this.options,(Q,D)=>{if(Q)A(JA.nativeError(Q,B));else A(null,D)})});else Vx.stats(this.options,(Q,D)=>{if(Q)A(JA.nativeError(Q,B));else A(null,D)});return this}else if(this._isStreamInput())return new Promise((Q,D)=>{this.on("finish",function(){this._flattenBufferIn(),Vx.stats(this.options,(Z,G)=>{if(Z)D(JA.nativeError(Z,B));else Q(G)})})});else return new Promise((Q,D)=>{Vx.stats(this.options,(Z,G)=>{if(Z)D(JA.nativeError(Z,B));else Q(G)})})}oAB.exports=function(A){Object.assign(A.prototype,{_inputOptionsFromObject:rAB,_createInputDescriptor:Yq6,_write:Wq6,_flattenBufferIn:Jq6,_isStreamInput:Xq6,metadata:Vq6,stats:Cq6}),A.align=Iq6}});
var vAB=E((eg5,xAB)=>{xAB.exports=function A(B){if(!B||typeof B==="string")return!1;return B instanceof Array||Array.isArray(B)||B.length>=0&&(B.splice instanceof Function||Object.getOwnPropertyDescriptor(B,B.length-1)&&B.constructor.name!=="String")}});
var w0B=E((Rg5,U0B)=>{var E0B=J1("fs"),rU6=(A)=>E0B.readFileSync(A,"utf-8"),oU6=(A)=>new Promise((B,Q)=>{E0B.readFile(A,"utf-8",(D,Z)=>{if(D)Q(D);else B(Z)})});U0B.exports={LDD_PATH:"/usr/bin/ldd",readFileSync:rU6,readFile:oU6}});
var wX0=E((dg5,IAB)=>{var mw6=EX0(),dw6=UX0(),cw6=Z71(),lw6=D71(),pw6=QS1(),iw6=DS1(),nw6=(A,B,Q,D)=>{switch(B){case"===":if(typeof A==="object")A=A.version;if(typeof Q==="object")Q=Q.version;return A===Q;case"!==":if(typeof A==="object")A=A.version;if(typeof Q==="object")Q=Q.version;return A!==Q;case"":case"=":case"==":return mw6(A,Q,D);case"!=":return dw6(A,Q,D);case">":return cw6(A,Q,D);case">=":return lw6(A,Q,D);case"<":return pw6(A,Q,D);case"<=":return iw6(A,Q,D);default:throw new TypeError(`Invalid operator: ${B}`)}};IAB.exports=nw6});
var yAB=E((og5,kAB)=>{var m$6=J1("node:util"),TX0=J1("node:stream"),d$6=YM();Y71();var c$6=m$6.debuglog("sharp"),tu=function(A,B){if(arguments.length===1&&!d$6.defined(A))throw new Error("Invalid input");if(!(this instanceof tu))return new tu(A,B);return TX0.Duplex.call(this),this.options={topOffsetPre:-1,leftOffsetPre:-1,widthPre:-1,heightPre:-1,topOffsetPost:-1,leftOffsetPost:-1,widthPost:-1,heightPost:-1,width:-1,height:-1,canvas:"crop",position:0,resizeBackground:[0,0,0,255],useExifOrientation:!1,angle:0,rotationAngle:0,rotationBackground:[0,0,0,255],rotateBeforePreExtract:!1,flip:!1,flop:!1,extendTop:0,extendBottom:0,extendLeft:0,extendRight:0,extendBackground:[0,0,0,255],extendWith:"background",withoutEnlargement:!1,withoutReduction:!1,affineMatrix:[],affineBackground:[0,0,0,255],affineIdx:0,affineIdy:0,affineOdx:0,affineOdy:0,affineInterpolator:this.constructor.interpolators.bilinear,kernel:"lanczos3",fastShrinkOnLoad:!0,tint:[-1,0,0,0],flatten:!1,flattenBackground:[0,0,0],unflatten:!1,negate:!1,negateAlpha:!0,medianSize:0,blurSigma:0,precision:"integer",minAmpl:0.2,sharpenSigma:0,sharpenM1:1,sharpenM2:2,sharpenX1:2,sharpenY2:10,sharpenY3:20,threshold:0,thresholdGrayscale:!0,trimBackground:[],trimThreshold:-1,trimLineArt:!1,gamma:0,gammaOut:0,greyscale:!1,normalise:!1,normaliseLower:1,normaliseUpper:99,claheWidth:0,claheHeight:0,claheMaxSlope:3,brightness:1,saturation:1,hue:0,lightness:0,booleanBufferIn:null,booleanFileIn:"",joinChannelIn:[],extractChannel:-1,removeAlpha:!1,ensureAlpha:-1,colourspace:"srgb",colourspacePipeline:"last",composite:[],fileOut:"",formatOut:"input",streamOut:!1,keepMetadata:0,withMetadataOrientation:-1,withMetadataDensity:0,withIccProfile:"",withExif:{},withExifMerge:!0,resolveWithObject:!1,jpegQuality:80,jpegProgressive:!1,jpegChromaSubsampling:"4:2:0",jpegTrellisQuantisation:!1,jpegOvershootDeringing:!1,jpegOptimiseScans:!1,jpegOptimiseCoding:!0,jpegQuantisationTable:0,pngProgressive:!1,pngCompressionLevel:6,pngAdaptiveFiltering:!1,pngPalette:!1,pngQuality:100,pngEffort:7,pngBitdepth:8,pngDither:1,jp2Quality:80,jp2TileHeight:512,jp2TileWidth:512,jp2Lossless:!1,jp2ChromaSubsampling:"4:4:4",webpQuality:80,webpAlphaQuality:100,webpLossless:!1,webpNearLossless:!1,webpSmartSubsample:!1,webpPreset:"default",webpEffort:4,webpMinSize:!1,webpMixed:!1,gifBitdepth:8,gifEffort:7,gifDither:1,gifInterFrameMaxError:0,gifInterPaletteMaxError:3,gifReuse:!0,gifProgressive:!1,tiffQuality:80,tiffCompression:"jpeg",tiffPredictor:"horizontal",tiffPyramid:!1,tiffMiniswhite:!1,tiffBitdepth:8,tiffTile:!1,tiffTileHeight:256,tiffTileWidth:256,tiffXres:1,tiffYres:1,tiffResolutionUnit:"inch",heifQuality:50,heifLossless:!1,heifCompression:"av1",heifEffort:4,heifChromaSubsampling:"4:4:4",heifBitdepth:8,jxlDistance:1,jxlDecodingTier:0,jxlEffort:7,jxlLossless:!1,rawDepth:"uchar",tileSize:256,tileOverlap:0,tileContainer:"fs",tileLayout:"dz",tileFormat:"last",tileDepth:"last",tileAngle:0,tileSkipBlanks:-1,tileBackground:[255,255,255,255],tileCentre:!1,tileId:"https://example.com/iiif",tileBasename:"",timeoutSeconds:0,linearA:[],linearB:[],debuglog:(Q)=>{this.emit("warning",Q),c$6(Q)},queueListener:function(Q){tu.queue.emit("change",Q)}},this.options.input=this._createInputDescriptor(A,B,{allowStream:!0}),this};Object.setPrototypeOf(tu.prototype,TX0.Duplex.prototype);Object.setPrototypeOf(tu,TX0.Duplex);function l$6(){let A=this.constructor.call(),{debuglog:B,queueListener:Q,...D}=this.options;if(A.options=structuredClone(D),A.options.debuglog=B,A.options.queueListener=Q,this._isStreamInput())this.on("finish",()=>{this._flattenBufferIn(),A.options.input.buffer=this.options.input.buffer,A.emit("finish")});return A}Object.assign(tu.prototype,{clone:l$6});kAB.exports=tu});
var z0B=E((Mg5,H0B)=>{var K0B=()=>process.platform==="linux",nP1=null,sU6=()=>{if(!nP1)if(K0B()&&process.report){let A=process.report.excludeNetwork;process.report.excludeNetwork=!0,nP1=process.report.getReport(),process.report.excludeNetwork=A}else nP1={};return nP1};H0B.exports={isLinux:K0B,getReport:sU6}});
var zX0=E((_g5,s0B)=>{var Rw6=AJ(),Ow6=ru(),{safeRe:AS1,t:BS1}=St(),Tw6=(A,B)=>{if(A instanceof Rw6)return A;if(typeof A==="number")A=String(A);if(typeof A!=="string")return null;B=B||{};let Q=null;if(!B.rtl)Q=A.match(B.includePrerelease?AS1[BS1.COERCEFULL]:AS1[BS1.COERCE]);else{let Y=B.includePrerelease?AS1[BS1.COERCERTLFULL]:AS1[BS1.COERCERTL],W;while((W=Y.exec(A))&&(!Q||Q.index+Q[0].length!==A.length)){if(!Q||W.index+W[0].length!==Q.index+Q[0].length)Q=W;Y.lastIndex=W.index+W[1].length+W[2].length}Y.lastIndex=-1}if(Q===null)return null;let D=Q[2],Z=Q[3]||"0",G=Q[4]||"0",F=B.includePrerelease&&Q[5]?`-${Q[5]}`:"",I=B.includePrerelease&&Q[6]?`+${Q[6]}`:"";return Ow6(`${D}.${Z}.${G}${F}${I}`,B)};s0B.exports=Tw6});

module.exports = KS1;
