// Package extracted with entry point: bSB

var $N0=E((CSB,KSB)=>{(function(){var A,B={}.hasOwnProperty;KSB.exports=A=function(){class Q{constructor(D){var Z,G,F;if(this.assertLegalChar=this.assertLegalChar.bind(this),this.assertLegalName=this.assertLegalName.bind(this),D||(D={}),this.options=D,!this.options.version)this.options.version="1.0";G=D.stringify||{};for(Z in G){if(!B.call(G,Z))continue;F=G[Z],this[Z]=F}}name(D){if(this.options.noValidation)return D;return this.assertLegalName(""+D||"")}text(D){if(this.options.noValidation)return D;return this.assertLegalChar(this.textEscape(""+D||""))}cdata(D){if(this.options.noValidation)return D;return D=""+D||"",D=D.replace("]]>","]]]]><![CDATA[>"),this.assertLegalChar(D)}comment(D){if(this.options.noValidation)return D;if(D=""+D||"",D.match(/--/))throw new Error("Comment text cannot contain double-hypen: "+D);return this.assertLegalChar(D)}raw(D){if(this.options.noValidation)return D;return""+D||""}attValue(D){if(this.options.noValidation)return D;return this.assertLegalChar(this.attEscape(D=""+D||""))}insTarget(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}insValue(D){if(this.options.noValidation)return D;if(D=""+D||"",D.match(/\?>/))throw new Error("Invalid processing instruction value: "+D);return this.assertLegalChar(D)}xmlVersion(D){if(this.options.noValidation)return D;if(D=""+D||"",!D.match(/1\.[0-9]+/))throw new Error("Invalid version number: "+D);return D}xmlEncoding(D){if(this.options.noValidation)return D;if(D=""+D||"",!D.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/))throw new Error("Invalid encoding: "+D);return this.assertLegalChar(D)}xmlStandalone(D){if(this.options.noValidation)return D;if(D)return"yes";else return"no"}dtdPubID(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}dtdSysID(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}dtdElementValue(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}dtdAttType(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}dtdAttDefault(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}dtdEntityValue(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}dtdNData(D){if(this.options.noValidation)return D;return this.assertLegalChar(""+D||"")}assertLegalChar(D){var Z,G;if(this.options.noValidation)return D;if(this.options.version==="1.0"){if(Z=/[\0-\x08\x0B\f\x0E-\x1F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,this.options.invalidCharReplacement!==void 0)D=D.replace(Z,this.options.invalidCharReplacement);else if(G=D.match(Z))throw new Error(`Invalid character in string: ${D} at index ${G.index}`)}else if(this.options.version==="1.1"){if(Z=/[\0\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/g,this.options.invalidCharReplacement!==void 0)D=D.replace(Z,this.options.invalidCharReplacement);else if(G=D.match(Z))throw new Error(`Invalid character in string: ${D} at index ${G.index}`)}return D}assertLegalName(D){var Z;if(this.options.noValidation)return D;if(D=this.assertLegalChar(D),Z=/^([:A-Z_a-z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])([\x2D\.0-:A-Z_a-z\xB7\xC0-\xD6\xD8-\xF6\xF8-\u037D\u037F-\u1FFF\u200C\u200D\u203F\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]|[\uD800-\uDB7F][\uDC00-\uDFFF])*$/,!D.match(Z))throw new Error(`Invalid character in name: ${D}`);return D}textEscape(D){var Z;if(this.options.noValidation)return D;return Z=this.options.noDoubleEncoding?/(?!&(lt|gt|amp|apos|quot);)&/g:/&/g,D.replace(Z,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\r/g,"&#xD;")}attEscape(D){var Z;if(this.options.noValidation)return D;return Z=this.options.noDoubleEncoding?/(?!&(lt|gt|amp|apos|quot);)&/g:/&/g,D.replace(Z,"&amp;").replace(/</g,"&lt;").replace(/"/g,"&quot;").replace(/\t/g,"&#x9;").replace(/\n/g,"&#xA;").replace(/\r/g,"&#xD;")}}return Q.prototype.convertAttKey="@",Q.prototype.convertPIKey="?",Q.prototype.convertTextKey="#text",Q.prototype.convertCDataKey="#cdata",Q.prototype.convertCommentKey="#comment",Q.prototype.convertRawKey="#raw",Q}.call(this)}).call(CSB)});
var $b1=E((nPB,aPB)=>{(function(){var A,B,Q;Q=cK(),A=IG(),aPB.exports=B=function(){class D extends Q{constructor(Z,G,F){super(Z);if(G==null)throw new Error("Missing DTD notation name. "+this.debugInfo(G));if(!F.pubID&&!F.sysID)throw new Error("Public or system identifiers are required for an external entity. "+this.debugInfo(G));if(this.name=this.stringify.name(G),this.type=A.NotationDeclaration,F.pubID!=null)this.pubID=this.stringify.dtdPubID(F.pubID);if(F.sysID!=null)this.sysID=this.stringify.dtdSysID(F.sysID)}toString(Z){return this.options.writer.dtdNotation(this,this.options.writer.filterOptions(Z))}}return Object.defineProperty(D.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(D.prototype,"systemId",{get:function(){return this.sysID}}),D}.call(this)}).call(nPB)});
var BPB=E((PI8)=>{var lG1=vG1().NAMESPACE,KN0=/[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,aTB=new RegExp("[\\-\\.0-9"+KN0.source.slice(1,-1)+"\\u00B7\\u0300-\\u036F\\u203F-\\u2040]"),sTB=new RegExp("^"+KN0.source+aTB.source+"*(?::"+KN0.source+aTB.source+"*)?$"),mG1=0,_v=1,Y01=2,dG1=3,W01=4,J01=5,cG1=6,Wb1=7;function X01(A,B){if(this.message=A,this.locator=B,Error.captureStackTrace)Error.captureStackTrace(this,X01)}X01.prototype=new Error;X01.prototype.name=X01.name;function tTB(){}tTB.prototype={parse:function(A,B,Q){var D=this.domBuilder;D.startDocument(),eTB(B,B={}),qI8(A,B,Q,D,this.errorHandler),D.endDocument()}};function qI8(A,B,Q,D,Z){function G(W1){if(W1>65535){W1-=65536;var U1=55296+(W1>>10),y1=56320+(W1&1023);return String.fromCharCode(U1,y1)}else return String.fromCharCode(W1)}function F(W1){var U1=W1.slice(1,-1);if(Object.hasOwnProperty.call(Q,U1))return Q[U1];else if(U1.charAt(0)==="#")return G(parseInt(U1.substr(1).replace("x","0x")));else return Z.error("entity not found:"+W1),W1}function I(W1){if(W1>H){var U1=A.substring(H,W1).replace(/&#?\w+;/g,F);V&&Y(H),D.characters(U1,0,W1-H),H=W1}}function Y(W1,U1){while(W1>=J&&(U1=X.exec(A)))W=U1.index,J=W+U1[0].length,V.lineNumber++;V.columnNumber=W1-W+1}var W=0,J=0,X=/.*(?:\r\n?|\n)|.*$/g,V=D.locator,C=[{currentNSMap:B}],K={},H=0;while(!0){try{var z=A.indexOf("<",H);if(z<0){if(!A.substr(H).match(/^\s*$/)){var $=D.doc,L=$.createTextNode(A.substr(H));$.appendChild(L),D.currentElement=L}return}if(z>H)I(z);switch(A.charAt(z+1)){case"/":var h=A.indexOf(">",z+3),N=A.substring(z+2,h).replace(/[ \t\n\r]+$/g,""),O=C.pop();if(h<0)N=A.substring(z+2).replace(/[\s<].*/,""),Z.error("end tag name: "+N+" is not complete:"+O.tagName),h=z+1+N.length;else if(N.match(/\s</))N=N.replace(/[\s<].*/,""),Z.error("end tag name: "+N+" maybe not complete"),h=z+1+N.length;var R=O.localNSMap,T=O.tagName==N,j=T||O.tagName&&O.tagName.toLowerCase()==N.toLowerCase();if(j){if(D.endElement(O.uri,O.localName,N),R){for(var f in R)if(Object.prototype.hasOwnProperty.call(R,f))D.endPrefixMapping(f)}if(!T)Z.fatalError("end tag name: "+N+" is not match the current start tagName:"+O.tagName)}else C.push(O);h++;break;case"?":V&&Y(z),h=OI8(A,z,D);break;case"!":V&&Y(z),h=RI8(A,z,D,Z);break;default:V&&Y(z);var k=new APB,c=C[C.length-1].currentNSMap,h=NI8(A,z,k,c,F,Z),n=k.length;if(!k.closed&&MI8(A,h,k.tagName,K)){if(k.closed=!0,!Q.nbsp)Z.warning("unclosed xml attribute")}if(V&&n){var a=rTB(V,{});for(var x=0;x<n;x++){var e=k[x];Y(e.offset),e.locator=rTB(V,{})}if(D.locator=a,oTB(k,D,c))C.push(k);D.locator=V}else if(oTB(k,D,c))C.push(k);if(lG1.isHTML(k.uri)&&!k.closed)h=LI8(A,h,k.tagName,F,D);else h++}}catch(W1){if(W1 instanceof X01)throw W1;Z.error("element parse error: "+W1),h=-1}if(h>H)H=h;else I(Math.max(z,H)+1)}}function rTB(A,B){return B.lineNumber=A.lineNumber,B.columnNumber=A.columnNumber,B}function NI8(A,B,Q,D,Z,G){function F(C,K,H){if(Q.attributeNames.hasOwnProperty(C))G.fatalError("Attribute "+C+" redefined");Q.addValue(C,K.replace(/[\t\n\r]/g," ").replace(/&#?\w+;/g,Z),H)}var I,Y,W=++B,J=mG1;while(!0){var X=A.charAt(W);switch(X){case"=":if(J===_v)I=A.slice(B,W),J=dG1;else if(J===Y01)J=dG1;else throw new Error("attribute equal must after attrName");break;case"'":case'"':if(J===dG1||J===_v){if(J===_v)G.warning('attribute value must after "="'),I=A.slice(B,W);if(B=W+1,W=A.indexOf(X,B),W>0)Y=A.slice(B,W),F(I,Y,B-1),J=J01;else throw new Error("attribute value no end '"+X+"' match")}else if(J==W01)Y=A.slice(B,W),F(I,Y,B),G.warning('attribute "'+I+'" missed start quot('+X+")!!"),B=W+1,J=J01;else throw new Error('attribute value must after "="');break;case"/":switch(J){case mG1:Q.setTagName(A.slice(B,W));case J01:case cG1:case Wb1:J=Wb1,Q.closed=!0;case W01:case _v:break;case Y01:Q.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":if(G.error("unexpected end of input"),J==mG1)Q.setTagName(A.slice(B,W));return W;case">":switch(J){case mG1:Q.setTagName(A.slice(B,W));case J01:case cG1:case Wb1:break;case W01:case _v:if(Y=A.slice(B,W),Y.slice(-1)==="/")Q.closed=!0,Y=Y.slice(0,-1);case Y01:if(J===Y01)Y=I;if(J==W01)G.warning('attribute "'+Y+'" missed quot(")!'),F(I,Y,B);else{if(!lG1.isHTML(D[""])||!Y.match(/^(?:disabled|checked|selected)$/i))G.warning('attribute "'+Y+'" missed value!! "'+Y+'" instead!!');F(Y,Y,B)}break;case dG1:throw new Error("attribute value missed!!")}return W;case"":X=" ";default:if(X<=" ")switch(J){case mG1:Q.setTagName(A.slice(B,W)),J=cG1;break;case _v:I=A.slice(B,W),J=Y01;break;case W01:var Y=A.slice(B,W);G.warning('attribute "'+Y+'" missed quot(")!!'),F(I,Y,B);case J01:J=cG1;break}else switch(J){case Y01:var V=Q.tagName;if(!lG1.isHTML(D[""])||!I.match(/^(?:disabled|checked|selected)$/i))G.warning('attribute "'+I+'" missed value!! "'+I+'" instead2!!');F(I,I,B),B=W,J=_v;break;case J01:G.warning('attribute space is required"'+I+'"!!');case cG1:J=_v,B=W;break;case dG1:J=W01,B=W;break;case Wb1:throw new Error("elements closed character '/' and '>' must be connected to")}}W++}}function oTB(A,B,Q){var D=A.tagName,Z=null,X=A.length;while(X--){var G=A[X],F=G.qName,I=G.value,V=F.indexOf(":");if(V>0)var Y=G.prefix=F.slice(0,V),W=F.slice(V+1),J=Y==="xmlns"&&W;else W=F,Y=null,J=F==="xmlns"&&"";if(G.localName=W,J!==!1){if(Z==null)Z={},eTB(Q,Q={});Q[J]=Z[J]=I,G.uri=lG1.XMLNS,B.startPrefixMapping(J,I)}}var X=A.length;while(X--){G=A[X];var Y=G.prefix;if(Y){if(Y==="xml")G.uri=lG1.XML;if(Y!=="xmlns")G.uri=Q[Y||""]}}var V=D.indexOf(":");if(V>0)Y=A.prefix=D.slice(0,V),W=A.localName=D.slice(V+1);else Y=null,W=A.localName=D;var C=A.uri=Q[Y||""];if(B.startElement(C,W,D,A),A.closed){if(B.endElement(C,W,D),Z){for(Y in Z)if(Object.prototype.hasOwnProperty.call(Z,Y))B.endPrefixMapping(Y)}}else return A.currentNSMap=Q,A.localNSMap=Z,!0}function LI8(A,B,Q,D,Z){if(/^(?:script|textarea)$/i.test(Q)){var G=A.indexOf("</"+Q+">",B),F=A.substring(B+1,G);if(/[&<]/.test(F)){if(/^script$/i.test(Q))return Z.characters(F,0,F.length),G;return F=F.replace(/&#?\w+;/g,D),Z.characters(F,0,F.length),G}}return B+1}function MI8(A,B,Q,D){var Z=D[Q];if(Z==null){if(Z=A.lastIndexOf("</"+Q+">"),Z<B)Z=A.lastIndexOf("</"+Q);D[Q]=Z}return Z<B}function eTB(A,B){for(var Q in A)if(Object.prototype.hasOwnProperty.call(A,Q))B[Q]=A[Q]}function RI8(A,B,Q,D){var Z=A.charAt(B+2);switch(Z){case"-":if(A.charAt(B+3)==="-"){var G=A.indexOf("-->",B+4);if(G>B)return Q.comment(A,B+4,G-B-4),G+3;else return D.error("Unclosed comment"),-1}else return-1;default:if(A.substr(B+3,6)=="CDATA["){var G=A.indexOf("]]>",B+9);return Q.startCDATA(),Q.characters(A,B+9,G-B-9),Q.endCDATA(),G+3}var F=TI8(A,B),I=F.length;if(I>1&&/!doctype/i.test(F[0][0])){var Y=F[1][0],W=!1,J=!1;if(I>3){if(/^public$/i.test(F[2][0]))W=F[3][0],J=I>4&&F[4][0];else if(/^system$/i.test(F[2][0]))J=F[3][0]}var X=F[I-1];return Q.startDTD(Y,W,J),Q.endDTD(),X.index+X[0].length}}return-1}function OI8(A,B,Q){var D=A.indexOf("?>",B);if(D){var Z=A.substring(B,D).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);if(Z){var G=Z[0].length;return Q.processingInstruction(Z[1],Z[2]),D+2}else return-1}return-1}function APB(){this.attributeNames={}}APB.prototype={setTagName:function(A){if(!sTB.test(A))throw new Error("invalid tagName:"+A);this.tagName=A},addValue:function(A,B,Q){if(!sTB.test(A))throw new Error("invalid attribute:"+A);this.attributeNames[A]=this.length,this[this.length++]={qName:A,value:B,offset:Q}},length:0,getLocalName:function(A){return this[A].localName},getLocator:function(A){return this[A].locator},getQName:function(A){return this[A].qName},getURI:function(A){return this[A].uri},getValue:function(A){return this[A].value}};function TI8(A,B){var Q,D=[],Z=/'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;Z.lastIndex=B,Z.exec(A);while(Q=Z.exec(A))if(D.push(Q),Q[1])return D}PI8.XMLReader=tTB;PI8.ParseError=X01});
var CN0=E((JI8)=>{var RTB=vG1(),pM=RTB.find,bG1=RTB.NAMESPACE;function oF8(A){return A!==""}function tF8(A){return A?A.split(/[\t\n\f\r ]+/).filter(oF8):[]}function eF8(A,B){if(!A.hasOwnProperty(B))A[B]=!0;return A}function UTB(A){if(!A)return[];var B=tF8(A);return Object.keys(B.reduce(eF8,{}))}function AI8(A){return function(B){return A&&A.indexOf(B)!==-1}}function hG1(A,B){for(var Q in A)if(Object.prototype.hasOwnProperty.call(A,Q))B[Q]=A[Q]}function BC(A,B){var Q=A.prototype;if(!(Q instanceof B)){let Z=function(){};var D=Z;Z.prototype=B.prototype,Z=new Z,hG1(Q,Z),A.prototype=Q=Z}if(Q.constructor!=A){if(typeof A!="function")console.error("unknown Class:"+A);Q.constructor=A}}var QC={},_$=QC.ELEMENT_NODE=1,F01=QC.ATTRIBUTE_NODE=2,Db1=QC.TEXT_NODE=3,OTB=QC.CDATA_SECTION_NODE=4,TTB=QC.ENTITY_REFERENCE_NODE=5,BI8=QC.ENTITY_NODE=6,PTB=QC.PROCESSING_INSTRUCTION_NODE=7,STB=QC.COMMENT_NODE=8,jTB=QC.DOCUMENT_NODE=9,kTB=QC.DOCUMENT_TYPE_NODE=10,YS=QC.DOCUMENT_FRAGMENT_NODE=11,QI8=QC.NOTATION_NODE=12,WJ={},SI={},AW3=WJ.INDEX_SIZE_ERR=(SI[1]="Index size error",1),BW3=WJ.DOMSTRING_SIZE_ERR=(SI[2]="DOMString size error",2),AC=WJ.HIERARCHY_REQUEST_ERR=(SI[3]="Hierarchy request error",3),QW3=WJ.WRONG_DOCUMENT_ERR=(SI[4]="Wrong document",4),DW3=WJ.INVALID_CHARACTER_ERR=(SI[5]="Invalid character",5),ZW3=WJ.NO_DATA_ALLOWED_ERR=(SI[6]="No data allowed",6),GW3=WJ.NO_MODIFICATION_ALLOWED_ERR=(SI[7]="No modification allowed",7),yTB=WJ.NOT_FOUND_ERR=(SI[8]="Not found",8),FW3=WJ.NOT_SUPPORTED_ERR=(SI[9]="Not supported",9),wTB=WJ.INUSE_ATTRIBUTE_ERR=(SI[10]="Attribute in use",10),IW3=WJ.INVALID_STATE_ERR=(SI[11]="Invalid state",11),YW3=WJ.SYNTAX_ERR=(SI[12]="Syntax error",12),WW3=WJ.INVALID_MODIFICATION_ERR=(SI[13]="Invalid modification",13),JW3=WJ.NAMESPACE_ERR=(SI[14]="Invalid namespace",14),XW3=WJ.INVALID_ACCESS_ERR=(SI[15]="Invalid access",15);function FG(A,B){if(B instanceof Error)var Q=B;else if(Q=this,Error.call(this,SI[A]),this.message=SI[A],Error.captureStackTrace)Error.captureStackTrace(this,FG);if(Q.code=A,B)this.message=this.message+": "+B;return Q}FG.prototype=Error.prototype;hG1(WJ,FG);function IS(){}IS.prototype={length:0,item:function(A){return A>=0&&A<this.length?this[A]:null},toString:function(A,B){for(var Q=[],D=0;D<this.length;D++)G01(this[D],Q,A,B);return Q.join("")},filter:function(A){return Array.prototype.filter.call(this,A)},indexOf:function(A){return Array.prototype.indexOf.call(this,A)}};function I01(A,B){this._node=A,this._refresh=B,GN0(this)}function GN0(A){var B=A._node._inc||A._node.ownerDocument._inc;if(A._inc!==B){var Q=A._refresh(A._node);if(lTB(A,"length",Q.length),!A.$$length||Q.length<A.$$length){for(var D=Q.length;D in A;D++)if(Object.prototype.hasOwnProperty.call(A,D))delete A[D]}hG1(Q,A),A._inc=B}}I01.prototype.item=function(A){return GN0(this),this[A]||null};BC(I01,IS);function Zb1(){}function _TB(A,B){var Q=A.length;while(Q--)if(A[Q]===B)return Q}function $TB(A,B,Q,D){if(D)B[_TB(B,D)]=Q;else B[B.length++]=Q;if(A){Q.ownerElement=A;var Z=A.ownerDocument;if(Z)D&&bTB(Z,A,D),DI8(Z,A,Q)}}function qTB(A,B,Q){var D=_TB(B,Q);if(D>=0){var Z=B.length-1;while(D<Z)B[D]=B[++D];if(B.length=Z,A){var G=A.ownerDocument;if(G)bTB(G,A,Q),Q.ownerElement=null}}else throw new FG(yTB,new Error(A.tagName+"@"+Q))}Zb1.prototype={length:0,item:IS.prototype.item,getNamedItem:function(A){var B=this.length;while(B--){var Q=this[B];if(Q.nodeName==A)return Q}},setNamedItem:function(A){var B=A.ownerElement;if(B&&B!=this._ownerElement)throw new FG(wTB);var Q=this.getNamedItem(A.nodeName);return $TB(this._ownerElement,this,A,Q),Q},setNamedItemNS:function(A){var B=A.ownerElement,Q;if(B&&B!=this._ownerElement)throw new FG(wTB);return Q=this.getNamedItemNS(A.namespaceURI,A.localName),$TB(this._ownerElement,this,A,Q),Q},removeNamedItem:function(A){var B=this.getNamedItem(A);return qTB(this._ownerElement,this,B),B},removeNamedItemNS:function(A,B){var Q=this.getNamedItemNS(A,B);return qTB(this._ownerElement,this,Q),Q},getNamedItemNS:function(A,B){var Q=this.length;while(Q--){var D=this[Q];if(D.localName==B&&D.namespaceURI==A)return D}return null}};function xTB(){}xTB.prototype={hasFeature:function(A,B){return!0},createDocument:function(A,B,Q){var D=new gG1;if(D.implementation=this,D.childNodes=new IS,D.doctype=Q||null,Q)D.appendChild(Q);if(B){var Z=D.createElementNS(A,B);D.appendChild(Z)}return D},createDocumentType:function(A,B,Q){var D=new Ib1;return D.name=A,D.nodeName=A,D.publicId=B||"",D.systemId=Q||"",D}};function p5(){}p5.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,attributes:null,parentNode:null,childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,insertBefore:function(A,B){return Gb1(this,A,B)},replaceChild:function(A,B){if(Gb1(this,A,B,hTB),B)this.removeChild(B)},removeChild:function(A){return fTB(this,A)},appendChild:function(A){return this.insertBefore(A,null)},hasChildNodes:function(){return this.firstChild!=null},cloneNode:function(A){return ZN0(this.ownerDocument||this,this,A)},normalize:function(){var A=this.firstChild;while(A){var B=A.nextSibling;if(B&&B.nodeType==Db1&&A.nodeType==Db1)this.removeChild(B),A.appendData(B.data);else A.normalize(),A=B}},isSupported:function(A,B){return this.ownerDocument.implementation.hasFeature(A,B)},hasAttributes:function(){return this.attributes.length>0},lookupPrefix:function(A){var B=this;while(B){var Q=B._nsMap;if(Q){for(var D in Q)if(Object.prototype.hasOwnProperty.call(Q,D)&&Q[D]===A)return D}B=B.nodeType==F01?B.ownerDocument:B.parentNode}return null},lookupNamespaceURI:function(A){var B=this;while(B){var Q=B._nsMap;if(Q){if(Object.prototype.hasOwnProperty.call(Q,A))return Q[A]}B=B.nodeType==F01?B.ownerDocument:B.parentNode}return null},isDefaultNamespace:function(A){var B=this.lookupPrefix(A);return B==null}};function vTB(A){return A=="<"&&"&lt;"||A==">"&&"&gt;"||A=="&"&&"&amp;"||A=='"'&&"&quot;"||"&#"+A.charCodeAt()+";"}hG1(QC,p5);hG1(QC,p5.prototype);function fG1(A,B){if(B(A))return!0;if(A=A.firstChild)do if(fG1(A,B))return!0;while(A=A.nextSibling)}function gG1(){this.ownerDocument=this}function DI8(A,B,Q){A&&A._inc++;var D=Q.namespaceURI;if(D===bG1.XMLNS)B._nsMap[Q.prefix?Q.localName:""]=Q.value}function bTB(A,B,Q,D){A&&A._inc++;var Z=Q.namespaceURI;if(Z===bG1.XMLNS)delete B._nsMap[Q.prefix?Q.localName:""]}function FN0(A,B,Q){if(A&&A._inc){A._inc++;var D=B.childNodes;if(Q)D[D.length++]=Q;else{var Z=B.firstChild,G=0;while(Z)D[G++]=Z,Z=Z.nextSibling;D.length=G,delete D[D.length]}}}function fTB(A,B){var{previousSibling:Q,nextSibling:D}=B;if(Q)Q.nextSibling=D;else A.firstChild=D;if(D)D.previousSibling=Q;else A.lastChild=Q;return B.parentNode=null,B.previousSibling=null,B.nextSibling=null,FN0(A.ownerDocument,A),B}function ZI8(A){return A&&(A.nodeType===p5.DOCUMENT_NODE||A.nodeType===p5.DOCUMENT_FRAGMENT_NODE||A.nodeType===p5.ELEMENT_NODE)}function GI8(A){return A&&(iM(A)||IN0(A)||WS(A)||A.nodeType===p5.DOCUMENT_FRAGMENT_NODE||A.nodeType===p5.COMMENT_NODE||A.nodeType===p5.PROCESSING_INSTRUCTION_NODE)}function WS(A){return A&&A.nodeType===p5.DOCUMENT_TYPE_NODE}function iM(A){return A&&A.nodeType===p5.ELEMENT_NODE}function IN0(A){return A&&A.nodeType===p5.TEXT_NODE}function NTB(A,B){var Q=A.childNodes||[];if(pM(Q,iM)||WS(B))return!1;var D=pM(Q,WS);return!(B&&D&&Q.indexOf(D)>Q.indexOf(B))}function LTB(A,B){var Q=A.childNodes||[];function D(G){return iM(G)&&G!==B}if(pM(Q,D))return!1;var Z=pM(Q,WS);return!(B&&Z&&Q.indexOf(Z)>Q.indexOf(B))}function FI8(A,B,Q){if(!ZI8(A))throw new FG(AC,"Unexpected parent node type "+A.nodeType);if(Q&&Q.parentNode!==A)throw new FG(yTB,"child not in parent");if(!GI8(B)||WS(B)&&A.nodeType!==p5.DOCUMENT_NODE)throw new FG(AC,"Unexpected node type "+B.nodeType+" for parent node type "+A.nodeType)}function II8(A,B,Q){var D=A.childNodes||[],Z=B.childNodes||[];if(B.nodeType===p5.DOCUMENT_FRAGMENT_NODE){var G=Z.filter(iM);if(G.length>1||pM(Z,IN0))throw new FG(AC,"More than one element or text in fragment");if(G.length===1&&!NTB(A,Q))throw new FG(AC,"Element in fragment can not be inserted before doctype")}if(iM(B)){if(!NTB(A,Q))throw new FG(AC,"Only one element can be added and only after doctype")}if(WS(B)){if(pM(D,WS))throw new FG(AC,"Only one doctype is allowed");var F=pM(D,iM);if(Q&&D.indexOf(F)<D.indexOf(Q))throw new FG(AC,"Doctype can only be inserted before an element");if(!Q&&F)throw new FG(AC,"Doctype can not be appended since element is present")}}function hTB(A,B,Q){var D=A.childNodes||[],Z=B.childNodes||[];if(B.nodeType===p5.DOCUMENT_FRAGMENT_NODE){var G=Z.filter(iM);if(G.length>1||pM(Z,IN0))throw new FG(AC,"More than one element or text in fragment");if(G.length===1&&!LTB(A,Q))throw new FG(AC,"Element in fragment can not be inserted before doctype")}if(iM(B)){if(!LTB(A,Q))throw new FG(AC,"Only one element can be added and only after doctype")}if(WS(B)){let Y=function(W){return WS(W)&&W!==Q};var I=Y;if(pM(D,Y))throw new FG(AC,"Only one doctype is allowed");var F=pM(D,iM);if(Q&&D.indexOf(F)<D.indexOf(Q))throw new FG(AC,"Doctype can only be inserted before an element")}}function Gb1(A,B,Q,D){if(FI8(A,B,Q),A.nodeType===p5.DOCUMENT_NODE)(D||II8)(A,B,Q);var Z=B.parentNode;if(Z)Z.removeChild(B);if(B.nodeType===YS){var G=B.firstChild;if(G==null)return B;var F=B.lastChild}else G=F=B;var I=Q?Q.previousSibling:A.lastChild;if(G.previousSibling=I,F.nextSibling=Q,I)I.nextSibling=G;else A.firstChild=G;if(Q==null)A.lastChild=F;else Q.previousSibling=F;do G.parentNode=A;while(G!==F&&(G=G.nextSibling));if(FN0(A.ownerDocument||A,A),B.nodeType==YS)B.firstChild=B.lastChild=null;return B}function YI8(A,B){if(B.parentNode)B.parentNode.removeChild(B);if(B.parentNode=A,B.previousSibling=A.lastChild,B.nextSibling=null,B.previousSibling)B.previousSibling.nextSibling=B;else A.firstChild=B;return A.lastChild=B,FN0(A.ownerDocument,A,B),B}gG1.prototype={nodeName:"#document",nodeType:jTB,doctype:null,documentElement:null,_inc:1,insertBefore:function(A,B){if(A.nodeType==YS){var Q=A.firstChild;while(Q){var D=Q.nextSibling;this.insertBefore(Q,B),Q=D}return A}if(Gb1(this,A,B),A.ownerDocument=this,this.documentElement===null&&A.nodeType===_$)this.documentElement=A;return A},removeChild:function(A){if(this.documentElement==A)this.documentElement=null;return fTB(this,A)},replaceChild:function(A,B){if(Gb1(this,A,B,hTB),A.ownerDocument=this,B)this.removeChild(B);if(iM(A))this.documentElement=A},importNode:function(A,B){return cTB(this,A,B)},getElementById:function(A){var B=null;return fG1(this.documentElement,function(Q){if(Q.nodeType==_$){if(Q.getAttribute("id")==A)return B=Q,!0}}),B},getElementsByClassName:function(A){var B=UTB(A);return new I01(this,function(Q){var D=[];if(B.length>0)fG1(Q.documentElement,function(Z){if(Z!==Q&&Z.nodeType===_$){var G=Z.getAttribute("class");if(G){var F=A===G;if(!F){var I=UTB(G);F=B.every(AI8(I))}if(F)D.push(Z)}}});return D})},createElement:function(A){var B=new Zd;B.ownerDocument=this,B.nodeName=A,B.tagName=A,B.localName=A,B.childNodes=new IS;var Q=B.attributes=new Zb1;return Q._ownerElement=B,B},createDocumentFragment:function(){var A=new Yb1;return A.ownerDocument=this,A.childNodes=new IS,A},createTextNode:function(A){var B=new YN0;return B.ownerDocument=this,B.appendData(A),B},createComment:function(A){var B=new WN0;return B.ownerDocument=this,B.appendData(A),B},createCDATASection:function(A){var B=new JN0;return B.ownerDocument=this,B.appendData(A),B},createProcessingInstruction:function(A,B){var Q=new VN0;return Q.ownerDocument=this,Q.tagName=Q.nodeName=Q.target=A,Q.nodeValue=Q.data=B,Q},createAttribute:function(A){var B=new Fb1;return B.ownerDocument=this,B.name=A,B.nodeName=A,B.localName=A,B.specified=!0,B},createEntityReference:function(A){var B=new XN0;return B.ownerDocument=this,B.nodeName=A,B},createElementNS:function(A,B){var Q=new Zd,D=B.split(":"),Z=Q.attributes=new Zb1;if(Q.childNodes=new IS,Q.ownerDocument=this,Q.nodeName=B,Q.tagName=B,Q.namespaceURI=A,D.length==2)Q.prefix=D[0],Q.localName=D[1];else Q.localName=B;return Z._ownerElement=Q,Q},createAttributeNS:function(A,B){var Q=new Fb1,D=B.split(":");if(Q.ownerDocument=this,Q.nodeName=B,Q.name=B,Q.namespaceURI=A,Q.specified=!0,D.length==2)Q.prefix=D[0],Q.localName=D[1];else Q.localName=B;return Q}};BC(gG1,p5);function Zd(){this._nsMap={}}Zd.prototype={nodeType:_$,hasAttribute:function(A){return this.getAttributeNode(A)!=null},getAttribute:function(A){var B=this.getAttributeNode(A);return B&&B.value||""},getAttributeNode:function(A){return this.attributes.getNamedItem(A)},setAttribute:function(A,B){var Q=this.ownerDocument.createAttribute(A);Q.value=Q.nodeValue=""+B,this.setAttributeNode(Q)},removeAttribute:function(A){var B=this.getAttributeNode(A);B&&this.removeAttributeNode(B)},appendChild:function(A){if(A.nodeType===YS)return this.insertBefore(A,null);else return YI8(this,A)},setAttributeNode:function(A){return this.attributes.setNamedItem(A)},setAttributeNodeNS:function(A){return this.attributes.setNamedItemNS(A)},removeAttributeNode:function(A){return this.attributes.removeNamedItem(A.nodeName)},removeAttributeNS:function(A,B){var Q=this.getAttributeNodeNS(A,B);Q&&this.removeAttributeNode(Q)},hasAttributeNS:function(A,B){return this.getAttributeNodeNS(A,B)!=null},getAttributeNS:function(A,B){var Q=this.getAttributeNodeNS(A,B);return Q&&Q.value||""},setAttributeNS:function(A,B,Q){var D=this.ownerDocument.createAttributeNS(A,B);D.value=D.nodeValue=""+Q,this.setAttributeNode(D)},getAttributeNodeNS:function(A,B){return this.attributes.getNamedItemNS(A,B)},getElementsByTagName:function(A){return new I01(this,function(B){var Q=[];return fG1(B,function(D){if(D!==B&&D.nodeType==_$&&(A==="*"||D.tagName==A))Q.push(D)}),Q})},getElementsByTagNameNS:function(A,B){return new I01(this,function(Q){var D=[];return fG1(Q,function(Z){if(Z!==Q&&Z.nodeType===_$&&(A==="*"||Z.namespaceURI===A)&&(B==="*"||Z.localName==B))D.push(Z)}),D})}};gG1.prototype.getElementsByTagName=Zd.prototype.getElementsByTagName;gG1.prototype.getElementsByTagNameNS=Zd.prototype.getElementsByTagNameNS;BC(Zd,p5);function Fb1(){}Fb1.prototype.nodeType=F01;BC(Fb1,p5);function uG1(){}uG1.prototype={data:"",substringData:function(A,B){return this.data.substring(A,A+B)},appendData:function(A){A=this.data+A,this.nodeValue=this.data=A,this.length=A.length},insertData:function(A,B){this.replaceData(A,0,B)},appendChild:function(A){throw new Error(SI[AC])},deleteData:function(A,B){this.replaceData(A,B,"")},replaceData:function(A,B,Q){var D=this.data.substring(0,A),Z=this.data.substring(A+B);Q=D+Q+Z,this.nodeValue=this.data=Q,this.length=Q.length}};BC(uG1,p5);function YN0(){}YN0.prototype={nodeName:"#text",nodeType:Db1,splitText:function(A){var B=this.data,Q=B.substring(A);B=B.substring(0,A),this.data=this.nodeValue=B,this.length=B.length;var D=this.ownerDocument.createTextNode(Q);if(this.parentNode)this.parentNode.insertBefore(D,this.nextSibling);return D}};BC(YN0,uG1);function WN0(){}WN0.prototype={nodeName:"#comment",nodeType:STB};BC(WN0,uG1);function JN0(){}JN0.prototype={nodeName:"#cdata-section",nodeType:OTB};BC(JN0,uG1);function Ib1(){}Ib1.prototype.nodeType=kTB;BC(Ib1,p5);function gTB(){}gTB.prototype.nodeType=QI8;BC(gTB,p5);function uTB(){}uTB.prototype.nodeType=BI8;BC(uTB,p5);function XN0(){}XN0.prototype.nodeType=TTB;BC(XN0,p5);function Yb1(){}Yb1.prototype.nodeName="#document-fragment";Yb1.prototype.nodeType=YS;BC(Yb1,p5);function VN0(){}VN0.prototype.nodeType=PTB;BC(VN0,p5);function mTB(){}mTB.prototype.serializeToString=function(A,B,Q){return dTB.call(A,B,Q)};p5.prototype.toString=dTB;function dTB(A,B){var Q=[],D=this.nodeType==9&&this.documentElement||this,Z=D.prefix,G=D.namespaceURI;if(G&&Z==null){var Z=D.lookupPrefix(G);if(Z==null)var F=[{namespace:G,prefix:null}]}return G01(this,Q,A,B,F),Q.join("")}function MTB(A,B,Q){var D=A.prefix||"",Z=A.namespaceURI;if(!Z)return!1;if(D==="xml"&&Z===bG1.XML||Z===bG1.XMLNS)return!1;var G=Q.length;while(G--){var F=Q[G];if(F.prefix===D)return F.namespace!==Z}return!0}function DN0(A,B,Q){A.push(" ",B,'="',Q.replace(/[<>&"\t\n\r]/g,vTB),'"')}function G01(A,B,Q,D,Z){if(!Z)Z=[];if(D)if(A=D(A),A){if(typeof A=="string"){B.push(A);return}}else return;switch(A.nodeType){case _$:var G=A.attributes,F=G.length,$=A.firstChild,I=A.tagName;Q=bG1.isHTML(A.namespaceURI)||Q;var Y=I;if(!Q&&!A.prefix&&A.namespaceURI){var W;for(var J=0;J<G.length;J++)if(G.item(J).name==="xmlns"){W=G.item(J).value;break}if(!W)for(var X=Z.length-1;X>=0;X--){var V=Z[X];if(V.prefix===""&&V.namespace===A.namespaceURI){W=V.namespace;break}}if(W!==A.namespaceURI)for(var X=Z.length-1;X>=0;X--){var V=Z[X];if(V.namespace===A.namespaceURI){if(V.prefix)Y=V.prefix+":"+I;break}}}B.push("<",Y);for(var C=0;C<F;C++){var K=G.item(C);if(K.prefix=="xmlns")Z.push({prefix:K.localName,namespace:K.value});else if(K.nodeName=="xmlns")Z.push({prefix:"",namespace:K.value})}for(var C=0;C<F;C++){var K=G.item(C);if(MTB(K,Q,Z)){var H=K.prefix||"",z=K.namespaceURI;DN0(B,H?"xmlns:"+H:"xmlns",z),Z.push({prefix:H,namespace:z})}G01(K,B,Q,D,Z)}if(I===Y&&MTB(A,Q,Z)){var H=A.prefix||"",z=A.namespaceURI;DN0(B,H?"xmlns:"+H:"xmlns",z),Z.push({prefix:H,namespace:z})}if($||Q&&!/^(?:meta|link|img|br|hr|input)$/i.test(I)){if(B.push(">"),Q&&/^script$/i.test(I))while($){if($.data)B.push($.data);else G01($,B,Q,D,Z.slice());$=$.nextSibling}else while($)G01($,B,Q,D,Z.slice()),$=$.nextSibling;B.push("</",Y,">")}else B.push("/>");return;case jTB:case YS:var $=A.firstChild;while($)G01($,B,Q,D,Z.slice()),$=$.nextSibling;return;case F01:return DN0(B,A.name,A.value);case Db1:return B.push(A.data.replace(/[<&>]/g,vTB));case OTB:return B.push("<![CDATA[",A.data,"]]>");case STB:return B.push("<!--",A.data,"-->");case kTB:var{publicId:L,systemId:N}=A;if(B.push("<!DOCTYPE ",A.name),L){if(B.push(" PUBLIC ",L),N&&N!=".")B.push(" ",N);B.push(">")}else if(N&&N!=".")B.push(" SYSTEM ",N,">");else{var O=A.internalSubset;if(O)B.push(" [",O,"]");B.push(">")}return;case PTB:return B.push("<?",A.target," ",A.data,"?>");case TTB:return B.push("&",A.nodeName,";");default:B.push("??",A.nodeName)}}function cTB(A,B,Q){var D;switch(B.nodeType){case _$:D=B.cloneNode(!1),D.ownerDocument=A;case YS:break;case F01:Q=!0;break}if(!D)D=B.cloneNode(!1);if(D.ownerDocument=A,D.parentNode=null,Q){var Z=B.firstChild;while(Z)D.appendChild(cTB(A,Z,Q)),Z=Z.nextSibling}return D}function ZN0(A,B,Q){var D=new B.constructor;for(var Z in B)if(Object.prototype.hasOwnProperty.call(B,Z)){var G=B[Z];if(typeof G!="object"){if(G!=D[Z])D[Z]=G}}if(B.childNodes)D.childNodes=new IS;switch(D.ownerDocument=A,D.nodeType){case _$:var F=B.attributes,I=D.attributes=new Zb1,Y=F.length;I._ownerElement=D;for(var W=0;W<Y;W++)D.setAttributeNode(ZN0(A,F.item(W),!0));break;case F01:Q=!0}if(Q){var J=B.firstChild;while(J)D.appendChild(ZN0(A,J,Q)),J=J.nextSibling}return D}function lTB(A,B,Q){A[B]=Q}try{if(Object.defineProperty){let A=function(B){switch(B.nodeType){case _$:case YS:var Q=[];B=B.firstChild;while(B){if(B.nodeType!==7&&B.nodeType!==8)Q.push(A(B));B=B.nextSibling}return Q.join("");default:return B.nodeValue}};WI8=A,Object.defineProperty(I01.prototype,"length",{get:function(){return GN0(this),this.$$length}}),Object.defineProperty(p5.prototype,"textContent",{get:function(){return A(this)},set:function(B){switch(this.nodeType){case _$:case YS:while(this.firstChild)this.removeChild(this.firstChild);if(B||String(B))this.appendChild(this.ownerDocument.createTextNode(B));break;default:this.data=B,this.value=B,this.nodeValue=B}}}),lTB=function(B,Q,D){B["$$"+Q]=D}}}catch(A){}var WI8;JI8.DocumentType=Ib1;JI8.DOMException=FG;JI8.DOMImplementation=xTB;JI8.Element=Zd;JI8.Node=p5;JI8.NodeList=IS;JI8.XMLSerializer=mTB});
var Cb1=E((kPB,yPB)=>{(function(){var A,B,Q,D,Z,G,F,I,Y={}.hasOwnProperty;({isObject:I,isFunction:F,getValue:G}=nM()),Z=cK(),A=IG(),B=UN0(),D=Vb1(),yPB.exports=Q=function(){class W extends Z{constructor(J,X,V){var C,K,H,z;super(J);if(X==null)throw new Error("Missing element name. "+this.debugInfo());if(this.name=this.stringify.name(X),this.type=A.Element,this.attribs={},this.schemaTypeInfo=null,V!=null)this.attribute(V);if(J.type===A.Document){if(this.isRoot=!0,this.documentObject=J,J.rootObject=this,J.children){z=J.children;for(K=0,H=z.length;K<H;K++)if(C=z[K],C.type===A.DocType){C.name=this.name;break}}}}clone(){var J,X,V,C;if(V=Object.create(this),V.isRoot)V.documentObject=null;V.attribs={},C=this.attribs;for(X in C){if(!Y.call(C,X))continue;J=C[X],V.attribs[X]=J.clone()}return V.children=[],this.children.forEach(function(K){var H=K.clone();return H.parent=V,V.children.push(H)}),V}attribute(J,X){var V,C;if(J!=null)J=G(J);if(I(J))for(V in J){if(!Y.call(J,V))continue;C=J[V],this.attribute(V,C)}else{if(F(X))X=X.apply();if(this.options.keepNullAttributes&&X==null)this.attribs[J]=new B(this,J,"");else if(X!=null)this.attribs[J]=new B(this,J,X)}return this}removeAttribute(J){var X,V,C;if(J==null)throw new Error("Missing attribute name. "+this.debugInfo());if(J=G(J),Array.isArray(J))for(V=0,C=J.length;V<C;V++)X=J[V],delete this.attribs[X];else delete this.attribs[J];return this}toString(J){return this.options.writer.element(this,this.options.writer.filterOptions(J))}att(J,X){return this.attribute(J,X)}a(J,X){return this.attribute(J,X)}getAttribute(J){if(this.attribs.hasOwnProperty(J))return this.attribs[J].value;else return null}setAttribute(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}getAttributeNode(J){if(this.attribs.hasOwnProperty(J))return this.attribs[J];else return null}setAttributeNode(J){throw new Error("This DOM method is not implemented."+this.debugInfo())}removeAttributeNode(J){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagName(J){throw new Error("This DOM method is not implemented."+this.debugInfo())}getAttributeNS(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}setAttributeNS(J,X,V){throw new Error("This DOM method is not implemented."+this.debugInfo())}removeAttributeNS(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}getAttributeNodeNS(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}setAttributeNodeNS(J){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagNameNS(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}hasAttribute(J){return this.attribs.hasOwnProperty(J)}hasAttributeNS(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}setIdAttribute(J,X){if(this.attribs.hasOwnProperty(J))return this.attribs[J].isId;else return X}setIdAttributeNS(J,X,V){throw new Error("This DOM method is not implemented."+this.debugInfo())}setIdAttributeNode(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagName(J){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagNameNS(J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByClassName(J){throw new Error("This DOM method is not implemented."+this.debugInfo())}isEqualNode(J){var X,V,C;if(!super.isEqualNode(J))return!1;if(J.namespaceURI!==this.namespaceURI)return!1;if(J.prefix!==this.prefix)return!1;if(J.localName!==this.localName)return!1;if(J.attribs.length!==this.attribs.length)return!1;for(X=V=0,C=this.attribs.length-1;0<=C?V<=C:V>=C;X=0<=C?++V:--V)if(!this.attribs[X].isEqualNode(J.attribs[X]))return!1;return!0}}return Object.defineProperty(W.prototype,"tagName",{get:function(){return this.name}}),Object.defineProperty(W.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(W.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(W.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(W.prototype,"id",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(W.prototype,"className",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(W.prototype,"classList",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(W.prototype,"attributes",{get:function(){if(!this.attributeMap||!this.attributeMap.nodes)this.attributeMap=new D(this.attribs);return this.attributeMap}}),W}.call(this)}).call(kPB)});
var EN0=E((KPB,HPB)=>{(function(){var A;HPB.exports=A=class B{hasFeature(Q,D){return!0}createDocumentType(Q,D,Z){throw new Error("This DOM method is not implemented.")}createDocument(Q,D,Z){throw new Error("This DOM method is not implemented.")}createHTMLDocument(Q){throw new Error("This DOM method is not implemented.")}getFeature(Q,D){throw new Error("This DOM method is not implemented.")}}}).call(KPB)});
var Eb1=E((mPB,dPB)=>{(function(){var A,B,Q;Q=cK(),A=IG(),dPB.exports=B=class D extends Q{constructor(Z,G,F,I,Y,W){super(Z);if(G==null)throw new Error("Missing DTD element name. "+this.debugInfo());if(F==null)throw new Error("Missing DTD attribute name. "+this.debugInfo(G));if(!I)throw new Error("Missing DTD attribute type. "+this.debugInfo(G));if(!Y)throw new Error("Missing DTD attribute default. "+this.debugInfo(G));if(Y.indexOf("#")!==0)Y="#"+Y;if(!Y.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/))throw new Error("Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. "+this.debugInfo(G));if(W&&!Y.match(/^(#FIXED|#DEFAULT)$/))throw new Error("Default value only applies to #FIXED or #DEFAULT. "+this.debugInfo(G));if(this.elementName=this.stringify.name(G),this.type=A.AttributeDeclaration,this.attributeName=this.stringify.name(F),this.attributeType=this.stringify.dtdAttType(I),W)this.defaultValue=this.stringify.dtdAttDefault(W);this.defaultValueType=Y}toString(Z){return this.options.writer.dtdAttList(this,this.options.writer.filterOptions(Z))}}}).call(mPB)});
var Hb1=E((fPB,hPB)=>{(function(){var A,B,Q;A=IG(),B=iG1(),hPB.exports=Q=class D extends B{constructor(Z,G){super(Z);if(G==null)throw new Error("Missing comment text. "+this.debugInfo());this.name="#comment",this.type=A.Comment,this.value=this.stringify.comment(G)}clone(){return Object.create(this)}toString(Z){return this.options.writer.comment(this,this.options.writer.filterOptions(Z))}}}).call(fPB)});
var IG=E((RPB,OPB)=>{(function(){OPB.exports={Element:1,Attribute:2,Text:3,CData:4,EntityReference:5,EntityDeclaration:6,ProcessingInstruction:7,Comment:8,Document:9,DocType:10,DocumentFragment:11,NotationDeclaration:12,Declaration:201,Raw:202,AttributeDeclaration:203,ElementDeclaration:204,Dummy:205}}).call(RPB)});
var ISB=E((GSB,FSB)=>{(function(){var A;FSB.exports=A=function(){class B{constructor(Q){this.nodes=Q}clone(){return this.nodes=null}item(Q){return this.nodes[Q]||null}}return Object.defineProperty(B.prototype,"length",{get:function(){return this.nodes.length||0}}),B}.call(this)}).call(GSB)});
var JPB=E((mI8)=>{var WPB=CN0();mI8.DOMImplementation=WPB.DOMImplementation;mI8.XMLSerializer=WPB.XMLSerializer;mI8.DOMParser=YPB().DOMParser});
var JSB=E((YSB,WSB)=>{(function(){WSB.exports={Disconnected:1,Preceding:2,Following:4,Contains:8,ContainedBy:16,ImplementationSpecific:32}}).call(YSB)});
var Kb1=E((vPB,bPB)=>{(function(){var A,B,Q;A=IG(),Q=iG1(),bPB.exports=B=class D extends Q{constructor(Z,G){super(Z);if(G==null)throw new Error("Missing CDATA text. "+this.debugInfo());this.name="#cdata-section",this.type=A.CData,this.value=this.stringify.cdata(G)}clone(){return Object.create(this)}toString(Z){return this.options.writer.cdata(this,this.options.writer.filterOptions(Z))}}}).call(vPB)});
var Lb1=E((ePB,ASB)=>{(function(){var A,B,Q;A=IG(),B=iG1(),ASB.exports=Q=function(){class D extends B{constructor(Z,G){super(Z);if(G==null)throw new Error("Missing element text. "+this.debugInfo());this.name="#text",this.type=A.Text,this.value=this.stringify.text(G)}clone(){return Object.create(this)}toString(Z){return this.options.writer.text(this,this.options.writer.filterOptions(Z))}splitText(Z){throw new Error("This DOM method is not implemented."+this.debugInfo())}replaceWholeText(Z){throw new Error("This DOM method is not implemented."+this.debugInfo())}}return Object.defineProperty(D.prototype,"isElementContentWhitespace",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),Object.defineProperty(D.prototype,"wholeText",{get:function(){var Z,G,F;F="",G=this.previousSibling;while(G)F=G.data+F,G=G.previousSibling;F+=this.data,Z=this.nextSibling;while(Z)F=F+Z.data,Z=Z.nextSibling;return F}}),D}.call(this)}).call(ePB)});
var MPB=E((NPB,LPB)=>{(function(){var A,B,Q;B=UPB(),Q=qPB(),LPB.exports=A=function(){class D{constructor(){var Z;this.defaultParams={"canonical-form":!1,"cdata-sections":!1,comments:!1,"datatype-normalization":!1,"element-content-whitespace":!0,entities:!0,"error-handler":new B,infoset:!0,"validate-if-schema":!1,namespaces:!0,"namespace-declarations":!0,"normalize-characters":!1,"schema-location":"","schema-type":"","split-cdata-sections":!0,validate:!1,"well-formed":!0},this.params=Z=Object.create(this.defaultParams)}getParameter(Z){if(this.params.hasOwnProperty(Z))return this.params[Z];else return null}canSetParameter(Z,G){return!0}setParameter(Z,G){if(G!=null)return this.params[Z]=G;else return delete this.params[Z]}}return Object.defineProperty(D.prototype,"parameterNames",{get:function(){return new Q(Object.keys(this.defaultParams))}}),D}.call(this)}).call(NPB)});
var Mb1=E((BSB,QSB)=>{(function(){var A,B,Q;A=IG(),B=iG1(),QSB.exports=Q=class D extends B{constructor(Z,G,F){super(Z);if(G==null)throw new Error("Missing instruction target. "+this.debugInfo());if(this.type=A.ProcessingInstruction,this.target=this.stringify.insTarget(G),this.name=this.target,F)this.value=this.stringify.insValue(F)}clone(){return Object.create(this)}toString(Z){return this.options.writer.processingInstruction(this,this.options.writer.filterOptions(Z))}isEqualNode(Z){if(!super.isEqualNode(Z))return!1;if(Z.target!==this.target)return!1;return!0}}}).call(BSB)});
var NN0=E((qSB,NSB)=>{(function(){var A,B,Q,D,Z,G,F,I;({isPlainObject:I}=nM()),Q=EN0(),B=MPB(),Z=cK(),A=IG(),F=$N0(),G=Rb1(),NSB.exports=D=function(){class Y extends Z{constructor(W){super(null);if(this.name="#document",this.type=A.Document,this.documentURI=null,this.domConfig=new B,W||(W={}),!W.writer)W.writer=new G;this.options=W,this.stringify=new F(W)}end(W){var J={};if(!W)W=this.options.writer;else if(I(W))J=W,W=this.options.writer;return W.document(this,W.filterOptions(J))}toString(W){return this.options.writer.document(this,this.options.writer.filterOptions(W))}createElement(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}createDocumentFragment(){throw new Error("This DOM method is not implemented."+this.debugInfo())}createTextNode(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}createComment(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}createCDATASection(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}createProcessingInstruction(W,J){throw new Error("This DOM method is not implemented."+this.debugInfo())}createAttribute(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}createEntityReference(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagName(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}importNode(W,J){throw new Error("This DOM method is not implemented."+this.debugInfo())}createElementNS(W,J){throw new Error("This DOM method is not implemented."+this.debugInfo())}createAttributeNS(W,J){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByTagNameNS(W,J){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementById(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}adoptNode(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}normalizeDocument(){throw new Error("This DOM method is not implemented."+this.debugInfo())}renameNode(W,J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}getElementsByClassName(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}createEvent(W){throw new Error("This DOM method is not implemented."+this.debugInfo())}createRange(){throw new Error("This DOM method is not implemented."+this.debugInfo())}createNodeIterator(W,J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}createTreeWalker(W,J,X){throw new Error("This DOM method is not implemented."+this.debugInfo())}}return Object.defineProperty(Y.prototype,"implementation",{value:new Q}),Object.defineProperty(Y.prototype,"doctype",{get:function(){var W,J,X,V;V=this.children;for(J=0,X=V.length;J<X;J++)if(W=V[J],W.type===A.DocType)return W;return null}}),Object.defineProperty(Y.prototype,"documentElement",{get:function(){return this.rootObject||null}}),Object.defineProperty(Y.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(Y.prototype,"strictErrorChecking",{get:function(){return!1}}),Object.defineProperty(Y.prototype,"xmlEncoding",{get:function(){if(this.children.length!==0&&this.children[0].type===A.Declaration)return this.children[0].encoding;else return null}}),Object.defineProperty(Y.prototype,"xmlStandalone",{get:function(){if(this.children.length!==0&&this.children[0].type===A.Declaration)return this.children[0].standalone==="yes";else return!1}}),Object.defineProperty(Y.prototype,"xmlVersion",{get:function(){if(this.children.length!==0&&this.children[0].type===A.Declaration)return this.children[0].version;else return"1.0"}}),Object.defineProperty(Y.prototype,"URL",{get:function(){return this.documentURI}}),Object.defineProperty(Y.prototype,"origin",{get:function(){return null}}),Object.defineProperty(Y.prototype,"compatMode",{get:function(){return null}}),Object.defineProperty(Y.prototype,"characterSet",{get:function(){return null}}),Object.defineProperty(Y.prototype,"contentType",{get:function(){return null}}),Y}.call(this)}).call(qSB)});
var Nb1=E((oPB,tPB)=>{(function(){var A,B,Q;A=IG(),B=cK(),tPB.exports=Q=class D extends B{constructor(Z,G){super(Z);if(G==null)throw new Error("Missing raw text. "+this.debugInfo());this.type=A.Raw,this.value=this.stringify.raw(G)}clone(){return Object.create(this)}toString(Z){return this.options.writer.raw(this,this.options.writer.filterOptions(Z))}}}).call(oPB)});
var PSB=E((OSB,TSB)=>{(function(){var A,B,Q,D,Z={}.hasOwnProperty;A=IG(),D=qN0(),B=nG1(),TSB.exports=Q=class G extends D{constructor(F,I){super(I);this.stream=F}endline(F,I,Y){if(F.isLastRootNode&&I.state===B.CloseTag)return"";else return super.endline(F,I,Y)}document(F,I){var Y,W,J,X,V,C,K,H,z;K=F.children;for(W=J=0,V=K.length;J<V;W=++J)Y=K[W],Y.isLastRootNode=W===F.children.length-1;I=this.filterOptions(I),H=F.children,z=[];for(X=0,C=H.length;X<C;X++)Y=H[X],z.push(this.writeChildNode(Y,I,0));return z}cdata(F,I,Y){return this.stream.write(super.cdata(F,I,Y))}comment(F,I,Y){return this.stream.write(super.comment(F,I,Y))}declaration(F,I,Y){return this.stream.write(super.declaration(F,I,Y))}docType(F,I,Y){var W,J,X,V;if(Y||(Y=0),this.openNode(F,I,Y),I.state=B.OpenTag,this.stream.write(this.indent(F,I,Y)),this.stream.write("<!DOCTYPE "+F.root().name),F.pubID&&F.sysID)this.stream.write(' PUBLIC "'+F.pubID+'" "'+F.sysID+'"');else if(F.sysID)this.stream.write(' SYSTEM "'+F.sysID+'"');if(F.children.length>0){this.stream.write(" ["),this.stream.write(this.endline(F,I,Y)),I.state=B.InsideTag,V=F.children;for(J=0,X=V.length;J<X;J++)W=V[J],this.writeChildNode(W,I,Y+1);I.state=B.CloseTag,this.stream.write("]")}return I.state=B.CloseTag,this.stream.write(I.spaceBeforeSlash+">"),this.stream.write(this.endline(F,I,Y)),I.state=B.None,this.closeNode(F,I,Y)}element(F,I,Y){var W,J,X,V,C,K,H,z,$,L,N,O,R,T,j,f;if(Y||(Y=0),this.openNode(F,I,Y),I.state=B.OpenTag,N=this.indent(F,I,Y)+"<"+F.name,I.pretty&&I.width>0){H=N.length,R=F.attribs;for($ in R){if(!Z.call(R,$))continue;if(W=R[$],O=this.attribute(W,I,Y),J=O.length,H+J>I.width)f=this.indent(F,I,Y+1)+O,N+=this.endline(F,I,Y)+f,H=f.length;else f=" "+O,N+=f,H+=f.length}}else{T=F.attribs;for($ in T){if(!Z.call(T,$))continue;W=T[$],N+=this.attribute(W,I,Y)}}if(this.stream.write(N),V=F.children.length,C=V===0?null:F.children[0],V===0||F.children.every(function(k){return(k.type===A.Text||k.type===A.Raw||k.type===A.CData)&&k.value===""}))if(I.allowEmpty)this.stream.write(">"),I.state=B.CloseTag,this.stream.write("</"+F.name+">");else I.state=B.CloseTag,this.stream.write(I.spaceBeforeSlash+"/>");else if(I.pretty&&V===1&&(C.type===A.Text||C.type===A.Raw||C.type===A.CData)&&C.value!=null)this.stream.write(">"),I.state=B.InsideTag,I.suppressPrettyCount++,L=!0,this.writeChildNode(C,I,Y+1),I.suppressPrettyCount--,L=!1,I.state=B.CloseTag,this.stream.write("</"+F.name+">");else{this.stream.write(">"+this.endline(F,I,Y)),I.state=B.InsideTag,j=F.children;for(K=0,z=j.length;K<z;K++)X=j[K],this.writeChildNode(X,I,Y+1);I.state=B.CloseTag,this.stream.write(this.indent(F,I,Y)+"</"+F.name+">")}return this.stream.write(this.endline(F,I,Y)),I.state=B.None,this.closeNode(F,I,Y)}processingInstruction(F,I,Y){return this.stream.write(super.processingInstruction(F,I,Y))}raw(F,I,Y){return this.stream.write(super.raw(F,I,Y))}text(F,I,Y){return this.stream.write(super.text(F,I,Y))}dtdAttList(F,I,Y){return this.stream.write(super.dtdAttList(F,I,Y))}dtdElement(F,I,Y){return this.stream.write(super.dtdElement(F,I,Y))}dtdEntity(F,I,Y){return this.stream.write(super.dtdEntity(F,I,Y))}dtdNotation(F,I,Y){return this.stream.write(super.dtdNotation(F,I,Y))}}}).call(OSB)});
var RSB=E((LSB,MSB)=>{(function(){var A,B,Q,D,Z,G,F,I,Y,W,J,X,V,C,K,H,z,$,L,N,O,R,T,j={}.hasOwnProperty;({isObject:R,isFunction:O,isPlainObject:T,getValue:N}=nM()),A=IG(),X=NN0(),C=Cb1(),D=Kb1(),Z=Hb1(),H=Nb1(),L=Lb1(),K=Mb1(),W=zb1(),J=qb1(),G=Eb1(),I=Ub1(),F=wb1(),Y=$b1(),Q=UN0(),$=$N0(),z=Rb1(),B=nG1(),MSB.exports=V=class f{constructor(k,c,h){var n;if(this.name="?xml",this.type=A.Document,k||(k={}),n={},!k.writer)k.writer=new z;else if(T(k.writer))n=k.writer,k.writer=new z;this.options=k,this.writer=k.writer,this.writerOptions=this.writer.filterOptions(n),this.stringify=new $(k),this.onDataCallback=c||function(){},this.onEndCallback=h||function(){},this.currentNode=null,this.currentLevel=-1,this.openTags={},this.documentStarted=!1,this.documentCompleted=!1,this.root=null}createChildNode(k){var c,h,n,a,x,e,W1,U1;switch(k.type){case A.CData:this.cdata(k.value);break;case A.Comment:this.comment(k.value);break;case A.Element:n={},W1=k.attribs;for(h in W1){if(!j.call(W1,h))continue;c=W1[h],n[h]=c.value}this.node(k.name,n);break;case A.Dummy:this.dummy();break;case A.Raw:this.raw(k.value);break;case A.Text:this.text(k.value);break;case A.ProcessingInstruction:this.instruction(k.target,k.value);break;default:throw new Error("This XML node type is not supported in a JS object: "+k.constructor.name)}U1=k.children;for(x=0,e=U1.length;x<e;x++)if(a=U1[x],this.createChildNode(a),a.type===A.Element)this.up();return this}dummy(){return this}node(k,c,h){if(k==null)throw new Error("Missing node name.");if(this.root&&this.currentLevel===-1)throw new Error("Document can only have one root node. "+this.debugInfo(k));if(this.openCurrent(),k=N(k),c==null)c={};if(c=N(c),!R(c))[h,c]=[c,h];if(this.currentNode=new C(this,k,c),this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,h!=null)this.text(h);return this}element(k,c,h){var n,a,x,e,W1,U1;if(this.currentNode&&this.currentNode.type===A.DocType)this.dtdElement(...arguments);else if(Array.isArray(k)||R(k)||O(k)){e=this.options.noValidation,this.options.noValidation=!0,U1=new X(this.options).element("TEMP_ROOT"),U1.element(k),this.options.noValidation=e,W1=U1.children;for(a=0,x=W1.length;a<x;a++)if(n=W1[a],this.createChildNode(n),n.type===A.Element)this.up()}else this.node(k,c,h);return this}attribute(k,c){var h,n;if(!this.currentNode||this.currentNode.children)throw new Error("att() can only be used immediately after an ele() call in callback mode. "+this.debugInfo(k));if(k!=null)k=N(k);if(R(k))for(h in k){if(!j.call(k,h))continue;n=k[h],this.attribute(h,n)}else{if(O(c))c=c.apply();if(this.options.keepNullAttributes&&c==null)this.currentNode.attribs[k]=new Q(this,k,"");else if(c!=null)this.currentNode.attribs[k]=new Q(this,k,c)}return this}text(k){var c;return this.openCurrent(),c=new L(this,k),this.onData(this.writer.text(c,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}cdata(k){var c;return this.openCurrent(),c=new D(this,k),this.onData(this.writer.cdata(c,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}comment(k){var c;return this.openCurrent(),c=new Z(this,k),this.onData(this.writer.comment(c,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}raw(k){var c;return this.openCurrent(),c=new H(this,k),this.onData(this.writer.raw(c,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}instruction(k,c){var h,n,a,x,e;if(this.openCurrent(),k!=null)k=N(k);if(c!=null)c=N(c);if(Array.isArray(k))for(h=0,x=k.length;h<x;h++)n=k[h],this.instruction(n);else if(R(k))for(n in k){if(!j.call(k,n))continue;a=k[n],this.instruction(n,a)}else{if(O(c))c=c.apply();e=new K(this,k,c),this.onData(this.writer.processingInstruction(e,this.writerOptions,this.currentLevel+1),this.currentLevel+1)}return this}declaration(k,c,h){var n;if(this.openCurrent(),this.documentStarted)throw new Error("declaration() must be the first node.");return n=new W(this,k,c,h),this.onData(this.writer.declaration(n,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}doctype(k,c,h){if(this.openCurrent(),k==null)throw new Error("Missing root node name.");if(this.root)throw new Error("dtd() must come before the root node.");return this.currentNode=new J(this,c,h),this.currentNode.rootNodeName=k,this.currentNode.children=!1,this.currentLevel++,this.openTags[this.currentLevel]=this.currentNode,this}dtdElement(k,c){var h;return this.openCurrent(),h=new F(this,k,c),this.onData(this.writer.dtdElement(h,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}attList(k,c,h,n,a){var x;return this.openCurrent(),x=new G(this,k,c,h,n,a),this.onData(this.writer.dtdAttList(x,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}entity(k,c){var h;return this.openCurrent(),h=new I(this,!1,k,c),this.onData(this.writer.dtdEntity(h,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}pEntity(k,c){var h;return this.openCurrent(),h=new I(this,!0,k,c),this.onData(this.writer.dtdEntity(h,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}notation(k,c){var h;return this.openCurrent(),h=new Y(this,k,c),this.onData(this.writer.dtdNotation(h,this.writerOptions,this.currentLevel+1),this.currentLevel+1),this}up(){if(this.currentLevel<0)throw new Error("The document node has no parent.");if(this.currentNode){if(this.currentNode.children)this.closeNode(this.currentNode);else this.openNode(this.currentNode);this.currentNode=null}else this.closeNode(this.openTags[this.currentLevel]);return delete this.openTags[this.currentLevel],this.currentLevel--,this}end(){while(this.currentLevel>=0)this.up();return this.onEnd()}openCurrent(){if(this.currentNode)return this.currentNode.children=!0,this.openNode(this.currentNode)}openNode(k){var c,h,n,a;if(!k.isOpen){if(!this.root&&this.currentLevel===0&&k.type===A.Element)this.root=k;if(h="",k.type===A.Element){this.writerOptions.state=B.OpenTag,h=this.writer.indent(k,this.writerOptions,this.currentLevel)+"<"+k.name,a=k.attribs;for(n in a){if(!j.call(a,n))continue;c=a[n],h+=this.writer.attribute(c,this.writerOptions,this.currentLevel)}h+=(k.children?">":"/>")+this.writer.endline(k,this.writerOptions,this.currentLevel),this.writerOptions.state=B.InsideTag}else{if(this.writerOptions.state=B.OpenTag,h=this.writer.indent(k,this.writerOptions,this.currentLevel)+"<!DOCTYPE "+k.rootNodeName,k.pubID&&k.sysID)h+=' PUBLIC "'+k.pubID+'" "'+k.sysID+'"';else if(k.sysID)h+=' SYSTEM "'+k.sysID+'"';if(k.children)h+=" [",this.writerOptions.state=B.InsideTag;else this.writerOptions.state=B.CloseTag,h+=">";h+=this.writer.endline(k,this.writerOptions,this.currentLevel)}return this.onData(h,this.currentLevel),k.isOpen=!0}}closeNode(k){var c;if(!k.isClosed){if(c="",this.writerOptions.state=B.CloseTag,k.type===A.Element)c=this.writer.indent(k,this.writerOptions,this.currentLevel)+"</"+k.name+">"+this.writer.endline(k,this.writerOptions,this.currentLevel);else c=this.writer.indent(k,this.writerOptions,this.currentLevel)+"]>"+this.writer.endline(k,this.writerOptions,this.currentLevel);return this.writerOptions.state=B.None,this.onData(c,this.currentLevel),k.isClosed=!0}}onData(k,c){return this.documentStarted=!0,this.onDataCallback(k,c+1)}onEnd(){return this.documentCompleted=!0,this.onEndCallback()}debugInfo(k){if(k==null)return"";else return"node: <"+k+">"}ele(){return this.element(...arguments)}nod(k,c,h){return this.node(k,c,h)}txt(k){return this.text(k)}dat(k){return this.cdata(k)}com(k){return this.comment(k)}ins(k,c){return this.instruction(k,c)}dec(k,c,h){return this.declaration(k,c,h)}dtd(k,c,h){return this.doctype(k,c,h)}e(k,c,h){return this.element(k,c,h)}n(k,c,h){return this.node(k,c,h)}t(k){return this.text(k)}d(k){return this.cdata(k)}c(k){return this.comment(k)}r(k){return this.raw(k)}i(k,c){return this.instruction(k,c)}att(){if(this.currentNode&&this.currentNode.type===A.DocType)return this.attList(...arguments);else return this.attribute(...arguments)}a(){if(this.currentNode&&this.currentNode.type===A.DocType)return this.attList(...arguments);else return this.attribute(...arguments)}ent(k,c){return this.entity(k,c)}pent(k,c){return this.pEntity(k,c)}not(k,c){return this.notation(k,c)}}}).call(LSB)});
var Rb1=E((wSB,$SB)=>{(function(){var A,B;B=qN0(),$SB.exports=A=class Q extends B{constructor(D){super(D)}document(D,Z){var G,F,I,Y,W;Z=this.filterOptions(Z),Y="",W=D.children;for(F=0,I=W.length;F<I;F++)G=W[F],Y+=this.writeChildNode(G,Z,0);if(Z.pretty&&Y.slice(-Z.newline.length)===Z.newline)Y=Y.slice(0,-Z.newline.length);return Y}}}).call(wSB)});
var UN0=E((TPB,PPB)=>{(function(){var A,B,Q;A=IG(),Q=cK(),PPB.exports=B=function(){class D{constructor(Z,G,F){if(this.parent=Z,this.parent)this.options=this.parent.options,this.stringify=this.parent.stringify;if(G==null)throw new Error("Missing attribute name. "+this.debugInfo(G));this.name=this.stringify.name(G),this.value=this.stringify.attValue(F),this.type=A.Attribute,this.isId=!1,this.schemaTypeInfo=null}clone(){return Object.create(this)}toString(Z){return this.options.writer.attribute(this,this.options.writer.filterOptions(Z))}debugInfo(Z){if(Z=Z||this.name,Z==null)return"parent: <"+this.parent.name+">";else return"attribute: {"+Z+"}, parent: <"+this.parent.name+">"}isEqualNode(Z){if(Z.namespaceURI!==this.namespaceURI)return!1;if(Z.prefix!==this.prefix)return!1;if(Z.localName!==this.localName)return!1;if(Z.value!==this.value)return!1;return!0}}return Object.defineProperty(D.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(D.prototype,"ownerElement",{get:function(){return this.parent}}),Object.defineProperty(D.prototype,"textContent",{get:function(){return this.value},set:function(Z){return this.value=Z||""}}),Object.defineProperty(D.prototype,"namespaceURI",{get:function(){return""}}),Object.defineProperty(D.prototype,"prefix",{get:function(){return""}}),Object.defineProperty(D.prototype,"localName",{get:function(){return this.name}}),Object.defineProperty(D.prototype,"specified",{get:function(){return!0}}),D}.call(this)}).call(TPB)});
var UPB=E((zPB,EPB)=>{(function(){var A;EPB.exports=A=class B{constructor(){}handleError(Q){throw new Error(Q)}}}).call(zPB)});
var Ub1=E((cPB,lPB)=>{(function(){var A,B,Q,D;({isObject:D}=nM()),Q=cK(),A=IG(),lPB.exports=B=function(){class Z extends Q{constructor(G,F,I,Y){super(G);if(I==null)throw new Error("Missing DTD entity name. "+this.debugInfo(I));if(Y==null)throw new Error("Missing DTD entity value. "+this.debugInfo(I));if(this.pe=!!F,this.name=this.stringify.name(I),this.type=A.EntityDeclaration,!D(Y))this.value=this.stringify.dtdEntityValue(Y),this.internal=!0;else{if(!Y.pubID&&!Y.sysID)throw new Error("Public and/or system identifiers are required for an external entity. "+this.debugInfo(I));if(Y.pubID&&!Y.sysID)throw new Error("System identifier is required for a public external entity. "+this.debugInfo(I));if(this.internal=!1,Y.pubID!=null)this.pubID=this.stringify.dtdPubID(Y.pubID);if(Y.sysID!=null)this.sysID=this.stringify.dtdSysID(Y.sysID);if(Y.nData!=null)this.nData=this.stringify.dtdNData(Y.nData);if(this.pe&&this.nData)throw new Error("Notation declaration is not allowed in a parameter entity. "+this.debugInfo(I))}}toString(G){return this.options.writer.dtdEntity(this,this.options.writer.filterOptions(G))}}return Object.defineProperty(Z.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(Z.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(Z.prototype,"notationName",{get:function(){return this.nData||null}}),Object.defineProperty(Z.prototype,"inputEncoding",{get:function(){return null}}),Object.defineProperty(Z.prototype,"xmlEncoding",{get:function(){return null}}),Object.defineProperty(Z.prototype,"xmlVersion",{get:function(){return null}}),Z}.call(this)}).call(cPB)});
var VPB=E((aI8)=>{var{DOMParser:pI8}=JPB();aI8.parse=nI8;var Xb1=3,XPB=4,iI8=8;function zN0(A){return A.nodeType===Xb1||A.nodeType===iI8||A.nodeType===XPB}function JS(A){if(!A.childNodes||A.childNodes.length===0)return!0;else return!1}function Gd(A,B){if(!A)throw new Error(B)}function nI8(A){var B=new pI8().parseFromString(A);Gd(B.documentElement.nodeName==="plist","malformed document. First element should be <plist>");var Q=C01(B.documentElement);if(Q.length==1)Q=Q[0];return Q}function C01(A){var B,Q,D,Z,G,F,I,Y;if(!A)return null;if(A.nodeName==="plist"){if(G=[],JS(A))return G;for(B=0;B<A.childNodes.length;B++)if(!zN0(A.childNodes[B]))G.push(C01(A.childNodes[B]));return G}else if(A.nodeName==="dict"){if(Q={},D=null,I=0,JS(A))return Q;for(B=0;B<A.childNodes.length;B++){if(zN0(A.childNodes[B]))continue;if(I%2===0)Gd(A.childNodes[B].nodeName==="key","Missing key while parsing <dict/>."),D=C01(A.childNodes[B]);else Gd(A.childNodes[B].nodeName!=="key",'Unexpected key "'+C01(A.childNodes[B])+'" while parsing <dict/>.'),Q[D]=C01(A.childNodes[B]);I+=1}if(I%2===1)Q[D]="";return Q}else if(A.nodeName==="array"){if(G=[],JS(A))return G;for(B=0;B<A.childNodes.length;B++)if(!zN0(A.childNodes[B])){if(F=C01(A.childNodes[B]),F!=null)G.push(F)}return G}else if(A.nodeName==="#text");else if(A.nodeName==="key"){if(JS(A))return"";return Gd(A.childNodes[0].nodeValue!=="__proto__","__proto__ keys can lead to prototype pollution. More details on CVE-2022-22912"),A.childNodes[0].nodeValue}else if(A.nodeName==="string"){if(F="",JS(A))return F;for(B=0;B<A.childNodes.length;B++){var Y=A.childNodes[B].nodeType;if(Y===Xb1||Y===XPB)F+=A.childNodes[B].nodeValue}return F}else if(A.nodeName==="integer")return Gd(!JS(A),'Cannot parse "" as integer.'),parseInt(A.childNodes[0].nodeValue,10);else if(A.nodeName==="real"){Gd(!JS(A),'Cannot parse "" as real.'),F="";for(B=0;B<A.childNodes.length;B++)if(A.childNodes[B].nodeType===Xb1)F+=A.childNodes[B].nodeValue;return parseFloat(F)}else if(A.nodeName==="data"){if(F="",JS(A))return Buffer.from(F,"base64");for(B=0;B<A.childNodes.length;B++)if(A.childNodes[B].nodeType===Xb1)F+=A.childNodes[B].nodeValue.replace(/\s+/g,"");return Buffer.from(F,"base64")}else if(A.nodeName==="date")return Gd(!JS(A),'Cannot parse "" as Date.'),new Date(A.childNodes[0].nodeValue);else if(A.nodeName==="null")return null;else if(A.nodeName==="true")return!0;else if(A.nodeName==="false")return!1;else throw new Error("Invalid PLIST tag "+A.nodeName)}});
var Vb1=E((SPB,jPB)=>{(function(){var A;jPB.exports=A=function(){class B{constructor(Q){this.nodes=Q}clone(){return this.nodes=null}getNamedItem(Q){return this.nodes[Q]}setNamedItem(Q){var D=this.nodes[Q.nodeName];return this.nodes[Q.nodeName]=Q,D||null}removeNamedItem(Q){var D=this.nodes[Q];return delete this.nodes[Q],D||null}item(Q){return this.nodes[Object.keys(this.nodes)[Q]]||null}getNamedItemNS(Q,D){throw new Error("This DOM method is not implemented.")}setNamedItemNS(Q){throw new Error("This DOM method is not implemented.")}removeNamedItemNS(Q,D){throw new Error("This DOM method is not implemented.")}}return Object.defineProperty(B.prototype,"length",{get:function(){return Object.keys(this.nodes).length||0}}),B}.call(this)}).call(SPB)});
var YPB=E((fI8)=>{var kI8=vG1(),yI8=CN0(),QPB=nTB(),GPB=BPB(),_I8=yI8.DOMImplementation,DPB=kI8.NAMESPACE,xI8=GPB.ParseError,vI8=GPB.XMLReader;function FPB(A){return A.replace(/\r[\n\u0085]/g,`
`).replace(/[\r\u0085\u2028]/g,`
`)}function IPB(A){this.options=A||{locator:{}}}IPB.prototype.parseFromString=function(A,B){var Q=this.options,D=new vI8,Z=Q.domBuilder||new pG1,G=Q.errorHandler,F=Q.locator,I=Q.xmlns||{},Y=/\/x?html?$/.test(B),W=Y?QPB.HTML_ENTITIES:QPB.XML_ENTITIES;if(F)Z.setDocumentLocator(F);if(D.errorHandler=bI8(G,Z,F),D.domBuilder=Q.domBuilder||Z,Y)I[""]=DPB.HTML;I.xml=I.xml||DPB.XML;var J=Q.normalizeLineEndings||FPB;if(A&&typeof A==="string")D.parse(J(A),I,W);else D.errorHandler.error("invalid doc source");return Z.doc};function bI8(A,B,Q){if(!A){if(B instanceof pG1)return B;A=B}var D={},Z=A instanceof Function;Q=Q||{};function G(F){var I=A[F];if(!I&&Z)I=A.length==2?function(Y){A(F,Y)}:A;D[F]=I&&function(Y){I("[xmldom "+F+"]	"+Y+HN0(Q))}||function(){}}return G("warning"),G("error"),G("fatalError"),D}function pG1(){this.cdata=!1}function V01(A,B){B.lineNumber=A.lineNumber,B.columnNumber=A.columnNumber}pG1.prototype={startDocument:function(){if(this.doc=new _I8().createDocument(null,null,null),this.locator)this.doc.documentURI=this.locator.systemId},startElement:function(A,B,Q,D){var Z=this.doc,G=Z.createElementNS(A,Q||B),F=D.length;Jb1(this,G),this.currentElement=G,this.locator&&V01(this.locator,G);for(var I=0;I<F;I++){var A=D.getURI(I),Y=D.getValue(I),Q=D.getQName(I),W=Z.createAttributeNS(A,Q);this.locator&&V01(D.getLocator(I),W),W.value=W.nodeValue=Y,G.setAttributeNode(W)}},endElement:function(A,B,Q){var D=this.currentElement,Z=D.tagName;this.currentElement=D.parentNode},startPrefixMapping:function(A,B){},endPrefixMapping:function(A){},processingInstruction:function(A,B){var Q=this.doc.createProcessingInstruction(A,B);this.locator&&V01(this.locator,Q),Jb1(this,Q)},ignorableWhitespace:function(A,B,Q){},characters:function(A,B,Q){if(A=ZPB.apply(this,arguments),A){if(this.cdata)var D=this.doc.createCDATASection(A);else var D=this.doc.createTextNode(A);if(this.currentElement)this.currentElement.appendChild(D);else if(/^\s*$/.test(A))this.doc.appendChild(D);this.locator&&V01(this.locator,D)}},skippedEntity:function(A){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(A){if(this.locator=A)A.lineNumber=0},comment:function(A,B,Q){A=ZPB.apply(this,arguments);var D=this.doc.createComment(A);this.locator&&V01(this.locator,D),Jb1(this,D)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(A,B,Q){var D=this.doc.implementation;if(D&&D.createDocumentType){var Z=D.createDocumentType(A,B,Q);this.locator&&V01(this.locator,Z),Jb1(this,Z),this.doc.doctype=Z}},warning:function(A){console.warn("[xmldom warning]	"+A,HN0(this.locator))},error:function(A){console.error("[xmldom error]	"+A,HN0(this.locator))},fatalError:function(A){throw new xI8(A,this.locator)}};function HN0(A){if(A)return`
@`+(A.systemId||"")+"#[line:"+A.lineNumber+",col:"+A.columnNumber+"]"}function ZPB(A,B,Q){if(typeof A=="string")return A.substr(B,Q);else{if(A.length>=B+Q||B)return new java.lang.String(A,B,Q)+"";return A}}"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,function(A){pG1.prototype[A]=function(){return null}});function Jb1(A,B){if(!A.currentElement)A.doc.appendChild(B);else A.currentElement.appendChild(B)}fI8.__DOMHandler=pG1;fI8.normalizeLineEndings=FPB;fI8.DOMParser=IPB});
var _SB=E((AY8)=>{var kSB=ZE0(),rI8=jSB();AY8.build=eI8;function oI8(A){function B(Q){return Q<10?"0"+Q:Q}return A.getUTCFullYear()+"-"+B(A.getUTCMonth()+1)+"-"+B(A.getUTCDate())+"T"+B(A.getUTCHours())+":"+B(A.getUTCMinutes())+":"+B(A.getUTCSeconds())+"Z"}var tI8=Object.prototype.toString;function ySB(A){var B=tI8.call(A).match(/\[object (.*)\]/);return B?B[1]:B}function eI8(A,B){var Q={version:"1.0",encoding:"UTF-8"},D={pubid:"-//Apple//DTD PLIST 1.0//EN",sysid:"http://www.apple.com/DTDs/PropertyList-1.0.dtd"},Z=rI8.create("plist");if(Z.dec(Q.version,Q.encoding,Q.standalone),Z.dtd(D.pubid,D.sysid),Z.att("version","1.0"),LN0(A,Z),!B)B={};return B.pretty=B.pretty!==!1,Z.end(B)}function LN0(A,B){var Q,D,Z,G=ySB(A);if(G=="Undefined")return;else if(Array.isArray(A)){B=B.ele("array");for(D=0;D<A.length;D++)LN0(A[D],B)}else if(Buffer.isBuffer(A))B.ele("data").raw(A.toString("base64"));else if(G=="Object"){B=B.ele("dict");for(Z in A)if(A.hasOwnProperty(Z))B.ele("key").txt(Z),LN0(A[Z],B)}else if(G=="Number")Q=A%1===0?"integer":"real",B.ele(Q).txt(A.toString());else if(G=="BigInt")B.ele("integer").txt(A);else if(G=="Date")B.ele("date").txt(oI8(new Date(A)));else if(G=="Boolean")B.ele(A?"true":"false");else if(G=="String")B.ele("string").txt(A);else if(G=="ArrayBuffer")B.ele("data").raw(kSB.fromByteArray(A));else if(A&&A.buffer&&ySB(A.buffer)=="ArrayBuffer")B.ele("data").raw(kSB.fromByteArray(new Uint8Array(A.buffer),B));else if(G==="Null")B.ele("null").txt("")}});
var bSB=E((MN0)=>{var xSB=VPB();Object.keys(xSB).forEach(function(A){MN0[A]=xSB[A]});var vSB=_SB();Object.keys(vSB).forEach(function(A){MN0[A]=vSB[A]})});
var cK=E((XSB,VSB)=>{(function(){var A,B,Q,D,Z,G,F,I,Y,W,J,X,V,C,K,H,z,$,L={}.hasOwnProperty,N=[].splice;({isObject:$,isFunction:z,isEmpty:H,getValue:K}=nM()),I=null,Q=null,D=null,Z=null,G=null,V=null,C=null,X=null,F=null,B=null,J=null,Y=null,A=null,VSB.exports=W=function(){class O{constructor(R){if(this.parent=R,this.parent)this.options=this.parent.options,this.stringify=this.parent.stringify;if(this.value=null,this.children=[],this.baseURI=null,!I)I=Cb1(),Q=Kb1(),D=Hb1(),Z=zb1(),G=qb1(),V=Nb1(),C=Lb1(),X=Mb1(),F=wN0(),B=IG(),J=ISB(),Y=Vb1(),A=JSB()}setParent(R){var T,j,f,k,c;if(this.parent=R,R)this.options=R.options,this.stringify=R.stringify;k=this.children,c=[];for(j=0,f=k.length;j<f;j++)T=k[j],c.push(T.setParent(this));return c}element(R,T,j){var f,k,c,h,n,a,x,e,W1;if(a=null,T===null&&j==null)[T,j]=[{},null];if(T==null)T={};if(T=K(T),!$(T))[j,T]=[T,j];if(R!=null)R=K(R);if(Array.isArray(R))for(c=0,x=R.length;c<x;c++)k=R[c],a=this.element(k);else if(z(R))a=this.element(R.apply());else if($(R))for(n in R){if(!L.call(R,n))continue;if(W1=R[n],z(W1))W1=W1.apply();if(!this.options.ignoreDecorators&&this.stringify.convertAttKey&&n.indexOf(this.stringify.convertAttKey)===0)a=this.attribute(n.substr(this.stringify.convertAttKey.length),W1);else if(!this.options.separateArrayItems&&Array.isArray(W1)&&H(W1))a=this.dummy();else if($(W1)&&H(W1))a=this.element(n);else if(!this.options.keepNullNodes&&W1==null)a=this.dummy();else if(!this.options.separateArrayItems&&Array.isArray(W1))for(h=0,e=W1.length;h<e;h++)k=W1[h],f={},f[n]=k,a=this.element(f);else if($(W1))if(!this.options.ignoreDecorators&&this.stringify.convertTextKey&&n.indexOf(this.stringify.convertTextKey)===0)a=this.element(W1);else a=this.element(n),a.element(W1);else a=this.element(n,W1)}else if(!this.options.keepNullNodes&&j===null)a=this.dummy();else if(!this.options.ignoreDecorators&&this.stringify.convertTextKey&&R.indexOf(this.stringify.convertTextKey)===0)a=this.text(j);else if(!this.options.ignoreDecorators&&this.stringify.convertCDataKey&&R.indexOf(this.stringify.convertCDataKey)===0)a=this.cdata(j);else if(!this.options.ignoreDecorators&&this.stringify.convertCommentKey&&R.indexOf(this.stringify.convertCommentKey)===0)a=this.comment(j);else if(!this.options.ignoreDecorators&&this.stringify.convertRawKey&&R.indexOf(this.stringify.convertRawKey)===0)a=this.raw(j);else if(!this.options.ignoreDecorators&&this.stringify.convertPIKey&&R.indexOf(this.stringify.convertPIKey)===0)a=this.instruction(R.substr(this.stringify.convertPIKey.length),j);else a=this.node(R,T,j);if(a==null)throw new Error("Could not create any elements with: "+R+". "+this.debugInfo());return a}insertBefore(R,T,j){var f,k,c,h,n;if(R!=null?R.type:void 0){if(c=R,h=T,c.setParent(this),h)k=children.indexOf(h),n=children.splice(k),children.push(c),Array.prototype.push.apply(children,n);else children.push(c);return c}else{if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(R));return k=this.parent.children.indexOf(this),n=this.parent.children.splice(k),f=this.parent.element(R,T,j),Array.prototype.push.apply(this.parent.children,n),f}}insertAfter(R,T,j){var f,k,c;if(this.isRoot)throw new Error("Cannot insert elements at root level. "+this.debugInfo(R));return k=this.parent.children.indexOf(this),c=this.parent.children.splice(k+1),f=this.parent.element(R,T,j),Array.prototype.push.apply(this.parent.children,c),f}remove(){var R,T;if(this.isRoot)throw new Error("Cannot remove the root element. "+this.debugInfo());return R=this.parent.children.indexOf(this),N.apply(this.parent.children,[R,R-R+1].concat(T=[])),this.parent}node(R,T,j){var f;if(R!=null)R=K(R);if(T||(T={}),T=K(T),!$(T))[j,T]=[T,j];if(f=new I(this,R,T),j!=null)f.text(j);return this.children.push(f),f}text(R){var T;if($(R))this.element(R);return T=new C(this,R),this.children.push(T),this}cdata(R){var T=new Q(this,R);return this.children.push(T),this}comment(R){var T=new D(this,R);return this.children.push(T),this}commentBefore(R){var T,j,f;return j=this.parent.children.indexOf(this),f=this.parent.children.splice(j),T=this.parent.comment(R),Array.prototype.push.apply(this.parent.children,f),this}commentAfter(R){var T,j,f;return j=this.parent.children.indexOf(this),f=this.parent.children.splice(j+1),T=this.parent.comment(R),Array.prototype.push.apply(this.parent.children,f),this}raw(R){var T=new V(this,R);return this.children.push(T),this}dummy(){var R=new F(this);return R}instruction(R,T){var j,f,k,c,h;if(R!=null)R=K(R);if(T!=null)T=K(T);if(Array.isArray(R))for(c=0,h=R.length;c<h;c++)j=R[c],this.instruction(j);else if($(R))for(j in R){if(!L.call(R,j))continue;f=R[j],this.instruction(j,f)}else{if(z(T))T=T.apply();k=new X(this,R,T),this.children.push(k)}return this}instructionBefore(R,T){var j,f,k;return f=this.parent.children.indexOf(this),k=this.parent.children.splice(f),j=this.parent.instruction(R,T),Array.prototype.push.apply(this.parent.children,k),this}instructionAfter(R,T){var j,f,k;return f=this.parent.children.indexOf(this),k=this.parent.children.splice(f+1),j=this.parent.instruction(R,T),Array.prototype.push.apply(this.parent.children,k),this}declaration(R,T,j){var f,k;if(f=this.document(),k=new Z(f,R,T,j),f.children.length===0)f.children.unshift(k);else if(f.children[0].type===B.Declaration)f.children[0]=k;else f.children.unshift(k);return f.root()||f}dtd(R,T){var j,f,k,c,h,n,a,x,e,W1;f=this.document(),k=new G(f,R,T),e=f.children;for(c=h=0,a=e.length;h<a;c=++h)if(j=e[c],j.type===B.DocType)return f.children[c]=k,k;W1=f.children;for(c=n=0,x=W1.length;n<x;c=++n)if(j=W1[c],j.isRoot)return f.children.splice(c,0,k),k;return f.children.push(k),k}up(){if(this.isRoot)throw new Error("The root node has no parent. Use doc() if you need to get the document object.");return this.parent}root(){var R=this;while(R)if(R.type===B.Document)return R.rootObject;else if(R.isRoot)return R;else R=R.parent}document(){var R=this;while(R)if(R.type===B.Document)return R;else R=R.parent}end(R){return this.document().end(R)}prev(){var R=this.parent.children.indexOf(this);if(R<1)throw new Error("Already at the first node. "+this.debugInfo());return this.parent.children[R-1]}next(){var R=this.parent.children.indexOf(this);if(R===-1||R===this.parent.children.length-1)throw new Error("Already at the last node. "+this.debugInfo());return this.parent.children[R+1]}importDocument(R){var T,j,f,k,c;if(j=R.root().clone(),j.parent=this,j.isRoot=!1,this.children.push(j),this.type===B.Document){if(j.isRoot=!0,j.documentObject=this,this.rootObject=j,this.children){c=this.children;for(f=0,k=c.length;f<k;f++)if(T=c[f],T.type===B.DocType){T.name=j.name;break}}}return this}debugInfo(R){var T,j;if(R=R||this.name,R==null&&!((T=this.parent)!=null?T.name:void 0))return"";else if(R==null)return"parent: <"+this.parent.name+">";else if(!((j=this.parent)!=null?j.name:void 0))return"node: <"+R+">";else return"node: <"+R+">, parent: <"+this.parent.name+">"}ele(R,T,j){return this.element(R,T,j)}nod(R,T,j){return this.node(R,T,j)}txt(R){return this.text(R)}dat(R){return this.cdata(R)}com(R){return this.comment(R)}ins(R,T){return this.instruction(R,T)}doc(){return this.document()}dec(R,T,j){return this.declaration(R,T,j)}e(R,T,j){return this.element(R,T,j)}n(R,T,j){return this.node(R,T,j)}t(R){return this.text(R)}d(R){return this.cdata(R)}c(R){return this.comment(R)}r(R){return this.raw(R)}i(R,T){return this.instruction(R,T)}u(){return this.up()}importXMLBuilder(R){return this.importDocument(R)}attribute(R,T){throw new Error("attribute() applies to element nodes only.")}att(R,T){return this.attribute(R,T)}a(R,T){return this.attribute(R,T)}removeAttribute(R){throw new Error("attribute() applies to element nodes only.")}replaceChild(R,T){throw new Error("This DOM method is not implemented."+this.debugInfo())}removeChild(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}appendChild(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}hasChildNodes(){return this.children.length!==0}cloneNode(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}normalize(){throw new Error("This DOM method is not implemented."+this.debugInfo())}isSupported(R,T){return!0}hasAttributes(){return this.attribs.length!==0}compareDocumentPosition(R){var T,j;if(T=this,T===R)return 0;else if(this.document()!==R.document()){if(j=A.Disconnected|A.ImplementationSpecific,Math.random()<0.5)j|=A.Preceding;else j|=A.Following;return j}else if(T.isAncestor(R))return A.Contains|A.Preceding;else if(T.isDescendant(R))return A.Contains|A.Following;else if(T.isPreceding(R))return A.Preceding;else return A.Following}isSameNode(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}lookupPrefix(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}isDefaultNamespace(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}lookupNamespaceURI(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}isEqualNode(R){var T,j,f;if(R.nodeType!==this.nodeType)return!1;if(R.children.length!==this.children.length)return!1;for(T=j=0,f=this.children.length-1;0<=f?j<=f:j>=f;T=0<=f?++j:--j)if(!this.children[T].isEqualNode(R.children[T]))return!1;return!0}getFeature(R,T){throw new Error("This DOM method is not implemented."+this.debugInfo())}setUserData(R,T,j){throw new Error("This DOM method is not implemented."+this.debugInfo())}getUserData(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}contains(R){if(!R)return!1;return R===this||this.isDescendant(R)}isDescendant(R){var T,j,f,k,c;c=this.children;for(f=0,k=c.length;f<k;f++){if(T=c[f],R===T)return!0;if(j=T.isDescendant(R),j)return!0}return!1}isAncestor(R){return R.isDescendant(this)}isPreceding(R){var T,j;if(T=this.treePosition(R),j=this.treePosition(this),T===-1||j===-1)return!1;else return T<j}isFollowing(R){var T,j;if(T=this.treePosition(R),j=this.treePosition(this),T===-1||j===-1)return!1;else return T>j}treePosition(R){var T,j;if(j=0,T=!1,this.foreachTreeNode(this.document(),function(f){if(j++,!T&&f===R)return T=!0}),T)return j;else return-1}foreachTreeNode(R,T){var j,f,k,c,h;R||(R=this.document()),c=R.children;for(f=0,k=c.length;f<k;f++)if(j=c[f],h=T(j))return h;else if(h=this.foreachTreeNode(j,T),h)return h}}return Object.defineProperty(O.prototype,"nodeName",{get:function(){return this.name}}),Object.defineProperty(O.prototype,"nodeType",{get:function(){return this.type}}),Object.defineProperty(O.prototype,"nodeValue",{get:function(){return this.value}}),Object.defineProperty(O.prototype,"parentNode",{get:function(){return this.parent}}),Object.defineProperty(O.prototype,"childNodes",{get:function(){if(!this.childNodeList||!this.childNodeList.nodes)this.childNodeList=new J(this.children);return this.childNodeList}}),Object.defineProperty(O.prototype,"firstChild",{get:function(){return this.children[0]||null}}),Object.defineProperty(O.prototype,"lastChild",{get:function(){return this.children[this.children.length-1]||null}}),Object.defineProperty(O.prototype,"previousSibling",{get:function(){var R=this.parent.children.indexOf(this);return this.parent.children[R-1]||null}}),Object.defineProperty(O.prototype,"nextSibling",{get:function(){var R=this.parent.children.indexOf(this);return this.parent.children[R+1]||null}}),Object.defineProperty(O.prototype,"ownerDocument",{get:function(){return this.document()||null}}),Object.defineProperty(O.prototype,"textContent",{get:function(){var R,T,j,f,k;if(this.nodeType===B.Element||this.nodeType===B.DocumentFragment){k="",f=this.children;for(T=0,j=f.length;T<j;T++)if(R=f[T],R.textContent)k+=R.textContent;return k}else return null},set:function(R){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),O}.call(this)}).call(XSB)});
var iG1=E((_PB,xPB)=>{(function(){var A,B;B=cK(),xPB.exports=A=function(){class Q extends B{constructor(D){super(D);this.value=""}clone(){return Object.create(this)}substringData(D,Z){throw new Error("This DOM method is not implemented."+this.debugInfo())}appendData(D){throw new Error("This DOM method is not implemented."+this.debugInfo())}insertData(D,Z){throw new Error("This DOM method is not implemented."+this.debugInfo())}deleteData(D,Z){throw new Error("This DOM method is not implemented."+this.debugInfo())}replaceData(D,Z,G){throw new Error("This DOM method is not implemented."+this.debugInfo())}isEqualNode(D){if(!super.isEqualNode(D))return!1;if(D.data!==this.data)return!1;return!0}}return Object.defineProperty(Q.prototype,"data",{get:function(){return this.value},set:function(D){return this.value=D||""}}),Object.defineProperty(Q.prototype,"length",{get:function(){return this.value.length}}),Object.defineProperty(Q.prototype,"textContent",{get:function(){return this.value},set:function(D){return this.value=D||""}}),Q}.call(this)}).call(_PB)});
var jSB=E((SSB,vv)=>{(function(){var A,B,Q,D,Z,G,F,I,Y;({assign:I,isFunction:Y}=nM()),Q=EN0(),D=NN0(),Z=RSB(),F=Rb1(),G=PSB(),A=IG(),B=nG1(),SSB.create=function(W,J,X,V){var C,K;if(W==null)throw new Error("Root element needs a name.");if(V=I({},J,X,V),C=new D(V),K=C.element(W),!V.headless){if(C.declaration(V),V.pubID!=null||V.sysID!=null)C.dtd(V)}return K},SSB.begin=function(W,J,X){if(Y(W))[J,X]=[W,J],W={};if(J)return new Z(W,J,X);else return new D(W)},SSB.stringWriter=function(W){return new F(W)},SSB.streamWriter=function(W,J){return new G(W,J)},SSB.implementation=new Q,SSB.nodeType=A,SSB.writerState=B}).call(SSB)});
var nG1=E((HSB,zSB)=>{(function(){zSB.exports={None:0,OpenTag:1,InsideTag:2,CloseTag:3}}).call(HSB)});
var nM=E((CPB,xv)=>{(function(){var A,B,Q,D,Z,G,F,I={}.hasOwnProperty;A=function(Y,...W){var J,X,V,C;if(Z(Object.assign))Object.assign.apply(null,arguments);else for(J=0,V=W.length;J<V;J++)if(C=W[J],C!=null)for(X in C){if(!I.call(C,X))continue;Y[X]=C[X]}return Y},Z=function(Y){return!!Y&&Object.prototype.toString.call(Y)==="[object Function]"},G=function(Y){var W;return!!Y&&((W=typeof Y)==="function"||W==="object")},Q=function(Y){if(Z(Array.isArray))return Array.isArray(Y);else return Object.prototype.toString.call(Y)==="[object Array]"},D=function(Y){var W;if(Q(Y))return!Y.length;else{for(W in Y){if(!I.call(Y,W))continue;return!1}return!0}},F=function(Y){var W,J;return G(Y)&&(J=Object.getPrototypeOf(Y))&&(W=J.constructor)&&typeof W==="function"&&W instanceof W&&Function.prototype.toString.call(W)===Function.prototype.toString.call(Object)},B=function(Y){if(Z(Y.valueOf))return Y.valueOf();else return Y},CPB.assign=A,CPB.isFunction=Z,CPB.isObject=G,CPB.isArray=Q,CPB.isEmpty=D,CPB.isPlainObject=F,CPB.getValue=B}).call(CPB)});
var nTB=E((UI8)=>{var pTB=vG1().freeze;UI8.XML_ENTITIES=pTB({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'});UI8.HTML_ENTITIES=pTB({Aacute:"Á",aacute:"á",Abreve:"Ă",abreve:"ă",ac:"∾",acd:"∿",acE:"∾̳",Acirc:"Â",acirc:"â",acute:"´",Acy:"А",acy:"а",AElig:"Æ",aelig:"æ",af:"⁡",Afr:"\uD835\uDD04",afr:"\uD835\uDD1E",Agrave:"À",agrave:"à",alefsym:"ℵ",aleph:"ℵ",Alpha:"Α",alpha:"α",Amacr:"Ā",amacr:"ā",amalg:"⨿",AMP:"&",amp:"&",And:"⩓",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"Å",angzarr:"⍼",Aogon:"Ą",aogon:"ą",Aopf:"\uD835\uDD38",aopf:"\uD835\uDD52",ap:"≈",apacir:"⩯",apE:"⩰",ape:"≊",apid:"≋",apos:"'",ApplyFunction:"⁡",approx:"≈",approxeq:"≊",Aring:"Å",aring:"å",Ascr:"\uD835\uDC9C",ascr:"\uD835\uDCB6",Assign:"≔",ast:"*",asymp:"≈",asympeq:"≍",Atilde:"Ã",atilde:"ã",Auml:"Ä",auml:"ä",awconint:"∳",awint:"⨑",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",Backslash:"∖",Barv:"⫧",barvee:"⊽",Barwed:"⌆",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",Bcy:"Б",bcy:"б",bdquo:"„",becaus:"∵",Because:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",Bernoullis:"ℬ",Beta:"Β",beta:"β",beth:"ℶ",between:"≬",Bfr:"\uD835\uDD05",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bNot:"⫭",bnot:"⌐",Bopf:"\uD835\uDD39",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxbox:"⧉",boxDL:"╗",boxDl:"╖",boxdL:"╕",boxdl:"┐",boxDR:"╔",boxDr:"╓",boxdR:"╒",boxdr:"┌",boxH:"═",boxh:"─",boxHD:"╦",boxHd:"╤",boxhD:"╥",boxhd:"┬",boxHU:"╩",boxHu:"╧",boxhU:"╨",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxUL:"╝",boxUl:"╜",boxuL:"╛",boxul:"┘",boxUR:"╚",boxUr:"╙",boxuR:"╘",boxur:"└",boxV:"║",boxv:"│",boxVH:"╬",boxVh:"╫",boxvH:"╪",boxvh:"┼",boxVL:"╣",boxVl:"╢",boxvL:"╡",boxvl:"┤",boxVR:"╠",boxVr:"╟",boxvR:"╞",boxvr:"├",bprime:"‵",Breve:"˘",breve:"˘",brvbar:"¦",Bscr:"ℬ",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",Bumpeq:"≎",bumpeq:"≏",Cacute:"Ć",cacute:"ć",Cap:"⋒",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",CapitalDifferentialD:"ⅅ",caps:"∩︀",caret:"⁁",caron:"ˇ",Cayleys:"ℭ",ccaps:"⩍",Ccaron:"Č",ccaron:"č",Ccedil:"Ç",ccedil:"ç",Ccirc:"Ĉ",ccirc:"ĉ",Cconint:"∰",ccups:"⩌",ccupssm:"⩐",Cdot:"Ċ",cdot:"ċ",cedil:"¸",Cedilla:"¸",cemptyv:"⦲",cent:"¢",CenterDot:"·",centerdot:"·",Cfr:"ℭ",cfr:"\uD835\uDD20",CHcy:"Ч",chcy:"ч",check:"✓",checkmark:"✓",Chi:"Χ",chi:"χ",cir:"○",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",CircleDot:"⊙",circledR:"®",circledS:"Ⓢ",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",cirE:"⧃",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",clubs:"♣",clubsuit:"♣",Colon:"∷",colon:":",Colone:"⩴",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",Congruent:"≡",Conint:"∯",conint:"∮",ContourIntegral:"∮",Copf:"ℂ",copf:"\uD835\uDD54",coprod:"∐",Coproduct:"∐",COPY:"©",copy:"©",copysr:"℗",CounterClockwiseContourIntegral:"∳",crarr:"↵",Cross:"⨯",cross:"✗",Cscr:"\uD835\uDC9E",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",Cup:"⋓",cup:"∪",cupbrcap:"⩈",CupCap:"≍",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"¤",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",Dagger:"‡",dagger:"†",daleth:"ℸ",Darr:"↡",dArr:"⇓",darr:"↓",dash:"‐",Dashv:"⫤",dashv:"⊣",dbkarow:"⤏",dblac:"˝",Dcaron:"Ď",dcaron:"ď",Dcy:"Д",dcy:"д",DD:"ⅅ",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",DDotrahd:"⤑",ddotseq:"⩷",deg:"°",Del:"∇",Delta:"Δ",delta:"δ",demptyv:"⦱",dfisht:"⥿",Dfr:"\uD835\uDD07",dfr:"\uD835\uDD21",dHar:"⥥",dharl:"⇃",dharr:"⇂",DiacriticalAcute:"´",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",diam:"⋄",Diamond:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"¨",DifferentialD:"ⅆ",digamma:"ϝ",disin:"⋲",div:"÷",divide:"÷",divideontimes:"⋇",divonx:"⋇",DJcy:"Ђ",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",Dopf:"\uD835\uDD3B",dopf:"\uD835\uDD55",Dot:"¨",dot:"˙",DotDot:"⃜",doteq:"≐",doteqdot:"≑",DotEqual:"≐",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",DoubleContourIntegral:"∯",DoubleDot:"¨",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",Downarrow:"⇓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",Dscr:"\uD835\uDC9F",dscr:"\uD835\uDCB9",DScy:"Ѕ",dscy:"ѕ",dsol:"⧶",Dstrok:"Đ",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",DZcy:"Џ",dzcy:"џ",dzigrarr:"⟿",Eacute:"É",eacute:"é",easter:"⩮",Ecaron:"Ě",ecaron:"ě",ecir:"≖",Ecirc:"Ê",ecirc:"ê",ecolon:"≕",Ecy:"Э",ecy:"э",eDDot:"⩷",Edot:"Ė",eDot:"≑",edot:"ė",ee:"ⅇ",efDot:"≒",Efr:"\uD835\uDD08",efr:"\uD835\uDD22",eg:"⪚",Egrave:"È",egrave:"è",egs:"⪖",egsdot:"⪘",el:"⪙",Element:"∈",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",Emacr:"Ē",emacr:"ē",empty:"∅",emptyset:"∅",EmptySmallSquare:"◻",emptyv:"∅",EmptyVerySmallSquare:"▫",emsp:" ",emsp13:" ",emsp14:" ",ENG:"Ŋ",eng:"ŋ",ensp:" ",Eogon:"Ę",eogon:"ę",Eopf:"\uD835\uDD3C",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",Epsilon:"Ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",Equal:"⩵",equals:"=",EqualTilde:"≂",equest:"≟",Equilibrium:"⇌",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erarr:"⥱",erDot:"≓",Escr:"ℰ",escr:"ℯ",esdot:"≐",Esim:"⩳",esim:"≂",Eta:"Η",eta:"η",ETH:"Ð",eth:"ð",Euml:"Ë",euml:"ë",euro:"€",excl:"!",exist:"∃",Exists:"∃",expectation:"ℰ",ExponentialE:"ⅇ",exponentiale:"ⅇ",fallingdotseq:"≒",Fcy:"Ф",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",Ffr:"\uD835\uDD09",ffr:"\uD835\uDD23",filig:"ﬁ",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",Fopf:"\uD835\uDD3D",fopf:"\uD835\uDD57",ForAll:"∀",forall:"∀",fork:"⋔",forkv:"⫙",Fouriertrf:"ℱ",fpartint:"⨍",frac12:"½",frac13:"⅓",frac14:"¼",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"¾",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",Fscr:"ℱ",fscr:"\uD835\uDCBB",gacute:"ǵ",Gamma:"Γ",gamma:"γ",Gammad:"Ϝ",gammad:"ϝ",gap:"⪆",Gbreve:"Ğ",gbreve:"ğ",Gcedil:"Ģ",Gcirc:"Ĝ",gcirc:"ĝ",Gcy:"Г",gcy:"г",Gdot:"Ġ",gdot:"ġ",gE:"≧",ge:"≥",gEl:"⪌",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",Gfr:"\uD835\uDD0A",gfr:"\uD835\uDD24",Gg:"⋙",gg:"≫",ggg:"⋙",gimel:"ℷ",GJcy:"Ѓ",gjcy:"ѓ",gl:"≷",gla:"⪥",glE:"⪒",glj:"⪤",gnap:"⪊",gnapprox:"⪊",gnE:"≩",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",Gopf:"\uD835\uDD3E",gopf:"\uD835\uDD58",grave:"`",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",Gt:"≫",GT:">",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",Hacek:"ˇ",hairsp:" ",half:"½",hamilt:"ℋ",HARDcy:"Ъ",hardcy:"ъ",hArr:"⇔",harr:"↔",harrcir:"⥈",harrw:"↭",Hat:"^",hbar:"ℏ",Hcirc:"Ĥ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",Hfr:"ℌ",hfr:"\uD835\uDD25",HilbertSpace:"ℋ",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",Hopf:"ℍ",hopf:"\uD835\uDD59",horbar:"―",HorizontalLine:"─",Hscr:"ℋ",hscr:"\uD835\uDCBD",hslash:"ℏ",Hstrok:"Ħ",hstrok:"ħ",HumpDownHump:"≎",HumpEqual:"≏",hybull:"⁃",hyphen:"‐",Iacute:"Í",iacute:"í",ic:"⁣",Icirc:"Î",icirc:"î",Icy:"И",icy:"и",Idot:"İ",IEcy:"Е",iecy:"е",iexcl:"¡",iff:"⇔",Ifr:"ℑ",ifr:"\uD835\uDD26",Igrave:"Ì",igrave:"ì",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",IJlig:"Ĳ",ijlig:"ĳ",Im:"ℑ",Imacr:"Ī",imacr:"ī",image:"ℑ",ImaginaryI:"ⅈ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",Implies:"⇒",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",Int:"∬",int:"∫",intcal:"⊺",integers:"ℤ",Integral:"∫",intercal:"⊺",Intersection:"⋂",intlarhk:"⨗",intprod:"⨼",InvisibleComma:"⁣",InvisibleTimes:"⁢",IOcy:"Ё",iocy:"ё",Iogon:"Į",iogon:"į",Iopf:"\uD835\uDD40",iopf:"\uD835\uDD5A",Iota:"Ι",iota:"ι",iprod:"⨼",iquest:"¿",Iscr:"ℐ",iscr:"\uD835\uDCBE",isin:"∈",isindot:"⋵",isinE:"⋹",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",Itilde:"Ĩ",itilde:"ĩ",Iukcy:"І",iukcy:"і",Iuml:"Ï",iuml:"ï",Jcirc:"Ĵ",jcirc:"ĵ",Jcy:"Й",jcy:"й",Jfr:"\uD835\uDD0D",jfr:"\uD835\uDD27",jmath:"ȷ",Jopf:"\uD835\uDD41",jopf:"\uD835\uDD5B",Jscr:"\uD835\uDCA5",jscr:"\uD835\uDCBF",Jsercy:"Ј",jsercy:"ј",Jukcy:"Є",jukcy:"є",Kappa:"Κ",kappa:"κ",kappav:"ϰ",Kcedil:"Ķ",kcedil:"ķ",Kcy:"К",kcy:"к",Kfr:"\uD835\uDD0E",kfr:"\uD835\uDD28",kgreen:"ĸ",KHcy:"Х",khcy:"х",KJcy:"Ќ",kjcy:"ќ",Kopf:"\uD835\uDD42",kopf:"\uD835\uDD5C",Kscr:"\uD835\uDCA6",kscr:"\uD835\uDCC0",lAarr:"⇚",Lacute:"Ĺ",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",Lambda:"Λ",lambda:"λ",Lang:"⟪",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",Laplacetrf:"ℒ",laquo:"«",Larr:"↞",lArr:"⇐",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",lAtail:"⤛",latail:"⤙",late:"⪭",lates:"⪭︀",lBarr:"⤎",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",Lcaron:"Ľ",lcaron:"ľ",Lcedil:"Ļ",lcedil:"ļ",lceil:"⌈",lcub:"{",Lcy:"Л",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",lE:"≦",le:"≤",LeftAngleBracket:"⟨",LeftArrow:"←",Leftarrow:"⇐",leftarrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",leftarrowtail:"↢",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",LeftRightArrow:"↔",Leftrightarrow:"⇔",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",leftthreetimes:"⋋",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",lEg:"⪋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",lessgtr:"≶",LessLess:"⪡",lesssim:"≲",LessSlantEqual:"⩽",LessTilde:"≲",lfisht:"⥼",lfloor:"⌊",Lfr:"\uD835\uDD0F",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lHar:"⥢",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",LJcy:"Љ",ljcy:"љ",Ll:"⋘",ll:"≪",llarr:"⇇",llcorner:"⌞",Lleftarrow:"⇚",llhard:"⥫",lltri:"◺",Lmidot:"Ŀ",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnap:"⪉",lnapprox:"⪉",lnE:"≨",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",LongLeftArrow:"⟵",Longleftarrow:"⟸",longleftarrow:"⟵",LongLeftRightArrow:"⟷",Longleftrightarrow:"⟺",longleftrightarrow:"⟷",longmapsto:"⟼",LongRightArrow:"⟶",Longrightarrow:"⟹",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",Lopf:"\uD835\uDD43",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",LowerLeftArrow:"↙",LowerRightArrow:"↘",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",Lscr:"ℒ",lscr:"\uD835\uDCC1",Lsh:"↰",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",Lstrok:"Ł",lstrok:"ł",Lt:"≪",LT:"<",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltri:"◃",ltrie:"⊴",ltrif:"◂",ltrPar:"⦖",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",macr:"¯",male:"♂",malt:"✠",maltese:"✠",Map:"⤅",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",Mcy:"М",mcy:"м",mdash:"—",mDDot:"∺",measuredangle:"∡",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",mfr:"\uD835\uDD2A",mho:"℧",micro:"µ",mid:"∣",midast:"*",midcir:"⫰",middot:"·",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",MinusPlus:"∓",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",Mopf:"\uD835\uDD44",mopf:"\uD835\uDD5E",mp:"∓",Mscr:"ℳ",mscr:"\uD835\uDCC2",mstpos:"∾",Mu:"Μ",mu:"μ",multimap:"⊸",mumap:"⊸",nabla:"∇",Nacute:"Ń",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:" ",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",Ncaron:"Ň",ncaron:"ň",Ncedil:"Ņ",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",Ncy:"Н",ncy:"н",ndash:"–",ne:"≠",nearhk:"⤤",neArr:"⇗",nearr:"↗",nearrow:"↗",nedot:"≐̸",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",nequiv:"≢",nesear:"⤨",nesim:"≂̸",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:`
`,nexist:"∄",nexists:"∄",Nfr:"\uD835\uDD11",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",nGg:"⋙̸",ngsim:"≵",nGt:"≫⃒",ngt:"≯",ngtr:"≯",nGtv:"≫̸",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",NJcy:"Њ",njcy:"њ",nlArr:"⇍",nlarr:"↚",nldr:"‥",nlE:"≦̸",nle:"≰",nLeftarrow:"⇍",nleftarrow:"↚",nLeftrightarrow:"⇎",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nLl:"⋘̸",nlsim:"≴",nLt:"≪⃒",nlt:"≮",nltri:"⋪",nltrie:"⋬",nLtv:"≪̸",nmid:"∤",NoBreak:"⁠",NonBreakingSpace:" ",Nopf:"ℕ",nopf:"\uD835\uDD5F",Not:"⫬",not:"¬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",notin:"∉",notindot:"⋵̸",notinE:"⋹̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nRightarrow:"⇏",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",Nscr:"\uD835\uDCA9",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",Ntilde:"Ñ",ntilde:"ñ",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",Nu:"Ν",nu:"ν",num:"#",numero:"№",numsp:" ",nvap:"≍⃒",nVDash:"⊯",nVdash:"⊮",nvDash:"⊭",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvHarr:"⤄",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwarhk:"⤣",nwArr:"⇖",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",Oacute:"Ó",oacute:"ó",oast:"⊛",ocir:"⊚",Ocirc:"Ô",ocirc:"ô",Ocy:"О",ocy:"о",odash:"⊝",Odblac:"Ő",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",OElig:"Œ",oelig:"œ",ofcir:"⦿",Ofr:"\uD835\uDD12",ofr:"\uD835\uDD2C",ogon:"˛",Ograve:"Ò",ograve:"ò",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",Omacr:"Ō",omacr:"ō",Omega:"Ω",omega:"ω",Omicron:"Ο",omicron:"ο",omid:"⦶",ominus:"⊖",Oopf:"\uD835\uDD46",oopf:"\uD835\uDD60",opar:"⦷",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",operp:"⦹",oplus:"⊕",Or:"⩔",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"ª",ordm:"º",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oS:"Ⓢ",Oscr:"\uD835\uDCAA",oscr:"ℴ",Oslash:"Ø",oslash:"ø",osol:"⊘",Otilde:"Õ",otilde:"õ",Otimes:"⨷",otimes:"⊗",otimesas:"⨶",Ouml:"Ö",ouml:"ö",ovbar:"⌽",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",par:"∥",para:"¶",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",PartialD:"∂",Pcy:"П",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",Pfr:"\uD835\uDD13",pfr:"\uD835\uDD2D",Phi:"Φ",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",Pi:"Π",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",PlusMinus:"±",plusmn:"±",plussim:"⨦",plustwo:"⨧",pm:"±",Poincareplane:"ℌ",pointint:"⨕",Popf:"ℙ",popf:"\uD835\uDD61",pound:"£",Pr:"⪻",pr:"≺",prap:"⪷",prcue:"≼",prE:"⪳",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",Prime:"″",prime:"′",primes:"ℙ",prnap:"⪹",prnE:"⪵",prnsim:"⋨",prod:"∏",Product:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",Proportion:"∷",Proportional:"∝",propto:"∝",prsim:"≾",prurel:"⊰",Pscr:"\uD835\uDCAB",pscr:"\uD835\uDCC5",Psi:"Ψ",psi:"ψ",puncsp:" ",Qfr:"\uD835\uDD14",qfr:"\uD835\uDD2E",qint:"⨌",Qopf:"ℚ",qopf:"\uD835\uDD62",qprime:"⁗",Qscr:"\uD835\uDCAC",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",QUOT:'"',quot:'"',rAarr:"⇛",race:"∽̱",Racute:"Ŕ",racute:"ŕ",radic:"√",raemptyv:"⦳",Rang:"⟫",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"»",Rarr:"↠",rArr:"⇒",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",Rarrtl:"⤖",rarrtl:"↣",rarrw:"↝",rAtail:"⤜",ratail:"⤚",ratio:"∶",rationals:"ℚ",RBarr:"⤐",rBarr:"⤏",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",Rcaron:"Ř",rcaron:"ř",Rcedil:"Ŗ",rcedil:"ŗ",rceil:"⌉",rcub:"}",Rcy:"Р",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",Re:"ℜ",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",REG:"®",reg:"®",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",rfisht:"⥽",rfloor:"⌋",Rfr:"ℜ",rfr:"\uD835\uDD2F",rHar:"⥤",rhard:"⇁",rharu:"⇀",rharul:"⥬",Rho:"Ρ",rho:"ρ",rhov:"ϱ",RightAngleBracket:"⟩",RightArrow:"→",Rightarrow:"⇒",rightarrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",rightarrowtail:"↣",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",rightthreetimes:"⋌",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",Ropf:"ℝ",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",RoundImplies:"⥰",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",Rrightarrow:"⇛",rsaquo:"›",Rscr:"ℛ",rscr:"\uD835\uDCC7",Rsh:"↱",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",RuleDelayed:"⧴",ruluhar:"⥨",rx:"℞",Sacute:"Ś",sacute:"ś",sbquo:"‚",Sc:"⪼",sc:"≻",scap:"⪸",Scaron:"Š",scaron:"š",sccue:"≽",scE:"⪴",sce:"⪰",Scedil:"Ş",scedil:"ş",Scirc:"Ŝ",scirc:"ŝ",scnap:"⪺",scnE:"⪶",scnsim:"⋩",scpolint:"⨓",scsim:"≿",Scy:"С",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",searhk:"⤥",seArr:"⇘",searr:"↘",searrow:"↘",sect:"§",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",Sfr:"\uD835\uDD16",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",SHCHcy:"Щ",shchcy:"щ",SHcy:"Ш",shcy:"ш",ShortDownArrow:"↓",ShortLeftArrow:"←",shortmid:"∣",shortparallel:"∥",ShortRightArrow:"→",ShortUpArrow:"↑",shy:"­",Sigma:"Σ",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",SmallCircle:"∘",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",SOFTcy:"Ь",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",Sopf:"\uD835\uDD4A",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",Sqrt:"√",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",Square:"□",square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",squarf:"▪",squf:"▪",srarr:"→",Sscr:"\uD835\uDCAE",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",Star:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"¯",Sub:"⋐",sub:"⊂",subdot:"⪽",subE:"⫅",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",Subset:"⋐",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",SubsetEqual:"⊆",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",SuchThat:"∋",Sum:"∑",sum:"∑",sung:"♪",Sup:"⋑",sup:"⊃",sup1:"¹",sup2:"²",sup3:"³",supdot:"⪾",supdsub:"⫘",supE:"⫆",supe:"⊇",supedot:"⫄",Superset:"⊃",SupersetEqual:"⊇",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",Supset:"⋑",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swarhk:"⤦",swArr:"⇙",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"ß",Tab:"\t",target:"⌖",Tau:"Τ",tau:"τ",tbrk:"⎴",Tcaron:"Ť",tcaron:"ť",Tcedil:"Ţ",tcedil:"ţ",Tcy:"Т",tcy:"т",tdot:"⃛",telrec:"⌕",Tfr:"\uD835\uDD17",tfr:"\uD835\uDD31",there4:"∴",Therefore:"∴",therefore:"∴",Theta:"Θ",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",ThickSpace:"  ",thinsp:" ",ThinSpace:" ",thkap:"≈",thksim:"∼",THORN:"Þ",thorn:"þ",Tilde:"∼",tilde:"˜",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",times:"×",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",Topf:"\uD835\uDD4B",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",TRADE:"™",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",TripleDot:"⃛",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",Tscr:"\uD835\uDCAF",tscr:"\uD835\uDCC9",TScy:"Ц",tscy:"ц",TSHcy:"Ћ",tshcy:"ћ",Tstrok:"Ŧ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",Uacute:"Ú",uacute:"ú",Uarr:"↟",uArr:"⇑",uarr:"↑",Uarrocir:"⥉",Ubrcy:"Ў",ubrcy:"ў",Ubreve:"Ŭ",ubreve:"ŭ",Ucirc:"Û",ucirc:"û",Ucy:"У",ucy:"у",udarr:"⇅",Udblac:"Ű",udblac:"ű",udhar:"⥮",ufisht:"⥾",Ufr:"\uD835\uDD18",ufr:"\uD835\uDD32",Ugrave:"Ù",ugrave:"ù",uHar:"⥣",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",Umacr:"Ū",umacr:"ū",uml:"¨",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",uogon:"ų",Uopf:"\uD835\uDD4C",uopf:"\uD835\uDD66",UpArrow:"↑",Uparrow:"⇑",uparrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",Updownarrow:"⇕",updownarrow:"↕",UpEquilibrium:"⥮",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",upsi:"υ",upsih:"ϒ",Upsilon:"Υ",upsilon:"υ",UpTee:"⊥",UpTeeArrow:"↥",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",Uring:"Ů",uring:"ů",urtri:"◹",Uscr:"\uD835\uDCB0",uscr:"\uD835\uDCCA",utdot:"⋰",Utilde:"Ũ",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",Uuml:"Ü",uuml:"ü",uwangle:"⦧",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",vArr:"⇕",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",Vbar:"⫫",vBar:"⫨",vBarv:"⫩",Vcy:"В",vcy:"в",VDash:"⊫",Vdash:"⊩",vDash:"⊨",vdash:"⊢",Vdashl:"⫦",Vee:"⋁",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",Verbar:"‖",verbar:"|",Vert:"‖",vert:"|",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",Vopf:"\uD835\uDD4D",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",Vscr:"\uD835\uDCB1",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",Vvdash:"⊪",vzigzag:"⦚",Wcirc:"Ŵ",wcirc:"ŵ",wedbar:"⩟",Wedge:"⋀",wedge:"∧",wedgeq:"≙",weierp:"℘",Wfr:"\uD835\uDD1A",wfr:"\uD835\uDD34",Wopf:"\uD835\uDD4E",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",Wscr:"\uD835\uDCB2",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",Xfr:"\uD835\uDD1B",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",Xi:"Ξ",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",Xopf:"\uD835\uDD4F",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",Xscr:"\uD835\uDCB3",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",Yacute:"Ý",yacute:"ý",YAcy:"Я",yacy:"я",Ycirc:"Ŷ",ycirc:"ŷ",Ycy:"Ы",ycy:"ы",yen:"¥",Yfr:"\uD835\uDD1C",yfr:"\uD835\uDD36",YIcy:"Ї",yicy:"ї",Yopf:"\uD835\uDD50",yopf:"\uD835\uDD6A",Yscr:"\uD835\uDCB4",yscr:"\uD835\uDCCE",YUcy:"Ю",yucy:"ю",Yuml:"Ÿ",yuml:"ÿ",Zacute:"Ź",zacute:"ź",Zcaron:"Ž",zcaron:"ž",Zcy:"З",zcy:"з",Zdot:"Ż",zdot:"ż",zeetrf:"ℨ",ZeroWidthSpace:"​",Zeta:"Ζ",zeta:"ζ",Zfr:"ℨ",zfr:"\uD835\uDD37",ZHcy:"Ж",zhcy:"ж",zigrarr:"⇝",Zopf:"ℤ",zopf:"\uD835\uDD6B",Zscr:"\uD835\uDCB5",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"});UI8.entityMap=UI8.HTML_ENTITIES});
var qN0=E((ESB,USB)=>{(function(){var A,B,Q,D,Z,G,F,I,Y,W,J,X,V,C,K,H,z,$={}.hasOwnProperty;({assign:z}=nM()),A=IG(),Y=zb1(),W=qb1(),Q=Kb1(),D=Hb1(),X=Cb1(),C=Nb1(),K=Lb1(),V=Mb1(),J=wN0(),Z=Eb1(),G=wb1(),F=Ub1(),I=$b1(),B=nG1(),USB.exports=H=class L{constructor(N){var O,R,T;N||(N={}),this.options=N,R=N.writer||{};for(O in R){if(!$.call(R,O))continue;T=R[O],this["_"+O]=this[O],this[O]=T}}filterOptions(N){var O,R,T,j,f,k,c,h,n;if(N||(N={}),N=z({},this.options,N),O={writer:this},O.pretty=N.pretty||!1,O.allowEmpty=N.allowEmpty||!1,O.indent=(R=N.indent)!=null?R:"  ",O.newline=(T=N.newline)!=null?T:`
`,O.offset=(j=N.offset)!=null?j:0,O.width=(f=N.width)!=null?f:0,O.dontPrettyTextNodes=(k=(c=N.dontPrettyTextNodes)!=null?c:N.dontprettytextnodes)!=null?k:0,O.spaceBeforeSlash=(h=(n=N.spaceBeforeSlash)!=null?n:N.spacebeforeslash)!=null?h:"",O.spaceBeforeSlash===!0)O.spaceBeforeSlash=" ";return O.suppressPrettyCount=0,O.user={},O.state=B.None,O}indent(N,O,R){var T;if(!O.pretty||O.suppressPrettyCount)return"";else if(O.pretty){if(T=(R||0)+O.offset+1,T>0)return new Array(T).join(O.indent)}return""}endline(N,O,R){if(!O.pretty||O.suppressPrettyCount)return"";else return O.newline}attribute(N,O,R){var T;if(this.openAttribute(N,O,R),O.pretty&&O.width>0)T=N.name+'="'+N.value+'"';else T=" "+N.name+'="'+N.value+'"';return this.closeAttribute(N,O,R),T}cdata(N,O,R){var T;return this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<![CDATA[",O.state=B.InsideTag,T+=N.value,O.state=B.CloseTag,T+="]]>"+this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}comment(N,O,R){var T;return this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<!-- ",O.state=B.InsideTag,T+=N.value,O.state=B.CloseTag,T+=" -->"+this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}declaration(N,O,R){var T;if(this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<?xml",O.state=B.InsideTag,T+=' version="'+N.version+'"',N.encoding!=null)T+=' encoding="'+N.encoding+'"';if(N.standalone!=null)T+=' standalone="'+N.standalone+'"';return O.state=B.CloseTag,T+=O.spaceBeforeSlash+"?>",T+=this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}docType(N,O,R){var T,j,f,k,c;if(R||(R=0),this.openNode(N,O,R),O.state=B.OpenTag,k=this.indent(N,O,R),k+="<!DOCTYPE "+N.root().name,N.pubID&&N.sysID)k+=' PUBLIC "'+N.pubID+'" "'+N.sysID+'"';else if(N.sysID)k+=' SYSTEM "'+N.sysID+'"';if(N.children.length>0){k+=" [",k+=this.endline(N,O,R),O.state=B.InsideTag,c=N.children;for(j=0,f=c.length;j<f;j++)T=c[j],k+=this.writeChildNode(T,O,R+1);O.state=B.CloseTag,k+="]"}return O.state=B.CloseTag,k+=O.spaceBeforeSlash+">",k+=this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),k}element(N,O,R){var T,j,f,k,c,h,n,a,x,e,W1,U1,y1,W0,F0,g1,K1,G1,L1;if(R||(R=0),U1=!1,this.openNode(N,O,R),O.state=B.OpenTag,y1=this.indent(N,O,R)+"<"+N.name,O.pretty&&O.width>0){a=y1.length,F0=N.attribs;for(W1 in F0){if(!$.call(F0,W1))continue;if(T=F0[W1],W0=this.attribute(T,O,R),j=W0.length,a+j>O.width)L1=this.indent(N,O,R+1)+W0,y1+=this.endline(N,O,R)+L1,a=L1.length;else L1=" "+W0,y1+=L1,a+=L1.length}}else{g1=N.attribs;for(W1 in g1){if(!$.call(g1,W1))continue;T=g1[W1],y1+=this.attribute(T,O,R)}}if(k=N.children.length,c=k===0?null:N.children[0],k===0||N.children.every(function(M1){return(M1.type===A.Text||M1.type===A.Raw||M1.type===A.CData)&&M1.value===""}))if(O.allowEmpty)y1+=">",O.state=B.CloseTag,y1+="</"+N.name+">"+this.endline(N,O,R);else O.state=B.CloseTag,y1+=O.spaceBeforeSlash+"/>"+this.endline(N,O,R);else if(O.pretty&&k===1&&(c.type===A.Text||c.type===A.Raw||c.type===A.CData)&&c.value!=null)y1+=">",O.state=B.InsideTag,O.suppressPrettyCount++,U1=!0,y1+=this.writeChildNode(c,O,R+1),O.suppressPrettyCount--,U1=!1,O.state=B.CloseTag,y1+="</"+N.name+">"+this.endline(N,O,R);else{if(O.dontPrettyTextNodes){K1=N.children;for(h=0,x=K1.length;h<x;h++)if(f=K1[h],(f.type===A.Text||f.type===A.Raw||f.type===A.CData)&&f.value!=null){O.suppressPrettyCount++,U1=!0;break}}y1+=">"+this.endline(N,O,R),O.state=B.InsideTag,G1=N.children;for(n=0,e=G1.length;n<e;n++)f=G1[n],y1+=this.writeChildNode(f,O,R+1);if(O.state=B.CloseTag,y1+=this.indent(N,O,R)+"</"+N.name+">",U1)O.suppressPrettyCount--;y1+=this.endline(N,O,R),O.state=B.None}return this.closeNode(N,O,R),y1}writeChildNode(N,O,R){switch(N.type){case A.CData:return this.cdata(N,O,R);case A.Comment:return this.comment(N,O,R);case A.Element:return this.element(N,O,R);case A.Raw:return this.raw(N,O,R);case A.Text:return this.text(N,O,R);case A.ProcessingInstruction:return this.processingInstruction(N,O,R);case A.Dummy:return"";case A.Declaration:return this.declaration(N,O,R);case A.DocType:return this.docType(N,O,R);case A.AttributeDeclaration:return this.dtdAttList(N,O,R);case A.ElementDeclaration:return this.dtdElement(N,O,R);case A.EntityDeclaration:return this.dtdEntity(N,O,R);case A.NotationDeclaration:return this.dtdNotation(N,O,R);default:throw new Error("Unknown XML node type: "+N.constructor.name)}}processingInstruction(N,O,R){var T;if(this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<?",O.state=B.InsideTag,T+=N.target,N.value)T+=" "+N.value;return O.state=B.CloseTag,T+=O.spaceBeforeSlash+"?>",T+=this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}raw(N,O,R){var T;return this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R),O.state=B.InsideTag,T+=N.value,O.state=B.CloseTag,T+=this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}text(N,O,R){var T;return this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R),O.state=B.InsideTag,T+=N.value,O.state=B.CloseTag,T+=this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}dtdAttList(N,O,R){var T;if(this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<!ATTLIST",O.state=B.InsideTag,T+=" "+N.elementName+" "+N.attributeName+" "+N.attributeType,N.defaultValueType!=="#DEFAULT")T+=" "+N.defaultValueType;if(N.defaultValue)T+=' "'+N.defaultValue+'"';return O.state=B.CloseTag,T+=O.spaceBeforeSlash+">"+this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}dtdElement(N,O,R){var T;return this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<!ELEMENT",O.state=B.InsideTag,T+=" "+N.name+" "+N.value,O.state=B.CloseTag,T+=O.spaceBeforeSlash+">"+this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}dtdEntity(N,O,R){var T;if(this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<!ENTITY",O.state=B.InsideTag,N.pe)T+=" %";if(T+=" "+N.name,N.value)T+=' "'+N.value+'"';else{if(N.pubID&&N.sysID)T+=' PUBLIC "'+N.pubID+'" "'+N.sysID+'"';else if(N.sysID)T+=' SYSTEM "'+N.sysID+'"';if(N.nData)T+=" NDATA "+N.nData}return O.state=B.CloseTag,T+=O.spaceBeforeSlash+">"+this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}dtdNotation(N,O,R){var T;if(this.openNode(N,O,R),O.state=B.OpenTag,T=this.indent(N,O,R)+"<!NOTATION",O.state=B.InsideTag,T+=" "+N.name,N.pubID&&N.sysID)T+=' PUBLIC "'+N.pubID+'" "'+N.sysID+'"';else if(N.pubID)T+=' PUBLIC "'+N.pubID+'"';else if(N.sysID)T+=' SYSTEM "'+N.sysID+'"';return O.state=B.CloseTag,T+=O.spaceBeforeSlash+">"+this.endline(N,O,R),O.state=B.None,this.closeNode(N,O,R),T}openNode(N,O,R){}closeNode(N,O,R){}openAttribute(N,O,R){}closeAttribute(N,O,R){}}}).call(ESB)});
var qPB=E((wPB,$PB)=>{(function(){var A;$PB.exports=A=function(){class B{constructor(Q){this.arr=Q||[]}item(Q){return this.arr[Q]||null}contains(Q){return this.arr.indexOf(Q)!==-1}}return Object.defineProperty(B.prototype,"length",{get:function(){return this.arr.length}}),B}.call(this)}).call(wPB)});
var qb1=E((sPB,rPB)=>{(function(){var A,B,Q,D,Z,G,F,I,Y;({isObject:Y}=nM()),I=cK(),A=IG(),B=Eb1(),D=Ub1(),Q=wb1(),Z=$b1(),F=Vb1(),rPB.exports=G=function(){class W extends I{constructor(J,X,V){var C,K,H,z;super(J);if(this.type=A.DocType,J.children){z=J.children;for(K=0,H=z.length;K<H;K++)if(C=z[K],C.type===A.Element){this.name=C.name;break}}if(this.documentObject=J,Y(X))({pubID:X,sysID:V}=X);if(V==null)[V,X]=[X,V];if(X!=null)this.pubID=this.stringify.dtdPubID(X);if(V!=null)this.sysID=this.stringify.dtdSysID(V)}element(J,X){var V=new Q(this,J,X);return this.children.push(V),this}attList(J,X,V,C,K){var H=new B(this,J,X,V,C,K);return this.children.push(H),this}entity(J,X){var V=new D(this,!1,J,X);return this.children.push(V),this}pEntity(J,X){var V=new D(this,!0,J,X);return this.children.push(V),this}notation(J,X){var V=new Z(this,J,X);return this.children.push(V),this}toString(J){return this.options.writer.docType(this,this.options.writer.filterOptions(J))}ele(J,X){return this.element(J,X)}att(J,X,V,C,K){return this.attList(J,X,V,C,K)}ent(J,X){return this.entity(J,X)}pent(J,X){return this.pEntity(J,X)}not(J,X){return this.notation(J,X)}up(){return this.root()||this.documentObject}isEqualNode(J){if(!super.isEqualNode(J))return!1;if(J.name!==this.name)return!1;if(J.publicId!==this.publicId)return!1;if(J.systemId!==this.systemId)return!1;return!0}}return Object.defineProperty(W.prototype,"entities",{get:function(){var J,X,V,C,K;C={},K=this.children;for(X=0,V=K.length;X<V;X++)if(J=K[X],J.type===A.EntityDeclaration&&!J.pe)C[J.name]=J;return new F(C)}}),Object.defineProperty(W.prototype,"notations",{get:function(){var J,X,V,C,K;C={},K=this.children;for(X=0,V=K.length;X<V;X++)if(J=K[X],J.type===A.NotationDeclaration)C[J.name]=J;return new F(C)}}),Object.defineProperty(W.prototype,"publicId",{get:function(){return this.pubID}}),Object.defineProperty(W.prototype,"systemId",{get:function(){return this.sysID}}),Object.defineProperty(W.prototype,"internalSubset",{get:function(){throw new Error("This DOM method is not implemented."+this.debugInfo())}}),W}.call(this)}).call(sPB)});
var vG1=E((pF8)=>{function cF8(A,B,Q){if(Q===void 0)Q=Array.prototype;if(A&&typeof Q.find==="function")return Q.find.call(A,B);for(var D=0;D<A.length;D++)if(Object.prototype.hasOwnProperty.call(A,D)){var Z=A[D];if(B.call(void 0,Z,D,A))return Z}}function QN0(A,B){if(B===void 0)B=Object;return B&&typeof B.freeze==="function"?B.freeze(A):A}function lF8(A,B){if(A===null||typeof A!=="object")throw new TypeError("target is not an object");for(var Q in B)if(Object.prototype.hasOwnProperty.call(B,Q))A[Q]=B[Q];return A}var zTB=QN0({HTML:"text/html",isHTML:function(A){return A===zTB.HTML},XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),ETB=QN0({HTML:"http://www.w3.org/1999/xhtml",isHTML:function(A){return A===ETB.HTML},SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});pF8.assign=lF8;pF8.find=cF8;pF8.freeze=QN0;pF8.MIME_TYPE=zTB;pF8.NAMESPACE=ETB});
var wN0=E((DSB,ZSB)=>{(function(){var A,B,Q;Q=cK(),A=IG(),ZSB.exports=B=class D extends Q{constructor(Z){super(Z);this.type=A.Dummy}clone(){return Object.create(this)}toString(Z){return""}}}).call(DSB)});
var wb1=E((pPB,iPB)=>{(function(){var A,B,Q;Q=cK(),A=IG(),iPB.exports=B=class D extends Q{constructor(Z,G,F){super(Z);if(G==null)throw new Error("Missing DTD element name. "+this.debugInfo());if(!F)F="(#PCDATA)";if(Array.isArray(F))F="("+F.join(",")+")";this.name=this.stringify.name(G),this.type=A.ElementDeclaration,this.value=this.stringify.dtdElementValue(F)}toString(Z){return this.options.writer.dtdElement(this,this.options.writer.filterOptions(Z))}}}).call(pPB)});
var zb1=E((gPB,uPB)=>{(function(){var A,B,Q,D;({isObject:D}=nM()),Q=cK(),A=IG(),uPB.exports=B=class Z extends Q{constructor(G,F,I,Y){super(G);if(D(F))({version:F,encoding:I,standalone:Y}=F);if(!F)F="1.0";if(this.type=A.Declaration,this.version=this.stringify.xmlVersion(F),I!=null)this.encoding=this.stringify.xmlEncoding(I);if(Y!=null)this.standalone=this.stringify.xmlStandalone(Y)}toString(G){return this.options.writer.declaration(this,this.options.writer.filterOptions(G))}}}).call(gPB)});

module.exports = bSB;
