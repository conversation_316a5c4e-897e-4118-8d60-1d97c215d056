// Filename: phase1-analyze.js (Upgraded Version)
// 阶段一：分析与标记（只读，内存优化版）
// 用法: node --max-old-space-size=8192 --expose-gc phase1-analyze.js <source-file.js>

const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const NPM_FINGERPRINTS = require('../npm-finger2.cjs');
const t = require('@babel/types');

// --- 数据结构 ---
const moduleRegistry = new Map();

// --- 主函数 ---
async function main() {
    const sourceFilePath = process.argv[2];
    if (!sourceFilePath) {
        console.error("错误: 请提供源文件路径。");
        console.error("用法: node --max-old-space-size=8192 --expose-gc phase1-analyze.js <source-file.js>");
        process.exit(1);
    }
    
    console.log(`[PHASE 1] 正在开始分析文件: ${sourceFilePath}`);
    logMemoryUsage('启动时');

    const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');
    logMemoryUsage('文件读取后');

    // 1. AST解析
    console.time("1. AST解析");
    const ast = parser.parse(sourceCode, {
        sourceType: 'module',
        plugins: ['importMeta'],
        // 关键：在这一步，我们依然需要range来获取源码片段用于指纹识别
        ranges: true, 
        attachComments: false,
    });
    console.timeEnd("1. AST解析");
    logMemoryUsage('AST解析后');

    // 2. 构建模块注册表
    console.time("2. 构建模块注册表");
    buildModuleRegistry(ast, sourceCode);
    console.timeEnd("2. 构建模块注册表");
    logMemoryUsage('模块注册表构建后');

    // 3. 构建依赖图
    console.time("3. 构建依赖图");
    buildDependencyGraph(ast);
    console.timeEnd("3. 构建依赖图");
    logMemoryUsage('依赖图构建后');

    // 4. 识别库种子模块
    console.time("4. 识别库种子模块");
    const librarySeedModules = identifySeedModules();
    console.timeEnd("4. 识别库种子模块");
    logMemoryUsage('种子识别后');

    // 5. 传播“库”标签
    console.time("5. 传播库标签");
    const totalLibraryModules = propagateLibraryTags(librarySeedModules);
    console.timeEnd("5. 传播库标签");
    logMemoryUsage('标签传播后');

    // 6. 生成分析报告
    generateReport(totalLibraryModules);
    console.log("\n[PHASE 1] 分析完成！报告已生成: analysis-report.json");
    logMemoryUsage('完成时');
}

function logMemoryUsage(stage) {
    // 强制GC
    if (global.gc) { global.gc(); }
    const usage = process.memoryUsage();
    const heapUsed = (usage.heapUsed / 1024 / 1024).toFixed(2);
    console.log(`  💾 [MEM at ${stage}] Heap used: ${heapUsed} MB`);
}

function buildModuleRegistry(ast, sourceCode) {
    traverse(ast, {
        noScope: true, // 优化：在顶层遍历时关闭作用域分析
        VariableDeclarator(path) {
            const { node } = path;
            if (
                t.isIdentifier(node.id) &&
                node.init &&
                t.isCallExpression(node.init) &&
                t.isIdentifier(node.init.callee, { name: 'z' })
            ) {
                const moduleName = node.id.name;
                const codeSlice = sourceCode.substring(node.range[0], node.range[1]);

                moduleRegistry.set(moduleName, {
                    name: moduleName,
                    dependencies: new Set(),
                    code: codeSlice,
                    size: codeSlice.length,
                    isLibrary: false,
                    libraryName: null,
                });
            }
        },
    });
    console.log(`✅ 模块注册表构建完毕，共 ${moduleRegistry.size} 个模块。`);
}

function buildDependencyGraph(ast) {
    // 使用Babel的强大能力，一次遍历完成所有模块的依赖分析
    traverse(ast, {
        // 进入模块函数体再进行详细分析
        CallExpression(path) {
            if (t.isIdentifier(path.node.callee, { name: 'z' })) {
                const declaratorPath = path.findParent(p => p.isVariableDeclarator());
                if (!declaratorPath || !t.isIdentifier(declaratorPath.node.id)) return;

                const moduleName = declaratorPath.node.id.name;
                const moduleInfo = moduleRegistry.get(moduleName);
                if (!moduleInfo) return;

                const moduleBodyPath = path.get('arguments.0');
                if (!moduleBodyPath.isFunction()) return;

                // 在模块函数的作用域内遍历，查找未绑定的引用
                moduleBodyPath.traverse({
                    Identifier(subPath) {
                        if (subPath.isReferencedIdentifier() && !subPath.scope.hasBinding(subPath.node.name)) {
                            if (moduleRegistry.has(subPath.node.name)) {
                                moduleInfo.dependencies.add(subPath.node.name);
                            }
                        }
                    }
                });
            }
        }
    });
    console.log(`✅ 依赖图构建完毕。`);
}

function identifySeedModules() {
    console.log('  -> 开始指纹匹配...');
    const seeds = new Set();
    for (const [moduleName, moduleInfo] of moduleRegistry.entries()) {
        for (const [libName, fingerprint] of Object.entries(NPM_FINGERPRINTS)) {
            let score = 0;
            // 字符串和正则匹配
            for (const fpString of fingerprint.strings) {
                if (fpString instanceof RegExp) {
                    if (fpString.test(moduleInfo.code)) score += 1.5; // 正则匹配权重更高
                } else {
                    if (moduleInfo.code.includes(fpString)) score++;
                }
            }
            if (score >= 2) {
                moduleInfo.isLibrary = true;
                moduleInfo.libraryName = libName;
                seeds.add(moduleName);
                console.log(`    [SEED FOUND] ${moduleName} -> ${libName} (score: ${score.toFixed(1)})`);
                break; // 找到一个就够了
            }
        }
        // 清理代码以释放内存
        moduleInfo.code = null;
    }
    logMemoryUsage('指纹匹配后（代码已清理）');
    return seeds;
}

function propagateLibraryTags(seedModules) {
    const queue = [...seedModules];
    const visited = new Set(seedModules);

    while (queue.length > 0) {
        const currentModuleName = queue.shift();
        const currentModule = moduleRegistry.get(currentModuleName);

        for (const depName of currentModule.dependencies) {
            if (!visited.has(depName)) {
                const depModule = moduleRegistry.get(depName);
                if (depModule) {
                    depModule.isLibrary = true;
                    depModule.libraryName = currentModule.libraryName;
                    visited.add(depName);
                    queue.push(depName);
                }
            }
        }
    }
    return visited.size;
}

function generateReport(totalLibraryModules) {
    const report = {
        stats: {
            totalModules: moduleRegistry.size,
            libraryModules: totalLibraryModules,
            appCodeModules: moduleRegistry.size - totalLibraryModules,
            pruneRatio: `${((totalLibraryModules / moduleRegistry.size) * 100).toFixed(2)}%`,
        },
        modules: {},
    };

    for (const [name, info] of moduleRegistry.entries()) {
        // 只保留分析结果，不保留AST路径等大对象
        report.modules[name] = {
            isLibrary: info.isLibrary,
            libraryName: info.libraryName,
            dependencies: [...info.dependencies],
            size: info.size,
        };
    }
    fs.writeFileSync('analysis-report.json', JSON.stringify(report, null, 2));
}

main().catch(error => {
    console.error("\n❌ 处理过程中发生严重错误:", error.stack);
    if (error.message.includes('heap out of memory')) {
        console.error("\n🔧 内存不足解决方案: node --max-old-space-size=8192 --expose-gc a.js <your-file.js>");
    }
    process.exit(1);
});