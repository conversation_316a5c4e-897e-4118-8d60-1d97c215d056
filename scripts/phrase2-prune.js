// Filename: phase2-prune.js (Upgraded Version)
// 阶段二：AST剪枝与生成（执行修改）
// 用法: node --max-old-space-size=8192 phase2-prune.js <source-file.js>

const fs = require('fs');
const path = require('path');
const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

async function main() {
    const sourceFilePath = process.argv[2];
    if (!sourceFilePath) {
        console.error("错误: 请提供源文件路径。");
        console.error("用法: node --max-old-space-size=8192 phase2-prune.js <source-file.js>");
        process.exit(1);
    }
    
    const reportPath = 'analysis-report.json';
    if (!fs.existsSync(reportPath)) {
        console.error("错误: 未找到 analysis-report.json。请先运行 phase1-analyze.js。");
        process.exit(1);
    }

    console.log(`[PHASE 2] 正在开始剪枝文件: ${sourceFilePath}`);
    const sourceCode = fs.readFileSync(sourceFilePath, 'utf8');
    const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    const libraryModuleNames = new Set(
        Object.keys(report.modules).filter(name => report.modules[name].isLibrary)
    );

    console.time("1. AST解析");
    const ast = parser.parse(sourceCode, { 
        sourceType: 'module',
        plugins: ['importMeta'],
    });
    console.timeEnd("1. AST解析");

    console.time("2. 安全地修改AST");
    // 使用过滤方式安全地移除整个库模块声明，而不是在遍历中修改
    ast.program.body = ast.program.body.filter(topLevelNode => {
        if (t.isVariableDeclaration(topLevelNode)) {
            // 过滤掉那些只包含库模块的声明
            topLevelNode.declarations = topLevelNode.declarations.filter(declarator => {
                if (t.isIdentifier(declarator.id)) {
                    return !libraryModuleNames.has(declarator.id.name);
                }
                return true;
            });
            // 如果一个 var/let/const 声明下的所有变量都被移除了，那就把整个声明都移除
            return topLevelNode.declarations.length > 0;
        }
        return true;
    });

    // 注入库的占位符声明
    const uniqueLibraries = new Map();
    for (const moduleName of libraryModuleNames) {
        const libInfo = report.modules[moduleName];
        if (libInfo.libraryName && !uniqueLibraries.has(libInfo.libraryName)) {
            uniqueLibraries.set(libInfo.libraryName, []);
        }
        if(libInfo.libraryName) {
            uniqueLibraries.get(libInfo.libraryName).push(moduleName);
        }
    }
    
    const placeholderDeclarations = [];
    for (const [libName, originalNames] of uniqueLibraries.entries()) {
        const comment = ` Library placeholder for: ${libName} (originally ${originalNames.slice(0, 3).join(', ')}${originalNames.length > 3 ? '...' : ''}) `;
        const placeholder = t.variableDeclaration('const', [
            t.variableDeclarator(
                t.identifier(libName.replace(/[^a-zA-Z0-9_$]/g, '_')), // 创建一个合法的变量名
                t.callExpression(
                    t.identifier('require'), 
                    [t.stringLiteral(libName)]
                )
            )
        ]);
        t.addComment(placeholder, "leading", comment, true);
        placeholderDeclarations.push(placeholder);
    }

    // 在AST的开头注入这些占位符
    ast.program.body.unshift(...placeholderDeclarations);
    console.timeEnd("2. 安全地修改AST");

    console.time("3. 生成剪枝后代码");
    const { code: prunedCode } = generate(ast, {
        comments: true,
        compact: false,
        retainLines: false,
    });
    fs.writeFileSync('pruned-cli.js', prunedCode, 'utf8');
    console.timeEnd("3. 生成剪枝后代码");
    console.log("✅ 剪枝后的代码已生成: pruned-cli.js");

    console.time("4. 生成依赖地图");
    generateDependencyMap(report);
    console.timeEnd("4. 生成依赖地图");
    console.log("✅ 业务逻辑依赖地图已生成: dependency-map.json");

    console.log("\n[PHASE 2] 剪枝完成！");
}

function generateDependencyMap(report) {
    const dependencyMap = {};
    for (const [moduleName, moduleInfo] of Object.entries(report.modules)) {
        if (!moduleInfo.isLibrary) {
            dependencyMap[moduleName] = {
                app_dependencies: moduleInfo.dependencies.filter(dep => !report.modules[dep].isLibrary),
                library_dependencies: [...new Set(
                    moduleInfo.dependencies
                        .filter(dep => report.modules[dep].isLibrary)
                        .map(dep => report.modules[dep].libraryName || 'unknown_library')
                )],
            };
        }
    }
    fs.writeFileSync('dependency-map.json', JSON.stringify(dependencyMap, null, 2));
}

main().catch(error => {
    console.error("\n❌ 处理过程中发生严重错误:", error.stack);
    if (error.message.includes('heap out of memory')) {
        console.error("\n🔧 内存不足解决方案: node --max-old-space-size=8192 phase2-prune.js <source-file.js>");
    }
    process.exit(1);
});